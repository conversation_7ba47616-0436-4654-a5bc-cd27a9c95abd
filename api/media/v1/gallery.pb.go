// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/media/v1/gallery.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Deployment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name             string                       `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Tags             []string                     `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	Lnglat           []float64                    `protobuf:"fixed64,3,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	Location         string                       `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	RequiredPictures *Deployment_RequiredPictures `protobuf:"bytes,6,opt,name=requiredPictures,proto3" json:"requiredPictures,omitempty"`
}

func (x *Deployment) Reset() {
	*x = Deployment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment) ProtoMessage() {}

func (x *Deployment) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment.ProtoReflect.Descriptor instead.
func (*Deployment) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{0}
}

func (x *Deployment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Deployment) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Deployment) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *Deployment) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Deployment) GetRequiredPictures() *Deployment_RequiredPictures {
	if x != nil {
		return x.RequiredPictures
	}
	return nil
}

type GalleryDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime     float64     `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime     float64     `protobuf:"fixed64,3,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	Sn              string      `protobuf:"bytes,4,opt,name=sn,proto3" json:"sn,omitempty"`
	Type            string      `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Model           string      `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`
	Category        string      `protobuf:"bytes,7,opt,name=category,proto3" json:"category,omitempty"`
	FirmwareVersion string      `protobuf:"bytes,8,opt,name=firmwareVersion,proto3" json:"firmwareVersion,omitempty"`
	Deployment      *Deployment `protobuf:"bytes,9,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *GalleryDevice) Reset() {
	*x = GalleryDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GalleryDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GalleryDevice) ProtoMessage() {}

func (x *GalleryDevice) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GalleryDevice.ProtoReflect.Descriptor instead.
func (*GalleryDevice) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{1}
}

func (x *GalleryDevice) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GalleryDevice) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *GalleryDevice) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *GalleryDevice) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *GalleryDevice) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GalleryDevice) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *GalleryDevice) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *GalleryDevice) GetFirmwareVersion() string {
	if x != nil {
		return x.FirmwareVersion
	}
	return ""
}

func (x *GalleryDevice) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type Airline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime float64 `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	Name        string  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	IsDefault   bool    `protobuf:"varint,4,opt,name=isDefault,proto3" json:"isDefault,omitempty"`
}

func (x *Airline) Reset() {
	*x = Airline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Airline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Airline) ProtoMessage() {}

func (x *Airline) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Airline.ProtoReflect.Descriptor instead.
func (*Airline) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{2}
}

func (x *Airline) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Airline) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *Airline) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Airline) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

type Voyage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StartTime  float64 `protobuf:"fixed64,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime    float64 `protobuf:"fixed64,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Runtime    int32   `protobuf:"varint,4,opt,name=runtime,proto3" json:"runtime,omitempty"`
	Mileage    float64 `protobuf:"fixed64,5,opt,name=mileage,proto3" json:"mileage,omitempty"`
	Status     string  `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	Images     int32   `protobuf:"varint,7,opt,name=images,proto3" json:"images,omitempty"`
	Videos     int32   `protobuf:"varint,8,opt,name=videos,proto3" json:"videos,omitempty"`
	IsFlown    bool    `protobuf:"varint,9,opt,name=isFlown,proto3" json:"isFlown,omitempty"`
	Name       string  `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	MediaTotal int32   `protobuf:"varint,11,opt,name=mediaTotal,proto3" json:"mediaTotal,omitempty"`
}

func (x *Voyage) Reset() {
	*x = Voyage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Voyage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Voyage) ProtoMessage() {}

func (x *Voyage) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Voyage.ProtoReflect.Descriptor instead.
func (*Voyage) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{3}
}

func (x *Voyage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Voyage) GetStartTime() float64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Voyage) GetEndTime() float64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Voyage) GetRuntime() int32 {
	if x != nil {
		return x.Runtime
	}
	return 0
}

func (x *Voyage) GetMileage() float64 {
	if x != nil {
		return x.Mileage
	}
	return 0
}

func (x *Voyage) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Voyage) GetImages() int32 {
	if x != nil {
		return x.Images
	}
	return 0
}

func (x *Voyage) GetVideos() int32 {
	if x != nil {
		return x.Videos
	}
	return 0
}

func (x *Voyage) GetIsFlown() bool {
	if x != nil {
		return x.IsFlown
	}
	return false
}

func (x *Voyage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Voyage) GetMediaTotal() int32 {
	if x != nil {
		return x.MediaTotal
	}
	return 0
}

type GalleryMediaItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VoyageId    int64                       `protobuf:"varint,3,opt,name=voyageId,proto3" json:"voyageId,omitempty"`
	WaypointId  int64                       `protobuf:"varint,4,opt,name=waypointId,proto3" json:"waypointId,omitempty"`
	Id          int64                       `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	Type        string                      `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Url         string                      `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	Name        string                      `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	CreatedTime float64                     `protobuf:"fixed64,9,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	ShootTime   float64                     `protobuf:"fixed64,10,opt,name=shootTime,proto3" json:"shootTime,omitempty"`
	Lnglat      []float64                   `protobuf:"fixed64,11,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	VInfo       *GalleryMediaItem_VideoInfo `protobuf:"bytes,12,opt,name=vInfo,proto3" json:"vInfo,omitempty"`
	PInfo       *GalleryMediaItem_PhotoInfo `protobuf:"bytes,13,opt,name=pInfo,proto3" json:"pInfo,omitempty"`
	SubType     string                      `protobuf:"bytes,14,opt,name=subType,proto3" json:"subType,omitempty"`
	Payload     string                      `protobuf:"bytes,15,opt,name=payload,proto3" json:"payload,omitempty"`
	Device      *GalleryDevice              `protobuf:"bytes,16,opt,name=device,proto3" json:"device,omitempty"`
	Airline     *Airline                    `protobuf:"bytes,17,opt,name=airline,proto3" json:"airline,omitempty"`
	Voyage      *Voyage                     `protobuf:"bytes,18,opt,name=voyage,proto3" json:"voyage,omitempty"`
}

func (x *GalleryMediaItem) Reset() {
	*x = GalleryMediaItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GalleryMediaItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GalleryMediaItem) ProtoMessage() {}

func (x *GalleryMediaItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GalleryMediaItem.ProtoReflect.Descriptor instead.
func (*GalleryMediaItem) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{4}
}

func (x *GalleryMediaItem) GetVoyageId() int64 {
	if x != nil {
		return x.VoyageId
	}
	return 0
}

func (x *GalleryMediaItem) GetWaypointId() int64 {
	if x != nil {
		return x.WaypointId
	}
	return 0
}

func (x *GalleryMediaItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GalleryMediaItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GalleryMediaItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GalleryMediaItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GalleryMediaItem) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *GalleryMediaItem) GetShootTime() float64 {
	if x != nil {
		return x.ShootTime
	}
	return 0
}

func (x *GalleryMediaItem) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *GalleryMediaItem) GetVInfo() *GalleryMediaItem_VideoInfo {
	if x != nil {
		return x.VInfo
	}
	return nil
}

func (x *GalleryMediaItem) GetPInfo() *GalleryMediaItem_PhotoInfo {
	if x != nil {
		return x.PInfo
	}
	return nil
}

func (x *GalleryMediaItem) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *GalleryMediaItem) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

func (x *GalleryMediaItem) GetDevice() *GalleryDevice {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *GalleryMediaItem) GetAirline() *Airline {
	if x != nil {
		return x.Airline
	}
	return nil
}

func (x *GalleryMediaItem) GetVoyage() *Voyage {
	if x != nil {
		return x.Voyage
	}
	return nil
}

type DeleteGalleryMediaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids       []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	VoyageIds []int64 `protobuf:"varint,2,rep,packed,name=voyageIds,proto3" json:"voyageIds,omitempty"`
}

func (x *DeleteGalleryMediaRequest) Reset() {
	*x = DeleteGalleryMediaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGalleryMediaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGalleryMediaRequest) ProtoMessage() {}

func (x *DeleteGalleryMediaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGalleryMediaRequest.ProtoReflect.Descriptor instead.
func (*DeleteGalleryMediaRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteGalleryMediaRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DeleteGalleryMediaRequest) GetVoyageIds() []int64 {
	if x != nil {
		return x.VoyageIds
	}
	return nil
}

type DeleteGalleryMediaReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *DeleteGalleryMediaReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DeleteGalleryMediaReply) Reset() {
	*x = DeleteGalleryMediaReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGalleryMediaReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGalleryMediaReply) ProtoMessage() {}

func (x *DeleteGalleryMediaReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGalleryMediaReply.ProtoReflect.Descriptor instead.
func (*DeleteGalleryMediaReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteGalleryMediaReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteGalleryMediaReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeleteGalleryMediaReply) GetData() *DeleteGalleryMediaReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListGalleryMediaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size        int32    `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	StartTime   int64    `protobuf:"varint,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime     int64    `protobuf:"varint,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Search      string   `protobuf:"bytes,5,opt,name=search,proto3" json:"search,omitempty"`
	Types       []string `protobuf:"bytes,6,rep,name=types,proto3" json:"types,omitempty"`
	Categories  []string `protobuf:"bytes,7,rep,name=categories,proto3" json:"categories,omitempty"`
	DeviceTypes []string `protobuf:"bytes,8,rep,name=deviceTypes,proto3" json:"deviceTypes,omitempty"`
	DeviceIds   []int64  `protobuf:"varint,9,rep,packed,name=deviceIds,proto3" json:"deviceIds,omitempty"`
	AirlineIds  []int64  `protobuf:"varint,10,rep,packed,name=airlineIds,proto3" json:"airlineIds,omitempty"`
	FileTypes   []string `protobuf:"bytes,11,rep,name=fileTypes,proto3" json:"fileTypes,omitempty"`
	Grouped     string   `protobuf:"bytes,12,opt,name=grouped,proto3" json:"grouped,omitempty"`
	LenTypes    []string `protobuf:"bytes,13,rep,name=lenTypes,proto3" json:"lenTypes,omitempty"`
}

func (x *ListGalleryMediaRequest) Reset() {
	*x = ListGalleryMediaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGalleryMediaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGalleryMediaRequest) ProtoMessage() {}

func (x *ListGalleryMediaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGalleryMediaRequest.ProtoReflect.Descriptor instead.
func (*ListGalleryMediaRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{7}
}

func (x *ListGalleryMediaRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGalleryMediaRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListGalleryMediaRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListGalleryMediaRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListGalleryMediaRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *ListGalleryMediaRequest) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *ListGalleryMediaRequest) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *ListGalleryMediaRequest) GetDeviceTypes() []string {
	if x != nil {
		return x.DeviceTypes
	}
	return nil
}

func (x *ListGalleryMediaRequest) GetDeviceIds() []int64 {
	if x != nil {
		return x.DeviceIds
	}
	return nil
}

func (x *ListGalleryMediaRequest) GetAirlineIds() []int64 {
	if x != nil {
		return x.AirlineIds
	}
	return nil
}

func (x *ListGalleryMediaRequest) GetFileTypes() []string {
	if x != nil {
		return x.FileTypes
	}
	return nil
}

func (x *ListGalleryMediaRequest) GetGrouped() string {
	if x != nil {
		return x.Grouped
	}
	return ""
}

func (x *ListGalleryMediaRequest) GetLenTypes() []string {
	if x != nil {
		return x.LenTypes
	}
	return nil
}

type ListGalleryMediaReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ListGalleryMediaReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListGalleryMediaReply) Reset() {
	*x = ListGalleryMediaReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGalleryMediaReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGalleryMediaReply) ProtoMessage() {}

func (x *ListGalleryMediaReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGalleryMediaReply.ProtoReflect.Descriptor instead.
func (*ListGalleryMediaReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{8}
}

func (x *ListGalleryMediaReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListGalleryMediaReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListGalleryMediaReply) GetData() *ListGalleryMediaReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetVideoMediaDownloadUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetVideoMediaDownloadUrlRequest) Reset() {
	*x = GetVideoMediaDownloadUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVideoMediaDownloadUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVideoMediaDownloadUrlRequest) ProtoMessage() {}

func (x *GetVideoMediaDownloadUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVideoMediaDownloadUrlRequest.ProtoReflect.Descriptor instead.
func (*GetVideoMediaDownloadUrlRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{9}
}

func (x *GetVideoMediaDownloadUrlRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetVideoMediaDownloadUrlReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *GetVideoMediaDownloadUrlReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetVideoMediaDownloadUrlReply) Reset() {
	*x = GetVideoMediaDownloadUrlReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVideoMediaDownloadUrlReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVideoMediaDownloadUrlReply) ProtoMessage() {}

func (x *GetVideoMediaDownloadUrlReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVideoMediaDownloadUrlReply.ProtoReflect.Descriptor instead.
func (*GetVideoMediaDownloadUrlReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{10}
}

func (x *GetVideoMediaDownloadUrlReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetVideoMediaDownloadUrlReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetVideoMediaDownloadUrlReply) GetData() *GetVideoMediaDownloadUrlReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CleanUpTmpDownloadableFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ClientId    string                                    `protobuf:"bytes,2,opt,name=clientId,proto3" json:"clientId,omitempty"`
	ExecuteTime int64                                     `protobuf:"varint,3,opt,name=executeTime,proto3" json:"executeTime,omitempty"`
	Body        *CleanUpTmpDownloadableFileRequestPayload `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *CleanUpTmpDownloadableFileRequest) Reset() {
	*x = CleanUpTmpDownloadableFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanUpTmpDownloadableFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanUpTmpDownloadableFileRequest) ProtoMessage() {}

func (x *CleanUpTmpDownloadableFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanUpTmpDownloadableFileRequest.ProtoReflect.Descriptor instead.
func (*CleanUpTmpDownloadableFileRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{11}
}

func (x *CleanUpTmpDownloadableFileRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CleanUpTmpDownloadableFileRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *CleanUpTmpDownloadableFileRequest) GetExecuteTime() int64 {
	if x != nil {
		return x.ExecuteTime
	}
	return 0
}

func (x *CleanUpTmpDownloadableFileRequest) GetBody() *CleanUpTmpDownloadableFileRequestPayload {
	if x != nil {
		return x.Body
	}
	return nil
}

type CleanUpTmpDownloadableFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                                `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *CleanUpTmpDownloadableFileReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CleanUpTmpDownloadableFileReply) Reset() {
	*x = CleanUpTmpDownloadableFileReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanUpTmpDownloadableFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanUpTmpDownloadableFileReply) ProtoMessage() {}

func (x *CleanUpTmpDownloadableFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanUpTmpDownloadableFileReply.ProtoReflect.Descriptor instead.
func (*CleanUpTmpDownloadableFileReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{12}
}

func (x *CleanUpTmpDownloadableFileReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CleanUpTmpDownloadableFileReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CleanUpTmpDownloadableFileReply) GetData() *CleanUpTmpDownloadableFileReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

// repeated string pictures = 5;
type Deployment_RequiredPictures struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceImg string `protobuf:"bytes,1,opt,name=deviceImg,proto3" json:"deviceImg,omitempty"`
	EnvImg    string `protobuf:"bytes,2,opt,name=envImg,proto3" json:"envImg,omitempty"`
	ShopImg   string `protobuf:"bytes,3,opt,name=shopImg,proto3" json:"shopImg,omitempty"`
}

func (x *Deployment_RequiredPictures) Reset() {
	*x = Deployment_RequiredPictures{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment_RequiredPictures) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment_RequiredPictures) ProtoMessage() {}

func (x *Deployment_RequiredPictures) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment_RequiredPictures.ProtoReflect.Descriptor instead.
func (*Deployment_RequiredPictures) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Deployment_RequiredPictures) GetDeviceImg() string {
	if x != nil {
		return x.DeviceImg
	}
	return ""
}

func (x *Deployment_RequiredPictures) GetEnvImg() string {
	if x != nil {
		return x.EnvImg
	}
	return ""
}

func (x *Deployment_RequiredPictures) GetShopImg() string {
	if x != nil {
		return x.ShopImg
	}
	return ""
}

type GalleryMediaItem_VideoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsFull   bool   `protobuf:"varint,1,opt,name=isFull,proto3" json:"isFull,omitempty"`
	CoverImg string `protobuf:"bytes,2,opt,name=coverImg,proto3" json:"coverImg,omitempty"`
	// seconds，全程录像时长
	Duration float64 `protobuf:"fixed64,3,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *GalleryMediaItem_VideoInfo) Reset() {
	*x = GalleryMediaItem_VideoInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GalleryMediaItem_VideoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GalleryMediaItem_VideoInfo) ProtoMessage() {}

func (x *GalleryMediaItem_VideoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GalleryMediaItem_VideoInfo.ProtoReflect.Descriptor instead.
func (*GalleryMediaItem_VideoInfo) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{4, 0}
}

func (x *GalleryMediaItem_VideoInfo) GetIsFull() bool {
	if x != nil {
		return x.IsFull
	}
	return false
}

func (x *GalleryMediaItem_VideoInfo) GetCoverImg() string {
	if x != nil {
		return x.CoverImg
	}
	return ""
}

func (x *GalleryMediaItem_VideoInfo) GetDuration() float64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type GalleryMediaItem_PhotoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Thumbnail string `protobuf:"bytes,1,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
}

func (x *GalleryMediaItem_PhotoInfo) Reset() {
	*x = GalleryMediaItem_PhotoInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GalleryMediaItem_PhotoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GalleryMediaItem_PhotoInfo) ProtoMessage() {}

func (x *GalleryMediaItem_PhotoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GalleryMediaItem_PhotoInfo.ProtoReflect.Descriptor instead.
func (*GalleryMediaItem_PhotoInfo) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{4, 1}
}

func (x *GalleryMediaItem_PhotoInfo) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

type DeleteGalleryMediaReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteGalleryMediaReply_Data) Reset() {
	*x = DeleteGalleryMediaReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGalleryMediaReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGalleryMediaReply_Data) ProtoMessage() {}

func (x *DeleteGalleryMediaReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGalleryMediaReply_Data.ProtoReflect.Descriptor instead.
func (*DeleteGalleryMediaReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{6, 0}
}

func (x *DeleteGalleryMediaReply_Data) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type ListGalleryMediaReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total      int32               `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List       []*GalleryMediaItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	Page       int32               `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Size       int32               `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	TotalFiles float64             `protobuf:"fixed64,5,opt,name=totalFiles,proto3" json:"totalFiles,omitempty"`
}

func (x *ListGalleryMediaReply_Data) Reset() {
	*x = ListGalleryMediaReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGalleryMediaReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGalleryMediaReply_Data) ProtoMessage() {}

func (x *ListGalleryMediaReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGalleryMediaReply_Data.ProtoReflect.Descriptor instead.
func (*ListGalleryMediaReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListGalleryMediaReply_Data) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListGalleryMediaReply_Data) GetList() []*GalleryMediaItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListGalleryMediaReply_Data) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGalleryMediaReply_Data) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListGalleryMediaReply_Data) GetTotalFiles() float64 {
	if x != nil {
		return x.TotalFiles
	}
	return 0
}

type GetVideoMediaDownloadUrlReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// 链接过期时间戳
	Expires float64 `protobuf:"fixed64,2,opt,name=expires,proto3" json:"expires,omitempty"`
}

func (x *GetVideoMediaDownloadUrlReply_Data) Reset() {
	*x = GetVideoMediaDownloadUrlReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVideoMediaDownloadUrlReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVideoMediaDownloadUrlReply_Data) ProtoMessage() {}

func (x *GetVideoMediaDownloadUrlReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVideoMediaDownloadUrlReply_Data.ProtoReflect.Descriptor instead.
func (*GetVideoMediaDownloadUrlReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{10, 0}
}

func (x *GetVideoMediaDownloadUrlReply_Data) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GetVideoMediaDownloadUrlReply_Data) GetExpires() float64 {
	if x != nil {
		return x.Expires
	}
	return 0
}

type CleanUpTmpDownloadableFileRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key    string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Bucket string `protobuf:"bytes,2,opt,name=bucket,proto3" json:"bucket,omitempty"`
}

func (x *CleanUpTmpDownloadableFileRequestPayload) Reset() {
	*x = CleanUpTmpDownloadableFileRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanUpTmpDownloadableFileRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanUpTmpDownloadableFileRequestPayload) ProtoMessage() {}

func (x *CleanUpTmpDownloadableFileRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanUpTmpDownloadableFileRequestPayload.ProtoReflect.Descriptor instead.
func (*CleanUpTmpDownloadableFileRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{11, 0}
}

func (x *CleanUpTmpDownloadableFileRequestPayload) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *CleanUpTmpDownloadableFileRequestPayload) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

type CleanUpTmpDownloadableFileReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CleanUpTmpDownloadableFileReply_Data) Reset() {
	*x = CleanUpTmpDownloadableFileReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_gallery_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanUpTmpDownloadableFileReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanUpTmpDownloadableFileReply_Data) ProtoMessage() {}

func (x *CleanUpTmpDownloadableFileReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_gallery_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanUpTmpDownloadableFileReply_Data.ProtoReflect.Descriptor instead.
func (*CleanUpTmpDownloadableFileReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_gallery_proto_rawDescGZIP(), []int{12, 0}
}

func (x *CleanUpTmpDownloadableFileReply_Data) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

var File_api_media_v1_gallery_proto protoreflect.FileDescriptor

var file_api_media_v1_gallery_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x67,
	0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x6f, 0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e,
	0x67, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c,
	0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55,
	0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x50, 0x69, 0x63, 0x74, 0x75,
	0x72, 0x65, 0x73, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x50, 0x69, 0x63,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x1a, 0x62, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6d, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x6d, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x76, 0x49, 0x6d,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x76, 0x49, 0x6d, 0x67, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x68, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x68, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x22, 0x9d, 0x02, 0x0a, 0x0d, 0x47, 0x61,
	0x6c, 0x6c, 0x65, 0x72, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x38, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x6d, 0x0a, 0x07, 0x41, 0x69, 0x72,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x9a, 0x02, 0x0a, 0x06, 0x56, 0x6f, 0x79,
	0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x46, 0x6c, 0x6f,
	0x77, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x46, 0x6c, 0x6f, 0x77,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xc0, 0x05, 0x0a, 0x10, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72,
	0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x6f,
	0x79, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x76, 0x6f,
	0x79, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x01,
	0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x05, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x33, 0x0a, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x6c, 0x6c,
	0x65, 0x72, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x2f, 0x0a, 0x07, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x07, 0x61, 0x69, 0x72, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x2c, 0x0a, 0x06, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x52, 0x06, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65,
	0x1a, 0x5b, 0x0a, 0x09, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a,
	0x06, 0x69, 0x73, 0x46, 0x75, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69,
	0x73, 0x46, 0x75, 0x6c, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d,
	0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x29, 0x0a,
	0x09, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68,
	0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74,
	0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x22, 0x4b, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x6f, 0x79, 0x61, 0x67,
	0x65, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x76, 0x6f, 0x79, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x3e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a,
	0x1e, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x88, 0x03, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1f, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x6d, 0x61, 0x78, 0x3d, 0x31, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x69, 0x72, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x65, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x65, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x6c, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x9e, 0x02, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x3c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x98, 0x01, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x32, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x6c,
	0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x31, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xc7,
	0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x44,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x1a, 0x32, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x22, 0xf3, 0x01, 0x0a, 0x21, 0x43, 0x6c, 0x65,
	0x61, 0x6e, 0x55, 0x70, 0x54, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x61,
	0x62, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55,
	0x70, 0x54, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x33, 0x0a, 0x07, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0xb7,
	0x01, 0x0a, 0x1f, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x54, 0x6d, 0x70, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x46, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c,
	0x65, 0x61, 0x6e, 0x55, 0x70, 0x54, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x1e, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0x8a, 0x05, 0x0a, 0x07, 0x47, 0x61, 0x6c,
	0x6c, 0x65, 0x72, 0x79, 0x12, 0x91, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47,
	0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72,
	0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x25, 0x22, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x2f, 0x67, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x2f, 0x72, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x85, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73,
	0x74, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1f, 0x22, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2f, 0x67, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a,
	0x12, 0xa1, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x2d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x23, 0x12, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2f, 0x67, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x2f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x55, 0x52, 0x4c, 0x12, 0xbe, 0x01, 0x0a, 0x1a, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70,
	0x54, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x54, 0x6d, 0x70, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x54, 0x6d, 0x70, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x40, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x22, 0x35, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f,
	0x67, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x2f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x54,
	0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x3a, 0x01, 0x2a, 0x42, 0x3e, 0x0a, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69,
	0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_media_v1_gallery_proto_rawDescOnce sync.Once
	file_api_media_v1_gallery_proto_rawDescData = file_api_media_v1_gallery_proto_rawDesc
)

func file_api_media_v1_gallery_proto_rawDescGZIP() []byte {
	file_api_media_v1_gallery_proto_rawDescOnce.Do(func() {
		file_api_media_v1_gallery_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_media_v1_gallery_proto_rawDescData)
	})
	return file_api_media_v1_gallery_proto_rawDescData
}

var file_api_media_v1_gallery_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_api_media_v1_gallery_proto_goTypes = []interface{}{
	(*Deployment)(nil),                               // 0: api.media.v1.Deployment
	(*GalleryDevice)(nil),                            // 1: api.media.v1.GalleryDevice
	(*Airline)(nil),                                  // 2: api.media.v1.Airline
	(*Voyage)(nil),                                   // 3: api.media.v1.Voyage
	(*GalleryMediaItem)(nil),                         // 4: api.media.v1.GalleryMediaItem
	(*DeleteGalleryMediaRequest)(nil),                // 5: api.media.v1.DeleteGalleryMediaRequest
	(*DeleteGalleryMediaReply)(nil),                  // 6: api.media.v1.DeleteGalleryMediaReply
	(*ListGalleryMediaRequest)(nil),                  // 7: api.media.v1.ListGalleryMediaRequest
	(*ListGalleryMediaReply)(nil),                    // 8: api.media.v1.ListGalleryMediaReply
	(*GetVideoMediaDownloadUrlRequest)(nil),          // 9: api.media.v1.GetVideoMediaDownloadUrlRequest
	(*GetVideoMediaDownloadUrlReply)(nil),            // 10: api.media.v1.GetVideoMediaDownloadUrlReply
	(*CleanUpTmpDownloadableFileRequest)(nil),        // 11: api.media.v1.CleanUpTmpDownloadableFileRequest
	(*CleanUpTmpDownloadableFileReply)(nil),          // 12: api.media.v1.CleanUpTmpDownloadableFileReply
	(*Deployment_RequiredPictures)(nil),              // 13: api.media.v1.Deployment.RequiredPictures
	(*GalleryMediaItem_VideoInfo)(nil),               // 14: api.media.v1.GalleryMediaItem.VideoInfo
	(*GalleryMediaItem_PhotoInfo)(nil),               // 15: api.media.v1.GalleryMediaItem.PhotoInfo
	(*DeleteGalleryMediaReply_Data)(nil),             // 16: api.media.v1.DeleteGalleryMediaReply.Data
	(*ListGalleryMediaReply_Data)(nil),               // 17: api.media.v1.ListGalleryMediaReply.Data
	(*GetVideoMediaDownloadUrlReply_Data)(nil),       // 18: api.media.v1.GetVideoMediaDownloadUrlReply.Data
	(*CleanUpTmpDownloadableFileRequestPayload)(nil), // 19: api.media.v1.CleanUpTmpDownloadableFileRequest.payload
	(*CleanUpTmpDownloadableFileReply_Data)(nil),     // 20: api.media.v1.CleanUpTmpDownloadableFileReply.Data
}
var file_api_media_v1_gallery_proto_depIdxs = []int32{
	13, // 0: api.media.v1.Deployment.requiredPictures:type_name -> api.media.v1.Deployment.RequiredPictures
	0,  // 1: api.media.v1.GalleryDevice.deployment:type_name -> api.media.v1.Deployment
	14, // 2: api.media.v1.GalleryMediaItem.vInfo:type_name -> api.media.v1.GalleryMediaItem.VideoInfo
	15, // 3: api.media.v1.GalleryMediaItem.pInfo:type_name -> api.media.v1.GalleryMediaItem.PhotoInfo
	1,  // 4: api.media.v1.GalleryMediaItem.device:type_name -> api.media.v1.GalleryDevice
	2,  // 5: api.media.v1.GalleryMediaItem.airline:type_name -> api.media.v1.Airline
	3,  // 6: api.media.v1.GalleryMediaItem.voyage:type_name -> api.media.v1.Voyage
	16, // 7: api.media.v1.DeleteGalleryMediaReply.data:type_name -> api.media.v1.DeleteGalleryMediaReply.Data
	17, // 8: api.media.v1.ListGalleryMediaReply.data:type_name -> api.media.v1.ListGalleryMediaReply.Data
	18, // 9: api.media.v1.GetVideoMediaDownloadUrlReply.data:type_name -> api.media.v1.GetVideoMediaDownloadUrlReply.Data
	19, // 10: api.media.v1.CleanUpTmpDownloadableFileRequest.body:type_name -> api.media.v1.CleanUpTmpDownloadableFileRequest.payload
	20, // 11: api.media.v1.CleanUpTmpDownloadableFileReply.data:type_name -> api.media.v1.CleanUpTmpDownloadableFileReply.Data
	4,  // 12: api.media.v1.ListGalleryMediaReply.Data.list:type_name -> api.media.v1.GalleryMediaItem
	5,  // 13: api.media.v1.Gallery.DeleteGalleryMedia:input_type -> api.media.v1.DeleteGalleryMediaRequest
	7,  // 14: api.media.v1.Gallery.ListGalleryMedia:input_type -> api.media.v1.ListGalleryMediaRequest
	9,  // 15: api.media.v1.Gallery.GetVideoMediaDownloadUrl:input_type -> api.media.v1.GetVideoMediaDownloadUrlRequest
	11, // 16: api.media.v1.Gallery.CleanUpTmpDownloadableFile:input_type -> api.media.v1.CleanUpTmpDownloadableFileRequest
	6,  // 17: api.media.v1.Gallery.DeleteGalleryMedia:output_type -> api.media.v1.DeleteGalleryMediaReply
	8,  // 18: api.media.v1.Gallery.ListGalleryMedia:output_type -> api.media.v1.ListGalleryMediaReply
	10, // 19: api.media.v1.Gallery.GetVideoMediaDownloadUrl:output_type -> api.media.v1.GetVideoMediaDownloadUrlReply
	12, // 20: api.media.v1.Gallery.CleanUpTmpDownloadableFile:output_type -> api.media.v1.CleanUpTmpDownloadableFileReply
	17, // [17:21] is the sub-list for method output_type
	13, // [13:17] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_media_v1_gallery_proto_init() }
func file_api_media_v1_gallery_proto_init() {
	if File_api_media_v1_gallery_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_media_v1_gallery_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GalleryDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Airline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Voyage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GalleryMediaItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGalleryMediaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGalleryMediaReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGalleryMediaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGalleryMediaReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVideoMediaDownloadUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVideoMediaDownloadUrlReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanUpTmpDownloadableFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanUpTmpDownloadableFileReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment_RequiredPictures); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GalleryMediaItem_VideoInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GalleryMediaItem_PhotoInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGalleryMediaReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGalleryMediaReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVideoMediaDownloadUrlReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanUpTmpDownloadableFileRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_gallery_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanUpTmpDownloadableFileReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_media_v1_gallery_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_media_v1_gallery_proto_goTypes,
		DependencyIndexes: file_api_media_v1_gallery_proto_depIdxs,
		MessageInfos:      file_api_media_v1_gallery_proto_msgTypes,
	}.Build()
	File_api_media_v1_gallery_proto = out.File
	file_api_media_v1_gallery_proto_rawDesc = nil
	file_api_media_v1_gallery_proto_goTypes = nil
	file_api_media_v1_gallery_proto_depIdxs = nil
}
