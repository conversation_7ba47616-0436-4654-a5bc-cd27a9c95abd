// Code generated by protoc-gen-go-http. DO NOT EDIT.

package v1

import (
	context "context"
	v10 "github.com/go-playground/validator/v10"
	validator "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
)

var _ = new(context.Context)
var _ = new(v10.Validate)
var _ = new(validator.ValidateError)

func (m *ListMediaRequest) Validate() error {
	ctx := context.TODO()
	v := v10.New()
	if err := v.VarCtx(ctx, m.Size, "max=1000"); err != nil {
		return validator.WrapValidatorError("Size", err)
	}
	return nil
}

func (m *LiveListRequest) Validate() error {
	ctx := context.TODO()
	v := v10.New()

	for _, mi := range m.Status {
		if err := validator.DoValidate(mi, "Status"); err != nil {
			return err
		}
	}
	if err := v.VarCtx(ctx, m.<PERSON>ze, "max=1000"); err != nil {
		return validator.WrapValidatorError("Size", err)
	}
	return nil
}
