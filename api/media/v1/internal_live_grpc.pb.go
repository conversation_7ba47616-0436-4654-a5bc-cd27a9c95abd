// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/media/v1/internal_live.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// InternalLiveClient is the client API for InternalLive service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type InternalLiveClient interface {
	LiveStatusChanged(ctx context.Context, in *LiveStatusChangedRequest, opts ...grpc.CallOption) (*StatusReply, error)
}

type internalLiveClient struct {
	cc grpc.ClientConnInterface
}

func NewInternalLiveClient(cc grpc.ClientConnInterface) InternalLiveClient {
	return &internalLiveClient{cc}
}

func (c *internalLiveClient) LiveStatusChanged(ctx context.Context, in *LiveStatusChangedRequest, opts ...grpc.CallOption) (*StatusReply, error) {
	out := new(StatusReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.InternalLive/LiveStatusChanged", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InternalLiveServer is the server API for InternalLive service.
// All implementations must embed UnimplementedInternalLiveServer
// for forward compatibility
type InternalLiveServer interface {
	LiveStatusChanged(context.Context, *LiveStatusChangedRequest) (*StatusReply, error)
	mustEmbedUnimplementedInternalLiveServer()
}

// UnimplementedInternalLiveServer must be embedded to have forward compatible implementations.
type UnimplementedInternalLiveServer struct {
}

func (UnimplementedInternalLiveServer) LiveStatusChanged(context.Context, *LiveStatusChangedRequest) (*StatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LiveStatusChanged not implemented")
}
func (UnimplementedInternalLiveServer) mustEmbedUnimplementedInternalLiveServer() {}

// UnsafeInternalLiveServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InternalLiveServer will
// result in compilation errors.
type UnsafeInternalLiveServer interface {
	mustEmbedUnimplementedInternalLiveServer()
}

func RegisterInternalLiveServer(s grpc.ServiceRegistrar, srv InternalLiveServer) {
	s.RegisterService(&InternalLive_ServiceDesc, srv)
}

func _InternalLive_LiveStatusChanged_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LiveStatusChangedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InternalLiveServer).LiveStatusChanged(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.InternalLive/LiveStatusChanged",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InternalLiveServer).LiveStatusChanged(ctx, req.(*LiveStatusChangedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// InternalLive_ServiceDesc is the grpc.ServiceDesc for InternalLive service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var InternalLive_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.media.v1.InternalLive",
	HandlerType: (*InternalLiveServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LiveStatusChanged",
			Handler:    _InternalLive_LiveStatusChanged_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/media/v1/internal_live.proto",
}
