// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type InternalLiveHTTPServer interface {
	LiveStatusChanged(context.Context, *LiveStatusChangedRequest) (*StatusReply, error)
}

func RegisterInternalLiveHTTPServer(s *http.Server, srv InternalLiveHTTPServer) {
	r := s.Route("/")
	r.POST("/internal/v1/live/status", _InternalLive_LiveStatusChanged0_HTTP_Handler(srv))
}

func _InternalLive_LiveStatusChanged0_HTTP_Handler(srv InternalLiveHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LiveStatusChangedRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.InternalLive/LiveStatusChanged")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LiveStatusChanged(ctx, req.(*LiveStatusChangedRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StatusReply)
		return ctx.Result(200, reply)
	}
}

type InternalLiveHTTPClient interface {
	LiveStatusChanged(ctx context.Context, req *LiveStatusChangedRequest, opts ...http.CallOption) (rsp *StatusReply, err error)
}

type InternalLiveHTTPClientImpl struct {
	cc *http.Client
}

func NewInternalLiveHTTPClient(client *http.Client) InternalLiveHTTPClient {
	return &InternalLiveHTTPClientImpl{client}
}

func (c *InternalLiveHTTPClientImpl) LiveStatusChanged(ctx context.Context, in *LiveStatusChangedRequest, opts ...http.CallOption) (*StatusReply, error) {
	var out StatusReply
	pattern := "/internal/v1/live/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.media.v1.InternalLive/LiveStatusChanged"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
