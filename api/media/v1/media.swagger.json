{"swagger": "2.0", "info": {"title": "api/media/v1/media.proto", "version": "version not set"}, "tags": [{"name": "Media"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/media": {"get": {"operationId": "Media_ListMedia", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListMediaReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "deviceId", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "airlineId", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "voyageId", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "waypointId", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "withVoyageRecord", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "lenTypes", "in": "query", "required": false, "type": "string"}], "tags": ["Media"]}}, "/api/v1/media/lives": {"get": {"operationId": "Media_ListLive", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1LiveListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "dronesOnly", "in": "query", "required": false, "type": "boolean"}, {"name": "status", "in": "query", "required": false, "type": "array", "items": {"type": "integer", "format": "int32"}, "collectionFormat": "multi"}, {"name": "search", "in": "query", "required": false, "type": "string"}], "tags": ["Media"]}}, "/internal/v1/media/live/{liveId}/callback/start": {"put": {"operationId": "Media_StartLiveCallback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1StartLiveCallbackReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "liveId", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"deviceId": {"type": "string", "format": "int64"}}}}], "tags": ["Media"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1Device": {"type": "object", "properties": {"sn": {"type": "string"}, "name": {"type": "string"}, "location": {"type": "string"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "type": {"type": "string"}, "model": {"type": "string"}, "category": {"type": "string"}, "networkStatus": {"type": "boolean"}}}, "v1ListMediaReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1ListMediaReplyData"}}}, "v1ListMediaReplyData": {"type": "object", "properties": {"total": {"type": "number", "format": "double"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1MediaItem"}}}}, "v1Live": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "deviceId": {"type": "string", "format": "int64"}, "status": {"type": "integer", "format": "int32"}, "url": {"type": "string"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "device": {"$ref": "#/definitions/v1Device"}, "type": {"type": "string"}, "clarity": {"type": "integer", "format": "int32"}, "position": {"type": "integer", "format": "int32"}}}, "v1LiveListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1LiveListReplyData"}}}, "v1LiveListReplyData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "offline": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1Live"}}}}, "v1MediaItem": {"type": "object", "properties": {"deviceId": {"type": "string", "format": "int64"}, "airlineId": {"type": "string", "format": "int64"}, "voyageId": {"type": "string", "format": "int64"}, "waypointId": {"type": "string", "format": "int64"}, "id": {"type": "string", "format": "int64"}, "type": {"type": "string"}, "url": {"type": "string"}, "name": {"type": "string"}, "createdTime": {"type": "number", "format": "double"}, "shootTime": {"type": "number", "format": "double"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "vInfo": {"$ref": "#/definitions/v1MediaItemVideoInfo"}, "pInfo": {"$ref": "#/definitions/v1MediaItemPhotoInfo"}}}, "v1MediaItemPhotoInfo": {"type": "object", "properties": {"thumbnail": {"type": "string"}}}, "v1MediaItemVideoInfo": {"type": "object", "properties": {"isFull": {"type": "boolean"}, "coverImg": {"type": "string"}}}, "v1StartLiveCallbackReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1StartLiveCallbackReplyData"}}}, "v1StartLiveCallbackReplyData": {"type": "object", "properties": {"status": {"type": "boolean"}}}}}