{"swagger": "2.0", "info": {"title": "api/media/v1/internal_live.proto", "version": "version not set"}, "tags": [{"name": "InternalLive"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/internal/v1/live/status": {"post": {"operationId": "InternalLive_LiveStatusChanged", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1StatusReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1LiveStatusChangedRequest"}}], "tags": ["InternalLive"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1LiveStatusChangedRequest": {"type": "object", "properties": {"liveId": {"type": "string", "format": "int64"}, "status": {"type": "integer", "format": "int32"}, "key": {"type": "string"}}}, "v1StatusReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1StatusReplyData"}}}, "v1StatusReplyData": {"type": "object", "properties": {"status": {"type": "boolean"}}}}}