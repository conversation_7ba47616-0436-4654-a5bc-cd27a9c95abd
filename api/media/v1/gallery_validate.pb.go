// Code generated by protoc-gen-go-http. DO NOT EDIT.

package v1

import (
	context "context"
	v10 "github.com/go-playground/validator/v10"
	validator "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
)

var _ = new(context.Context)
var _ = new(v10.Validate)
var _ = new(validator.ValidateError)

func (m *ListGalleryMediaRequest) Validate() error {
	ctx := context.TODO()
	v := v10.New()

	for _, mi := range m.AirlineIds {
		if err := validator.DoValidate(mi, "AirlineIds"); err != nil {
			return err
		}
	}

	for _, mi := range m.Categories {
		if err := validator.DoValidate(mi, "Categories"); err != nil {
			return err
		}
	}

	for _, mi := range m.DeviceIds {
		if err := validator.DoValidate(mi, "DeviceIds"); err != nil {
			return err
		}
	}

	for _, mi := range m.DeviceTypes {
		if err := validator.DoValidate(mi, "DeviceTypes"); err != nil {
			return err
		}
	}

	for _, mi := range m.FileTypes {
		if err := validator.DoValidate(mi, "FileTypes"); err != nil {
			return err
		}
	}

	for _, mi := range m.LenTypes {
		if err := validator.DoValidate(mi, "LenTypes"); err != nil {
			return err
		}
	}

	for _, mi := range m.Types {
		if err := validator.DoValidate(mi, "Types"); err != nil {
			return err
		}
	}
	if err := v.VarCtx(ctx, m.Size, "max=1000"); err != nil {
		return validator.WrapValidatorError("Size", err)
	}
	return nil
}
