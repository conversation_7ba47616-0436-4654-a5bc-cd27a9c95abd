{"swagger": "2.0", "info": {"title": "api/media/v1/gallery.proto", "version": "version not set"}, "tags": [{"name": "Gallery"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/media/gallery/downloadURL": {"get": {"operationId": "Gallery_GetVideoMediaDownloadUrl", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetVideoMediaDownloadUrlReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["Gallery"]}}, "/api/v1/media/gallery/list": {"post": {"operationId": "Gallery_ListGalleryMedia", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListGalleryMediaReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1ListGalleryMediaRequest"}}], "tags": ["Gallery"]}}, "/api/v1/media/gallery/removement": {"post": {"operationId": "Gallery_DeleteGalleryMedia", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1DeleteGalleryMediaReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1DeleteGalleryMediaRequest"}}], "tags": ["Gallery"]}}, "/internal/v1/media/gallery/cleanUpTmpDownloadableFile": {"put": {"operationId": "Gallery_CleanUpTmpDownloadableFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CleanUpTmpDownloadableFileReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1CleanUpTmpDownloadableFileRequest"}}], "tags": ["Gallery"]}}}, "definitions": {"DeploymentRequiredPictures": {"type": "object", "properties": {"deviceImg": {"type": "string"}, "envImg": {"type": "string"}, "shopImg": {"type": "string"}}, "title": "repeated string pictures = 5;"}, "apimediav1Deployment": {"type": "object", "properties": {"name": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "location": {"type": "string"}, "requiredPictures": {"$ref": "#/definitions/DeploymentRequiredPictures"}}}, "mediav1Airline": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "name": {"type": "string"}, "isDefault": {"type": "boolean"}}}, "mediav1Voyage": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "startTime": {"type": "number", "format": "double"}, "endTime": {"type": "number", "format": "double"}, "runtime": {"type": "integer", "format": "int32"}, "mileage": {"type": "number", "format": "double"}, "status": {"type": "string"}, "images": {"type": "integer", "format": "int32"}, "videos": {"type": "integer", "format": "int32"}, "isFlown": {"type": "boolean"}, "name": {"type": "string"}, "mediaTotal": {"type": "integer", "format": "int32"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1CleanUpTmpDownloadableFileReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1CleanUpTmpDownloadableFileReplyData"}}}, "v1CleanUpTmpDownloadableFileReplyData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "v1CleanUpTmpDownloadableFileRequest": {"type": "object", "properties": {"key": {"type": "string"}, "bucket": {"type": "string"}}}, "v1DeleteGalleryMediaReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1DeleteGalleryMediaReplyData"}}}, "v1DeleteGalleryMediaReplyData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "v1DeleteGalleryMediaRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "int64"}}, "voyageIds": {"type": "array", "items": {"type": "string", "format": "int64"}}}}, "v1GalleryDevice": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "sn": {"type": "string"}, "type": {"type": "string"}, "model": {"type": "string"}, "category": {"type": "string"}, "firmwareVersion": {"type": "string"}, "deployment": {"$ref": "#/definitions/apimediav1Deployment"}}}, "v1GalleryMediaItem": {"type": "object", "properties": {"voyageId": {"type": "string", "format": "int64"}, "waypointId": {"type": "string", "format": "int64"}, "id": {"type": "string", "format": "int64"}, "type": {"type": "string"}, "url": {"type": "string"}, "name": {"type": "string"}, "createdTime": {"type": "number", "format": "double"}, "shootTime": {"type": "number", "format": "double"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "vInfo": {"$ref": "#/definitions/v1GalleryMediaItemVideoInfo"}, "pInfo": {"$ref": "#/definitions/v1GalleryMediaItemPhotoInfo"}, "subType": {"type": "string"}, "payload": {"type": "string"}, "device": {"$ref": "#/definitions/v1GalleryDevice"}, "airline": {"$ref": "#/definitions/mediav1Airline"}, "voyage": {"$ref": "#/definitions/mediav1Voyage"}}}, "v1GalleryMediaItemPhotoInfo": {"type": "object", "properties": {"thumbnail": {"type": "string"}}}, "v1GalleryMediaItemVideoInfo": {"type": "object", "properties": {"isFull": {"type": "boolean"}, "coverImg": {"type": "string"}, "duration": {"type": "number", "format": "double", "title": "seconds，全程录像时长"}}}, "v1GetVideoMediaDownloadUrlReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1GetVideoMediaDownloadUrlReplyData"}}}, "v1GetVideoMediaDownloadUrlReplyData": {"type": "object", "properties": {"url": {"type": "string"}, "expires": {"type": "number", "format": "double", "title": "链接过期时间戳"}}}, "v1ListGalleryMediaReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1ListGalleryMediaReplyData"}}}, "v1ListGalleryMediaReplyData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1GalleryMediaItem"}}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "totalFiles": {"type": "number", "format": "double"}}}, "v1ListGalleryMediaRequest": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "int64"}, "endTime": {"type": "string", "format": "int64"}, "search": {"type": "string"}, "types": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "array", "items": {"type": "string"}}, "deviceTypes": {"type": "array", "items": {"type": "string"}}, "deviceIds": {"type": "array", "items": {"type": "string", "format": "int64"}}, "airlineIds": {"type": "array", "items": {"type": "string", "format": "int64"}}, "fileTypes": {"type": "array", "items": {"type": "string"}}, "grouped": {"type": "string"}, "lenTypes": {"type": "array", "items": {"type": "string"}}}}}}