// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/media/v1/media.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChannelListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size   int32  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Search string `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
}

func (x *ChannelListRequest) Reset() {
	*x = ChannelListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelListRequest) ProtoMessage() {}

func (x *ChannelListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelListRequest.ProtoReflect.Descriptor instead.
func (*ChannelListRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{0}
}

func (x *ChannelListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ChannelListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ChannelListRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

type ChannelListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ChannelListReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ChannelListReply) Reset() {
	*x = ChannelListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelListReply) ProtoMessage() {}

func (x *ChannelListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelListReply.ProtoReflect.Descriptor instead.
func (*ChannelListReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{1}
}

func (x *ChannelListReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChannelListReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChannelListReply) GetData() *ChannelListReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ChannelDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ChannelDetailRequest) Reset() {
	*x = ChannelDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelDetailRequest) ProtoMessage() {}

func (x *ChannelDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelDetailRequest.ProtoReflect.Descriptor instead.
func (*ChannelDetailRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{2}
}

func (x *ChannelDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ChannelDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *VideoChannel `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ChannelDetailReply) Reset() {
	*x = ChannelDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelDetailReply) ProtoMessage() {}

func (x *ChannelDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelDetailReply.ProtoReflect.Descriptor instead.
func (*ChannelDetailReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{3}
}

func (x *ChannelDetailReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChannelDetailReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChannelDetailReply) GetData() *VideoChannel {
	if x != nil {
		return x.Data
	}
	return nil
}

type VideoChannel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TenantId         int64          `protobuf:"varint,2,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	MerchantId       int64          `protobuf:"varint,3,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	Camera           *ChannelCamera `protobuf:"bytes,4,opt,name=camera,proto3" json:"camera,omitempty"`
	Name             string         `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Type             string         `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	ProtocolType     string         `protobuf:"bytes,7,opt,name=protocolType,proto3" json:"protocolType,omitempty"`
	Status           int32          `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	Image            string         `protobuf:"bytes,9,opt,name=image,proto3" json:"image,omitempty"`
	Streams          []string       `protobuf:"bytes,10,rep,name=streams,proto3" json:"streams,omitempty"`
	Sequence         string         `protobuf:"bytes,11,opt,name=sequence,proto3" json:"sequence,omitempty"`
	Serial           string         `protobuf:"bytes,12,opt,name=serial,proto3" json:"serial,omitempty"`
	TranscodeStreams []string       `protobuf:"bytes,17,rep,name=transcodeStreams,proto3" json:"transcodeStreams,omitempty"`
	ImageOrigin      string         `protobuf:"bytes,18,opt,name=imageOrigin,proto3" json:"imageOrigin,omitempty"`
}

func (x *VideoChannel) Reset() {
	*x = VideoChannel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoChannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoChannel) ProtoMessage() {}

func (x *VideoChannel) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoChannel.ProtoReflect.Descriptor instead.
func (*VideoChannel) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{4}
}

func (x *VideoChannel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoChannel) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *VideoChannel) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *VideoChannel) GetCamera() *ChannelCamera {
	if x != nil {
		return x.Camera
	}
	return nil
}

func (x *VideoChannel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VideoChannel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *VideoChannel) GetProtocolType() string {
	if x != nil {
		return x.ProtocolType
	}
	return ""
}

func (x *VideoChannel) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *VideoChannel) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *VideoChannel) GetStreams() []string {
	if x != nil {
		return x.Streams
	}
	return nil
}

func (x *VideoChannel) GetSequence() string {
	if x != nil {
		return x.Sequence
	}
	return ""
}

func (x *VideoChannel) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *VideoChannel) GetTranscodeStreams() []string {
	if x != nil {
		return x.TranscodeStreams
	}
	return nil
}

func (x *VideoChannel) GetImageOrigin() string {
	if x != nil {
		return x.ImageOrigin
	}
	return ""
}

type ChannelCamera struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// string name = 2;
	Sn   string `protobuf:"bytes,3,opt,name=sn,proto3" json:"sn,omitempty"`
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	// string model = 5;
	//string category = 6;
	//string sourceId = 7;
	// string originId = 8;
	Deployment  *ChannelCamera_Deployment `protobuf:"bytes,9,opt,name=deployment,proto3" json:"deployment,omitempty"`
	CreatedTime float64                   `protobuf:"fixed64,10,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime float64                   `protobuf:"fixed64,11,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	SourceSn    string                    `protobuf:"bytes,12,opt,name=sourceSn,proto3" json:"sourceSn,omitempty"`
}

func (x *ChannelCamera) Reset() {
	*x = ChannelCamera{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelCamera) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelCamera) ProtoMessage() {}

func (x *ChannelCamera) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelCamera.ProtoReflect.Descriptor instead.
func (*ChannelCamera) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{5}
}

func (x *ChannelCamera) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChannelCamera) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ChannelCamera) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ChannelCamera) GetDeployment() *ChannelCamera_Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

func (x *ChannelCamera) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *ChannelCamera) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *ChannelCamera) GetSourceSn() string {
	if x != nil {
		return x.SourceSn
	}
	return ""
}

type MediaItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId    int64                `protobuf:"varint,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	AirlineId   int64                `protobuf:"varint,2,opt,name=airlineId,proto3" json:"airlineId,omitempty"`
	VoyageId    int64                `protobuf:"varint,3,opt,name=voyageId,proto3" json:"voyageId,omitempty"`
	WaypointId  int64                `protobuf:"varint,4,opt,name=waypointId,proto3" json:"waypointId,omitempty"`
	Id          int64                `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	Type        string               `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Url         string               `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	Name        string               `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	CreatedTime float64              `protobuf:"fixed64,9,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	ShootTime   float64              `protobuf:"fixed64,10,opt,name=shootTime,proto3" json:"shootTime,omitempty"`
	Lnglat      []float64            `protobuf:"fixed64,11,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	VInfo       *MediaItem_VideoInfo `protobuf:"bytes,12,opt,name=vInfo,proto3" json:"vInfo,omitempty"`
	PInfo       *MediaItem_PhotoInfo `protobuf:"bytes,13,opt,name=pInfo,proto3" json:"pInfo,omitempty"`
}

func (x *MediaItem) Reset() {
	*x = MediaItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaItem) ProtoMessage() {}

func (x *MediaItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaItem.ProtoReflect.Descriptor instead.
func (*MediaItem) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{6}
}

func (x *MediaItem) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *MediaItem) GetAirlineId() int64 {
	if x != nil {
		return x.AirlineId
	}
	return 0
}

func (x *MediaItem) GetVoyageId() int64 {
	if x != nil {
		return x.VoyageId
	}
	return 0
}

func (x *MediaItem) GetWaypointId() int64 {
	if x != nil {
		return x.WaypointId
	}
	return 0
}

func (x *MediaItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MediaItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *MediaItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *MediaItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MediaItem) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *MediaItem) GetShootTime() float64 {
	if x != nil {
		return x.ShootTime
	}
	return 0
}

func (x *MediaItem) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *MediaItem) GetVInfo() *MediaItem_VideoInfo {
	if x != nil {
		return x.VInfo
	}
	return nil
}

func (x *MediaItem) GetPInfo() *MediaItem_PhotoInfo {
	if x != nil {
		return x.PInfo
	}
	return nil
}

type ListMediaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId         int64  `protobuf:"varint,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	AirlineId        int64  `protobuf:"varint,2,opt,name=airlineId,proto3" json:"airlineId,omitempty"`
	VoyageId         int64  `protobuf:"varint,3,opt,name=voyageId,proto3" json:"voyageId,omitempty"`
	WaypointId       int64  `protobuf:"varint,4,opt,name=waypointId,proto3" json:"waypointId,omitempty"`
	Page             int32  `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	Size             int32  `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`
	WithVoyageRecord int32  `protobuf:"varint,7,opt,name=withVoyageRecord,proto3" json:"withVoyageRecord,omitempty"`
	LenTypes         string `protobuf:"bytes,8,opt,name=lenTypes,proto3" json:"lenTypes,omitempty"`
}

func (x *ListMediaRequest) Reset() {
	*x = ListMediaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMediaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMediaRequest) ProtoMessage() {}

func (x *ListMediaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMediaRequest.ProtoReflect.Descriptor instead.
func (*ListMediaRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{7}
}

func (x *ListMediaRequest) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *ListMediaRequest) GetAirlineId() int64 {
	if x != nil {
		return x.AirlineId
	}
	return 0
}

func (x *ListMediaRequest) GetVoyageId() int64 {
	if x != nil {
		return x.VoyageId
	}
	return 0
}

func (x *ListMediaRequest) GetWaypointId() int64 {
	if x != nil {
		return x.WaypointId
	}
	return 0
}

func (x *ListMediaRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListMediaRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListMediaRequest) GetWithVoyageRecord() int32 {
	if x != nil {
		return x.WithVoyageRecord
	}
	return 0
}

func (x *ListMediaRequest) GetLenTypes() string {
	if x != nil {
		return x.LenTypes
	}
	return ""
}

type ListMediaReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ListMediaReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListMediaReply) Reset() {
	*x = ListMediaReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMediaReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMediaReply) ProtoMessage() {}

func (x *ListMediaReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMediaReply.ProtoReflect.Descriptor instead.
func (*ListMediaReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{8}
}

func (x *ListMediaReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListMediaReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListMediaReply) GetData() *ListMediaReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn            string    `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn,omitempty"`
	Name          string    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Location      string    `protobuf:"bytes,3,opt,name=location,proto3" json:"location,omitempty"`
	Lnglat        []float64 `protobuf:"fixed64,4,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	Type          string    `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Model         string    `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`
	Category      string    `protobuf:"bytes,7,opt,name=category,proto3" json:"category,omitempty"`
	NetworkStatus bool      `protobuf:"varint,8,opt,name=networkStatus,proto3" json:"networkStatus,omitempty"`
}

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{9}
}

func (x *Device) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *Device) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Device) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Device) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *Device) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Device) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *Device) GetNetworkStatus() bool {
	if x != nil {
		return x.NetworkStatus
	}
	return false
}

type LiveListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size       int32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	DronesOnly bool    `protobuf:"varint,3,opt,name=dronesOnly,proto3" json:"dronesOnly,omitempty"`
	Status     []int32 `protobuf:"varint,4,rep,packed,name=status,proto3" json:"status,omitempty"`
	Search     string  `protobuf:"bytes,5,opt,name=search,proto3" json:"search,omitempty"`
}

func (x *LiveListRequest) Reset() {
	*x = LiveListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiveListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveListRequest) ProtoMessage() {}

func (x *LiveListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveListRequest.ProtoReflect.Descriptor instead.
func (*LiveListRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{10}
}

func (x *LiveListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *LiveListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *LiveListRequest) GetDronesOnly() bool {
	if x != nil {
		return x.DronesOnly
	}
	return false
}

func (x *LiveListRequest) GetStatus() []int32 {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *LiveListRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

type Live struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeviceId    int64   `protobuf:"varint,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	Status      int32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Url         string  `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	CreatedTime float64 `protobuf:"fixed64,5,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime float64 `protobuf:"fixed64,6,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	Device      *Device `protobuf:"bytes,7,opt,name=device,proto3" json:"device,omitempty"`
	Type        string  `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`
	Clarity     int32   `protobuf:"varint,9,opt,name=clarity,proto3" json:"clarity,omitempty"`
	Position    int32   `protobuf:"varint,10,opt,name=position,proto3" json:"position,omitempty"`
}

func (x *Live) Reset() {
	*x = Live{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Live) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Live) ProtoMessage() {}

func (x *Live) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Live.ProtoReflect.Descriptor instead.
func (*Live) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{11}
}

func (x *Live) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Live) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *Live) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Live) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Live) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *Live) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *Live) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *Live) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Live) GetClarity() int32 {
	if x != nil {
		return x.Clarity
	}
	return 0
}

func (x *Live) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

type LiveListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *LiveListReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *LiveListReply) Reset() {
	*x = LiveListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiveListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveListReply) ProtoMessage() {}

func (x *LiveListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveListReply.ProtoReflect.Descriptor instead.
func (*LiveListReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{12}
}

func (x *LiveListReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LiveListReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LiveListReply) GetData() *LiveListReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type StartLiveCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LiveId      int64                            `protobuf:"varint,1,opt,name=liveId,proto3" json:"liveId,omitempty"`
	Id          string                           `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	ClientId    string                           `protobuf:"bytes,3,opt,name=clientId,proto3" json:"clientId,omitempty"`
	ExecuteTime int64                            `protobuf:"varint,4,opt,name=executeTime,proto3" json:"executeTime,omitempty"`
	Body        *StartLiveCallbackRequestPayload `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *StartLiveCallbackRequest) Reset() {
	*x = StartLiveCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartLiveCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartLiveCallbackRequest) ProtoMessage() {}

func (x *StartLiveCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartLiveCallbackRequest.ProtoReflect.Descriptor instead.
func (*StartLiveCallbackRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{13}
}

func (x *StartLiveCallbackRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

func (x *StartLiveCallbackRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StartLiveCallbackRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *StartLiveCallbackRequest) GetExecuteTime() int64 {
	if x != nil {
		return x.ExecuteTime
	}
	return 0
}

func (x *StartLiveCallbackRequest) GetBody() *StartLiveCallbackRequestPayload {
	if x != nil {
		return x.Body
	}
	return nil
}

type StartLiveCallbackReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *StartLiveCallbackReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *StartLiveCallbackReply) Reset() {
	*x = StartLiveCallbackReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartLiveCallbackReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartLiveCallbackReply) ProtoMessage() {}

func (x *StartLiveCallbackReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartLiveCallbackReply.ProtoReflect.Descriptor instead.
func (*StartLiveCallbackReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{14}
}

func (x *StartLiveCallbackReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *StartLiveCallbackReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StartLiveCallbackReply) GetData() *StartLiveCallbackReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ChannelListReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32           `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Page  int32           `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32           `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	List  []*VideoChannel `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ChannelListReply_Data) Reset() {
	*x = ChannelListReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelListReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelListReply_Data) ProtoMessage() {}

func (x *ChannelListReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelListReply_Data.ProtoReflect.Descriptor instead.
func (*ChannelListReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ChannelListReply_Data) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ChannelListReply_Data) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ChannelListReply_Data) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ChannelListReply_Data) GetList() []*VideoChannel {
	if x != nil {
		return x.List
	}
	return nil
}

// 专网
// bool privateNetwork = 15;
type ChannelCamera_Deployment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string                   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Tags     []string                 `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	Images   *ChannelCamera_Images    `protobuf:"bytes,3,opt,name=images,proto3" json:"images,omitempty"`
	Contacts []*ChannelCamera_Contact `protobuf:"bytes,4,rep,name=contacts,proto3" json:"contacts,omitempty"`
	Lnglat   []float64                `protobuf:"fixed64,5,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	Location string                   `protobuf:"bytes,6,opt,name=location,proto3" json:"location,omitempty"`
	Time     float64                  `protobuf:"fixed64,7,opt,name=time,proto3" json:"time,omitempty"`
	Labels   []string                 `protobuf:"bytes,8,rep,name=labels,proto3" json:"labels,omitempty"`
}

func (x *ChannelCamera_Deployment) Reset() {
	*x = ChannelCamera_Deployment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelCamera_Deployment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelCamera_Deployment) ProtoMessage() {}

func (x *ChannelCamera_Deployment) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelCamera_Deployment.ProtoReflect.Descriptor instead.
func (*ChannelCamera_Deployment) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ChannelCamera_Deployment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChannelCamera_Deployment) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ChannelCamera_Deployment) GetImages() *ChannelCamera_Images {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ChannelCamera_Deployment) GetContacts() []*ChannelCamera_Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *ChannelCamera_Deployment) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *ChannelCamera_Deployment) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ChannelCamera_Deployment) GetTime() float64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *ChannelCamera_Deployment) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type ChannelCamera_Images struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceImg string  `protobuf:"bytes,1,opt,name=deviceImg,proto3" json:"deviceImg,omitempty"`
	EnvImg    string  `protobuf:"bytes,2,opt,name=envImg,proto3" json:"envImg,omitempty"`
	ShopImg   *string `protobuf:"bytes,3,opt,name=shopImg,proto3,oneof" json:"shopImg,omitempty"`
}

func (x *ChannelCamera_Images) Reset() {
	*x = ChannelCamera_Images{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelCamera_Images) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelCamera_Images) ProtoMessage() {}

func (x *ChannelCamera_Images) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelCamera_Images.ProtoReflect.Descriptor instead.
func (*ChannelCamera_Images) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{5, 1}
}

func (x *ChannelCamera_Images) GetDeviceImg() string {
	if x != nil {
		return x.DeviceImg
	}
	return ""
}

func (x *ChannelCamera_Images) GetEnvImg() string {
	if x != nil {
		return x.EnvImg
	}
	return ""
}

func (x *ChannelCamera_Images) GetShopImg() string {
	if x != nil && x.ShopImg != nil {
		return *x.ShopImg
	}
	return ""
}

type ChannelCamera_Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Contact string `protobuf:"bytes,2,opt,name=contact,proto3" json:"contact,omitempty"`
}

func (x *ChannelCamera_Contact) Reset() {
	*x = ChannelCamera_Contact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelCamera_Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelCamera_Contact) ProtoMessage() {}

func (x *ChannelCamera_Contact) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelCamera_Contact.ProtoReflect.Descriptor instead.
func (*ChannelCamera_Contact) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{5, 2}
}

func (x *ChannelCamera_Contact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChannelCamera_Contact) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

type MediaItem_VideoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsFull   bool   `protobuf:"varint,1,opt,name=isFull,proto3" json:"isFull,omitempty"`
	CoverImg string `protobuf:"bytes,2,opt,name=coverImg,proto3" json:"coverImg,omitempty"`
}

func (x *MediaItem_VideoInfo) Reset() {
	*x = MediaItem_VideoInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaItem_VideoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaItem_VideoInfo) ProtoMessage() {}

func (x *MediaItem_VideoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaItem_VideoInfo.ProtoReflect.Descriptor instead.
func (*MediaItem_VideoInfo) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{6, 0}
}

func (x *MediaItem_VideoInfo) GetIsFull() bool {
	if x != nil {
		return x.IsFull
	}
	return false
}

func (x *MediaItem_VideoInfo) GetCoverImg() string {
	if x != nil {
		return x.CoverImg
	}
	return ""
}

type MediaItem_PhotoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Thumbnail string `protobuf:"bytes,1,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
}

func (x *MediaItem_PhotoInfo) Reset() {
	*x = MediaItem_PhotoInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaItem_PhotoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaItem_PhotoInfo) ProtoMessage() {}

func (x *MediaItem_PhotoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaItem_PhotoInfo.ProtoReflect.Descriptor instead.
func (*MediaItem_PhotoInfo) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{6, 1}
}

func (x *MediaItem_PhotoInfo) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

type ListMediaReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total float64      `protobuf:"fixed64,1,opt,name=total,proto3" json:"total,omitempty"`
	Page  int32        `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32        `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	List  []*MediaItem `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListMediaReply_Data) Reset() {
	*x = ListMediaReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMediaReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMediaReply_Data) ProtoMessage() {}

func (x *ListMediaReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMediaReply_Data.ProtoReflect.Descriptor instead.
func (*ListMediaReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListMediaReply_Data) GetTotal() float64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListMediaReply_Data) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListMediaReply_Data) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListMediaReply_Data) GetList() []*MediaItem {
	if x != nil {
		return x.List
	}
	return nil
}

type LiveListReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   int32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Offline int32   `protobuf:"varint,2,opt,name=offline,proto3" json:"offline,omitempty"`
	Page    int32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Size    int32   `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	List    []*Live `protobuf:"bytes,5,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *LiveListReply_Data) Reset() {
	*x = LiveListReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiveListReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveListReply_Data) ProtoMessage() {}

func (x *LiveListReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveListReply_Data.ProtoReflect.Descriptor instead.
func (*LiveListReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{12, 0}
}

func (x *LiveListReply_Data) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *LiveListReply_Data) GetOffline() int32 {
	if x != nil {
		return x.Offline
	}
	return 0
}

func (x *LiveListReply_Data) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *LiveListReply_Data) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *LiveListReply_Data) GetList() []*Live {
	if x != nil {
		return x.List
	}
	return nil
}

type StartLiveCallbackRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId int64 `protobuf:"varint,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
}

func (x *StartLiveCallbackRequestPayload) Reset() {
	*x = StartLiveCallbackRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartLiveCallbackRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartLiveCallbackRequestPayload) ProtoMessage() {}

func (x *StartLiveCallbackRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartLiveCallbackRequestPayload.ProtoReflect.Descriptor instead.
func (*StartLiveCallbackRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{13, 0}
}

func (x *StartLiveCallbackRequestPayload) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

type StartLiveCallbackReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StartLiveCallbackReply_Data) Reset() {
	*x = StartLiveCallbackReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_media_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartLiveCallbackReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartLiveCallbackReply_Data) ProtoMessage() {}

func (x *StartLiveCallbackReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_media_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartLiveCallbackReply_Data.ProtoReflect.Descriptor instead.
func (*StartLiveCallbackReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_media_proto_rawDescGZIP(), []int{14, 0}
}

func (x *StartLiveCallbackReply_Data) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

var File_api_media_v1_media_proto protoreflect.FileDescriptor

var file_api_media_v1_media_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f,
	0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x61, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x6d, 0x61,
	0x78, 0x3d, 0x31, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x22, 0xef, 0x01, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x74, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x26, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x72,
	0x0a, 0x12, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0xa5, 0x03, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x06, 0x63, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x10,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x6f, 0x64,
	0x65, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x22, 0xa3, 0x05, 0x0a, 0x0d, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x73, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x46, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x6e, 0x1a, 0x91, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12,
	0x3a, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x08, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e,
	0x67, 0x6c, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x69, 0x0a, 0x06,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6d, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6d, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x76, 0x49, 0x6d, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x76, 0x49, 0x6d, 0x67, 0x12, 0x1d, 0x0a, 0x07,
	0x73, 0x68, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x07, 0x73, 0x68, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x73, 0x68, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x1a, 0x37, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x22, 0x81, 0x04, 0x0a, 0x09, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x69,
	0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61,
	0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x6f, 0x79, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x76, 0x6f, 0x79, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06,
	0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x37, 0x0a, 0x05, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x3f, 0x0a, 0x09, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x46, 0x75, 0x6c, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x46, 0x75, 0x6c, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x1a, 0x29, 0x0a, 0x09, 0x50, 0x68, 0x6f,
	0x74, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62,
	0x6e, 0x61, 0x69, 0x6c, 0x22, 0x85, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x6d, 0x61, 0x78, 0x3d, 0x31, 0x30, 0x30, 0x30, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x77, 0x69, 0x74, 0x68, 0x56, 0x6f, 0x79, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x77, 0x69, 0x74, 0x68, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xe8, 0x01, 0x0a,
	0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x35, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x1a, 0x71, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xcc, 0x01, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x76, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x6d, 0x61, 0x78, 0x3d, 0x31, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x73, 0x4f, 0x6e, 0x6c, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x73, 0x4f, 0x6e, 0x6c, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x22,
	0x98, 0x02, 0x0a, 0x04, 0x4c, 0x69, 0x76, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xfc, 0x01, 0x0a, 0x0d, 0x4c,
	0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x86, 0x01, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x18, 0x0a, 0x07, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x26, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x76, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xf8, 0x01, 0x0a, 0x18, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x76, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x25, 0x0a,
	0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x22, 0xa5, 0x01, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69,
	0x76, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3d, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x4c, 0x69, 0x76, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x1e, 0x0a, 0x04,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xd3, 0x04, 0x0a,
	0x05, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x60, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x63, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74,
	0x4c, 0x69, 0x76, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x12, 0x9d, 0x01,
	0x0a, 0x11, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x4c, 0x69, 0x76, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x22, 0x2f, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x7b, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x7d, 0x2f, 0x63, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x6a, 0x0a,
	0x0c, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x77, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x42, 0x3e, 0x0a, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b,
	0x61, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x76, 0x31, 0x3b,
	0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_media_v1_media_proto_rawDescOnce sync.Once
	file_api_media_v1_media_proto_rawDescData = file_api_media_v1_media_proto_rawDesc
)

func file_api_media_v1_media_proto_rawDescGZIP() []byte {
	file_api_media_v1_media_proto_rawDescOnce.Do(func() {
		file_api_media_v1_media_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_media_v1_media_proto_rawDescData)
	})
	return file_api_media_v1_media_proto_rawDescData
}

var file_api_media_v1_media_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_api_media_v1_media_proto_goTypes = []interface{}{
	(*ChannelListRequest)(nil),              // 0: api.media.v1.ChannelListRequest
	(*ChannelListReply)(nil),                // 1: api.media.v1.ChannelListReply
	(*ChannelDetailRequest)(nil),            // 2: api.media.v1.ChannelDetailRequest
	(*ChannelDetailReply)(nil),              // 3: api.media.v1.ChannelDetailReply
	(*VideoChannel)(nil),                    // 4: api.media.v1.VideoChannel
	(*ChannelCamera)(nil),                   // 5: api.media.v1.ChannelCamera
	(*MediaItem)(nil),                       // 6: api.media.v1.MediaItem
	(*ListMediaRequest)(nil),                // 7: api.media.v1.ListMediaRequest
	(*ListMediaReply)(nil),                  // 8: api.media.v1.ListMediaReply
	(*Device)(nil),                          // 9: api.media.v1.Device
	(*LiveListRequest)(nil),                 // 10: api.media.v1.LiveListRequest
	(*Live)(nil),                            // 11: api.media.v1.Live
	(*LiveListReply)(nil),                   // 12: api.media.v1.LiveListReply
	(*StartLiveCallbackRequest)(nil),        // 13: api.media.v1.StartLiveCallbackRequest
	(*StartLiveCallbackReply)(nil),          // 14: api.media.v1.StartLiveCallbackReply
	(*ChannelListReply_Data)(nil),           // 15: api.media.v1.ChannelListReply.Data
	(*ChannelCamera_Deployment)(nil),        // 16: api.media.v1.ChannelCamera.Deployment
	(*ChannelCamera_Images)(nil),            // 17: api.media.v1.ChannelCamera.Images
	(*ChannelCamera_Contact)(nil),           // 18: api.media.v1.ChannelCamera.Contact
	(*MediaItem_VideoInfo)(nil),             // 19: api.media.v1.MediaItem.VideoInfo
	(*MediaItem_PhotoInfo)(nil),             // 20: api.media.v1.MediaItem.PhotoInfo
	(*ListMediaReply_Data)(nil),             // 21: api.media.v1.ListMediaReply.Data
	(*LiveListReply_Data)(nil),              // 22: api.media.v1.LiveListReply.Data
	(*StartLiveCallbackRequestPayload)(nil), // 23: api.media.v1.StartLiveCallbackRequest.payload
	(*StartLiveCallbackReply_Data)(nil),     // 24: api.media.v1.StartLiveCallbackReply.Data
}
var file_api_media_v1_media_proto_depIdxs = []int32{
	15, // 0: api.media.v1.ChannelListReply.data:type_name -> api.media.v1.ChannelListReply.Data
	4,  // 1: api.media.v1.ChannelDetailReply.data:type_name -> api.media.v1.VideoChannel
	5,  // 2: api.media.v1.VideoChannel.camera:type_name -> api.media.v1.ChannelCamera
	16, // 3: api.media.v1.ChannelCamera.deployment:type_name -> api.media.v1.ChannelCamera.Deployment
	19, // 4: api.media.v1.MediaItem.vInfo:type_name -> api.media.v1.MediaItem.VideoInfo
	20, // 5: api.media.v1.MediaItem.pInfo:type_name -> api.media.v1.MediaItem.PhotoInfo
	21, // 6: api.media.v1.ListMediaReply.data:type_name -> api.media.v1.ListMediaReply.Data
	9,  // 7: api.media.v1.Live.device:type_name -> api.media.v1.Device
	22, // 8: api.media.v1.LiveListReply.data:type_name -> api.media.v1.LiveListReply.Data
	23, // 9: api.media.v1.StartLiveCallbackRequest.body:type_name -> api.media.v1.StartLiveCallbackRequest.payload
	24, // 10: api.media.v1.StartLiveCallbackReply.data:type_name -> api.media.v1.StartLiveCallbackReply.Data
	4,  // 11: api.media.v1.ChannelListReply.Data.list:type_name -> api.media.v1.VideoChannel
	17, // 12: api.media.v1.ChannelCamera.Deployment.images:type_name -> api.media.v1.ChannelCamera.Images
	18, // 13: api.media.v1.ChannelCamera.Deployment.contacts:type_name -> api.media.v1.ChannelCamera.Contact
	6,  // 14: api.media.v1.ListMediaReply.Data.list:type_name -> api.media.v1.MediaItem
	11, // 15: api.media.v1.LiveListReply.Data.list:type_name -> api.media.v1.Live
	7,  // 16: api.media.v1.Media.ListMedia:input_type -> api.media.v1.ListMediaRequest
	10, // 17: api.media.v1.Media.ListLive:input_type -> api.media.v1.LiveListRequest
	13, // 18: api.media.v1.Media.StartLiveCallback:input_type -> api.media.v1.StartLiveCallbackRequest
	0,  // 19: api.media.v1.Media.ListChannels:input_type -> api.media.v1.ChannelListRequest
	2,  // 20: api.media.v1.Media.GetChannelDetail:input_type -> api.media.v1.ChannelDetailRequest
	8,  // 21: api.media.v1.Media.ListMedia:output_type -> api.media.v1.ListMediaReply
	12, // 22: api.media.v1.Media.ListLive:output_type -> api.media.v1.LiveListReply
	14, // 23: api.media.v1.Media.StartLiveCallback:output_type -> api.media.v1.StartLiveCallbackReply
	1,  // 24: api.media.v1.Media.ListChannels:output_type -> api.media.v1.ChannelListReply
	3,  // 25: api.media.v1.Media.GetChannelDetail:output_type -> api.media.v1.ChannelDetailReply
	21, // [21:26] is the sub-list for method output_type
	16, // [16:21] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_api_media_v1_media_proto_init() }
func file_api_media_v1_media_proto_init() {
	if File_api_media_v1_media_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_media_v1_media_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoChannel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelCamera); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMediaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMediaReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiveListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Live); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiveListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartLiveCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartLiveCallbackReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelListReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelCamera_Deployment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelCamera_Images); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelCamera_Contact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaItem_VideoInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaItem_PhotoInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMediaReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiveListReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartLiveCallbackRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_media_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartLiveCallbackReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_media_v1_media_proto_msgTypes[17].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_media_v1_media_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_media_v1_media_proto_goTypes,
		DependencyIndexes: file_api_media_v1_media_proto_depIdxs,
		MessageInfos:      file_api_media_v1_media_proto_msgTypes,
	}.Build()
	File_api_media_v1_media_proto = out.File
	file_api_media_v1_media_proto_rawDesc = nil
	file_api_media_v1_media_proto_goTypes = nil
	file_api_media_v1_media_proto_depIdxs = nil
}
