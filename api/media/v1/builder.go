package v1

import (
	"github.com/samber/lo"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/service/thing"
)

func newMediaType(t biz.MediaType) string {
	switch t {
	case biz.MediaTypeVideo:
		return "video"
	case biz.MediaTypePhoto:
		return "image"
	default:
		return ""
	}
}

func BuildMediaType(t string) biz.MediaType {
	switch t {
	case "video":
		return biz.MediaTypeVideo
	case "image":
		return biz.MediaTypePhoto
	default:
		return biz.MediaTypeOther
	}
}

func NewMediaItem(bm *biz.Media) *MediaItem {
	if bm == nil {
		return nil
	}
	r := &MediaItem{
		Id:          bm.Id,
		DeviceId:    bm.DeviceId,
		AirlineId:   bm.AirlineId,
		VoyageId:    bm.VoyageId,
		WaypointId:  bm.WaypointId,
		Type:        newMediaType(bm.Type),
		Url:         bm.URL,
		Name:        bm.Name,
		CreatedTime: float64(bm.CreatedTime.UnixMilli()),
	}
	if m := bm.Meta; m != nil {
		r.ShootTime = float64(m.ShootTime.UnixMilli())
		r.Lnglat = m.ShootLnglat
	}
	if bm.Type == biz.MediaTypeVideo {
		r.VInfo = &MediaItem_VideoInfo{
			IsFull:   bm.IsVoyageRecordVideo(),
			CoverImg: bm.GetThumbnailUrl(),
		}
	}
	if bm.Type == biz.MediaTypePhoto {
		r.PInfo = &MediaItem_PhotoInfo{
			Thumbnail: bm.GetThumbnailUrl(),
		}
	}
	return r
}

func NewLive(bl *biz.Live) *Live {
	if bl == nil {
		return nil
	}
	l := &Live{
		Id:          bl.Id,
		DeviceId:    bl.DeviceId,
		Status:      bl.Status,
		Url:         bl.URL,
		CreatedTime: float64(bl.CreatedTime.UnixMilli()),
		UpdatedTime: float64(bl.CreatedTime.UnixMilli()),
		Type:        bl.Type.String(),
		Clarity:     bl.Clarity,
		Position:    bl.Position,
	}
	if d := bl.Device; d != nil {
		l.Device = &Device{
			Sn:            d.Sn,
			Type:          d.Type,
			Model:         d.Model.String(),
			Category:      d.Category.String(),
			NetworkStatus: d.NetworkStatus,
		}
		if deploy := d.Deployment; deploy != nil {
			l.Device.Name = deploy.Name
			l.Device.Location = deploy.Location
			l.Device.Lnglat = deploy.Lnglat
		}
	}
	return l
}

func NewGalleryDevice(d *biz.Device) *GalleryDevice {
	gd := &GalleryDevice{
		Id:          d.Id,
		Sn:          d.Sn,
		Type:        d.Type,
		Model:       d.Model.String(),
		Category:    d.Category.String(),
		CreatedTime: float64(d.CreatedTime.UnixMilli()),
		UpdatedTime: float64(d.UpdatedTime.UnixMilli()),
	}
	if deploy := d.Deployment; deploy != nil {
		gd.Deployment = &Deployment{
			Name:     deploy.Name,
			Location: deploy.Location,
			Lnglat:   deploy.Lnglat,
			Tags:     deploy.Tags,
			RequiredPictures: &Deployment_RequiredPictures{
				DeviceImg: deploy.Images.DeviceImg,
				EnvImg:    deploy.Images.EnvImg,
				ShopImg:   lo.FromPtrOr(deploy.Images.ShopImg, ""),
			},
		}
	}
	return gd
}

func NewAirline(a *biz.Airline) *Airline {
	return &Airline{
		Id:          a.Id,
		Name:        a.Name,
		CreatedTime: float64(a.CreatedTime.UnixMilli()),
	}
}

func NewVoyage(v *biz.Voyage) *Voyage {
	return &Voyage{
		Id:         v.Id,
		Name:       v.Name,
		StartTime:  float64(v.StartTime.UnixMilli()),
		EndTime:    float64(v.EndTime.UnixMilli()),
		Runtime:    v.Runtime,
		Mileage:    float64(v.Mileage),
		Status:     v.Status,
		Images:     v.Images,
		Videos:     v.Videos,
		IsFlown:    v.IsFlown,
		MediaTotal: v.DroneMediaTotal,
	}
}

func NewGalleryMediaItem(bm *biz.Media, d *biz.Device, a *biz.Airline, v *biz.Voyage) *GalleryMediaItem {
	if bm == nil {
		return nil
	}
	r := &GalleryMediaItem{
		Id:          bm.Id,
		VoyageId:    bm.VoyageId,
		WaypointId:  bm.WaypointId,
		Type:        newMediaType(bm.Type),
		Url:         bm.URL,
		Name:        bm.Name,
		CreatedTime: float64(bm.CreatedTime.UnixMilli()),
		SubType:     biz.MediaSubTypeKeyMapper[bm.SubType],
		Payload:     thing.NewTypeForDJIDockSubFromIndex(bm.SubDeviceIndex),
	}
	if m := bm.Meta; m != nil {
		r.ShootTime = float64(m.ShootTime.UnixMilli())
		r.Lnglat = m.ShootLnglat
	}
	if bm.Type == biz.MediaTypeVideo {
		r.VInfo = &GalleryMediaItem_VideoInfo{
			IsFull:   bm.IsVoyageRecordVideo(),
			CoverImg: bm.GetThumbnailUrl(),
		}
		if v, ok := bm.Extra["vTime"].(float64); ok {
			r.VInfo.Duration = v
		}
	}
	if bm.Type == biz.MediaTypePhoto {
		r.PInfo = &GalleryMediaItem_PhotoInfo{
			Thumbnail: bm.GetThumbnailUrl(),
		}
	}
	if d != nil {
		r.Device = NewGalleryDevice(d)
	}
	if a != nil {
		r.Airline = NewAirline(a)
	}
	if v != nil {
		r.Voyage = NewVoyage(v)
	}
	return r
}

func NewVideoChannel(ch *biz.CameraChannel) *VideoChannel {
	vc := &VideoChannel{
		Id:               ch.Id,
		TenantId:         ch.TenantId,
		MerchantId:       ch.MerchantId,
		Name:             ch.Name,
		Type:             ch.Type,
		ProtocolType:     ch.ProtocolType,
		Status:           ch.Status,
		Image:            ch.Image,
		Streams:          ch.Streams,
		Sequence:         ch.Sequence,
		Serial:           ch.Serial,
		ImageOrigin:      ch.ImageOrigin,
		TranscodeStreams: ch.TranscodeStreams,
	}

	cm := ch.Camera
	if cm != nil {
		vc.Camera = &ChannelCamera{
			Id:   cm.Id,
			Sn:   cm.Sn,
			Type: cm.Type,
		}
		if cm.Deployment != nil && cm.Deployment.Time != nil {
			vc.Camera.Deployment = &ChannelCamera_Deployment{
				Name: cm.Deployment.Name,
				Tags: cm.Deployment.Tags,
				// Images: &ChannelCamera_Images{
				// 	DeviceImg: lo,
				// 	EnvImg:    cm.Deployment.Images.EnvImg,
				// 	ShopImg:   cm.Deployment.Images.ShopImg,
				// },
				// Lnglat:   cm.Deployment.,
				Location: cm.Deployment.Location,
				Time:     *cm.Deployment.Time,
				//	Labels:   cm.Deployment.GetLabels(labelMap),
			}
		}
	}

	return vc
}
