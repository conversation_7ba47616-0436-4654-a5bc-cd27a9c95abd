syntax = "proto3";

package api.media.v1;

option go_package = "gitlab.sensoro.com/skai/skai/api/media/v1;v1";
option java_multiple_files = true;
option java_package = "api.media.v1";

import "google/api/annotations.proto";
import "validator/validator.proto";

service Gallery {
	rpc DeleteGalleryMedia (DeleteGalleryMediaRequest) returns (DeleteGalleryMediaReply) {
		option (google.api.http) = {
      post: "/api/v1/media/gallery/removement"
			body:"*"
    };
	};
	rpc ListGalleryMedia (ListGalleryMediaRequest) returns (ListGalleryMediaReply) {
		option (google.api.http)= {
			post: "/api/v1/media/gallery/list"
			body:"*"
		};
	};

	rpc GetVideoMediaDownloadUrl (GetVideoMediaDownloadUrlRequest) returns (GetVideoMediaDownloadUrlReply) {
		option (google.api.http) = {
			get: "/api/v1/media/gallery/downloadURL"
		};
	};

	rpc CleanUpTmpDownloadableFile(CleanUpTmpDownloadableFileRequest) returns (CleanUpTmpDownloadableFileReply) {
		option (google.api.http) = {
			post: "/internal/v1/media/gallery/cleanUpTmpDownloadableFile"
			body:"*"
		};
	};
}


message Deployment {
	string name = 1;
	repeated string tags = 2;
	repeated double lnglat = 3;
	string location = 4;
	// repeated string pictures = 5;
	message RequiredPictures {
		string deviceImg = 1;
		string envImg = 2;
		string shopImg = 3;
	}
	RequiredPictures requiredPictures = 6;
}

message GalleryDevice {
	int64 id = 1;
	double createdTime = 2;
	double updatedTime = 3;
	string sn = 4;
	string type = 5;
	string model = 6;
	string category = 7;
	string firmwareVersion = 8;
  Deployment deployment = 9;
}

message Airline {
	int64 id = 1;
	double createdTime = 2;
	string name = 3;
	bool isDefault = 4;
}

message Voyage {
	int64 id = 1;
	double startTime = 2;
	double endTime = 3;
	int32 runtime = 4;
	double mileage = 5;
	string status = 6;
	int32 images = 7;
	int32 videos = 8;
	bool isFlown = 9;
	string name = 10;
	int32 mediaTotal = 11;
}

message GalleryMediaItem {
	int64 voyageId = 3;
	int64 waypointId = 4;
	int64 id = 5;
	string type = 6;
	string url = 7;
	string name = 8;
	double createdTime = 9;
	double shootTime = 10;
	repeated double lnglat = 11;
	message VideoInfo {
		bool isFull = 1;
		string coverImg = 2;
		// seconds，全程录像时长
		double duration = 3;
	}
	message PhotoInfo {
		string thumbnail = 1;
	}
	VideoInfo vInfo = 12;
	PhotoInfo pInfo = 13;
	string subType = 14;
	string payload = 15;
	GalleryDevice device = 16;
	Airline airline = 17;
	Voyage voyage = 18;
}


message DeleteGalleryMediaRequest {
	repeated int64 ids = 1;
	repeated int64 voyageIds = 2;
}
message DeleteGalleryMediaReply {
	int32 code = 1;
	string message = 2;
	message Data {
		bool status = 1;
	}
	Data data = 3;
}


message ListGalleryMediaRequest {
	int32 page = 1;
	int32 size = 2 [(validator.rules) = "max=1000"];
	int64 startTime = 3;
	int64 endTime = 4;
	string search = 5;
	repeated string types = 6;
	repeated string categories = 7;
	repeated string deviceTypes = 8;
	repeated int64 deviceIds = 9;
	repeated int64 airlineIds = 10;
	repeated string fileTypes = 11;
	string grouped = 12;
	repeated string lenTypes = 13;
}
message ListGalleryMediaReply {
	int32 code = 1;
	string message = 2;
	message Data {
		int32 total = 1;
		repeated GalleryMediaItem list = 2;
		int32 page = 3;
		int32 size = 4;
		double totalFiles = 5;
	}
	Data data = 3;
}

message GetVideoMediaDownloadUrlRequest {
	int64 id = 1;
}

message GetVideoMediaDownloadUrlReply {
	int32 code = 1;
	string message = 2;
	message Data {
		string url = 1;
		// 链接过期时间戳
		double expires = 2;
	}
	Data data = 3;
}

message CleanUpTmpDownloadableFileRequest {
	string id = 1;
	string clientId = 2;
	int64 executeTime = 3;
  payload body = 4;

  message payload {
		string key = 1;
		string bucket = 2;
  }
}

message CleanUpTmpDownloadableFileReply {
	int32 code = 1;
	string message = 2;
	message Data {
		bool status = 1;
	}
	Data data = 3;
}