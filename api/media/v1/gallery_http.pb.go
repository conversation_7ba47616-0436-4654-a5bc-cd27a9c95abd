// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type GalleryHTTPServer interface {
	CleanUpTmpDownloadableFile(context.Context, *CleanUpTmpDownloadableFileRequest) (*CleanUpTmpDownloadableFileReply, error)
	DeleteGalleryMedia(context.Context, *DeleteGalleryMediaRequest) (*DeleteGalleryMediaReply, error)
	GetVideoMediaDownloadUrl(context.Context, *GetVideoMediaDownloadUrlRequest) (*GetVideoMediaDownloadUrlReply, error)
	ListGalleryMedia(context.Context, *ListGalleryMediaRequest) (*ListGalleryMediaReply, error)
}

func RegisterGalleryHTTPServer(s *http.Server, srv GalleryHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/media/gallery/removement", _Gallery_DeleteGalleryMedia0_HTTP_Handler(srv))
	r.POST("/api/v1/media/gallery/list", _Gallery_ListGalleryMedia0_HTTP_Handler(srv))
	r.GET("/api/v1/media/gallery/downloadURL", _Gallery_GetVideoMediaDownloadUrl0_HTTP_Handler(srv))
	r.POST("/internal/v1/media/gallery/cleanUpTmpDownloadableFile", _Gallery_CleanUpTmpDownloadableFile0_HTTP_Handler(srv))
}

func _Gallery_DeleteGalleryMedia0_HTTP_Handler(srv GalleryHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteGalleryMediaRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Gallery/DeleteGalleryMedia")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteGalleryMedia(ctx, req.(*DeleteGalleryMediaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteGalleryMediaReply)
		return ctx.Result(200, reply)
	}
}

func _Gallery_ListGalleryMedia0_HTTP_Handler(srv GalleryHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListGalleryMediaRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Gallery/ListGalleryMedia")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListGalleryMedia(ctx, req.(*ListGalleryMediaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListGalleryMediaReply)
		return ctx.Result(200, reply)
	}
}

func _Gallery_GetVideoMediaDownloadUrl0_HTTP_Handler(srv GalleryHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetVideoMediaDownloadUrlRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Gallery/GetVideoMediaDownloadUrl")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVideoMediaDownloadUrl(ctx, req.(*GetVideoMediaDownloadUrlRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVideoMediaDownloadUrlReply)
		return ctx.Result(200, reply)
	}
}

func _Gallery_CleanUpTmpDownloadableFile0_HTTP_Handler(srv GalleryHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CleanUpTmpDownloadableFileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Gallery/CleanUpTmpDownloadableFile")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CleanUpTmpDownloadableFile(ctx, req.(*CleanUpTmpDownloadableFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CleanUpTmpDownloadableFileReply)
		return ctx.Result(200, reply)
	}
}

type GalleryHTTPClient interface {
	CleanUpTmpDownloadableFile(ctx context.Context, req *CleanUpTmpDownloadableFileRequest, opts ...http.CallOption) (rsp *CleanUpTmpDownloadableFileReply, err error)
	DeleteGalleryMedia(ctx context.Context, req *DeleteGalleryMediaRequest, opts ...http.CallOption) (rsp *DeleteGalleryMediaReply, err error)
	GetVideoMediaDownloadUrl(ctx context.Context, req *GetVideoMediaDownloadUrlRequest, opts ...http.CallOption) (rsp *GetVideoMediaDownloadUrlReply, err error)
	ListGalleryMedia(ctx context.Context, req *ListGalleryMediaRequest, opts ...http.CallOption) (rsp *ListGalleryMediaReply, err error)
}

type GalleryHTTPClientImpl struct {
	cc *http.Client
}

func NewGalleryHTTPClient(client *http.Client) GalleryHTTPClient {
	return &GalleryHTTPClientImpl{client}
}

func (c *GalleryHTTPClientImpl) CleanUpTmpDownloadableFile(ctx context.Context, in *CleanUpTmpDownloadableFileRequest, opts ...http.CallOption) (*CleanUpTmpDownloadableFileReply, error) {
	var out CleanUpTmpDownloadableFileReply
	pattern := "/internal/v1/media/gallery/cleanUpTmpDownloadableFile"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.media.v1.Gallery/CleanUpTmpDownloadableFile"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GalleryHTTPClientImpl) DeleteGalleryMedia(ctx context.Context, in *DeleteGalleryMediaRequest, opts ...http.CallOption) (*DeleteGalleryMediaReply, error) {
	var out DeleteGalleryMediaReply
	pattern := "/api/v1/media/gallery/removement"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.media.v1.Gallery/DeleteGalleryMedia"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GalleryHTTPClientImpl) GetVideoMediaDownloadUrl(ctx context.Context, in *GetVideoMediaDownloadUrlRequest, opts ...http.CallOption) (*GetVideoMediaDownloadUrlReply, error) {
	var out GetVideoMediaDownloadUrlReply
	pattern := "/api/v1/media/gallery/downloadURL"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.media.v1.Gallery/GetVideoMediaDownloadUrl"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *GalleryHTTPClientImpl) ListGalleryMedia(ctx context.Context, in *ListGalleryMediaRequest, opts ...http.CallOption) (*ListGalleryMediaReply, error) {
	var out ListGalleryMediaReply
	pattern := "/api/v1/media/gallery/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.media.v1.Gallery/ListGalleryMedia"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
