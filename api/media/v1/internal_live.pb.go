// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/media/v1/internal_live.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string            `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *StatusReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *StatusReply) Reset() {
	*x = StatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_internal_live_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusReply) ProtoMessage() {}

func (x *StatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_internal_live_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusReply.ProtoReflect.Descriptor instead.
func (*StatusReply) Descriptor() ([]byte, []int) {
	return file_api_media_v1_internal_live_proto_rawDescGZIP(), []int{0}
}

func (x *StatusReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *StatusReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StatusReply) GetData() *StatusReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type LiveStatusChangedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LiveId int64  `protobuf:"varint,1,opt,name=liveId,proto3" json:"liveId,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Key    string `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *LiveStatusChangedRequest) Reset() {
	*x = LiveStatusChangedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_internal_live_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiveStatusChangedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveStatusChangedRequest) ProtoMessage() {}

func (x *LiveStatusChangedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_internal_live_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveStatusChangedRequest.ProtoReflect.Descriptor instead.
func (*LiveStatusChangedRequest) Descriptor() ([]byte, []int) {
	return file_api_media_v1_internal_live_proto_rawDescGZIP(), []int{1}
}

func (x *LiveStatusChangedRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

func (x *LiveStatusChangedRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *LiveStatusChangedRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type StatusReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StatusReply_Data) Reset() {
	*x = StatusReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_media_v1_internal_live_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusReply_Data) ProtoMessage() {}

func (x *StatusReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_media_v1_internal_live_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusReply_Data.ProtoReflect.Descriptor instead.
func (*StatusReply_Data) Descriptor() ([]byte, []int) {
	return file_api_media_v1_internal_live_proto_rawDescGZIP(), []int{0, 0}
}

func (x *StatusReply_Data) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

var File_api_media_v1_internal_live_proto protoreflect.FileDescriptor

var file_api_media_v1_internal_live_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8f,
	0x01, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x1e, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x5c, 0x0a, 0x18, 0x4c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x69,
	0x76, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x32, 0x8b,
	0x01, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4c, 0x69, 0x76, 0x65, 0x12,
	0x7b, 0x0a, 0x11, 0x4c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x64, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x22,
	0x18, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x3a, 0x01, 0x2a, 0x42, 0x3e, 0x0a, 0x0c,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2c,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_media_v1_internal_live_proto_rawDescOnce sync.Once
	file_api_media_v1_internal_live_proto_rawDescData = file_api_media_v1_internal_live_proto_rawDesc
)

func file_api_media_v1_internal_live_proto_rawDescGZIP() []byte {
	file_api_media_v1_internal_live_proto_rawDescOnce.Do(func() {
		file_api_media_v1_internal_live_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_media_v1_internal_live_proto_rawDescData)
	})
	return file_api_media_v1_internal_live_proto_rawDescData
}

var file_api_media_v1_internal_live_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_media_v1_internal_live_proto_goTypes = []interface{}{
	(*StatusReply)(nil),              // 0: api.media.v1.StatusReply
	(*LiveStatusChangedRequest)(nil), // 1: api.media.v1.LiveStatusChangedRequest
	(*StatusReply_Data)(nil),         // 2: api.media.v1.StatusReply.Data
}
var file_api_media_v1_internal_live_proto_depIdxs = []int32{
	2, // 0: api.media.v1.StatusReply.data:type_name -> api.media.v1.StatusReply.Data
	1, // 1: api.media.v1.InternalLive.LiveStatusChanged:input_type -> api.media.v1.LiveStatusChangedRequest
	0, // 2: api.media.v1.InternalLive.LiveStatusChanged:output_type -> api.media.v1.StatusReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_media_v1_internal_live_proto_init() }
func file_api_media_v1_internal_live_proto_init() {
	if File_api_media_v1_internal_live_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_media_v1_internal_live_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_internal_live_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiveStatusChangedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_media_v1_internal_live_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_media_v1_internal_live_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_media_v1_internal_live_proto_goTypes,
		DependencyIndexes: file_api_media_v1_internal_live_proto_depIdxs,
		MessageInfos:      file_api_media_v1_internal_live_proto_msgTypes,
	}.Build()
	File_api_media_v1_internal_live_proto = out.File
	file_api_media_v1_internal_live_proto_rawDesc = nil
	file_api_media_v1_internal_live_proto_goTypes = nil
	file_api_media_v1_internal_live_proto_depIdxs = nil
}
