syntax = "proto3";

package api.media.v1;

import "google/api/annotations.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/media/v1;v1";
option java_multiple_files = true;
option java_package = "api.media.v1";

service InternalLive {
	rpc LiveStatusChanged (LiveStatusChangedRequest) returns (StatusReply) {
		option (google.api.http) = {
			post: "/internal/v1/live/status"
			body: "*"
		};
	};
}


message StatusReply {
	int32 code = 1;
	string message = 2;
	message Data {
		bool status = 1;
	}
	Data data = 3;
}

message LiveStatusChangedRequest {
	int64 liveId = 1;
	int32 status = 2;
	string key = 3;
}