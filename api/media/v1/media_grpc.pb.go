// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/media/v1/media.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MediaClient is the client API for Media service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MediaClient interface {
	ListMedia(ctx context.Context, in *ListMediaRequest, opts ...grpc.CallOption) (*ListMediaReply, error)
	ListLive(ctx context.Context, in *LiveListRequest, opts ...grpc.CallOption) (*LiveListReply, error)
	StartLiveCallback(ctx context.Context, in *StartLiveCallbackRequest, opts ...grpc.CallOption) (*StartLiveCallbackReply, error)
	ListChannels(ctx context.Context, in *ChannelListRequest, opts ...grpc.CallOption) (*ChannelListReply, error)
	GetChannelDetail(ctx context.Context, in *ChannelDetailRequest, opts ...grpc.CallOption) (*ChannelDetailReply, error)
}

type mediaClient struct {
	cc grpc.ClientConnInterface
}

func NewMediaClient(cc grpc.ClientConnInterface) MediaClient {
	return &mediaClient{cc}
}

func (c *mediaClient) ListMedia(ctx context.Context, in *ListMediaRequest, opts ...grpc.CallOption) (*ListMediaReply, error) {
	out := new(ListMediaReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Media/ListMedia", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaClient) ListLive(ctx context.Context, in *LiveListRequest, opts ...grpc.CallOption) (*LiveListReply, error) {
	out := new(LiveListReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Media/ListLive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaClient) StartLiveCallback(ctx context.Context, in *StartLiveCallbackRequest, opts ...grpc.CallOption) (*StartLiveCallbackReply, error) {
	out := new(StartLiveCallbackReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Media/StartLiveCallback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaClient) ListChannels(ctx context.Context, in *ChannelListRequest, opts ...grpc.CallOption) (*ChannelListReply, error) {
	out := new(ChannelListReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Media/ListChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaClient) GetChannelDetail(ctx context.Context, in *ChannelDetailRequest, opts ...grpc.CallOption) (*ChannelDetailReply, error) {
	out := new(ChannelDetailReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Media/GetChannelDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MediaServer is the server API for Media service.
// All implementations must embed UnimplementedMediaServer
// for forward compatibility
type MediaServer interface {
	ListMedia(context.Context, *ListMediaRequest) (*ListMediaReply, error)
	ListLive(context.Context, *LiveListRequest) (*LiveListReply, error)
	StartLiveCallback(context.Context, *StartLiveCallbackRequest) (*StartLiveCallbackReply, error)
	ListChannels(context.Context, *ChannelListRequest) (*ChannelListReply, error)
	GetChannelDetail(context.Context, *ChannelDetailRequest) (*ChannelDetailReply, error)
	mustEmbedUnimplementedMediaServer()
}

// UnimplementedMediaServer must be embedded to have forward compatible implementations.
type UnimplementedMediaServer struct {
}

func (UnimplementedMediaServer) ListMedia(context.Context, *ListMediaRequest) (*ListMediaReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMedia not implemented")
}
func (UnimplementedMediaServer) ListLive(context.Context, *LiveListRequest) (*LiveListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLive not implemented")
}
func (UnimplementedMediaServer) StartLiveCallback(context.Context, *StartLiveCallbackRequest) (*StartLiveCallbackReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartLiveCallback not implemented")
}
func (UnimplementedMediaServer) ListChannels(context.Context, *ChannelListRequest) (*ChannelListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChannels not implemented")
}
func (UnimplementedMediaServer) GetChannelDetail(context.Context, *ChannelDetailRequest) (*ChannelDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannelDetail not implemented")
}
func (UnimplementedMediaServer) mustEmbedUnimplementedMediaServer() {}

// UnsafeMediaServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MediaServer will
// result in compilation errors.
type UnsafeMediaServer interface {
	mustEmbedUnimplementedMediaServer()
}

func RegisterMediaServer(s grpc.ServiceRegistrar, srv MediaServer) {
	s.RegisterService(&Media_ServiceDesc, srv)
}

func _Media_ListMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMediaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServer).ListMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Media/ListMedia",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServer).ListMedia(ctx, req.(*ListMediaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Media_ListLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LiveListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServer).ListLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Media/ListLive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServer).ListLive(ctx, req.(*LiveListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Media_StartLiveCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartLiveCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServer).StartLiveCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Media/StartLiveCallback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServer).StartLiveCallback(ctx, req.(*StartLiveCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Media_ListChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServer).ListChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Media/ListChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServer).ListChannels(ctx, req.(*ChannelListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Media_GetChannelDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServer).GetChannelDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Media/GetChannelDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServer).GetChannelDetail(ctx, req.(*ChannelDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Media_ServiceDesc is the grpc.ServiceDesc for Media service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Media_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.media.v1.Media",
	HandlerType: (*MediaServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListMedia",
			Handler:    _Media_ListMedia_Handler,
		},
		{
			MethodName: "ListLive",
			Handler:    _Media_ListLive_Handler,
		},
		{
			MethodName: "StartLiveCallback",
			Handler:    _Media_StartLiveCallback_Handler,
		},
		{
			MethodName: "ListChannels",
			Handler:    _Media_ListChannels_Handler,
		},
		{
			MethodName: "GetChannelDetail",
			Handler:    _Media_GetChannelDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/media/v1/media.proto",
}
