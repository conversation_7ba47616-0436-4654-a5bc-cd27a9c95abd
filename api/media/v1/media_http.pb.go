// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type MediaHTTPServer interface {
	GetChannelDetail(context.Context, *ChannelDetailRequest) (*ChannelDetailReply, error)
	ListChannels(context.Context, *ChannelListRequest) (*ChannelListReply, error)
	ListLive(context.Context, *LiveListRequest) (*LiveListReply, error)
	ListMedia(context.Context, *ListMediaRequest) (*ListMediaReply, error)
	StartLiveCallback(context.Context, *StartLiveCallbackRequest) (*StartLiveCallbackReply, error)
}

func RegisterMediaHTTPServer(s *http.Server, srv MediaHTTPServer) {
	r := s.Route("/")
	r.GET("/api/v1/media", _Media_ListMedia0_HTTP_Handler(srv))
	r.GET("/api/v1/media/lives", _Media_ListLive0_HTTP_Handler(srv))
	r.POST("/internal/v1/media/live/{liveId}/callback/start", _Media_StartLiveCallback0_HTTP_Handler(srv))
	r.GET("/api/v1/channels", _Media_ListChannels0_HTTP_Handler(srv))
	r.GET("/api/v1/channels/{id}", _Media_GetChannelDetail0_HTTP_Handler(srv))
}

func _Media_ListMedia0_HTTP_Handler(srv MediaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListMediaRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Media/ListMedia")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListMedia(ctx, req.(*ListMediaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListMediaReply)
		return ctx.Result(200, reply)
	}
}

func _Media_ListLive0_HTTP_Handler(srv MediaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LiveListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Media/ListLive")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLive(ctx, req.(*LiveListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LiveListReply)
		return ctx.Result(200, reply)
	}
}

func _Media_StartLiveCallback0_HTTP_Handler(srv MediaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StartLiveCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Media/StartLiveCallback")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartLiveCallback(ctx, req.(*StartLiveCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StartLiveCallbackReply)
		return ctx.Result(200, reply)
	}
}

func _Media_ListChannels0_HTTP_Handler(srv MediaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChannelListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Media/ListChannels")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListChannels(ctx, req.(*ChannelListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ChannelListReply)
		return ctx.Result(200, reply)
	}
}

func _Media_GetChannelDetail0_HTTP_Handler(srv MediaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChannelDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.media.v1.Media/GetChannelDetail")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetChannelDetail(ctx, req.(*ChannelDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ChannelDetailReply)
		return ctx.Result(200, reply)
	}
}

type MediaHTTPClient interface {
	GetChannelDetail(ctx context.Context, req *ChannelDetailRequest, opts ...http.CallOption) (rsp *ChannelDetailReply, err error)
	ListChannels(ctx context.Context, req *ChannelListRequest, opts ...http.CallOption) (rsp *ChannelListReply, err error)
	ListLive(ctx context.Context, req *LiveListRequest, opts ...http.CallOption) (rsp *LiveListReply, err error)
	ListMedia(ctx context.Context, req *ListMediaRequest, opts ...http.CallOption) (rsp *ListMediaReply, err error)
	StartLiveCallback(ctx context.Context, req *StartLiveCallbackRequest, opts ...http.CallOption) (rsp *StartLiveCallbackReply, err error)
}

type MediaHTTPClientImpl struct {
	cc *http.Client
}

func NewMediaHTTPClient(client *http.Client) MediaHTTPClient {
	return &MediaHTTPClientImpl{client}
}

func (c *MediaHTTPClientImpl) GetChannelDetail(ctx context.Context, in *ChannelDetailRequest, opts ...http.CallOption) (*ChannelDetailReply, error) {
	var out ChannelDetailReply
	pattern := "/api/v1/channels/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.media.v1.Media/GetChannelDetail"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MediaHTTPClientImpl) ListChannels(ctx context.Context, in *ChannelListRequest, opts ...http.CallOption) (*ChannelListReply, error) {
	var out ChannelListReply
	pattern := "/api/v1/channels"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.media.v1.Media/ListChannels"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MediaHTTPClientImpl) ListLive(ctx context.Context, in *LiveListRequest, opts ...http.CallOption) (*LiveListReply, error) {
	var out LiveListReply
	pattern := "/api/v1/media/lives"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.media.v1.Media/ListLive"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MediaHTTPClientImpl) ListMedia(ctx context.Context, in *ListMediaRequest, opts ...http.CallOption) (*ListMediaReply, error) {
	var out ListMediaReply
	pattern := "/api/v1/media"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.media.v1.Media/ListMedia"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MediaHTTPClientImpl) StartLiveCallback(ctx context.Context, in *StartLiveCallbackRequest, opts ...http.CallOption) (*StartLiveCallbackReply, error) {
	var out StartLiveCallbackReply
	pattern := "/internal/v1/media/live/{liveId}/callback/start"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.media.v1.Media/StartLiveCallback"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
