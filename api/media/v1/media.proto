syntax = "proto3";

package api.media.v1;

import "google/api/annotations.proto";
import "validator/validator.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/media/v1;v1";
option java_multiple_files = true;
option java_package = "api.media.v1";

service Media {
	rpc ListMedia (ListMediaRequest) returns (ListMediaReply) {
		option (google.api.http) = {
      get: "/api/v1/media"
    };
	};

	rpc ListLive(LiveListRequest) returns (LiveListReply) {
		option (google.api.http) = {
      get: "/api/v1/media/lives"
    };
	}

	rpc StartLiveCallback(StartLiveCallbackRequest) returns (StartLiveCallbackReply) {
		option (google.api.http) = {
      post: "/internal/v1/media/live/{liveId}/callback/start"
			body: "*"
    };
	}

	rpc ListChannels(ChannelListRequest) returns (ChannelListReply) {
		option (google.api.http) = {
      get: "/api/v1/channels"
    };
	}

	rpc GetChannelDetail(ChannelDetailRequest) returns (ChannelDetailReply) {
		option (google.api.http) = {
      get: "/api/v1/channels/{id}"
    };
	}
}

message ChannelListRequest {
	int32 page = 1;
	int32 size = 2 [(validator.rules) = "max=1000"];
	string search = 3;
}

message ChannelListReply {
	int32 code = 1;
	string message = 2;
	message Data {
		int32 total = 1;
		int32 page = 2;
		int32 size = 3;
		repeated VideoChannel list = 4;
	}
	Data data = 3;
}

message ChannelDetailRequest {
	int64 id = 1;
}

message ChannelDetailReply {
	int32 code = 1;
	string message = 2;
	VideoChannel data = 3;
}


message VideoChannel {
	int64 id = 1;
	int64 tenantId = 2;
	int64 merchantId = 3;
	ChannelCamera camera = 4;
	string name = 5;
	string type = 6;
	string protocolType =7;
	int32 status = 8;
	string image = 9;
	repeated string streams = 10;
	string sequence = 11;
	string serial = 12;
	repeated string transcodeStreams = 17;
	string imageOrigin = 18;
}

message ChannelCamera {
	int64 id = 1;
	// string name = 2;
	string sn = 3;
	string type = 4;
	// string model = 5;
	//string category = 6;
	//string sourceId = 7;
	// string originId = 8;
	Deployment deployment = 9;
	double createdTime = 10;
  double updatedTime = 11;
	string sourceSn = 12;
	// 专网
	// bool privateNetwork = 15;
	message Deployment {
		string name = 1;
		repeated string tags = 2;
		Images images = 3;
		repeated Contact contacts = 4;
		repeated double lnglat = 5;
		string location = 6;
		double time = 7;
		repeated string labels = 8;
	}

	message Images {
		string deviceImg = 1;
		string envImg = 2;
		optional string shopImg = 3;
	}

	message Contact {
		string name = 1;
		string contact = 2;
	}
}


message MediaItem  {
	int64 deviceId = 1;
	int64 airlineId = 2;
	int64 voyageId = 3;
	int64 waypointId = 4;
	int64 id = 5;
	string type = 6;
	string url = 7;
	string name = 8;
	double createdTime = 9;
	double shootTime = 10;
	repeated double lnglat = 11;
	message VideoInfo {
		bool isFull = 1;
		string coverImg = 2;
	}
	message PhotoInfo {
		string thumbnail = 1;
	}
	VideoInfo vInfo = 12;
	PhotoInfo pInfo = 13;
}

message ListMediaRequest {
	int64 deviceId = 1;
	int64 airlineId = 2;
	int64 voyageId = 3;
	int64 waypointId = 4;
	int32 page = 5;
	int32 size = 6 [(validator.rules) = "max=1000"];
	int32 withVoyageRecord= 7;
	string lenTypes = 8;
}

message ListMediaReply {
	int32 code = 1;
	string message = 2;
	message Data {
		double total = 1;
		int32 page = 2;
		int32 size = 3;
		repeated MediaItem list = 4;
	}
	Data data = 3;
}

message Device {
	string sn = 1;
	string name = 2;
	string location = 3;
	repeated double lnglat = 4;
	string type = 5;
	string model = 6;
	string category = 7;
	bool networkStatus = 8;
}

message LiveListRequest {
	int32 page = 1;
	int32 size = 2 [(validator.rules) = "max=1000"];
	bool dronesOnly = 3;
	repeated int32 status = 4;
	string search = 5;
}

message Live {
	int64 id = 1;
	int64 deviceId = 2;
	int32 status = 3;
	string url = 4;
	double createdTime = 5;
	double updatedTime = 6;
	Device device = 7;
	string type = 8;
	int32 clarity = 9;
	int32 position = 10;
}

message LiveListReply {
	int32 code = 1;
	string message = 2;
	message Data {
		int32 total = 1;
		int32 offline = 2;
		int32 page = 3;
		int32 size = 4;
		repeated Live list = 5;
	}
	Data data = 3;
}

message StartLiveCallbackRequest {
	int64 liveId = 1 [(validator.rules) = "required"];
	string id = 2;
	string clientId = 3;
  int64 executeTime = 4;
  payload body = 5;

  message payload {
		int64 deviceId = 1;
	}
}

message StartLiveCallbackReply {
	int32 code = 1;
	string message = 2;
	message Data {
		bool status = 1;
	}
	Data data = 3;
}