// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/media/v1/gallery.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GalleryClient is the client API for Gallery service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GalleryClient interface {
	DeleteGalleryMedia(ctx context.Context, in *DeleteGalleryMediaRequest, opts ...grpc.CallOption) (*DeleteGalleryMediaReply, error)
	ListGalleryMedia(ctx context.Context, in *ListGalleryMediaRequest, opts ...grpc.CallOption) (*ListGalleryMediaReply, error)
	GetVideoMediaDownloadUrl(ctx context.Context, in *GetVideoMediaDownloadUrlRequest, opts ...grpc.CallOption) (*GetVideoMediaDownloadUrlReply, error)
	CleanUpTmpDownloadableFile(ctx context.Context, in *CleanUpTmpDownloadableFileRequest, opts ...grpc.CallOption) (*CleanUpTmpDownloadableFileReply, error)
}

type galleryClient struct {
	cc grpc.ClientConnInterface
}

func NewGalleryClient(cc grpc.ClientConnInterface) GalleryClient {
	return &galleryClient{cc}
}

func (c *galleryClient) DeleteGalleryMedia(ctx context.Context, in *DeleteGalleryMediaRequest, opts ...grpc.CallOption) (*DeleteGalleryMediaReply, error) {
	out := new(DeleteGalleryMediaReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Gallery/DeleteGalleryMedia", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *galleryClient) ListGalleryMedia(ctx context.Context, in *ListGalleryMediaRequest, opts ...grpc.CallOption) (*ListGalleryMediaReply, error) {
	out := new(ListGalleryMediaReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Gallery/ListGalleryMedia", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *galleryClient) GetVideoMediaDownloadUrl(ctx context.Context, in *GetVideoMediaDownloadUrlRequest, opts ...grpc.CallOption) (*GetVideoMediaDownloadUrlReply, error) {
	out := new(GetVideoMediaDownloadUrlReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Gallery/GetVideoMediaDownloadUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *galleryClient) CleanUpTmpDownloadableFile(ctx context.Context, in *CleanUpTmpDownloadableFileRequest, opts ...grpc.CallOption) (*CleanUpTmpDownloadableFileReply, error) {
	out := new(CleanUpTmpDownloadableFileReply)
	err := c.cc.Invoke(ctx, "/api.media.v1.Gallery/CleanUpTmpDownloadableFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GalleryServer is the server API for Gallery service.
// All implementations must embed UnimplementedGalleryServer
// for forward compatibility
type GalleryServer interface {
	DeleteGalleryMedia(context.Context, *DeleteGalleryMediaRequest) (*DeleteGalleryMediaReply, error)
	ListGalleryMedia(context.Context, *ListGalleryMediaRequest) (*ListGalleryMediaReply, error)
	GetVideoMediaDownloadUrl(context.Context, *GetVideoMediaDownloadUrlRequest) (*GetVideoMediaDownloadUrlReply, error)
	CleanUpTmpDownloadableFile(context.Context, *CleanUpTmpDownloadableFileRequest) (*CleanUpTmpDownloadableFileReply, error)
	mustEmbedUnimplementedGalleryServer()
}

// UnimplementedGalleryServer must be embedded to have forward compatible implementations.
type UnimplementedGalleryServer struct {
}

func (UnimplementedGalleryServer) DeleteGalleryMedia(context.Context, *DeleteGalleryMediaRequest) (*DeleteGalleryMediaReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGalleryMedia not implemented")
}
func (UnimplementedGalleryServer) ListGalleryMedia(context.Context, *ListGalleryMediaRequest) (*ListGalleryMediaReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGalleryMedia not implemented")
}
func (UnimplementedGalleryServer) GetVideoMediaDownloadUrl(context.Context, *GetVideoMediaDownloadUrlRequest) (*GetVideoMediaDownloadUrlReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVideoMediaDownloadUrl not implemented")
}
func (UnimplementedGalleryServer) CleanUpTmpDownloadableFile(context.Context, *CleanUpTmpDownloadableFileRequest) (*CleanUpTmpDownloadableFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanUpTmpDownloadableFile not implemented")
}
func (UnimplementedGalleryServer) mustEmbedUnimplementedGalleryServer() {}

// UnsafeGalleryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GalleryServer will
// result in compilation errors.
type UnsafeGalleryServer interface {
	mustEmbedUnimplementedGalleryServer()
}

func RegisterGalleryServer(s grpc.ServiceRegistrar, srv GalleryServer) {
	s.RegisterService(&Gallery_ServiceDesc, srv)
}

func _Gallery_DeleteGalleryMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGalleryMediaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GalleryServer).DeleteGalleryMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Gallery/DeleteGalleryMedia",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GalleryServer).DeleteGalleryMedia(ctx, req.(*DeleteGalleryMediaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Gallery_ListGalleryMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGalleryMediaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GalleryServer).ListGalleryMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Gallery/ListGalleryMedia",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GalleryServer).ListGalleryMedia(ctx, req.(*ListGalleryMediaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Gallery_GetVideoMediaDownloadUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVideoMediaDownloadUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GalleryServer).GetVideoMediaDownloadUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Gallery/GetVideoMediaDownloadUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GalleryServer).GetVideoMediaDownloadUrl(ctx, req.(*GetVideoMediaDownloadUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Gallery_CleanUpTmpDownloadableFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanUpTmpDownloadableFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GalleryServer).CleanUpTmpDownloadableFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.media.v1.Gallery/CleanUpTmpDownloadableFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GalleryServer).CleanUpTmpDownloadableFile(ctx, req.(*CleanUpTmpDownloadableFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Gallery_ServiceDesc is the grpc.ServiceDesc for Gallery service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Gallery_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.media.v1.Gallery",
	HandlerType: (*GalleryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DeleteGalleryMedia",
			Handler:    _Gallery_DeleteGalleryMedia_Handler,
		},
		{
			MethodName: "ListGalleryMedia",
			Handler:    _Gallery_ListGalleryMedia_Handler,
		},
		{
			MethodName: "GetVideoMediaDownloadUrl",
			Handler:    _Gallery_GetVideoMediaDownloadUrl_Handler,
		},
		{
			MethodName: "CleanUpTmpDownloadableFile",
			Handler:    _Gallery_CleanUpTmpDownloadableFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/media/v1/gallery.proto",
}
