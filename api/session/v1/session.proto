syntax = "proto3";

package api.session.v1;
import "google/api/annotations.proto";
import "validator/validator.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/session/v1;v1";
option java_multiple_files = true;
option java_package = "api.session.v1";

service Session {
	rpc Login (LoginRequest) returns (LoginReply) {
    option (google.api.http) = {
      post: "/api/v1/session/login"
			body: "*"
    };
  };
  rpc Logout (EmptyRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/session/logout"
			body: "*"
    };
  };
}

message EmptyRequest {}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message LoginRequest {
	string username = 1 [(validator.rules) = "required"];
	string password = 2 [(validator.rules) = "required"];
}

message SessionTenant {
	int64 id = 1;
	string name = 2;
	int32 type = 3;
	string code = 4;
	string icon = 5;
	bool isAdmin = 6;
	string area = 7;
	string areaName = 8;
	string city = 9;
	string cityName = 10;
	string province = 11;
	string provinceName = 12;
	string address = 13;
	repeated double lnglat = 14;
	int32 isDisabled = 15;
	string description = 16;
	double createdTime = 17;
	double updatedTime = 18;
}

message SessionProject {
	int64 id = 1;
	string name = 2;
	int32 type = 3;
	string icon = 4;
	string logo = 5;
	string footer = 6;
	int64 tenantId = 7;
	string description = 8;
	string city = 9;
	string cityName = 10;
	string area = 11;
	string areaName = 12;
	string province = 13;
	string provinceName = 14;
	string address = 15;
	int32 isDisabled = 16;
	repeated double lnglat = 17;
}

message SessionProfile {
	int64 id = 1;
	string mobile = 2;
	string nickname = 3;
	string avatar = 5;
	int64 tenantId = 6;
	int64 accountId = 7;
	bool isTenantAdmin = 8;
	bool isProjectAdmin = 9;
	int32 gender = 10;
	int32 isDisabled = 11;
	int64 lastActiveTime = 12;
	int32 roleType = 13;
	double createdTime = 14;
	double updatedTime = 15;
	repeated role roles = 16;
	repeated permission permissions = 17;

	message role {
		int64 id = 1;
		string name = 2;
		int32 isAdmin = 3;
	}

	message permission {
		int64 id =1;
		string code = 2;
		string name = 3;
		int64 parentId = 4;
	}
}

message LoginReply {
  int32 code = 1;
  string message = 2;
  sessionData data = 3;

	message sessionData {
		string token = 1;
		SessionTenant tenant = 2;
		SessionProject project = 3;
		SessionProfile profile = 4;
	}
}