{"swagger": "2.0", "info": {"title": "api/session/v1/session.proto", "version": "version not set"}, "tags": [{"name": "Session"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/session/login": {"post": {"operationId": "Session_Login", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1LoginReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1LoginRequest"}}], "tags": ["Session"]}}, "/api/v1/session/logout": {"post": {"operationId": "Session_Logout", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apisessionv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/apisessionv1EmptyRequest"}}], "tags": ["Session"]}}}, "definitions": {"LoginReplysessionData": {"type": "object", "properties": {"token": {"type": "string"}, "tenant": {"$ref": "#/definitions/v1SessionTenant"}, "project": {"$ref": "#/definitions/v1SessionProject"}, "profile": {"$ref": "#/definitions/v1SessionProfile"}}}, "SessionProfilepermission": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "code": {"type": "string"}, "name": {"type": "string"}, "parentId": {"type": "string", "format": "int64"}}}, "SessionProfilerole": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "isAdmin": {"type": "integer", "format": "int32"}}}, "apisessionv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apisessionv1CommonReplycommonData"}}}, "apisessionv1CommonReplycommonData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "apisessionv1EmptyRequest": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1LoginReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/LoginReplysessionData"}}}, "v1LoginRequest": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}, "v1SessionProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "mobile": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}, "tenantId": {"type": "string", "format": "int64"}, "accountId": {"type": "string", "format": "int64"}, "isTenantAdmin": {"type": "boolean"}, "isProjectAdmin": {"type": "boolean"}, "gender": {"type": "integer", "format": "int32"}, "isDisabled": {"type": "integer", "format": "int32"}, "lastActiveTime": {"type": "string", "format": "int64"}, "roleType": {"type": "integer", "format": "int32"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/SessionProfilerole"}}, "permissions": {"type": "array", "items": {"$ref": "#/definitions/SessionProfilepermission"}}}}, "v1SessionProject": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "icon": {"type": "string"}, "logo": {"type": "string"}, "footer": {"type": "string"}, "tenantId": {"type": "string", "format": "int64"}, "description": {"type": "string"}, "city": {"type": "string"}, "cityName": {"type": "string"}, "area": {"type": "string"}, "areaName": {"type": "string"}, "province": {"type": "string"}, "provinceName": {"type": "string"}, "address": {"type": "string"}, "isDisabled": {"type": "integer", "format": "int32"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}, "v1SessionTenant": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "code": {"type": "string"}, "icon": {"type": "string"}, "isAdmin": {"type": "boolean"}, "area": {"type": "string"}, "areaName": {"type": "string"}, "city": {"type": "string"}, "cityName": {"type": "string"}, "province": {"type": "string"}, "provinceName": {"type": "string"}, "address": {"type": "string"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "isDisabled": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}}}}}