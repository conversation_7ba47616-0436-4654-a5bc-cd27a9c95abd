// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type SessionHTTPServer interface {
	Login(context.Context, *LoginRequest) (*LoginReply, error)
	Logout(context.Context, *EmptyRequest) (*CommonReply, error)
}

func RegisterSessionHTTPServer(s *http.Server, srv SessionHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/session/login", _Session_Login0_HTTP_Handler(srv))
	r.POST("/api/v1/session/logout", _Session_Logout0_HTTP_Handler(srv))
}

func _Session_Login0_HTTP_Handler(srv SessionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LoginRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.session.v1.Session/Login")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Login(ctx, req.(*LoginRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Session_Logout0_HTTP_Handler(srv SessionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.session.v1.Session/Logout")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Logout(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type SessionHTTPClient interface {
	Login(ctx context.Context, req *LoginRequest, opts ...http.CallOption) (rsp *LoginReply, err error)
	Logout(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type SessionHTTPClientImpl struct {
	cc *http.Client
}

func NewSessionHTTPClient(client *http.Client) SessionHTTPClient {
	return &SessionHTTPClientImpl{client}
}

func (c *SessionHTTPClientImpl) Login(ctx context.Context, in *LoginRequest, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/api/v1/session/login"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.session.v1.Session/Login"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *SessionHTTPClientImpl) Logout(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/session/logout"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.session.v1.Session/Logout"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
