syntax = "proto3";

package api.subjects.v1;
import "google/api/annotations.proto";
import "validator/validator.proto";
import "list/list.proto";
import "api/missions/v1/mission.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/subjects/v1;v1";
option java_multiple_files = true;
option java_package = "api.subjects.v1";

service Subject {
	rpc CreateSubject (SubjectItem) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/subjects"
			body: "*"
    };
  };
	rpc ListSubject (ListRequest) returns (ListReply) {
    option (google.api.http) = {
      get: "/api/v1/subjects"
    };
  };
	rpc GetSubject (GetRequest) returns (SubjectReply) {
    option (google.api.http) = {
      get: "/api/v1/subjects/{id}"
    };
  };
	rpc UpdateSubject (SubjectItem) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/subjects/{id}"
      body: "*"
    };
  };
	rpc DeleteSubject (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      delete: "/api/v1/subjects/{id}"
    };
  };
}

message CommonRequest {
  int64 id = 1 [(validator.rules) = "required"];
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message SubjectItem {
  int64 id = 1;
  double createdTime = 2;
  double updatedTime = 3;
  string name = 4;
  string eventType = 5;
  string description = 6;
	int64 tenantId = 7;
  int64 projectId = 8;
  int64 avatarId = 9;
  int64 editorId = 10;
  int32 reportCount = 11;
  int32 pendingCount = 12;
  int32 finishCount = 13;
  bool isDeleted = 14;
  double editedTime = 15;
  avatarInfo avatar = 16;
  avatarInfo editor = 17;
  repeated api.missions.v1.NoticeRule noticeRules = 18;

  message avatarInfo {
    int64 id = 1;
    string mobile = 2;
    string nickname = 3;
    string avatar = 4;
  }
}

message SubjectReply {
  int32 code = 1;
  string message = 2;
  SubjectItem data = 3;
}

message ListRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  optional string eventType = 3;
  optional string search = 4 [(list.filter_options)={filter_name: "name", operator:"search" }];
}

message ListReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated SubjectItem list = 4;
  }
}

message GetRequest {
  int64 id = 1 [(validator.rules) = "required"];
  int32 scope = 2;
}
