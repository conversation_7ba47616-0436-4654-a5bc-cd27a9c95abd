// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type SubjectHTTPServer interface {
	CreateSubject(context.Context, *SubjectItem) (*CommonReply, error)
	DeleteSubject(context.Context, *CommonRequest) (*CommonReply, error)
	GetSubject(context.Context, *GetRequest) (*SubjectReply, error)
	ListSubject(context.Context, *ListRequest) (*ListReply, error)
	UpdateSubject(context.Context, *SubjectItem) (*CommonReply, error)
}

func RegisterSubjectHTTPServer(s *http.Server, srv SubjectHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/subjects", _Subject_CreateSubject0_HTTP_Handler(srv))
	r.GET("/api/v1/subjects", _Subject_ListSubject0_HTTP_Handler(srv))
	r.GET("/api/v1/subjects/{id}", _Subject_GetSubject0_HTTP_Handler(srv))
	r.PUT("/api/v1/subjects/{id}", _Subject_UpdateSubject0_HTTP_Handler(srv))
	r.DELETE("/api/v1/subjects/{id}", _Subject_DeleteSubject0_HTTP_Handler(srv))
}

func _Subject_CreateSubject0_HTTP_Handler(srv SubjectHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SubjectItem
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.subjects.v1.Subject/CreateSubject")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateSubject(ctx, req.(*SubjectItem))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Subject_ListSubject0_HTTP_Handler(srv SubjectHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.subjects.v1.Subject/ListSubject")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSubject(ctx, req.(*ListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListReply)
		return ctx.Result(200, reply)
	}
}

func _Subject_GetSubject0_HTTP_Handler(srv SubjectHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.subjects.v1.Subject/GetSubject")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSubject(ctx, req.(*GetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SubjectReply)
		return ctx.Result(200, reply)
	}
}

func _Subject_UpdateSubject0_HTTP_Handler(srv SubjectHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SubjectItem
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.subjects.v1.Subject/UpdateSubject")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateSubject(ctx, req.(*SubjectItem))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Subject_DeleteSubject0_HTTP_Handler(srv SubjectHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.subjects.v1.Subject/DeleteSubject")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteSubject(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type SubjectHTTPClient interface {
	CreateSubject(ctx context.Context, req *SubjectItem, opts ...http.CallOption) (rsp *CommonReply, err error)
	DeleteSubject(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	GetSubject(ctx context.Context, req *GetRequest, opts ...http.CallOption) (rsp *SubjectReply, err error)
	ListSubject(ctx context.Context, req *ListRequest, opts ...http.CallOption) (rsp *ListReply, err error)
	UpdateSubject(ctx context.Context, req *SubjectItem, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type SubjectHTTPClientImpl struct {
	cc *http.Client
}

func NewSubjectHTTPClient(client *http.Client) SubjectHTTPClient {
	return &SubjectHTTPClientImpl{client}
}

func (c *SubjectHTTPClientImpl) CreateSubject(ctx context.Context, in *SubjectItem, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/subjects"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.subjects.v1.Subject/CreateSubject"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *SubjectHTTPClientImpl) DeleteSubject(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/subjects/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.subjects.v1.Subject/DeleteSubject"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *SubjectHTTPClientImpl) GetSubject(ctx context.Context, in *GetRequest, opts ...http.CallOption) (*SubjectReply, error) {
	var out SubjectReply
	pattern := "/api/v1/subjects/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.subjects.v1.Subject/GetSubject"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *SubjectHTTPClientImpl) ListSubject(ctx context.Context, in *ListRequest, opts ...http.CallOption) (*ListReply, error) {
	var out ListReply
	pattern := "/api/v1/subjects"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.subjects.v1.Subject/ListSubject"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *SubjectHTTPClientImpl) UpdateSubject(ctx context.Context, in *SubjectItem, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/subjects/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.subjects.v1.Subject/UpdateSubject"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
