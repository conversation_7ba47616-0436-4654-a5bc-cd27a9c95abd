// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/subjects/v1/subject.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SubjectClient is the client API for Subject service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SubjectClient interface {
	CreateSubject(ctx context.Context, in *SubjectItem, opts ...grpc.CallOption) (*CommonReply, error)
	ListSubject(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error)
	GetSubject(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*SubjectReply, error)
	UpdateSubject(ctx context.Context, in *SubjectItem, opts ...grpc.CallOption) (*CommonReply, error)
	DeleteSubject(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
}

type subjectClient struct {
	cc grpc.ClientConnInterface
}

func NewSubjectClient(cc grpc.ClientConnInterface) SubjectClient {
	return &subjectClient{cc}
}

func (c *subjectClient) CreateSubject(ctx context.Context, in *SubjectItem, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.subjects.v1.Subject/CreateSubject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subjectClient) ListSubject(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error) {
	out := new(ListReply)
	err := c.cc.Invoke(ctx, "/api.subjects.v1.Subject/ListSubject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subjectClient) GetSubject(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*SubjectReply, error) {
	out := new(SubjectReply)
	err := c.cc.Invoke(ctx, "/api.subjects.v1.Subject/GetSubject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subjectClient) UpdateSubject(ctx context.Context, in *SubjectItem, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.subjects.v1.Subject/UpdateSubject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subjectClient) DeleteSubject(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.subjects.v1.Subject/DeleteSubject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubjectServer is the server API for Subject service.
// All implementations must embed UnimplementedSubjectServer
// for forward compatibility
type SubjectServer interface {
	CreateSubject(context.Context, *SubjectItem) (*CommonReply, error)
	ListSubject(context.Context, *ListRequest) (*ListReply, error)
	GetSubject(context.Context, *GetRequest) (*SubjectReply, error)
	UpdateSubject(context.Context, *SubjectItem) (*CommonReply, error)
	DeleteSubject(context.Context, *CommonRequest) (*CommonReply, error)
	mustEmbedUnimplementedSubjectServer()
}

// UnimplementedSubjectServer must be embedded to have forward compatible implementations.
type UnimplementedSubjectServer struct {
}

func (UnimplementedSubjectServer) CreateSubject(context.Context, *SubjectItem) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSubject not implemented")
}
func (UnimplementedSubjectServer) ListSubject(context.Context, *ListRequest) (*ListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubject not implemented")
}
func (UnimplementedSubjectServer) GetSubject(context.Context, *GetRequest) (*SubjectReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubject not implemented")
}
func (UnimplementedSubjectServer) UpdateSubject(context.Context, *SubjectItem) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSubject not implemented")
}
func (UnimplementedSubjectServer) DeleteSubject(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSubject not implemented")
}
func (UnimplementedSubjectServer) mustEmbedUnimplementedSubjectServer() {}

// UnsafeSubjectServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SubjectServer will
// result in compilation errors.
type UnsafeSubjectServer interface {
	mustEmbedUnimplementedSubjectServer()
}

func RegisterSubjectServer(s grpc.ServiceRegistrar, srv SubjectServer) {
	s.RegisterService(&Subject_ServiceDesc, srv)
}

func _Subject_CreateSubject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubjectItem)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubjectServer).CreateSubject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.subjects.v1.Subject/CreateSubject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubjectServer).CreateSubject(ctx, req.(*SubjectItem))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subject_ListSubject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubjectServer).ListSubject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.subjects.v1.Subject/ListSubject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubjectServer).ListSubject(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subject_GetSubject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubjectServer).GetSubject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.subjects.v1.Subject/GetSubject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubjectServer).GetSubject(ctx, req.(*GetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subject_UpdateSubject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubjectItem)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubjectServer).UpdateSubject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.subjects.v1.Subject/UpdateSubject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubjectServer).UpdateSubject(ctx, req.(*SubjectItem))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subject_DeleteSubject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubjectServer).DeleteSubject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.subjects.v1.Subject/DeleteSubject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubjectServer).DeleteSubject(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Subject_ServiceDesc is the grpc.ServiceDesc for Subject service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Subject_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.subjects.v1.Subject",
	HandlerType: (*SubjectServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSubject",
			Handler:    _Subject_CreateSubject_Handler,
		},
		{
			MethodName: "ListSubject",
			Handler:    _Subject_ListSubject_Handler,
		},
		{
			MethodName: "GetSubject",
			Handler:    _Subject_GetSubject_Handler,
		},
		{
			MethodName: "UpdateSubject",
			Handler:    _Subject_UpdateSubject_Handler,
		},
		{
			MethodName: "DeleteSubject",
			Handler:    _Subject_DeleteSubject_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/subjects/v1/subject.proto",
}
