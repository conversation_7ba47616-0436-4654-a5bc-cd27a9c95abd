{"swagger": "2.0", "info": {"title": "api/subjects/v1/subject.proto", "version": "version not set"}, "tags": [{"name": "Subject"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/subjects": {"get": {"operationId": "Subject_ListSubject", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apisubjectsv1ListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "eventType", "in": "query", "required": false, "type": "string"}, {"name": "search", "in": "query", "required": false, "type": "string"}], "tags": ["Subject"]}, "post": {"operationId": "Subject_CreateSubject", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apisubjectsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1SubjectItem"}}], "tags": ["Subject"]}}, "/api/v1/subjects/{id}": {"get": {"operationId": "Subject_GetSubject", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1SubjectReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "scope", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["Subject"]}, "delete": {"operationId": "Subject_DeleteSubject", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apisubjectsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Subject"]}, "put": {"operationId": "Subject_UpdateSubject", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apisubjectsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "name": {"type": "string"}, "eventType": {"type": "string"}, "description": {"type": "string"}, "tenantId": {"type": "string", "format": "int64"}, "projectId": {"type": "string", "format": "int64"}, "avatarId": {"type": "string", "format": "int64"}, "editorId": {"type": "string", "format": "int64"}, "reportCount": {"type": "integer", "format": "int32"}, "pendingCount": {"type": "integer", "format": "int32"}, "finishCount": {"type": "integer", "format": "int32"}, "isDeleted": {"type": "boolean"}, "editedTime": {"type": "number", "format": "double"}, "avatar": {"$ref": "#/definitions/v1SubjectItemavatarInfo"}, "editor": {"$ref": "#/definitions/v1SubjectItemavatarInfo"}, "noticeRules": {"type": "array", "items": {"$ref": "#/definitions/v1NoticeRule"}}}}}], "tags": ["Subject"]}}}, "definitions": {"apisubjectsv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apisubjectsv1CommonReplycommonData"}}}, "apisubjectsv1CommonReplycommonData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "apisubjectsv1ListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apisubjectsv1ListReplylistData"}}}, "apisubjectsv1ListReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1SubjectItem"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1NoticeRule": {"type": "object", "properties": {"time": {"type": "integer", "format": "int32"}, "multilevel": {"type": "integer", "format": "int32"}, "noticeTypes": {"type": "array", "items": {"type": "string"}}, "conditions": {"type": "array", "items": {"type": "string"}}, "contacts": {"type": "array", "items": {"$ref": "#/definitions/v1NoticeRulecontact"}}}}, "v1NoticeRulecontact": {"type": "object", "properties": {"name": {"type": "string"}, "contact": {"type": "string"}}}, "v1SubjectItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "name": {"type": "string"}, "eventType": {"type": "string"}, "description": {"type": "string"}, "tenantId": {"type": "string", "format": "int64"}, "projectId": {"type": "string", "format": "int64"}, "avatarId": {"type": "string", "format": "int64"}, "editorId": {"type": "string", "format": "int64"}, "reportCount": {"type": "integer", "format": "int32"}, "pendingCount": {"type": "integer", "format": "int32"}, "finishCount": {"type": "integer", "format": "int32"}, "isDeleted": {"type": "boolean"}, "editedTime": {"type": "number", "format": "double"}, "avatar": {"$ref": "#/definitions/v1SubjectItemavatarInfo"}, "editor": {"$ref": "#/definitions/v1SubjectItemavatarInfo"}, "noticeRules": {"type": "array", "items": {"$ref": "#/definitions/v1NoticeRule"}}}}, "v1SubjectItemavatarInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "mobile": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}}}, "v1SubjectReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1SubjectItem"}}}}}