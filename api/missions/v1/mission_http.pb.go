// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type MissionHTTPServer interface {
	AlarmsMission(context.Context, *AlarmsRequest) (*AlarmsReply, error)
	BatchMission(context.Context, *BatchRequest) (*BatchReply, error)
	CreateMission(context.Context, *MissionItem) (*CommonReply, error)
	DeleteMission(context.Context, *CommonRequest) (*CommonReply, error)
	GetMission(context.Context, *GetRequest) (*MissionReply, error)
	ListExecution(context.Context, *ExecutionsRequest) (*ExecutionsReply, error)
	ListMission(context.Context, *ListRequest) (*ListReply, error)
	StatsMission(context.Context, *EmptyRequest) (*StatsReply, error)
	StatusMission(context.Context, *StatusRequest) (*CommonReply, error)
	SwitchMission(context.Context, *SwitchRequest) (*CommonReply, error)
	UpdateMission(context.Context, *MissionItem) (*CommonReply, error)
}

func RegisterMissionHTTPServer(s *http.Server, srv MissionHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/missions", _Mission_CreateMission0_HTTP_Handler(srv))
	r.GET("/api/v1/missions", _Mission_ListMission0_HTTP_Handler(srv))
	r.GET("/api/v1/missions/{id}", _Mission_GetMission0_HTTP_Handler(srv))
	r.PUT("/api/v1/missions/{id}", _Mission_UpdateMission0_HTTP_Handler(srv))
	r.PUT("/api/v1/missions/batch/status", _Mission_BatchMission0_HTTP_Handler(srv))
	r.GET("/api/v1/missions/statistics/status", _Mission_StatsMission0_HTTP_Handler(srv))
	r.GET("/api/v1/missions/statistics/alarms", _Mission_AlarmsMission0_HTTP_Handler(srv))
	r.PUT("/api/v1/missions/{id}/switch", _Mission_SwitchMission0_HTTP_Handler(srv))
	r.DELETE("/api/v1/missions/{id}", _Mission_DeleteMission0_HTTP_Handler(srv))
	r.GET("/api/v1/missions/{id}/executions", _Mission_ListExecution0_HTTP_Handler(srv))
	r.POST("/internal/v1/missions/{mid}/status", _Mission_StatusMission0_HTTP_Handler(srv))
}

func _Mission_CreateMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in MissionItem
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/CreateMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateMission(ctx, req.(*MissionItem))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_ListMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/ListMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListMission(ctx, req.(*ListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_GetMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/GetMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMission(ctx, req.(*GetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*MissionReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_UpdateMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in MissionItem
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/UpdateMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateMission(ctx, req.(*MissionItem))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_BatchMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BatchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/BatchMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BatchMission(ctx, req.(*BatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BatchReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_StatsMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/StatsMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StatsMission(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StatsReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_AlarmsMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AlarmsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/AlarmsMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AlarmsMission(ctx, req.(*AlarmsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AlarmsReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_SwitchMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SwitchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/SwitchMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SwitchMission(ctx, req.(*SwitchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_DeleteMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/DeleteMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteMission(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_ListExecution0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExecutionsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/ListExecution")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExecution(ctx, req.(*ExecutionsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExecutionsReply)
		return ctx.Result(200, reply)
	}
}

func _Mission_StatusMission0_HTTP_Handler(srv MissionHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.missions.v1.Mission/StatusMission")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StatusMission(ctx, req.(*StatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type MissionHTTPClient interface {
	AlarmsMission(ctx context.Context, req *AlarmsRequest, opts ...http.CallOption) (rsp *AlarmsReply, err error)
	BatchMission(ctx context.Context, req *BatchRequest, opts ...http.CallOption) (rsp *BatchReply, err error)
	CreateMission(ctx context.Context, req *MissionItem, opts ...http.CallOption) (rsp *CommonReply, err error)
	DeleteMission(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	GetMission(ctx context.Context, req *GetRequest, opts ...http.CallOption) (rsp *MissionReply, err error)
	ListExecution(ctx context.Context, req *ExecutionsRequest, opts ...http.CallOption) (rsp *ExecutionsReply, err error)
	ListMission(ctx context.Context, req *ListRequest, opts ...http.CallOption) (rsp *ListReply, err error)
	StatsMission(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *StatsReply, err error)
	StatusMission(ctx context.Context, req *StatusRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	SwitchMission(ctx context.Context, req *SwitchRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UpdateMission(ctx context.Context, req *MissionItem, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type MissionHTTPClientImpl struct {
	cc *http.Client
}

func NewMissionHTTPClient(client *http.Client) MissionHTTPClient {
	return &MissionHTTPClientImpl{client}
}

func (c *MissionHTTPClientImpl) AlarmsMission(ctx context.Context, in *AlarmsRequest, opts ...http.CallOption) (*AlarmsReply, error) {
	var out AlarmsReply
	pattern := "/api/v1/missions/statistics/alarms"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/AlarmsMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) BatchMission(ctx context.Context, in *BatchRequest, opts ...http.CallOption) (*BatchReply, error) {
	var out BatchReply
	pattern := "/api/v1/missions/batch/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/BatchMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) CreateMission(ctx context.Context, in *MissionItem, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/missions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/CreateMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) DeleteMission(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/missions/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/DeleteMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) GetMission(ctx context.Context, in *GetRequest, opts ...http.CallOption) (*MissionReply, error) {
	var out MissionReply
	pattern := "/api/v1/missions/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/GetMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) ListExecution(ctx context.Context, in *ExecutionsRequest, opts ...http.CallOption) (*ExecutionsReply, error) {
	var out ExecutionsReply
	pattern := "/api/v1/missions/{id}/executions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/ListExecution"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) ListMission(ctx context.Context, in *ListRequest, opts ...http.CallOption) (*ListReply, error) {
	var out ListReply
	pattern := "/api/v1/missions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/ListMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) StatsMission(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*StatsReply, error) {
	var out StatsReply
	pattern := "/api/v1/missions/statistics/status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/StatsMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) StatusMission(ctx context.Context, in *StatusRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/internal/v1/missions/{mid}/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/StatusMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) SwitchMission(ctx context.Context, in *SwitchRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/missions/{id}/switch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/SwitchMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *MissionHTTPClientImpl) UpdateMission(ctx context.Context, in *MissionItem, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/missions/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.missions.v1.Mission/UpdateMission"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
