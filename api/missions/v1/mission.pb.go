// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/missions/v1/mission.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list"
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{0}
}

type CommonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CommonRequest) Reset() {
	*x = CommonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRequest) ProtoMessage() {}

func (x *CommonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRequest.ProtoReflect.Descriptor instead.
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{1}
}

func (x *CommonRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type CommonReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *CommonReplyCommonData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CommonReply) Reset() {
	*x = CommonReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReply) ProtoMessage() {}

func (x *CommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReply.ProtoReflect.Descriptor instead.
func (*CommonReply) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{2}
}

func (x *CommonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommonReply) GetData() *CommonReplyCommonData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Unscoped   bool  `protobuf:"varint,2,opt,name=unscoped,proto3" json:"unscoped,omitempty"`
	WithDevice bool  `protobuf:"varint,3,opt,name=withDevice,proto3" json:"withDevice,omitempty"`
}

func (x *GetRequest) Reset() {
	*x = GetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequest) ProtoMessage() {}

func (x *GetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequest.ProtoReflect.Descriptor instead.
func (*GetRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{3}
}

func (x *GetRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetRequest) GetUnscoped() bool {
	if x != nil {
		return x.Unscoped
	}
	return false
}

func (x *GetRequest) GetWithDevice() bool {
	if x != nil {
		return x.WithDevice
	}
	return false
}

type BatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Ids    []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *BatchRequest) Reset() {
	*x = BatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRequest) ProtoMessage() {}

func (x *BatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRequest.ProtoReflect.Descriptor instead.
func (*BatchRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{4}
}

func (x *BatchRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BatchRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type BatchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *BatchReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *BatchReply) Reset() {
	*x = BatchReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchReply) ProtoMessage() {}

func (x *BatchReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchReply.ProtoReflect.Descriptor instead.
func (*BatchReply) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{5}
}

func (x *BatchReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BatchReply) GetData() *BatchReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AlarmsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime int64 `protobuf:"varint,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   int64 `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *AlarmsRequest) Reset() {
	*x = AlarmsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmsRequest) ProtoMessage() {}

func (x *AlarmsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmsRequest.ProtoReflect.Descriptor instead.
func (*AlarmsRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{6}
}

func (x *AlarmsRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *AlarmsRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type AlarmsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *AlarmsReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AlarmsReply) Reset() {
	*x = AlarmsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmsReply) ProtoMessage() {}

func (x *AlarmsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmsReply.ProtoReflect.Descriptor instead.
func (*AlarmsReply) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{7}
}

func (x *AlarmsReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AlarmsReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AlarmsReply) GetData() *AlarmsReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Enabled bool  `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SwitchRequest) Reset() {
	*x = SwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchRequest) ProtoMessage() {}

func (x *SwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchRequest.ProtoReflect.Descriptor instead.
func (*SwitchRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{8}
}

func (x *SwitchRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwitchRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type AlgConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      int32  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Interval  int32  `protobuf:"varint,3,opt,name=interval,proto3" json:"interval,omitempty"`
	LensType  int32  `protobuf:"varint,4,opt,name=lensType,proto3" json:"lensType,omitempty"`
	Threshold int32  `protobuf:"varint,5,opt,name=threshold,proto3" json:"threshold,omitempty"`
}

func (x *AlgConfig) Reset() {
	*x = AlgConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlgConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlgConfig) ProtoMessage() {}

func (x *AlgConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlgConfig.ProtoReflect.Descriptor instead.
func (*AlgConfig) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{9}
}

func (x *AlgConfig) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AlgConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AlgConfig) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *AlgConfig) GetLensType() int32 {
	if x != nil {
		return x.LensType
	}
	return 0
}

func (x *AlgConfig) GetThreshold() int32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

type NoticeRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time        int32                `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`
	Multilevel  int32                `protobuf:"varint,2,opt,name=multilevel,proto3" json:"multilevel,omitempty"`
	NoticeTypes []string             `protobuf:"bytes,3,rep,name=noticeTypes,proto3" json:"noticeTypes,omitempty"`
	Conditions  []string             `protobuf:"bytes,4,rep,name=conditions,proto3" json:"conditions,omitempty"`
	Contacts    []*NoticeRuleContact `protobuf:"bytes,5,rep,name=contacts,proto3" json:"contacts,omitempty"`
}

func (x *NoticeRule) Reset() {
	*x = NoticeRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeRule) ProtoMessage() {}

func (x *NoticeRule) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeRule.ProtoReflect.Descriptor instead.
func (*NoticeRule) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{10}
}

func (x *NoticeRule) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *NoticeRule) GetMultilevel() int32 {
	if x != nil {
		return x.Multilevel
	}
	return 0
}

func (x *NoticeRule) GetNoticeTypes() []string {
	if x != nil {
		return x.NoticeTypes
	}
	return nil
}

func (x *NoticeRule) GetConditions() []string {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *NoticeRule) GetContacts() []*NoticeRuleContact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

type ExecuteTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Moment  int32   `protobuf:"varint,1,opt,name=moment,proto3" json:"moment,omitempty"`
	Rundays []int32 `protobuf:"varint,2,rep,packed,name=rundays,proto3" json:"rundays,omitempty"`
}

func (x *ExecuteTime) Reset() {
	*x = ExecuteTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteTime) ProtoMessage() {}

func (x *ExecuteTime) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteTime.ProtoReflect.Descriptor instead.
func (*ExecuteTime) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{11}
}

func (x *ExecuteTime) GetMoment() int32 {
	if x != nil {
		return x.Moment
	}
	return 0
}

func (x *ExecuteTime) GetRundays() []int32 {
	if x != nil {
		return x.Rundays
	}
	return nil
}

type MissionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime  float64                 `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime  float64                 `protobuf:"fixed64,3,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	Name         string                  `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type         int32                   `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	Status       int32                   `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	TenantId     int64                   `protobuf:"varint,7,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	MerchantId   int64                   `protobuf:"varint,8,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	AvatarId     int64                   `protobuf:"varint,9,opt,name=avatarId,proto3" json:"avatarId,omitempty"`
	DeviceId     int64                   `protobuf:"varint,10,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	AirlineId    int64                   `protobuf:"varint,11,opt,name=airlineId,proto3" json:"airlineId,omitempty"`
	StartTime    float64                 `protobuf:"fixed64,12,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime      float64                 `protobuf:"fixed64,13,opt,name=endTime,proto3" json:"endTime,omitempty"`
	ExecuteCount int32                   `protobuf:"varint,14,opt,name=executeCount,proto3" json:"executeCount,omitempty"`
	AlarmCount   int32                   `protobuf:"varint,15,opt,name=alarmCount,proto3" json:"alarmCount,omitempty"`
	Description  string                  `protobuf:"bytes,16,opt,name=description,proto3" json:"description,omitempty"`
	Avatar       *MissionItemAvatarInfo  `protobuf:"bytes,17,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Editor       *MissionItemAvatarInfo  `protobuf:"bytes,18,opt,name=editor,proto3" json:"editor,omitempty"`
	Device       *MissionItemDeviceInfo  `protobuf:"bytes,19,opt,name=device,proto3" json:"device,omitempty"`
	Airline      *MissionItemAirlineInfo `protobuf:"bytes,20,opt,name=airline,proto3" json:"airline,omitempty"`
	IsDeleted    bool                    `protobuf:"varint,21,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`
	EditedTime   float64                 `protobuf:"fixed64,22,opt,name=editedTime,proto3" json:"editedTime,omitempty"`
	AlgConfigs   []*AlgConfig            `protobuf:"bytes,23,rep,name=algConfigs,proto3" json:"algConfigs,omitempty"`
	NoticeRules  []*NoticeRule           `protobuf:"bytes,24,rep,name=noticeRules,proto3" json:"noticeRules,omitempty"`
	ExecuteTimes []*ExecuteTime          `protobuf:"bytes,25,rep,name=executeTimes,proto3" json:"executeTimes,omitempty"`
}

func (x *MissionItem) Reset() {
	*x = MissionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MissionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MissionItem) ProtoMessage() {}

func (x *MissionItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MissionItem.ProtoReflect.Descriptor instead.
func (*MissionItem) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{12}
}

func (x *MissionItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MissionItem) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *MissionItem) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *MissionItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MissionItem) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *MissionItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MissionItem) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *MissionItem) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *MissionItem) GetAvatarId() int64 {
	if x != nil {
		return x.AvatarId
	}
	return 0
}

func (x *MissionItem) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *MissionItem) GetAirlineId() int64 {
	if x != nil {
		return x.AirlineId
	}
	return 0
}

func (x *MissionItem) GetStartTime() float64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *MissionItem) GetEndTime() float64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *MissionItem) GetExecuteCount() int32 {
	if x != nil {
		return x.ExecuteCount
	}
	return 0
}

func (x *MissionItem) GetAlarmCount() int32 {
	if x != nil {
		return x.AlarmCount
	}
	return 0
}

func (x *MissionItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MissionItem) GetAvatar() *MissionItemAvatarInfo {
	if x != nil {
		return x.Avatar
	}
	return nil
}

func (x *MissionItem) GetEditor() *MissionItemAvatarInfo {
	if x != nil {
		return x.Editor
	}
	return nil
}

func (x *MissionItem) GetDevice() *MissionItemDeviceInfo {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *MissionItem) GetAirline() *MissionItemAirlineInfo {
	if x != nil {
		return x.Airline
	}
	return nil
}

func (x *MissionItem) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *MissionItem) GetEditedTime() float64 {
	if x != nil {
		return x.EditedTime
	}
	return 0
}

func (x *MissionItem) GetAlgConfigs() []*AlgConfig {
	if x != nil {
		return x.AlgConfigs
	}
	return nil
}

func (x *MissionItem) GetNoticeRules() []*NoticeRule {
	if x != nil {
		return x.NoticeRules
	}
	return nil
}

func (x *MissionItem) GetExecuteTimes() []*ExecuteTime {
	if x != nil {
		return x.ExecuteTimes
	}
	return nil
}

type MissionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *MissionItem `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *MissionReply) Reset() {
	*x = MissionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MissionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MissionReply) ProtoMessage() {}

func (x *MissionReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MissionReply.ProtoReflect.Descriptor instead.
func (*MissionReply) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{13}
}

func (x *MissionReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MissionReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *MissionReply) GetData() *MissionItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size      int32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	DeviceIds string  `protobuf:"bytes,3,opt,name=deviceIds,proto3" json:"deviceIds,omitempty"`
	Status    *int32  `protobuf:"varint,4,opt,name=status,proto3,oneof" json:"status,omitempty"`
	Search    *string `protobuf:"bytes,5,opt,name=search,proto3,oneof" json:"search,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{14}
}

func (x *ListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListRequest) GetDeviceIds() string {
	if x != nil {
		return x.DeviceIds
	}
	return ""
}

func (x *ListRequest) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *ListRequest) GetSearch() string {
	if x != nil && x.Search != nil {
		return *x.Search
	}
	return ""
}

type ListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ListReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListReply) Reset() {
	*x = ListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReply) ProtoMessage() {}

func (x *ListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReply.ProtoReflect.Descriptor instead.
func (*ListReply) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{15}
}

func (x *ListReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListReply) GetData() *ListReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type StatsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *StatsReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *StatsReply) Reset() {
	*x = StatsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsReply) ProtoMessage() {}

func (x *StatsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsReply.ProtoReflect.Descriptor instead.
func (*StatsReply) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{16}
}

func (x *StatsReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *StatsReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StatsReply) GetData() *StatsReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ExecutionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size int32 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Id   int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ExecutionsRequest) Reset() {
	*x = ExecutionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionsRequest) ProtoMessage() {}

func (x *ExecutionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionsRequest.ProtoReflect.Descriptor instead.
func (*ExecutionsRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{17}
}

func (x *ExecutionsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ExecutionsRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ExecutionsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ExecutionsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ExecutionsReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ExecutionsReply) Reset() {
	*x = ExecutionsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionsReply) ProtoMessage() {}

func (x *ExecutionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionsReply.ProtoReflect.Descriptor instead.
func (*ExecutionsReply) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{18}
}

func (x *ExecutionsReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ExecutionsReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ExecutionsReply) GetData() *ExecutionsReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type StatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mid         int64                 `protobuf:"varint,1,opt,name=mid,proto3" json:"mid,omitempty"`
	Id          string                `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	ClientId    string                `protobuf:"bytes,3,opt,name=clientId,proto3" json:"clientId,omitempty"`
	ExecuteTime int64                 `protobuf:"varint,4,opt,name=executeTime,proto3" json:"executeTime,omitempty"`
	Body        *StatusRequestPayload `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *StatusRequest) Reset() {
	*x = StatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusRequest) ProtoMessage() {}

func (x *StatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusRequest.ProtoReflect.Descriptor instead.
func (*StatusRequest) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{19}
}

func (x *StatusRequest) GetMid() int64 {
	if x != nil {
		return x.Mid
	}
	return 0
}

func (x *StatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StatusRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *StatusRequest) GetExecuteTime() int64 {
	if x != nil {
		return x.ExecuteTime
	}
	return 0
}

func (x *StatusRequest) GetBody() *StatusRequestPayload {
	if x != nil {
		return x.Body
	}
	return nil
}

type CommonReplyCommonData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CommonReplyCommonData) Reset() {
	*x = CommonReplyCommonData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReplyCommonData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReplyCommonData) ProtoMessage() {}

func (x *CommonReplyCommonData) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReplyCommonData.ProtoReflect.Descriptor instead.
func (*CommonReplyCommonData) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{2, 0}
}

func (x *CommonReplyCommonData) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type BatchReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessCount int32               `protobuf:"varint,1,opt,name=successCount,proto3" json:"successCount,omitempty"`
	FailureCount int32               `protobuf:"varint,2,opt,name=failureCount,proto3" json:"failureCount,omitempty"`
	List         []*BatchReplyDetail `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *BatchReplyListData) Reset() {
	*x = BatchReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchReplyListData) ProtoMessage() {}

func (x *BatchReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchReplyListData.ProtoReflect.Descriptor instead.
func (*BatchReplyListData) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{5, 0}
}

func (x *BatchReplyListData) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *BatchReplyListData) GetFailureCount() int32 {
	if x != nil {
		return x.FailureCount
	}
	return 0
}

func (x *BatchReplyListData) GetList() []*BatchReplyDetail {
	if x != nil {
		return x.List
	}
	return nil
}

type BatchReplyDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type   string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Reason string `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *BatchReplyDetail) Reset() {
	*x = BatchReplyDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchReplyDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchReplyDetail) ProtoMessage() {}

func (x *BatchReplyDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchReplyDetail.ProtoReflect.Descriptor instead.
func (*BatchReplyDetail) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{5, 1}
}

func (x *BatchReplyDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BatchReplyDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BatchReplyDetail) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BatchReplyDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *BatchReplyDetail) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type AlarmsReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*AlarmsReplyTaskCount `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AlarmsReplyListData) Reset() {
	*x = AlarmsReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmsReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmsReplyListData) ProtoMessage() {}

func (x *AlarmsReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmsReplyListData.ProtoReflect.Descriptor instead.
func (*AlarmsReplyListData) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{7, 0}
}

func (x *AlarmsReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AlarmsReplyListData) GetList() []*AlarmsReplyTaskCount {
	if x != nil {
		return x.List
	}
	return nil
}

type AlarmsReplyTaskCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int32  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *AlarmsReplyTaskCount) Reset() {
	*x = AlarmsReplyTaskCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmsReplyTaskCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmsReplyTaskCount) ProtoMessage() {}

func (x *AlarmsReplyTaskCount) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmsReplyTaskCount.ProtoReflect.Descriptor instead.
func (*AlarmsReplyTaskCount) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{7, 1}
}

func (x *AlarmsReplyTaskCount) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *AlarmsReplyTaskCount) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type NoticeRuleContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Contact string `protobuf:"bytes,2,opt,name=contact,proto3" json:"contact,omitempty"`
}

func (x *NoticeRuleContact) Reset() {
	*x = NoticeRuleContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeRuleContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeRuleContact) ProtoMessage() {}

func (x *NoticeRuleContact) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeRuleContact.ProtoReflect.Descriptor instead.
func (*NoticeRuleContact) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{10, 0}
}

func (x *NoticeRuleContact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NoticeRuleContact) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

type MissionItemAvatarInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Mobile   string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Nickname string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Avatar   string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
}

func (x *MissionItemAvatarInfo) Reset() {
	*x = MissionItemAvatarInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MissionItemAvatarInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MissionItemAvatarInfo) ProtoMessage() {}

func (x *MissionItemAvatarInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MissionItemAvatarInfo.ProtoReflect.Descriptor instead.
func (*MissionItemAvatarInfo) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{12, 0}
}

func (x *MissionItemAvatarInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MissionItemAvatarInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *MissionItemAvatarInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *MissionItemAvatarInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type MissionItemDeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sn   string `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *MissionItemDeviceInfo) Reset() {
	*x = MissionItemDeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MissionItemDeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MissionItemDeviceInfo) ProtoMessage() {}

func (x *MissionItemDeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MissionItemDeviceInfo.ProtoReflect.Descriptor instead.
func (*MissionItemDeviceInfo) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{12, 1}
}

func (x *MissionItemDeviceInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MissionItemDeviceInfo) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *MissionItemDeviceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MissionItemDeviceInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type MissionItemAirlineInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type            string  `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	EstimateMileage float32 `protobuf:"fixed32,4,opt,name=estimateMileage,proto3" json:"estimateMileage,omitempty"`
}

func (x *MissionItemAirlineInfo) Reset() {
	*x = MissionItemAirlineInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MissionItemAirlineInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MissionItemAirlineInfo) ProtoMessage() {}

func (x *MissionItemAirlineInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MissionItemAirlineInfo.ProtoReflect.Descriptor instead.
func (*MissionItemAirlineInfo) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{12, 2}
}

func (x *MissionItemAirlineInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MissionItemAirlineInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MissionItemAirlineInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *MissionItemAirlineInfo) GetEstimateMileage() float32 {
	if x != nil {
		return x.EstimateMileage
	}
	return 0
}

type ListReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32          `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32          `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*MissionItem `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListReplyListData) Reset() {
	*x = ListReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReplyListData) ProtoMessage() {}

func (x *ListReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReplyListData.ProtoReflect.Descriptor instead.
func (*ListReplyListData) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{15, 0}
}

func (x *ListReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListReplyListData) GetList() []*MissionItem {
	if x != nil {
		return x.List
	}
	return nil
}

type StatsReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*StatsReplyStatusCount `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *StatsReplyListData) Reset() {
	*x = StatsReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsReplyListData) ProtoMessage() {}

func (x *StatsReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsReplyListData.ProtoReflect.Descriptor instead.
func (*StatsReplyListData) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{16, 0}
}

func (x *StatsReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *StatsReplyListData) GetList() []*StatsReplyStatusCount {
	if x != nil {
		return x.List
	}
	return nil
}

type StatsReplyStatusCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count  int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StatsReplyStatusCount) Reset() {
	*x = StatsReplyStatusCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsReplyStatusCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsReplyStatusCount) ProtoMessage() {}

func (x *StatsReplyStatusCount) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsReplyStatusCount.ProtoReflect.Descriptor instead.
func (*StatsReplyStatusCount) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{16, 1}
}

func (x *StatsReplyStatusCount) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *StatsReplyStatusCount) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type ExecutionsReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32                       `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32                       `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32                       `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*ExecutionsReplyExecution `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ExecutionsReplyListData) Reset() {
	*x = ExecutionsReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionsReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionsReplyListData) ProtoMessage() {}

func (x *ExecutionsReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionsReplyListData.ProtoReflect.Descriptor instead.
func (*ExecutionsReplyListData) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{18, 0}
}

func (x *ExecutionsReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ExecutionsReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ExecutionsReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ExecutionsReplyListData) GetList() []*ExecutionsReplyExecution {
	if x != nil {
		return x.List
	}
	return nil
}

type ExecutionsReplyExecution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime float64 `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime float64 `protobuf:"fixed64,3,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	Status      string  `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Reason      string  `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	AlarmCount  int32   `protobuf:"varint,6,opt,name=alarmCount,proto3" json:"alarmCount,omitempty"`
	TenantId    int64   `protobuf:"varint,7,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	MerchantId  int64   `protobuf:"varint,8,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	MissionId   int64   `protobuf:"varint,9,opt,name=missionId,proto3" json:"missionId,omitempty"`
	VoyageId    int64   `protobuf:"varint,10,opt,name=voyageId,proto3" json:"voyageId,omitempty"`
}

func (x *ExecutionsReplyExecution) Reset() {
	*x = ExecutionsReplyExecution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionsReplyExecution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionsReplyExecution) ProtoMessage() {}

func (x *ExecutionsReplyExecution) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionsReplyExecution.ProtoReflect.Descriptor instead.
func (*ExecutionsReplyExecution) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{18, 1}
}

func (x *ExecutionsReplyExecution) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExecutionsReplyExecution) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *ExecutionsReplyExecution) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *ExecutionsReplyExecution) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ExecutionsReplyExecution) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ExecutionsReplyExecution) GetAlarmCount() int32 {
	if x != nil {
		return x.AlarmCount
	}
	return 0
}

func (x *ExecutionsReplyExecution) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *ExecutionsReplyExecution) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *ExecutionsReplyExecution) GetMissionId() int64 {
	if x != nil {
		return x.MissionId
	}
	return 0
}

func (x *ExecutionsReplyExecution) GetVoyageId() int64 {
	if x != nil {
		return x.VoyageId
	}
	return 0
}

type StatusRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action string `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *StatusRequestPayload) Reset() {
	*x = StatusRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_missions_v1_mission_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusRequestPayload) ProtoMessage() {}

func (x *StatusRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_missions_v1_mission_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusRequestPayload.ProtoReflect.Descriptor instead.
func (*StatusRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_missions_v1_mission_proto_rawDescGZIP(), []int{19, 0}
}

func (x *StatusRequestPayload) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

var File_api_missions_v1_mission_proto protoreflect.FileDescriptor

var file_api_missions_v1_mission_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x6c, 0x69, 0x73, 0x74, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2c, 0x0a, 0x0d, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x9e, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x24, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x65, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x6e, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x22, 0x38, 0x0a, 0x0c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xf3, 0x02, 0x0a, 0x0a, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0x8a, 0x01, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22,
	0x0a, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x70,
	0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x22, 0x47, 0x0a, 0x0d, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x8b, 0x02, 0x0a, 0x0b, 0x41, 0x6c,
	0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x5c, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x1a, 0x35, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x0d, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22,
	0x89, 0x01, 0x0a, 0x09, 0x41, 0x6c, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x6e, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x6c, 0x65, 0x6e, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0xfc, 0x01, 0x0a, 0x0a,
	0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x20,
	0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x3f, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x73, 0x1a, 0x37, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x22, 0x3f, 0x0a, 0x0b, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x73, 0x22, 0x84, 0x0a, 0x0a, 0x0b,
	0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3f, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x3f, 0x0a, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f,
	0x72, 0x12, 0x3f, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x2e, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61,
	0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x64, 0x69, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x65, 0x64, 0x69, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x61, 0x6c, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x67, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x61, 0x6c, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x12, 0x3d, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12,
	0x40, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18,
	0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x1a, 0x68, 0x0a, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x1a, 0x54, 0x0a, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x1a, 0x6f, 0x0a, 0x0b, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x22, 0x6e, 0x0a, 0x0c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0xe6, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69,
	0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x30,
	0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13,
	0x9a, 0x7c, 0x10, 0x9a, 0x43, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0xa2, 0x43, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x48, 0x01, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x88, 0x01, 0x01,
	0x3a, 0x03, 0x88, 0x43, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x22, 0xee, 0x01, 0x0a, 0x09,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x7a, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x30, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x90, 0x02, 0x0a,
	0x0a, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x5d, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x1a, 0x3b, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x79, 0x0a, 0x11, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c,
	0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x3a, 0x03, 0x88, 0x43, 0x01, 0x22, 0xb1, 0x04, 0x0a, 0x0f, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3d, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x88, 0x01, 0x0a, 0x08,
	0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0xa5, 0x02, 0x0a, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x6c, 0x61, 0x72,
	0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x6c,
	0x61, 0x72, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0xdb,
	0x01, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a,
	0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x21, 0x0a, 0x07, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xfc, 0x09, 0x0a,
	0x07, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x68, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22, 0x10, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x3a,
	0x01, 0x2a, 0x12, 0x61, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x67, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x6d,
	0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x1a, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1a, 0x1a, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x3a, 0x01, 0x2a, 0x12, 0x74, 0x0a,
	0x0c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x22, 0x1a, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x3a, 0x01, 0x2a, 0x12, 0x76, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x4d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x79, 0x0a, 0x0d, 0x41,
	0x6c, 0x61, 0x72, 0x6d, 0x73, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x6c, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x6c, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x24, 0x12, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f,
	0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x12, 0x76, 0x0a, 0x0d, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x1a, 0x1c, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x3a, 0x01, 0x2a, 0x12, 0x6c,
	0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x17, 0x2a, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x7f, 0x0a, 0x0d,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x2f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x7c, 0x0a,
	0x0d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2d, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6d, 0x69, 0x64,
	0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x3a, 0x01, 0x2a, 0x42, 0x44, 0x0a, 0x0f, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_missions_v1_mission_proto_rawDescOnce sync.Once
	file_api_missions_v1_mission_proto_rawDescData = file_api_missions_v1_mission_proto_rawDesc
)

func file_api_missions_v1_mission_proto_rawDescGZIP() []byte {
	file_api_missions_v1_mission_proto_rawDescOnce.Do(func() {
		file_api_missions_v1_mission_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_missions_v1_mission_proto_rawDescData)
	})
	return file_api_missions_v1_mission_proto_rawDescData
}

var file_api_missions_v1_mission_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_api_missions_v1_mission_proto_goTypes = []interface{}{
	(*EmptyRequest)(nil),             // 0: api.missions.v1.EmptyRequest
	(*CommonRequest)(nil),            // 1: api.missions.v1.CommonRequest
	(*CommonReply)(nil),              // 2: api.missions.v1.CommonReply
	(*GetRequest)(nil),               // 3: api.missions.v1.GetRequest
	(*BatchRequest)(nil),             // 4: api.missions.v1.BatchRequest
	(*BatchReply)(nil),               // 5: api.missions.v1.BatchReply
	(*AlarmsRequest)(nil),            // 6: api.missions.v1.AlarmsRequest
	(*AlarmsReply)(nil),              // 7: api.missions.v1.AlarmsReply
	(*SwitchRequest)(nil),            // 8: api.missions.v1.SwitchRequest
	(*AlgConfig)(nil),                // 9: api.missions.v1.AlgConfig
	(*NoticeRule)(nil),               // 10: api.missions.v1.NoticeRule
	(*ExecuteTime)(nil),              // 11: api.missions.v1.ExecuteTime
	(*MissionItem)(nil),              // 12: api.missions.v1.MissionItem
	(*MissionReply)(nil),             // 13: api.missions.v1.MissionReply
	(*ListRequest)(nil),              // 14: api.missions.v1.ListRequest
	(*ListReply)(nil),                // 15: api.missions.v1.ListReply
	(*StatsReply)(nil),               // 16: api.missions.v1.StatsReply
	(*ExecutionsRequest)(nil),        // 17: api.missions.v1.ExecutionsRequest
	(*ExecutionsReply)(nil),          // 18: api.missions.v1.ExecutionsReply
	(*StatusRequest)(nil),            // 19: api.missions.v1.StatusRequest
	(*CommonReplyCommonData)(nil),    // 20: api.missions.v1.CommonReply.commonData
	(*BatchReplyListData)(nil),       // 21: api.missions.v1.BatchReply.listData
	(*BatchReplyDetail)(nil),         // 22: api.missions.v1.BatchReply.detail
	(*AlarmsReplyListData)(nil),      // 23: api.missions.v1.AlarmsReply.listData
	(*AlarmsReplyTaskCount)(nil),     // 24: api.missions.v1.AlarmsReply.taskCount
	(*NoticeRuleContact)(nil),        // 25: api.missions.v1.NoticeRule.contact
	(*MissionItemAvatarInfo)(nil),    // 26: api.missions.v1.MissionItem.avatarInfo
	(*MissionItemDeviceInfo)(nil),    // 27: api.missions.v1.MissionItem.deviceInfo
	(*MissionItemAirlineInfo)(nil),   // 28: api.missions.v1.MissionItem.airlineInfo
	(*ListReplyListData)(nil),        // 29: api.missions.v1.ListReply.listData
	(*StatsReplyListData)(nil),       // 30: api.missions.v1.StatsReply.listData
	(*StatsReplyStatusCount)(nil),    // 31: api.missions.v1.StatsReply.statusCount
	(*ExecutionsReplyListData)(nil),  // 32: api.missions.v1.ExecutionsReply.listData
	(*ExecutionsReplyExecution)(nil), // 33: api.missions.v1.ExecutionsReply.execution
	(*StatusRequestPayload)(nil),     // 34: api.missions.v1.StatusRequest.payload
}
var file_api_missions_v1_mission_proto_depIdxs = []int32{
	20, // 0: api.missions.v1.CommonReply.data:type_name -> api.missions.v1.CommonReply.commonData
	21, // 1: api.missions.v1.BatchReply.data:type_name -> api.missions.v1.BatchReply.listData
	23, // 2: api.missions.v1.AlarmsReply.data:type_name -> api.missions.v1.AlarmsReply.listData
	25, // 3: api.missions.v1.NoticeRule.contacts:type_name -> api.missions.v1.NoticeRule.contact
	26, // 4: api.missions.v1.MissionItem.avatar:type_name -> api.missions.v1.MissionItem.avatarInfo
	26, // 5: api.missions.v1.MissionItem.editor:type_name -> api.missions.v1.MissionItem.avatarInfo
	27, // 6: api.missions.v1.MissionItem.device:type_name -> api.missions.v1.MissionItem.deviceInfo
	28, // 7: api.missions.v1.MissionItem.airline:type_name -> api.missions.v1.MissionItem.airlineInfo
	9,  // 8: api.missions.v1.MissionItem.algConfigs:type_name -> api.missions.v1.AlgConfig
	10, // 9: api.missions.v1.MissionItem.noticeRules:type_name -> api.missions.v1.NoticeRule
	11, // 10: api.missions.v1.MissionItem.executeTimes:type_name -> api.missions.v1.ExecuteTime
	12, // 11: api.missions.v1.MissionReply.data:type_name -> api.missions.v1.MissionItem
	29, // 12: api.missions.v1.ListReply.data:type_name -> api.missions.v1.ListReply.listData
	30, // 13: api.missions.v1.StatsReply.data:type_name -> api.missions.v1.StatsReply.listData
	32, // 14: api.missions.v1.ExecutionsReply.data:type_name -> api.missions.v1.ExecutionsReply.listData
	34, // 15: api.missions.v1.StatusRequest.body:type_name -> api.missions.v1.StatusRequest.payload
	22, // 16: api.missions.v1.BatchReply.listData.list:type_name -> api.missions.v1.BatchReply.detail
	24, // 17: api.missions.v1.AlarmsReply.listData.list:type_name -> api.missions.v1.AlarmsReply.taskCount
	12, // 18: api.missions.v1.ListReply.listData.list:type_name -> api.missions.v1.MissionItem
	31, // 19: api.missions.v1.StatsReply.listData.list:type_name -> api.missions.v1.StatsReply.statusCount
	33, // 20: api.missions.v1.ExecutionsReply.listData.list:type_name -> api.missions.v1.ExecutionsReply.execution
	12, // 21: api.missions.v1.Mission.CreateMission:input_type -> api.missions.v1.MissionItem
	14, // 22: api.missions.v1.Mission.ListMission:input_type -> api.missions.v1.ListRequest
	3,  // 23: api.missions.v1.Mission.GetMission:input_type -> api.missions.v1.GetRequest
	12, // 24: api.missions.v1.Mission.UpdateMission:input_type -> api.missions.v1.MissionItem
	4,  // 25: api.missions.v1.Mission.BatchMission:input_type -> api.missions.v1.BatchRequest
	0,  // 26: api.missions.v1.Mission.StatsMission:input_type -> api.missions.v1.EmptyRequest
	6,  // 27: api.missions.v1.Mission.AlarmsMission:input_type -> api.missions.v1.AlarmsRequest
	8,  // 28: api.missions.v1.Mission.SwitchMission:input_type -> api.missions.v1.SwitchRequest
	1,  // 29: api.missions.v1.Mission.DeleteMission:input_type -> api.missions.v1.CommonRequest
	17, // 30: api.missions.v1.Mission.ListExecution:input_type -> api.missions.v1.ExecutionsRequest
	19, // 31: api.missions.v1.Mission.StatusMission:input_type -> api.missions.v1.StatusRequest
	2,  // 32: api.missions.v1.Mission.CreateMission:output_type -> api.missions.v1.CommonReply
	15, // 33: api.missions.v1.Mission.ListMission:output_type -> api.missions.v1.ListReply
	13, // 34: api.missions.v1.Mission.GetMission:output_type -> api.missions.v1.MissionReply
	2,  // 35: api.missions.v1.Mission.UpdateMission:output_type -> api.missions.v1.CommonReply
	5,  // 36: api.missions.v1.Mission.BatchMission:output_type -> api.missions.v1.BatchReply
	16, // 37: api.missions.v1.Mission.StatsMission:output_type -> api.missions.v1.StatsReply
	7,  // 38: api.missions.v1.Mission.AlarmsMission:output_type -> api.missions.v1.AlarmsReply
	2,  // 39: api.missions.v1.Mission.SwitchMission:output_type -> api.missions.v1.CommonReply
	2,  // 40: api.missions.v1.Mission.DeleteMission:output_type -> api.missions.v1.CommonReply
	18, // 41: api.missions.v1.Mission.ListExecution:output_type -> api.missions.v1.ExecutionsReply
	2,  // 42: api.missions.v1.Mission.StatusMission:output_type -> api.missions.v1.CommonReply
	32, // [32:43] is the sub-list for method output_type
	21, // [21:32] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_api_missions_v1_mission_proto_init() }
func file_api_missions_v1_mission_proto_init() {
	if File_api_missions_v1_mission_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_missions_v1_mission_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlgConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MissionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MissionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReplyCommonData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchReplyDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmsReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmsReplyTaskCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeRuleContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MissionItemAvatarInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MissionItemDeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MissionItemAirlineInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatsReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatsReplyStatusCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionsReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionsReplyExecution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_missions_v1_mission_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_missions_v1_mission_proto_msgTypes[14].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_missions_v1_mission_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_missions_v1_mission_proto_goTypes,
		DependencyIndexes: file_api_missions_v1_mission_proto_depIdxs,
		MessageInfos:      file_api_missions_v1_mission_proto_msgTypes,
	}.Build()
	File_api_missions_v1_mission_proto = out.File
	file_api_missions_v1_mission_proto_rawDesc = nil
	file_api_missions_v1_mission_proto_goTypes = nil
	file_api_missions_v1_mission_proto_depIdxs = nil
}
