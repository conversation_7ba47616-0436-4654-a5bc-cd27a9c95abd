syntax = "proto3";

package api.missions.v1;
import "google/api/annotations.proto";
import "validator/validator.proto";
import "list/list.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/missions/v1;v1";
option java_multiple_files = true;
option java_package = "api.missions.v1";

service Mission {
	rpc CreateMission (MissionItem) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/missions"
			body: "*"
    };
  };
	rpc ListMission (ListRequest) returns (ListReply) {
    option (google.api.http) = {
      get: "/api/v1/missions"
    };
  };
	rpc GetMission (GetRequest) returns (MissionReply) {
    option (google.api.http) = {
      get: "/api/v1/missions/{id}"
    };
  };
	rpc UpdateMission (MissionItem) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/missions/{id}"
      body: "*"
    };
  };
	rpc BatchMission (BatchRequest) returns (BatchReply) {
    option (google.api.http) = {
      put: "/api/v1/missions/batch/status"
      body: "*"
    };
  };
	rpc StatsMission (EmptyRequest) returns (StatsReply) {
    option (google.api.http) = {
      get: "/api/v1/missions/statistics/status"
    };
  };
	rpc AlarmsMission (AlarmsRequest) returns (AlarmsReply) {
    option (google.api.http) = {
      get: "/api/v1/missions/statistics/alarms"
    };
  };
  rpc SwitchMission (SwitchRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/missions/{id}/switch"
      body: "*"
    };
  };
	rpc DeleteMission (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      delete: "/api/v1/missions/{id}"
    };
  };
  rpc ListExecution (ExecutionsRequest) returns (ExecutionsReply) {
    option (google.api.http) = {
      get: "/api/v1/missions/{id}/executions"
    };
  };
  rpc StatusMission (StatusRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/internal/v1/missions/{mid}/status"
      body: "*"
    };
  };
}

message EmptyRequest {}

message CommonRequest {
  int64 id = 1 [(validator.rules) = "required"];
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message GetRequest {
  int64 id = 1 [(validator.rules) = "required"];
  bool unscoped = 2;
  bool withDevice = 3;
}

message BatchRequest {
  int32 status = 1;
  repeated int64 ids = 2;
}

message BatchReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 successCount = 1;
    int32 failureCount = 2;
    repeated detail list = 3;
  }

  message detail {
    int64 id = 1;
    string name = 2;
    string type = 3;
    string status = 4;
    string reason = 5;
  }
}

message AlarmsRequest {
  int64 startTime = 1;
  int64 endTime = 2;
}

message AlarmsReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 total = 1;
    repeated taskCount list = 2;
  }

  message taskCount {
    int32 count = 1;
    string name = 2;
  }
}

message SwitchRequest {
  int64 id = 1 [(validator.rules) = "required"];
  bool enabled = 2;
}

message AlgConfig {
  int32 type = 1;
	string name = 2;
  int32 interval = 3;
  int32 lensType = 4;
	int32 threshold = 5;
}

message NoticeRule {
	int32 time = 1;
	int32 multilevel = 2;
	repeated string noticeTypes = 3;
	repeated string conditions = 4;
	repeated contact contacts = 5;

	message contact {
		string name = 1;
		string contact = 2;
	}
}

message ExecuteTime {
	int32 moment = 1;
	repeated int32 rundays = 2;
}

message MissionItem {
  int64 id = 1;
  double createdTime = 2;
  double updatedTime = 3;
  string name = 4;
  int32 type = 5;
  int32 status = 6;
	int64 tenantId = 7;
  int64 merchantId = 8;
  int64 avatarId = 9;
  int64 deviceId = 10;
  int64 airlineId = 11;
  double startTime = 12;
  double endTime = 13;
  int32 executeCount = 14;
  int32 alarmCount = 15;
  string description = 16;
  avatarInfo avatar = 17;
  avatarInfo editor = 18;
  deviceInfo device = 19;
  airlineInfo airline = 20;
  bool isDeleted = 21;
  double editedTime = 22;
  repeated AlgConfig algConfigs = 23;
  repeated NoticeRule noticeRules = 24;
  repeated ExecuteTime executeTimes = 25;

  message avatarInfo {
    int64 id = 1;
    string mobile = 2;
    string nickname = 3;
    string avatar = 4;
  }
  message deviceInfo {
    int64 id = 1;
		string sn = 2;
		string name = 3;
		string type = 4;
  }
  message airlineInfo {
    int64 id = 1;
    string name = 2;
    string type = 3;
    float estimateMileage = 4;
  }
}

message MissionReply {
  int32 code = 1;
  string message = 2;
  MissionItem data = 3;
}

message ListRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  string deviceIds = 3;
  optional int32 status = 4;
  optional string search = 5 [(list.filter_options)={filter_name: "name", operator:"search" }];
}

message ListReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated MissionItem list = 4;
  }
}

message StatsReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 total = 1;
    repeated statusCount list = 2;
  }

  message statusCount {
    int32 count = 1;
    int32 status = 2;
  }
}

message ExecutionsRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  int64 id = 3;
}

message ExecutionsReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated execution list = 4;
  }

  message execution {
    int64 id = 1;
    double createdTime = 2;
    double updatedTime = 3;
    string status = 4;
    string reason = 5;
    int32 alarmCount = 6;
    int64 tenantId = 7;
    int64 merchantId = 8;
    int64 missionId = 9;
    int64 voyageId = 10;
  }
}

message StatusRequest {
  int64 mid = 1 [(validator.rules) = "required"];
  string id = 2;
  string clientId = 3;
  int64 executeTime = 4;
  payload body = 5;

  message payload {
    string action = 1;
  }
}
