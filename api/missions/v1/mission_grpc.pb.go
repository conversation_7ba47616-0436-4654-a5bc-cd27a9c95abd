// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/missions/v1/mission.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MissionClient is the client API for Mission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MissionClient interface {
	CreateMission(ctx context.Context, in *MissionItem, opts ...grpc.CallOption) (*CommonReply, error)
	ListMission(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error)
	GetMission(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*MissionReply, error)
	UpdateMission(ctx context.Context, in *MissionItem, opts ...grpc.CallOption) (*CommonReply, error)
	BatchMission(ctx context.Context, in *BatchRequest, opts ...grpc.CallOption) (*BatchReply, error)
	StatsMission(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*StatsReply, error)
	AlarmsMission(ctx context.Context, in *AlarmsRequest, opts ...grpc.CallOption) (*AlarmsReply, error)
	SwitchMission(ctx context.Context, in *SwitchRequest, opts ...grpc.CallOption) (*CommonReply, error)
	DeleteMission(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	ListExecution(ctx context.Context, in *ExecutionsRequest, opts ...grpc.CallOption) (*ExecutionsReply, error)
	StatusMission(ctx context.Context, in *StatusRequest, opts ...grpc.CallOption) (*CommonReply, error)
}

type missionClient struct {
	cc grpc.ClientConnInterface
}

func NewMissionClient(cc grpc.ClientConnInterface) MissionClient {
	return &missionClient{cc}
}

func (c *missionClient) CreateMission(ctx context.Context, in *MissionItem, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/CreateMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) ListMission(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error) {
	out := new(ListReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/ListMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) GetMission(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*MissionReply, error) {
	out := new(MissionReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/GetMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) UpdateMission(ctx context.Context, in *MissionItem, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/UpdateMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) BatchMission(ctx context.Context, in *BatchRequest, opts ...grpc.CallOption) (*BatchReply, error) {
	out := new(BatchReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/BatchMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) StatsMission(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*StatsReply, error) {
	out := new(StatsReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/StatsMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) AlarmsMission(ctx context.Context, in *AlarmsRequest, opts ...grpc.CallOption) (*AlarmsReply, error) {
	out := new(AlarmsReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/AlarmsMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) SwitchMission(ctx context.Context, in *SwitchRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/SwitchMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) DeleteMission(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/DeleteMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) ListExecution(ctx context.Context, in *ExecutionsRequest, opts ...grpc.CallOption) (*ExecutionsReply, error) {
	out := new(ExecutionsReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/ListExecution", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) StatusMission(ctx context.Context, in *StatusRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.missions.v1.Mission/StatusMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MissionServer is the server API for Mission service.
// All implementations must embed UnimplementedMissionServer
// for forward compatibility
type MissionServer interface {
	CreateMission(context.Context, *MissionItem) (*CommonReply, error)
	ListMission(context.Context, *ListRequest) (*ListReply, error)
	GetMission(context.Context, *GetRequest) (*MissionReply, error)
	UpdateMission(context.Context, *MissionItem) (*CommonReply, error)
	BatchMission(context.Context, *BatchRequest) (*BatchReply, error)
	StatsMission(context.Context, *EmptyRequest) (*StatsReply, error)
	AlarmsMission(context.Context, *AlarmsRequest) (*AlarmsReply, error)
	SwitchMission(context.Context, *SwitchRequest) (*CommonReply, error)
	DeleteMission(context.Context, *CommonRequest) (*CommonReply, error)
	ListExecution(context.Context, *ExecutionsRequest) (*ExecutionsReply, error)
	StatusMission(context.Context, *StatusRequest) (*CommonReply, error)
	mustEmbedUnimplementedMissionServer()
}

// UnimplementedMissionServer must be embedded to have forward compatible implementations.
type UnimplementedMissionServer struct {
}

func (UnimplementedMissionServer) CreateMission(context.Context, *MissionItem) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMission not implemented")
}
func (UnimplementedMissionServer) ListMission(context.Context, *ListRequest) (*ListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMission not implemented")
}
func (UnimplementedMissionServer) GetMission(context.Context, *GetRequest) (*MissionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMission not implemented")
}
func (UnimplementedMissionServer) UpdateMission(context.Context, *MissionItem) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMission not implemented")
}
func (UnimplementedMissionServer) BatchMission(context.Context, *BatchRequest) (*BatchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchMission not implemented")
}
func (UnimplementedMissionServer) StatsMission(context.Context, *EmptyRequest) (*StatsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StatsMission not implemented")
}
func (UnimplementedMissionServer) AlarmsMission(context.Context, *AlarmsRequest) (*AlarmsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AlarmsMission not implemented")
}
func (UnimplementedMissionServer) SwitchMission(context.Context, *SwitchRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SwitchMission not implemented")
}
func (UnimplementedMissionServer) DeleteMission(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMission not implemented")
}
func (UnimplementedMissionServer) ListExecution(context.Context, *ExecutionsRequest) (*ExecutionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExecution not implemented")
}
func (UnimplementedMissionServer) StatusMission(context.Context, *StatusRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StatusMission not implemented")
}
func (UnimplementedMissionServer) mustEmbedUnimplementedMissionServer() {}

// UnsafeMissionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MissionServer will
// result in compilation errors.
type UnsafeMissionServer interface {
	mustEmbedUnimplementedMissionServer()
}

func RegisterMissionServer(s grpc.ServiceRegistrar, srv MissionServer) {
	s.RegisterService(&Mission_ServiceDesc, srv)
}

func _Mission_CreateMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MissionItem)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).CreateMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/CreateMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).CreateMission(ctx, req.(*MissionItem))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_ListMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).ListMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/ListMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).ListMission(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_GetMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).GetMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/GetMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).GetMission(ctx, req.(*GetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_UpdateMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MissionItem)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).UpdateMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/UpdateMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).UpdateMission(ctx, req.(*MissionItem))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_BatchMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).BatchMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/BatchMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).BatchMission(ctx, req.(*BatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_StatsMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).StatsMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/StatsMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).StatsMission(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_AlarmsMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AlarmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).AlarmsMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/AlarmsMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).AlarmsMission(ctx, req.(*AlarmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_SwitchMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).SwitchMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/SwitchMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).SwitchMission(ctx, req.(*SwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_DeleteMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).DeleteMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/DeleteMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).DeleteMission(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_ListExecution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecutionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).ListExecution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/ListExecution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).ListExecution(ctx, req.(*ExecutionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_StatusMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).StatusMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.missions.v1.Mission/StatusMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).StatusMission(ctx, req.(*StatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Mission_ServiceDesc is the grpc.ServiceDesc for Mission service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Mission_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.missions.v1.Mission",
	HandlerType: (*MissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMission",
			Handler:    _Mission_CreateMission_Handler,
		},
		{
			MethodName: "ListMission",
			Handler:    _Mission_ListMission_Handler,
		},
		{
			MethodName: "GetMission",
			Handler:    _Mission_GetMission_Handler,
		},
		{
			MethodName: "UpdateMission",
			Handler:    _Mission_UpdateMission_Handler,
		},
		{
			MethodName: "BatchMission",
			Handler:    _Mission_BatchMission_Handler,
		},
		{
			MethodName: "StatsMission",
			Handler:    _Mission_StatsMission_Handler,
		},
		{
			MethodName: "AlarmsMission",
			Handler:    _Mission_AlarmsMission_Handler,
		},
		{
			MethodName: "SwitchMission",
			Handler:    _Mission_SwitchMission_Handler,
		},
		{
			MethodName: "DeleteMission",
			Handler:    _Mission_DeleteMission_Handler,
		},
		{
			MethodName: "ListExecution",
			Handler:    _Mission_ListExecution_Handler,
		},
		{
			MethodName: "StatusMission",
			Handler:    _Mission_StatusMission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/missions/v1/mission.proto",
}
