{"swagger": "2.0", "info": {"title": "api/missions/v1/mission.proto", "version": "version not set"}, "tags": [{"name": "Mission"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/missions": {"get": {"operationId": "Mission_ListMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apimissionsv1ListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "deviceIds", "in": "query", "required": false, "type": "string"}, {"name": "status", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "search", "in": "query", "required": false, "type": "string"}], "tags": ["Mission"]}, "post": {"operationId": "Mission_CreateMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apimissionsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1MissionItem"}}], "tags": ["Mission"]}}, "/api/v1/missions/batch/status": {"put": {"operationId": "Mission_BatchMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1BatchReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1BatchRequest"}}], "tags": ["Mission"]}}, "/api/v1/missions/statistics/alarms": {"get": {"operationId": "Mission_AlarmsMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1AlarmsReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "startTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "endTime", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["Mission"]}}, "/api/v1/missions/statistics/status": {"get": {"operationId": "Mission_StatsMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1StatsReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["Mission"]}}, "/api/v1/missions/{id}": {"get": {"operationId": "Mission_GetMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1MissionReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "unscoped", "in": "query", "required": false, "type": "boolean"}, {"name": "withDevice", "in": "query", "required": false, "type": "boolean"}], "tags": ["Mission"]}, "delete": {"operationId": "Mission_DeleteMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apimissionsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Mission"]}, "put": {"operationId": "Mission_UpdateMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apimissionsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "avatarId": {"type": "string", "format": "int64"}, "deviceId": {"type": "string", "format": "int64"}, "airlineId": {"type": "string", "format": "int64"}, "startTime": {"type": "number", "format": "double"}, "endTime": {"type": "number", "format": "double"}, "executeCount": {"type": "integer", "format": "int32"}, "alarmCount": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "avatar": {"$ref": "#/definitions/v1MissionItemavatarInfo"}, "editor": {"$ref": "#/definitions/v1MissionItemavatarInfo"}, "device": {"$ref": "#/definitions/MissionItemdeviceInfo"}, "airline": {"$ref": "#/definitions/MissionItemairlineInfo"}, "isDeleted": {"type": "boolean"}, "editedTime": {"type": "number", "format": "double"}, "algConfigs": {"type": "array", "items": {"$ref": "#/definitions/v1AlgConfig"}}, "noticeRules": {"type": "array", "items": {"$ref": "#/definitions/v1NoticeRule"}}, "executeTimes": {"type": "array", "items": {"$ref": "#/definitions/v1ExecuteTime"}}}}}], "tags": ["Mission"]}}, "/api/v1/missions/{id}/executions": {"get": {"operationId": "Mission_ListExecution", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ExecutionsReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["Mission"]}}, "/api/v1/missions/{id}/switch": {"put": {"operationId": "Mission_SwitchMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apimissionsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enabled": {"type": "boolean"}}}}], "tags": ["Mission"]}}, "/internal/v1/missions/{id}/status": {"put": {"operationId": "Mission_StatusMission", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apimissionsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"action": {"type": "string"}}}}], "tags": ["Mission"]}}}, "definitions": {"AlarmsReplytaskCount": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "name": {"type": "string"}}}, "BatchReplydetail": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "reason": {"type": "string"}}}, "ExecutionsReplyexecution": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "status": {"type": "string"}, "reason": {"type": "string"}, "alarmCount": {"type": "integer", "format": "int32"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "missionId": {"type": "string", "format": "int64"}, "voyageId": {"type": "string", "format": "int64"}}}, "MissionItemairlineInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "type": {"type": "string"}, "estimateMileage": {"type": "number", "format": "float"}}}, "MissionItemdeviceInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "sn": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}}}, "StatsReplystatusCount": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}}}, "apimissionsv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apimissionsv1CommonReplycommonData"}}}, "apimissionsv1CommonReplycommonData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "apimissionsv1ListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apimissionsv1ListReplylistData"}}}, "apimissionsv1ListReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1MissionItem"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1AlarmsReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1AlarmsReplylistData"}}}, "v1AlarmsReplylistData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/AlarmsReplytaskCount"}}}}, "v1AlgConfig": {"type": "object", "properties": {"name": {"type": "string"}, "interval": {"type": "integer", "format": "int32"}, "threshold": {"type": "integer", "format": "int32"}}}, "v1BatchReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1BatchReplylistData"}}}, "v1BatchReplylistData": {"type": "object", "properties": {"successCount": {"type": "integer", "format": "int32"}, "failureCount": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/BatchReplydetail"}}}}, "v1BatchRequest": {"type": "object", "properties": {"status": {"type": "integer", "format": "int32"}, "ids": {"type": "array", "items": {"type": "string", "format": "int64"}}}}, "v1ExecuteTime": {"type": "object", "properties": {"moment": {"type": "integer", "format": "int32"}, "rundays": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "v1ExecutionsReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1ExecutionsReplylistData"}}}, "v1ExecutionsReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/ExecutionsReplyexecution"}}}}, "v1MissionItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "avatarId": {"type": "string", "format": "int64"}, "deviceId": {"type": "string", "format": "int64"}, "airlineId": {"type": "string", "format": "int64"}, "startTime": {"type": "number", "format": "double"}, "endTime": {"type": "number", "format": "double"}, "executeCount": {"type": "integer", "format": "int32"}, "alarmCount": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "avatar": {"$ref": "#/definitions/v1MissionItemavatarInfo"}, "editor": {"$ref": "#/definitions/v1MissionItemavatarInfo"}, "device": {"$ref": "#/definitions/MissionItemdeviceInfo"}, "airline": {"$ref": "#/definitions/MissionItemairlineInfo"}, "isDeleted": {"type": "boolean"}, "editedTime": {"type": "number", "format": "double"}, "algConfigs": {"type": "array", "items": {"$ref": "#/definitions/v1AlgConfig"}}, "noticeRules": {"type": "array", "items": {"$ref": "#/definitions/v1NoticeRule"}}, "executeTimes": {"type": "array", "items": {"$ref": "#/definitions/v1ExecuteTime"}}}}, "v1MissionItemavatarInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "mobile": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}}}, "v1MissionReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1MissionItem"}}}, "v1NoticeRule": {"type": "object", "properties": {"time": {"type": "integer", "format": "int32"}, "multilevel": {"type": "integer", "format": "int32"}, "noticeTypes": {"type": "array", "items": {"type": "string"}}, "conditions": {"type": "array", "items": {"type": "string"}}, "contacts": {"type": "array", "items": {"$ref": "#/definitions/v1NoticeRulecontact"}}}}, "v1NoticeRulecontact": {"type": "object", "properties": {"name": {"type": "string"}, "contact": {"type": "string"}}}, "v1StatsReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1StatsReplylistData"}}}, "v1StatsReplylistData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/StatsReplystatusCount"}}}}}}