// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/waypoints/v1/waypoint.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list"
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CommonRequest) Reset() {
	*x = CommonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRequest) ProtoMessage() {}

func (x *CommonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRequest.ProtoReflect.Descriptor instead.
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{0}
}

func (x *CommonRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type CommonReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *CommonReplyCommonData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CommonReply) Reset() {
	*x = CommonReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReply) ProtoMessage() {}

func (x *CommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReply.ProtoReflect.Descriptor instead.
func (*CommonReply) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{1}
}

func (x *CommonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommonReply) GetData() *CommonReplyCommonData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AddRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AirlineId int64                 `protobuf:"varint,1,opt,name=airlineId,proto3" json:"airlineId,omitempty"`
	Filename  *string               `protobuf:"bytes,2,opt,name=filename,proto3,oneof" json:"filename,omitempty"`
	List      []*AddRequestWaypoint `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AddRequest) Reset() {
	*x = AddRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRequest) ProtoMessage() {}

func (x *AddRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRequest.ProtoReflect.Descriptor instead.
func (*AddRequest) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{2}
}

func (x *AddRequest) GetAirlineId() int64 {
	if x != nil {
		return x.AirlineId
	}
	return 0
}

func (x *AddRequest) GetFilename() string {
	if x != nil && x.Filename != nil {
		return *x.Filename
	}
	return ""
}

func (x *AddRequest) GetList() []*AddRequestWaypoint {
	if x != nil {
		return x.List
	}
	return nil
}

type WaypointItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime float64   `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime float64   `protobuf:"fixed64,3,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	AirlineId   int64     `protobuf:"varint,4,opt,name=airlineId,proto3" json:"airlineId,omitempty"`
	Serial      int32     `protobuf:"varint,5,opt,name=serial,proto3" json:"serial,omitempty"`
	Height      float32   `protobuf:"fixed32,6,opt,name=height,proto3" json:"height,omitempty"`
	Speed       float32   `protobuf:"fixed32,7,opt,name=speed,proto3" json:"speed,omitempty"`
	TurnMode    string    `protobuf:"bytes,8,opt,name=turnMode,proto3" json:"turnMode,omitempty"`
	TurnDamping float32   `protobuf:"fixed32,9,opt,name=turnDamping,proto3" json:"turnDamping,omitempty"`
	Lnglat      []float64 `protobuf:"fixed64,10,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	TenantId    int64     `protobuf:"varint,11,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	MerchantId  int64     `protobuf:"varint,12,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
}

func (x *WaypointItem) Reset() {
	*x = WaypointItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaypointItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaypointItem) ProtoMessage() {}

func (x *WaypointItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaypointItem.ProtoReflect.Descriptor instead.
func (*WaypointItem) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{3}
}

func (x *WaypointItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WaypointItem) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *WaypointItem) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *WaypointItem) GetAirlineId() int64 {
	if x != nil {
		return x.AirlineId
	}
	return 0
}

func (x *WaypointItem) GetSerial() int32 {
	if x != nil {
		return x.Serial
	}
	return 0
}

func (x *WaypointItem) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *WaypointItem) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *WaypointItem) GetTurnMode() string {
	if x != nil {
		return x.TurnMode
	}
	return ""
}

func (x *WaypointItem) GetTurnDamping() float32 {
	if x != nil {
		return x.TurnDamping
	}
	return 0
}

func (x *WaypointItem) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *WaypointItem) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *WaypointItem) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

type WaypointReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *WaypointItem `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *WaypointReply) Reset() {
	*x = WaypointReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaypointReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaypointReply) ProtoMessage() {}

func (x *WaypointReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaypointReply.ProtoReflect.Descriptor instead.
func (*WaypointReply) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{4}
}

func (x *WaypointReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WaypointReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *WaypointReply) GetData() *WaypointItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size      int32  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	AirlineId *int64 `protobuf:"varint,3,opt,name=airlineId,proto3,oneof" json:"airlineId,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{5}
}

func (x *ListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListRequest) GetAirlineId() int64 {
	if x != nil && x.AirlineId != nil {
		return *x.AirlineId
	}
	return 0
}

type ListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ListReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListReply) Reset() {
	*x = ListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReply) ProtoMessage() {}

func (x *ListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReply.ProtoReflect.Descriptor instead.
func (*ListReply) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{6}
}

func (x *ListReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListReply) GetData() *ListReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type CommonReplyCommonData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CommonReplyCommonData) Reset() {
	*x = CommonReplyCommonData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReplyCommonData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReplyCommonData) ProtoMessage() {}

func (x *CommonReplyCommonData) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReplyCommonData.ProtoReflect.Descriptor instead.
func (*CommonReplyCommonData) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CommonReplyCommonData) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type AddRequestWaypoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Speed  float32   `protobuf:"fixed32,1,opt,name=speed,proto3" json:"speed,omitempty"`
	Serial int32     `protobuf:"varint,2,opt,name=serial,proto3" json:"serial,omitempty"`
	Height float64   `protobuf:"fixed64,3,opt,name=height,proto3" json:"height,omitempty"`
	Lnglat []float64 `protobuf:"fixed64,4,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
}

func (x *AddRequestWaypoint) Reset() {
	*x = AddRequestWaypoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRequestWaypoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRequestWaypoint) ProtoMessage() {}

func (x *AddRequestWaypoint) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRequestWaypoint.ProtoReflect.Descriptor instead.
func (*AddRequestWaypoint) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{2, 0}
}

func (x *AddRequestWaypoint) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *AddRequestWaypoint) GetSerial() int32 {
	if x != nil {
		return x.Serial
	}
	return 0
}

func (x *AddRequestWaypoint) GetHeight() float64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *AddRequestWaypoint) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

type ListReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32           `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32           `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32           `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*WaypointItem `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListReplyListData) Reset() {
	*x = ListReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReplyListData) ProtoMessage() {}

func (x *ListReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_waypoints_v1_waypoint_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReplyListData.ProtoReflect.Descriptor instead.
func (*ListReplyListData) Descriptor() ([]byte, []int) {
	return file_api_waypoints_v1_waypoint_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ListReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListReplyListData) GetList() []*WaypointItem {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_waypoints_v1_waypoint_proto protoreflect.FileDescriptor

var file_api_waypoints_v1_waypoint_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x10, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x6c, 0x69,
	0x73, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2c, 0x0a,
	0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x9f, 0x01, 0x0a, 0x0b,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3c, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x24, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xfd, 0x01,
	0x0a, 0x0a, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x68, 0x0a, 0x08, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xd8, 0x02,
	0x0a, 0x0c, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x75, 0x72, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x75, 0x72, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x6d, 0x70, 0x69, 0x6e,
	0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x6d,
	0x70, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x0d, 0x57, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x94, 0x01, 0x0a, 0x0b,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61,
	0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x21, 0x0a, 0x09,
	0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x3a,
	0x03, 0x88, 0x43, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x22, 0xf1, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x38,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x7c, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0xb4, 0x04, 0x0a, 0x08, 0x57, 0x61, 0x79, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x69, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x22, 0x11, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x65,
	0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x13, 0x12, 0x11, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x6f, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x57, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x73, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b,
	0x1a, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x3a, 0x01, 0x2a, 0x12, 0x70, 0x0a, 0x0e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x18, 0x2a, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x42, 0x46, 0x0a,
	0x10, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61,
	0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_waypoints_v1_waypoint_proto_rawDescOnce sync.Once
	file_api_waypoints_v1_waypoint_proto_rawDescData = file_api_waypoints_v1_waypoint_proto_rawDesc
)

func file_api_waypoints_v1_waypoint_proto_rawDescGZIP() []byte {
	file_api_waypoints_v1_waypoint_proto_rawDescOnce.Do(func() {
		file_api_waypoints_v1_waypoint_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_waypoints_v1_waypoint_proto_rawDescData)
	})
	return file_api_waypoints_v1_waypoint_proto_rawDescData
}

var file_api_waypoints_v1_waypoint_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_waypoints_v1_waypoint_proto_goTypes = []interface{}{
	(*CommonRequest)(nil),         // 0: api.waypoints.v1.CommonRequest
	(*CommonReply)(nil),           // 1: api.waypoints.v1.CommonReply
	(*AddRequest)(nil),            // 2: api.waypoints.v1.AddRequest
	(*WaypointItem)(nil),          // 3: api.waypoints.v1.WaypointItem
	(*WaypointReply)(nil),         // 4: api.waypoints.v1.WaypointReply
	(*ListRequest)(nil),           // 5: api.waypoints.v1.ListRequest
	(*ListReply)(nil),             // 6: api.waypoints.v1.ListReply
	(*CommonReplyCommonData)(nil), // 7: api.waypoints.v1.CommonReply.commonData
	(*AddRequestWaypoint)(nil),    // 8: api.waypoints.v1.AddRequest.waypoint
	(*ListReplyListData)(nil),     // 9: api.waypoints.v1.ListReply.listData
}
var file_api_waypoints_v1_waypoint_proto_depIdxs = []int32{
	7,  // 0: api.waypoints.v1.CommonReply.data:type_name -> api.waypoints.v1.CommonReply.commonData
	8,  // 1: api.waypoints.v1.AddRequest.list:type_name -> api.waypoints.v1.AddRequest.waypoint
	3,  // 2: api.waypoints.v1.WaypointReply.data:type_name -> api.waypoints.v1.WaypointItem
	9,  // 3: api.waypoints.v1.ListReply.data:type_name -> api.waypoints.v1.ListReply.listData
	3,  // 4: api.waypoints.v1.ListReply.listData.list:type_name -> api.waypoints.v1.WaypointItem
	2,  // 5: api.waypoints.v1.Waypoint.AddWaypoints:input_type -> api.waypoints.v1.AddRequest
	5,  // 6: api.waypoints.v1.Waypoint.ListWaypoint:input_type -> api.waypoints.v1.ListRequest
	0,  // 7: api.waypoints.v1.Waypoint.GetWaypoint:input_type -> api.waypoints.v1.CommonRequest
	0,  // 8: api.waypoints.v1.Waypoint.UpdateWaypoint:input_type -> api.waypoints.v1.CommonRequest
	0,  // 9: api.waypoints.v1.Waypoint.DeleteWaypoint:input_type -> api.waypoints.v1.CommonRequest
	1,  // 10: api.waypoints.v1.Waypoint.AddWaypoints:output_type -> api.waypoints.v1.CommonReply
	6,  // 11: api.waypoints.v1.Waypoint.ListWaypoint:output_type -> api.waypoints.v1.ListReply
	4,  // 12: api.waypoints.v1.Waypoint.GetWaypoint:output_type -> api.waypoints.v1.WaypointReply
	1,  // 13: api.waypoints.v1.Waypoint.UpdateWaypoint:output_type -> api.waypoints.v1.CommonReply
	1,  // 14: api.waypoints.v1.Waypoint.DeleteWaypoint:output_type -> api.waypoints.v1.CommonReply
	10, // [10:15] is the sub-list for method output_type
	5,  // [5:10] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_waypoints_v1_waypoint_proto_init() }
func file_api_waypoints_v1_waypoint_proto_init() {
	if File_api_waypoints_v1_waypoint_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_waypoints_v1_waypoint_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaypointItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaypointReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReplyCommonData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRequestWaypoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_waypoints_v1_waypoint_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_waypoints_v1_waypoint_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_api_waypoints_v1_waypoint_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_waypoints_v1_waypoint_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_waypoints_v1_waypoint_proto_goTypes,
		DependencyIndexes: file_api_waypoints_v1_waypoint_proto_depIdxs,
		MessageInfos:      file_api_waypoints_v1_waypoint_proto_msgTypes,
	}.Build()
	File_api_waypoints_v1_waypoint_proto = out.File
	file_api_waypoints_v1_waypoint_proto_rawDesc = nil
	file_api_waypoints_v1_waypoint_proto_goTypes = nil
	file_api_waypoints_v1_waypoint_proto_depIdxs = nil
}
