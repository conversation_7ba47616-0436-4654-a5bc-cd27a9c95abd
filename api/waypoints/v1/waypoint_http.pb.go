// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type WaypointHTTPServer interface {
	AddWaypoints(context.Context, *AddRequest) (*CommonReply, error)
	DeleteWaypoint(context.Context, *CommonRequest) (*CommonReply, error)
	GetWaypoint(context.Context, *CommonRequest) (*WaypointReply, error)
	ListWaypoint(context.Context, *ListRequest) (*ListReply, error)
	UpdateWaypoint(context.Context, *CommonRequest) (*CommonReply, error)
}

func RegisterWaypointHTTPServer(s *http.Server, srv WaypointHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/waypoints", _Waypoint_AddWaypoints0_HTTP_Handler(srv))
	r.GET("/api/v1/waypoints", _Waypoint_ListWaypoint0_HTTP_Handler(srv))
	r.GET("/api/v1/waypoints/{id}", _Waypoint_GetWaypoint0_HTTP_Handler(srv))
	r.PUT("/api/v1/waypoints/{id}", _Waypoint_UpdateWaypoint0_HTTP_Handler(srv))
	r.DELETE("/api/v1/waypoints/{id}", _Waypoint_DeleteWaypoint0_HTTP_Handler(srv))
}

func _Waypoint_AddWaypoints0_HTTP_Handler(srv WaypointHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.waypoints.v1.Waypoint/AddWaypoints")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddWaypoints(ctx, req.(*AddRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Waypoint_ListWaypoint0_HTTP_Handler(srv WaypointHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.waypoints.v1.Waypoint/ListWaypoint")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWaypoint(ctx, req.(*ListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListReply)
		return ctx.Result(200, reply)
	}
}

func _Waypoint_GetWaypoint0_HTTP_Handler(srv WaypointHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.waypoints.v1.Waypoint/GetWaypoint")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWaypoint(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WaypointReply)
		return ctx.Result(200, reply)
	}
}

func _Waypoint_UpdateWaypoint0_HTTP_Handler(srv WaypointHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.waypoints.v1.Waypoint/UpdateWaypoint")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateWaypoint(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Waypoint_DeleteWaypoint0_HTTP_Handler(srv WaypointHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.waypoints.v1.Waypoint/DeleteWaypoint")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteWaypoint(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type WaypointHTTPClient interface {
	AddWaypoints(ctx context.Context, req *AddRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	DeleteWaypoint(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	GetWaypoint(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *WaypointReply, err error)
	ListWaypoint(ctx context.Context, req *ListRequest, opts ...http.CallOption) (rsp *ListReply, err error)
	UpdateWaypoint(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type WaypointHTTPClientImpl struct {
	cc *http.Client
}

func NewWaypointHTTPClient(client *http.Client) WaypointHTTPClient {
	return &WaypointHTTPClientImpl{client}
}

func (c *WaypointHTTPClientImpl) AddWaypoints(ctx context.Context, in *AddRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/waypoints"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.waypoints.v1.Waypoint/AddWaypoints"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *WaypointHTTPClientImpl) DeleteWaypoint(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/waypoints/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.waypoints.v1.Waypoint/DeleteWaypoint"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *WaypointHTTPClientImpl) GetWaypoint(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*WaypointReply, error) {
	var out WaypointReply
	pattern := "/api/v1/waypoints/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.waypoints.v1.Waypoint/GetWaypoint"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *WaypointHTTPClientImpl) ListWaypoint(ctx context.Context, in *ListRequest, opts ...http.CallOption) (*ListReply, error) {
	var out ListReply
	pattern := "/api/v1/waypoints"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.waypoints.v1.Waypoint/ListWaypoint"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *WaypointHTTPClientImpl) UpdateWaypoint(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/waypoints/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.waypoints.v1.Waypoint/UpdateWaypoint"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
