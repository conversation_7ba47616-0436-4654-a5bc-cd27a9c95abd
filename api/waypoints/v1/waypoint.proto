syntax = "proto3";

package api.waypoints.v1;
import "google/api/annotations.proto";
import "validator/validator.proto";
import "list/list.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/waypoints/v1;v1";
option java_multiple_files = true;
option java_package = "api.waypoints.v1";

service Waypoint {
  rpc AddWaypoints (AddRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/waypoints"
      body: "*"
    };
  };
  rpc ListWaypoint (ListRequest) returns (ListReply) {
    option (google.api.http) = {
      get: "/api/v1/waypoints"
    };
  };
  rpc GetWaypoint (CommonRequest) returns (WaypointReply) {
    option (google.api.http) = {
      get: "/api/v1/waypoints/{id}"
    };
  };
  rpc UpdateWaypoint (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/waypoints/{id}"
			body: "*"
    };
  };
	rpc DeleteWaypoint (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      delete: "/api/v1/waypoints/{id}"
    };
  };
}

message CommonRequest {
  int64 id = 1 [(validator.rules) = "required"];
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message AddRequest {
  int64 airlineId = 1;
  optional string filename = 2;
  repeated waypoint list = 3;

  message waypoint {
    float speed = 1;
    int32 serial = 2;
    double height = 3;
    repeated double lnglat = 4;
  }
}

message WaypointItem {
  int64 id = 1;
  double createdTime = 2;
  double updatedTime = 3;
  int64 airlineId = 4;
  int32 serial = 5;
  float height = 6;
  float speed = 7;
  string turnMode = 8;
  float turnDamping = 9;
  repeated double lnglat = 10;
  int64 tenantId = 11;
  int64 merchantId = 12;
}

message WaypointReply {
  int32 code = 1;
  string message = 2;
  WaypointItem data = 3;
}

message ListRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  optional int64 airlineId = 3;
}

message ListReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated WaypointItem list = 4;
  }
}
