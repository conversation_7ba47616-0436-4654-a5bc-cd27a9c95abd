// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/waypoints/v1/waypoint.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WaypointClient is the client API for Waypoint service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WaypointClient interface {
	AddWaypoints(ctx context.Context, in *AddRequest, opts ...grpc.CallOption) (*CommonReply, error)
	ListWaypoint(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error)
	GetWaypoint(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*WaypointReply, error)
	UpdateWaypoint(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	DeleteWaypoint(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
}

type waypointClient struct {
	cc grpc.ClientConnInterface
}

func NewWaypointClient(cc grpc.ClientConnInterface) WaypointClient {
	return &waypointClient{cc}
}

func (c *waypointClient) AddWaypoints(ctx context.Context, in *AddRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.waypoints.v1.Waypoint/AddWaypoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *waypointClient) ListWaypoint(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error) {
	out := new(ListReply)
	err := c.cc.Invoke(ctx, "/api.waypoints.v1.Waypoint/ListWaypoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *waypointClient) GetWaypoint(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*WaypointReply, error) {
	out := new(WaypointReply)
	err := c.cc.Invoke(ctx, "/api.waypoints.v1.Waypoint/GetWaypoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *waypointClient) UpdateWaypoint(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.waypoints.v1.Waypoint/UpdateWaypoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *waypointClient) DeleteWaypoint(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.waypoints.v1.Waypoint/DeleteWaypoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WaypointServer is the server API for Waypoint service.
// All implementations must embed UnimplementedWaypointServer
// for forward compatibility
type WaypointServer interface {
	AddWaypoints(context.Context, *AddRequest) (*CommonReply, error)
	ListWaypoint(context.Context, *ListRequest) (*ListReply, error)
	GetWaypoint(context.Context, *CommonRequest) (*WaypointReply, error)
	UpdateWaypoint(context.Context, *CommonRequest) (*CommonReply, error)
	DeleteWaypoint(context.Context, *CommonRequest) (*CommonReply, error)
	mustEmbedUnimplementedWaypointServer()
}

// UnimplementedWaypointServer must be embedded to have forward compatible implementations.
type UnimplementedWaypointServer struct {
}

func (UnimplementedWaypointServer) AddWaypoints(context.Context, *AddRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddWaypoints not implemented")
}
func (UnimplementedWaypointServer) ListWaypoint(context.Context, *ListRequest) (*ListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWaypoint not implemented")
}
func (UnimplementedWaypointServer) GetWaypoint(context.Context, *CommonRequest) (*WaypointReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWaypoint not implemented")
}
func (UnimplementedWaypointServer) UpdateWaypoint(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWaypoint not implemented")
}
func (UnimplementedWaypointServer) DeleteWaypoint(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWaypoint not implemented")
}
func (UnimplementedWaypointServer) mustEmbedUnimplementedWaypointServer() {}

// UnsafeWaypointServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WaypointServer will
// result in compilation errors.
type UnsafeWaypointServer interface {
	mustEmbedUnimplementedWaypointServer()
}

func RegisterWaypointServer(s grpc.ServiceRegistrar, srv WaypointServer) {
	s.RegisterService(&Waypoint_ServiceDesc, srv)
}

func _Waypoint_AddWaypoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WaypointServer).AddWaypoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.waypoints.v1.Waypoint/AddWaypoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WaypointServer).AddWaypoints(ctx, req.(*AddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Waypoint_ListWaypoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WaypointServer).ListWaypoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.waypoints.v1.Waypoint/ListWaypoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WaypointServer).ListWaypoint(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Waypoint_GetWaypoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WaypointServer).GetWaypoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.waypoints.v1.Waypoint/GetWaypoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WaypointServer).GetWaypoint(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Waypoint_UpdateWaypoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WaypointServer).UpdateWaypoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.waypoints.v1.Waypoint/UpdateWaypoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WaypointServer).UpdateWaypoint(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Waypoint_DeleteWaypoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WaypointServer).DeleteWaypoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.waypoints.v1.Waypoint/DeleteWaypoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WaypointServer).DeleteWaypoint(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Waypoint_ServiceDesc is the grpc.ServiceDesc for Waypoint service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Waypoint_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.waypoints.v1.Waypoint",
	HandlerType: (*WaypointServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddWaypoints",
			Handler:    _Waypoint_AddWaypoints_Handler,
		},
		{
			MethodName: "ListWaypoint",
			Handler:    _Waypoint_ListWaypoint_Handler,
		},
		{
			MethodName: "GetWaypoint",
			Handler:    _Waypoint_GetWaypoint_Handler,
		},
		{
			MethodName: "UpdateWaypoint",
			Handler:    _Waypoint_UpdateWaypoint_Handler,
		},
		{
			MethodName: "DeleteWaypoint",
			Handler:    _Waypoint_DeleteWaypoint_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/waypoints/v1/waypoint.proto",
}
