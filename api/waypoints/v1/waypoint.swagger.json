{"swagger": "2.0", "info": {"title": "api/waypoints/v1/waypoint.proto", "version": "version not set"}, "tags": [{"name": "Waypoint"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/waypoints": {"get": {"operationId": "Waypoint_ListWaypoint", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiwaypointsv1ListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "airlineId", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["Waypoint"]}, "post": {"operationId": "Waypoint_AddWaypoints", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiwaypointsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/apiwaypointsv1AddRequest"}}], "tags": ["Waypoint"]}}, "/api/v1/waypoints/{id}": {"get": {"operationId": "Waypoint_GetWaypoint", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1WaypointReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Waypoint"]}, "delete": {"operationId": "Waypoint_DeleteWaypoint", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiwaypointsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Waypoint"]}, "put": {"operationId": "Waypoint_UpdateWaypoint", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiwaypointsv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["Waypoint"]}}}, "definitions": {"AddRequestwaypoint": {"type": "object", "properties": {"speed": {"type": "number", "format": "float"}, "serial": {"type": "integer", "format": "int32"}, "height": {"type": "number", "format": "double"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}, "apiwaypointsv1AddRequest": {"type": "object", "properties": {"airlineId": {"type": "string", "format": "int64"}, "filename": {"type": "string"}, "list": {"type": "array", "items": {"$ref": "#/definitions/AddRequestwaypoint"}}}}, "apiwaypointsv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apiwaypointsv1CommonReplycommonData"}}}, "apiwaypointsv1CommonReplycommonData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "apiwaypointsv1ListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apiwaypointsv1ListReplylistData"}}}, "apiwaypointsv1ListReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1WaypointItem"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1WaypointItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "airlineId": {"type": "string", "format": "int64"}, "serial": {"type": "integer", "format": "int32"}, "height": {"type": "number", "format": "float"}, "speed": {"type": "number", "format": "float"}, "turnMode": {"type": "string"}, "turnDamping": {"type": "number", "format": "float"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}}}, "v1WaypointReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1WaypointItem"}}}}}