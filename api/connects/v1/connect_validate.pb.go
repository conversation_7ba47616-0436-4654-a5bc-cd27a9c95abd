// Code generated by protoc-gen-go-http. DO NOT EDIT.

package v1

import (
	context "context"
	v10 "github.com/go-playground/validator/v10"
	validator "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
)

var _ = new(context.Context)
var _ = new(v10.Validate)
var _ = new(validator.ValidateError)

func (m *EventUpRequest) Validate() error {
	ctx := context.TODO()
	v := v10.New()

	if err := validator.DoValidate(m.Flight, "Flight"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Hms, "Hms"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.ResourceReq, "ResourceReq"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Topo, "Topo"); err != nil {
		return err
	}

	if err := v.VarCtx(ctx, m.Sn, "required"); err != nil {
		return validator.WrapValidatorError("Sn", err)
	}
	return nil
}

func (m *PropertyUpRequest) Validate() error {
	ctx := context.TODO()
	v := v10.New()

	if err := validator.DoValidate(m.Battery, "Battery"); err != nil {
		return err
	}

	for _, mi := range m.Cameras {
		if err := validator.DoValidate(mi, "Cameras"); err != nil {
			return err
		}
	}

	if err := validator.DoValidate(m.Charge, "Charge"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Dock, "Dock"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Elec, "Elec"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Env, "Env"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Flight, "Flight"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.FlightTask, "FlightTask"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Network, "Network"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Other, "Other"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Pilot, "Pilot"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Position, "Position"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Speaker, "Speaker"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Storage, "Storage"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.Version, "Version"); err != nil {
		return err
	}

	if err := validator.DoValidate(m.WirelessLink, "WirelessLink"); err != nil {
		return err
	}

	if err := v.VarCtx(ctx, m.Sn, "required"); err != nil {
		return validator.WrapValidatorError("Sn", err)
	}
	return nil
}

func (m *ServiceReplyRequest) Validate() error {
	ctx := context.TODO()
	v := v10.New()

	if err := validator.DoValidate(m.Data, "Data"); err != nil {
		return err
	}

	if err := v.VarCtx(ctx, m.Sn, "required"); err != nil {
		return validator.WrapValidatorError("Sn", err)
	}
	return nil
}
