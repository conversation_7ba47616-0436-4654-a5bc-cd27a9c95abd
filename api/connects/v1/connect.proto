syntax = "proto3";

package api.connects.v1;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "validator/validator.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/connects/v1;v1";
option java_multiple_files = true;
option java_package = "api.connects.v1";

service Connect {
	rpc EventUp (EventUpRequest) returns (EventUpReply) {
    option (google.api.http) = {
      put: "/internal/v1/connects/{sn}/event"
			body: "*"
    };
  };
	rpc PropertyUp (PropertyUpRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/internal/v1/connects/{sn}/property"
			body: "*"
    };
  };
  rpc ServiceReply (ServiceReplyRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/internal/v1/connects/{sn}/reply"
			body: "*"
    };
  };
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message EventUpRequest {
	string sn = 1 [(validator.rules) = "required"];
  string id = 2;
  int64 rxTime = 3;
  int64 timestamp = 4;
  int64 deviceId = 5;
  int32 type  = 6;
  FlightTaskProgressEvent flight = 7;
  DockHealMonitorEvent hms = 8;
  DockTopoUpdateEvent topo = 9;
  DockFlightTaskResourceRequestEvent resourceReq = 10;
  bytes simpleEventValue = 11;
}

message EventUpReply {
	int32 code = 1;
	string message = 2;
	google.protobuf.Struct data = 3;
}

message PropertyUpRequest {
	string sn = 1 [(validator.rules) = "required"];
  string id = 2;
  int64 rxTime = 3;
  int64 timestamp = 4;
  int64 deviceId = 5;
  string droneSn = 6;
  string droneType = 7;
  int32 mode = 8;
  DockState dock = 9;
  PilotState pilot = 10;
  NetworkState network = 11;
  PositionState position = 12;
  EnvironmentState env = 13;
  FlightTaskState flightTask = 14;
  ElecPowerState elec = 15;
  StorageState storage = 16;
  DroneFlightState flight = 17;
  DroneBatteryState battery = 18;
  BatteryChargeState charge = 19;
  WirelessLinkState wirelessLink = 20;
  Version version = 21;
  google.protobuf.Struct other = 22;
  DroneSpeakerState speaker = 23;
  repeated DroneCameraState cameras = 24;
}

message ServiceReplyRequest {
	string sn = 1 [(validator.rules) = "required"];
  string id = 2;
  int64 rxTime = 3;
  int64 timestamp = 4;
  int64 deviceId = 5;
  int64 serviceId = 7;
  string identifier = 8;
  string status = 9;
  string message = 10;
  google.protobuf.Struct data = 11;
  int32 code = 12;
}

message DockState {
  int32 mode = 1;
  double longitude = 2;
  double latitude = 3;
  float height = 4;
  int32 coverState = 5;
  int32 putterState = 6;
  float temperature = 7;
  float humidity = 8;
  int64 storageTotal = 9;
  int64 storageUsed = 10;
  int32 droneOnlineState = 11;
  int32 airConditionerState = 12;
  int32 supplementLightState = 13;
}

message PositionState {
  int32 isFixed = 1;
  int32 isCalibration = 2;
  int32 quality = 3;
  int32 gpsNumber = 4;
  int32 rtkNumber = 5;
}

message NetworkState {
  int32 type = 1;
  int32 quality = 2;
}

message WirelessLinkState {
  int32 mode = 1;
  int32 state4G= 2;
  int32 stateSDR = 3;
  int32 qualitySDR = 4;
  int32 quality4G = 5;
  int32 quality4GUav = 6;
  int32 quality4GGnd = 7;
}

message FlightTaskState {
  int32 count = 1;
  double longitude = 2;
  double latitude = 3;
  float safeLandHeight = 4;
  int32 safeLandConfigured = 5;
  int32 droneInDock = 6;
}

message BatteryChargeState {
  int32 droneBatteryPercent = 1;
  int32 droneBatteryChargeState = 2;
}

message ElecPowerState {
  int32 workingVoltage = 1;
  int32 workingCurrent = 2;
  int32 dockBackupBatterySwitch = 3;
  int32 dockBackupBatteryVoltage = 4;
  float dockBackupTemperature = 5;
  int32 supplyVoltage = 6;
}

message StorageState {
  int64 total = 1;
  int64 used = 2;
}

message EnvironmentState {
  int32 rainfall = 1;
  float windSpeed = 2;
  float temperature = 3;
  float humidity = 4;
}

message DroneFlightState {
  float horizontalSpeed = 1;
  float verticalSpeed = 2;
  double longitude = 3;
  double latitude = 4;
  float height = 5;
  float elevation = 6;
  float attitudePitch = 7;
  float attitudeRoll = 8;
  float attitudeHead = 9;
  int64 totalFlightTime = 10;
  float windSpeed = 11;
  int32 windDirection = 12;
  int32 isNearHeightLimit = 13;
  int32 isNearAreaLimit = 14;
  int32 isNearIDistanceLimit = 15;
  int32 nightLightsState = 16;
  int32 horizonObstacleAvoidance = 17;
  int32 upsideObstacleAvoidance = 18;
  int32 downsideObstacleAvoidance = 19;
}

message DroneBatteryState {
  int32 capacityPercent = 1;
  int32 remainFlightTime = 2;
  int32 returnHomePower = 3;
  int32 landingPower = 4;
  message Battery {
    int32 index = 1;
    int32 capacityPercent = 2;
    int32 voltage = 3;
    float temperature = 4;
    int32 loop = 5;
    string sn = 6;
  }
  repeated Battery batteries = 5;
}

message FlightTaskProgressEvent  {
  string flightId = 1;
  int32 currentWaypointIndex = 3;
  int32 status = 4;
  int32 percent = 5;
  google.protobuf.Struct extra = 6;
  int32 step = 7;
  int32 voyageState = 8;
  int32 breakReason = 9;
}

message DockHealMonitorEvent {
  int32 level = 1;
  int32 module = 2;
  int32 inTheSky = 3;
  int32 imminent = 4;
  string code = 5;
  google.protobuf.Struct extra = 6;
  int32 source = 7;
}

message DockTopoUpdateEvent {
  string deviceModel = 1;
  repeated subdevice subdevices = 2;
  message subdevice {
    string sn = 1;
    string type = 2;
    string index = 3;
    google.protobuf.Struct extra = 4;
    int32 domain = 5;
  }
}

message DockFlightTaskResourceRequestEvent {
  string flightId = 1;
}

message Version {
  string firmware = 1;
}

message PilotState {
  int32 capacityPercent = 1;
  double longitude = 2;
  double latitude = 3;
}


message DroneSpeakerState {
  string  index = 1;
  string name = 2;
  string sn = 3;
  string firmwareVersion = 4;
  string libVersion = 5;
  string type = 6;
  int32 workMode = 7;
  int32 systemState = 8;
  int32 playMode = 9;
  int32 playVolume = 10;
  string playFileName = 11;
  string playFileSignature = 12;
}

message DroneCameraState {
  string index = 1;
  int32 mode = 2;
  int32 photoState = 3;
  int32 recordingState = 4;
  float zoomFactor = 5;
  float irZoomFactor = 6;
  int32 zoomFocusMode = 7;
  int32 zoomFocusValue = 8;
  int32 zoomFocusState = 9;
  int32 irMeteringMode = 10;
  double gimbalPitch = 11;
  double gimbalYaw = 12;
  double gimbalRoll = 13;
  double measureTargetLongitude = 14;
  double measureTargetLatitude = 15;
  double measureTargetAltitude = 16;
  double measureTargetDistance = 17;
  int32 measureErrState = 18;
  DroneCameraLiveViewWorldRegion liveViewWorldRegion = 19;
}

message DroneCameraLiveViewWorldRegion {
  double top = 1;
  double bottom = 2;
  double left = 3;
  double right = 4;
}