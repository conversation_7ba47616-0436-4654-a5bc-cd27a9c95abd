// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/connects/v1/connect.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommonReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *CommonReplyCommonData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CommonReply) Reset() {
	*x = CommonReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReply) ProtoMessage() {}

func (x *CommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReply.ProtoReflect.Descriptor instead.
func (*CommonReply) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{0}
}

func (x *CommonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommonReply) GetData() *CommonReplyCommonData {
	if x != nil {
		return x.Data
	}
	return nil
}

type EventUpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn               string                              `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn,omitempty"`
	Id               string                              `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	RxTime           int64                               `protobuf:"varint,3,opt,name=rxTime,proto3" json:"rxTime,omitempty"`
	Timestamp        int64                               `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	DeviceId         int64                               `protobuf:"varint,5,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	Type             int32                               `protobuf:"varint,6,opt,name=type,proto3" json:"type,omitempty"`
	Flight           *FlightTaskProgressEvent            `protobuf:"bytes,7,opt,name=flight,proto3" json:"flight,omitempty"`
	Hms              *DockHealMonitorEvent               `protobuf:"bytes,8,opt,name=hms,proto3" json:"hms,omitempty"`
	Topo             *DockTopoUpdateEvent                `protobuf:"bytes,9,opt,name=topo,proto3" json:"topo,omitempty"`
	ResourceReq      *DockFlightTaskResourceRequestEvent `protobuf:"bytes,10,opt,name=resourceReq,proto3" json:"resourceReq,omitempty"`
	SimpleEventValue []byte                              `protobuf:"bytes,11,opt,name=simpleEventValue,proto3" json:"simpleEventValue,omitempty"`
}

func (x *EventUpRequest) Reset() {
	*x = EventUpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventUpRequest) ProtoMessage() {}

func (x *EventUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventUpRequest.ProtoReflect.Descriptor instead.
func (*EventUpRequest) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{1}
}

func (x *EventUpRequest) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *EventUpRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EventUpRequest) GetRxTime() int64 {
	if x != nil {
		return x.RxTime
	}
	return 0
}

func (x *EventUpRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *EventUpRequest) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *EventUpRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *EventUpRequest) GetFlight() *FlightTaskProgressEvent {
	if x != nil {
		return x.Flight
	}
	return nil
}

func (x *EventUpRequest) GetHms() *DockHealMonitorEvent {
	if x != nil {
		return x.Hms
	}
	return nil
}

func (x *EventUpRequest) GetTopo() *DockTopoUpdateEvent {
	if x != nil {
		return x.Topo
	}
	return nil
}

func (x *EventUpRequest) GetResourceReq() *DockFlightTaskResourceRequestEvent {
	if x != nil {
		return x.ResourceReq
	}
	return nil
}

func (x *EventUpRequest) GetSimpleEventValue() []byte {
	if x != nil {
		return x.SimpleEventValue
	}
	return nil
}

type EventUpReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *structpb.Struct `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *EventUpReply) Reset() {
	*x = EventUpReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventUpReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventUpReply) ProtoMessage() {}

func (x *EventUpReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventUpReply.ProtoReflect.Descriptor instead.
func (*EventUpReply) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{2}
}

func (x *EventUpReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *EventUpReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *EventUpReply) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

type PropertyUpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn           string              `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn,omitempty"`
	Id           string              `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	RxTime       int64               `protobuf:"varint,3,opt,name=rxTime,proto3" json:"rxTime,omitempty"`
	Timestamp    int64               `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	DeviceId     int64               `protobuf:"varint,5,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	DroneSn      string              `protobuf:"bytes,6,opt,name=droneSn,proto3" json:"droneSn,omitempty"`
	DroneType    string              `protobuf:"bytes,7,opt,name=droneType,proto3" json:"droneType,omitempty"`
	Mode         int32               `protobuf:"varint,8,opt,name=mode,proto3" json:"mode,omitempty"`
	Dock         *DockState          `protobuf:"bytes,9,opt,name=dock,proto3" json:"dock,omitempty"`
	Pilot        *PilotState         `protobuf:"bytes,10,opt,name=pilot,proto3" json:"pilot,omitempty"`
	Network      *NetworkState       `protobuf:"bytes,11,opt,name=network,proto3" json:"network,omitempty"`
	Position     *PositionState      `protobuf:"bytes,12,opt,name=position,proto3" json:"position,omitempty"`
	Env          *EnvironmentState   `protobuf:"bytes,13,opt,name=env,proto3" json:"env,omitempty"`
	FlightTask   *FlightTaskState    `protobuf:"bytes,14,opt,name=flightTask,proto3" json:"flightTask,omitempty"`
	Elec         *ElecPowerState     `protobuf:"bytes,15,opt,name=elec,proto3" json:"elec,omitempty"`
	Storage      *StorageState       `protobuf:"bytes,16,opt,name=storage,proto3" json:"storage,omitempty"`
	Flight       *DroneFlightState   `protobuf:"bytes,17,opt,name=flight,proto3" json:"flight,omitempty"`
	Battery      *DroneBatteryState  `protobuf:"bytes,18,opt,name=battery,proto3" json:"battery,omitempty"`
	Charge       *BatteryChargeState `protobuf:"bytes,19,opt,name=charge,proto3" json:"charge,omitempty"`
	WirelessLink *WirelessLinkState  `protobuf:"bytes,20,opt,name=wirelessLink,proto3" json:"wirelessLink,omitempty"`
	Version      *Version            `protobuf:"bytes,21,opt,name=version,proto3" json:"version,omitempty"`
	Other        *structpb.Struct    `protobuf:"bytes,22,opt,name=other,proto3" json:"other,omitempty"`
	Speaker      *DroneSpeakerState  `protobuf:"bytes,23,opt,name=speaker,proto3" json:"speaker,omitempty"`
	Cameras      []*DroneCameraState `protobuf:"bytes,24,rep,name=cameras,proto3" json:"cameras,omitempty"`
}

func (x *PropertyUpRequest) Reset() {
	*x = PropertyUpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropertyUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyUpRequest) ProtoMessage() {}

func (x *PropertyUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyUpRequest.ProtoReflect.Descriptor instead.
func (*PropertyUpRequest) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{3}
}

func (x *PropertyUpRequest) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *PropertyUpRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PropertyUpRequest) GetRxTime() int64 {
	if x != nil {
		return x.RxTime
	}
	return 0
}

func (x *PropertyUpRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *PropertyUpRequest) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *PropertyUpRequest) GetDroneSn() string {
	if x != nil {
		return x.DroneSn
	}
	return ""
}

func (x *PropertyUpRequest) GetDroneType() string {
	if x != nil {
		return x.DroneType
	}
	return ""
}

func (x *PropertyUpRequest) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *PropertyUpRequest) GetDock() *DockState {
	if x != nil {
		return x.Dock
	}
	return nil
}

func (x *PropertyUpRequest) GetPilot() *PilotState {
	if x != nil {
		return x.Pilot
	}
	return nil
}

func (x *PropertyUpRequest) GetNetwork() *NetworkState {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *PropertyUpRequest) GetPosition() *PositionState {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *PropertyUpRequest) GetEnv() *EnvironmentState {
	if x != nil {
		return x.Env
	}
	return nil
}

func (x *PropertyUpRequest) GetFlightTask() *FlightTaskState {
	if x != nil {
		return x.FlightTask
	}
	return nil
}

func (x *PropertyUpRequest) GetElec() *ElecPowerState {
	if x != nil {
		return x.Elec
	}
	return nil
}

func (x *PropertyUpRequest) GetStorage() *StorageState {
	if x != nil {
		return x.Storage
	}
	return nil
}

func (x *PropertyUpRequest) GetFlight() *DroneFlightState {
	if x != nil {
		return x.Flight
	}
	return nil
}

func (x *PropertyUpRequest) GetBattery() *DroneBatteryState {
	if x != nil {
		return x.Battery
	}
	return nil
}

func (x *PropertyUpRequest) GetCharge() *BatteryChargeState {
	if x != nil {
		return x.Charge
	}
	return nil
}

func (x *PropertyUpRequest) GetWirelessLink() *WirelessLinkState {
	if x != nil {
		return x.WirelessLink
	}
	return nil
}

func (x *PropertyUpRequest) GetVersion() *Version {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *PropertyUpRequest) GetOther() *structpb.Struct {
	if x != nil {
		return x.Other
	}
	return nil
}

func (x *PropertyUpRequest) GetSpeaker() *DroneSpeakerState {
	if x != nil {
		return x.Speaker
	}
	return nil
}

func (x *PropertyUpRequest) GetCameras() []*DroneCameraState {
	if x != nil {
		return x.Cameras
	}
	return nil
}

type ServiceReplyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn         string           `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn,omitempty"`
	Id         string           `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	RxTime     int64            `protobuf:"varint,3,opt,name=rxTime,proto3" json:"rxTime,omitempty"`
	Timestamp  int64            `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	DeviceId   int64            `protobuf:"varint,5,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ServiceId  int64            `protobuf:"varint,7,opt,name=serviceId,proto3" json:"serviceId,omitempty"`
	Identifier string           `protobuf:"bytes,8,opt,name=identifier,proto3" json:"identifier,omitempty"`
	Status     string           `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	Message    string           `protobuf:"bytes,10,opt,name=message,proto3" json:"message,omitempty"`
	Data       *structpb.Struct `protobuf:"bytes,11,opt,name=data,proto3" json:"data,omitempty"`
	Code       int32            `protobuf:"varint,12,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ServiceReplyRequest) Reset() {
	*x = ServiceReplyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceReplyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceReplyRequest) ProtoMessage() {}

func (x *ServiceReplyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceReplyRequest.ProtoReflect.Descriptor instead.
func (*ServiceReplyRequest) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{4}
}

func (x *ServiceReplyRequest) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ServiceReplyRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ServiceReplyRequest) GetRxTime() int64 {
	if x != nil {
		return x.RxTime
	}
	return 0
}

func (x *ServiceReplyRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ServiceReplyRequest) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *ServiceReplyRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceReplyRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *ServiceReplyRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ServiceReplyRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ServiceReplyRequest) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ServiceReplyRequest) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type DockState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode                 int32   `protobuf:"varint,1,opt,name=mode,proto3" json:"mode,omitempty"`
	Longitude            float64 `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude             float64 `protobuf:"fixed64,3,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Height               float32 `protobuf:"fixed32,4,opt,name=height,proto3" json:"height,omitempty"`
	CoverState           int32   `protobuf:"varint,5,opt,name=coverState,proto3" json:"coverState,omitempty"`
	PutterState          int32   `protobuf:"varint,6,opt,name=putterState,proto3" json:"putterState,omitempty"`
	Temperature          float32 `protobuf:"fixed32,7,opt,name=temperature,proto3" json:"temperature,omitempty"`
	Humidity             float32 `protobuf:"fixed32,8,opt,name=humidity,proto3" json:"humidity,omitempty"`
	StorageTotal         int64   `protobuf:"varint,9,opt,name=storageTotal,proto3" json:"storageTotal,omitempty"`
	StorageUsed          int64   `protobuf:"varint,10,opt,name=storageUsed,proto3" json:"storageUsed,omitempty"`
	DroneOnlineState     int32   `protobuf:"varint,11,opt,name=droneOnlineState,proto3" json:"droneOnlineState,omitempty"`
	AirConditionerState  int32   `protobuf:"varint,12,opt,name=airConditionerState,proto3" json:"airConditionerState,omitempty"`
	SupplementLightState int32   `protobuf:"varint,13,opt,name=supplementLightState,proto3" json:"supplementLightState,omitempty"`
}

func (x *DockState) Reset() {
	*x = DockState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DockState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DockState) ProtoMessage() {}

func (x *DockState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DockState.ProtoReflect.Descriptor instead.
func (*DockState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{5}
}

func (x *DockState) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *DockState) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *DockState) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *DockState) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *DockState) GetCoverState() int32 {
	if x != nil {
		return x.CoverState
	}
	return 0
}

func (x *DockState) GetPutterState() int32 {
	if x != nil {
		return x.PutterState
	}
	return 0
}

func (x *DockState) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *DockState) GetHumidity() float32 {
	if x != nil {
		return x.Humidity
	}
	return 0
}

func (x *DockState) GetStorageTotal() int64 {
	if x != nil {
		return x.StorageTotal
	}
	return 0
}

func (x *DockState) GetStorageUsed() int64 {
	if x != nil {
		return x.StorageUsed
	}
	return 0
}

func (x *DockState) GetDroneOnlineState() int32 {
	if x != nil {
		return x.DroneOnlineState
	}
	return 0
}

func (x *DockState) GetAirConditionerState() int32 {
	if x != nil {
		return x.AirConditionerState
	}
	return 0
}

func (x *DockState) GetSupplementLightState() int32 {
	if x != nil {
		return x.SupplementLightState
	}
	return 0
}

type PositionState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsFixed       int32 `protobuf:"varint,1,opt,name=isFixed,proto3" json:"isFixed,omitempty"`
	IsCalibration int32 `protobuf:"varint,2,opt,name=isCalibration,proto3" json:"isCalibration,omitempty"`
	Quality       int32 `protobuf:"varint,3,opt,name=quality,proto3" json:"quality,omitempty"`
	GpsNumber     int32 `protobuf:"varint,4,opt,name=gpsNumber,proto3" json:"gpsNumber,omitempty"`
	RtkNumber     int32 `protobuf:"varint,5,opt,name=rtkNumber,proto3" json:"rtkNumber,omitempty"`
}

func (x *PositionState) Reset() {
	*x = PositionState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionState) ProtoMessage() {}

func (x *PositionState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionState.ProtoReflect.Descriptor instead.
func (*PositionState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{6}
}

func (x *PositionState) GetIsFixed() int32 {
	if x != nil {
		return x.IsFixed
	}
	return 0
}

func (x *PositionState) GetIsCalibration() int32 {
	if x != nil {
		return x.IsCalibration
	}
	return 0
}

func (x *PositionState) GetQuality() int32 {
	if x != nil {
		return x.Quality
	}
	return 0
}

func (x *PositionState) GetGpsNumber() int32 {
	if x != nil {
		return x.GpsNumber
	}
	return 0
}

func (x *PositionState) GetRtkNumber() int32 {
	if x != nil {
		return x.RtkNumber
	}
	return 0
}

type NetworkState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Quality int32 `protobuf:"varint,2,opt,name=quality,proto3" json:"quality,omitempty"`
}

func (x *NetworkState) Reset() {
	*x = NetworkState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkState) ProtoMessage() {}

func (x *NetworkState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkState.ProtoReflect.Descriptor instead.
func (*NetworkState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{7}
}

func (x *NetworkState) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *NetworkState) GetQuality() int32 {
	if x != nil {
		return x.Quality
	}
	return 0
}

type WirelessLinkState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode         int32 `protobuf:"varint,1,opt,name=mode,proto3" json:"mode,omitempty"`
	State4G      int32 `protobuf:"varint,2,opt,name=state4G,proto3" json:"state4G,omitempty"`
	StateSDR     int32 `protobuf:"varint,3,opt,name=stateSDR,proto3" json:"stateSDR,omitempty"`
	QualitySDR   int32 `protobuf:"varint,4,opt,name=qualitySDR,proto3" json:"qualitySDR,omitempty"`
	Quality4G    int32 `protobuf:"varint,5,opt,name=quality4G,proto3" json:"quality4G,omitempty"`
	Quality4GUav int32 `protobuf:"varint,6,opt,name=quality4GUav,proto3" json:"quality4GUav,omitempty"`
	Quality4GGnd int32 `protobuf:"varint,7,opt,name=quality4GGnd,proto3" json:"quality4GGnd,omitempty"`
}

func (x *WirelessLinkState) Reset() {
	*x = WirelessLinkState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WirelessLinkState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WirelessLinkState) ProtoMessage() {}

func (x *WirelessLinkState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WirelessLinkState.ProtoReflect.Descriptor instead.
func (*WirelessLinkState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{8}
}

func (x *WirelessLinkState) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *WirelessLinkState) GetState4G() int32 {
	if x != nil {
		return x.State4G
	}
	return 0
}

func (x *WirelessLinkState) GetStateSDR() int32 {
	if x != nil {
		return x.StateSDR
	}
	return 0
}

func (x *WirelessLinkState) GetQualitySDR() int32 {
	if x != nil {
		return x.QualitySDR
	}
	return 0
}

func (x *WirelessLinkState) GetQuality4G() int32 {
	if x != nil {
		return x.Quality4G
	}
	return 0
}

func (x *WirelessLinkState) GetQuality4GUav() int32 {
	if x != nil {
		return x.Quality4GUav
	}
	return 0
}

func (x *WirelessLinkState) GetQuality4GGnd() int32 {
	if x != nil {
		return x.Quality4GGnd
	}
	return 0
}

type FlightTaskState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count              int32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Longitude          float64 `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude           float64 `protobuf:"fixed64,3,opt,name=latitude,proto3" json:"latitude,omitempty"`
	SafeLandHeight     float32 `protobuf:"fixed32,4,opt,name=safeLandHeight,proto3" json:"safeLandHeight,omitempty"`
	SafeLandConfigured int32   `protobuf:"varint,5,opt,name=safeLandConfigured,proto3" json:"safeLandConfigured,omitempty"`
	DroneInDock        int32   `protobuf:"varint,6,opt,name=droneInDock,proto3" json:"droneInDock,omitempty"`
}

func (x *FlightTaskState) Reset() {
	*x = FlightTaskState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlightTaskState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlightTaskState) ProtoMessage() {}

func (x *FlightTaskState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlightTaskState.ProtoReflect.Descriptor instead.
func (*FlightTaskState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{9}
}

func (x *FlightTaskState) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *FlightTaskState) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *FlightTaskState) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *FlightTaskState) GetSafeLandHeight() float32 {
	if x != nil {
		return x.SafeLandHeight
	}
	return 0
}

func (x *FlightTaskState) GetSafeLandConfigured() int32 {
	if x != nil {
		return x.SafeLandConfigured
	}
	return 0
}

func (x *FlightTaskState) GetDroneInDock() int32 {
	if x != nil {
		return x.DroneInDock
	}
	return 0
}

type BatteryChargeState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DroneBatteryPercent     int32 `protobuf:"varint,1,opt,name=droneBatteryPercent,proto3" json:"droneBatteryPercent,omitempty"`
	DroneBatteryChargeState int32 `protobuf:"varint,2,opt,name=droneBatteryChargeState,proto3" json:"droneBatteryChargeState,omitempty"`
}

func (x *BatteryChargeState) Reset() {
	*x = BatteryChargeState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatteryChargeState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatteryChargeState) ProtoMessage() {}

func (x *BatteryChargeState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatteryChargeState.ProtoReflect.Descriptor instead.
func (*BatteryChargeState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{10}
}

func (x *BatteryChargeState) GetDroneBatteryPercent() int32 {
	if x != nil {
		return x.DroneBatteryPercent
	}
	return 0
}

func (x *BatteryChargeState) GetDroneBatteryChargeState() int32 {
	if x != nil {
		return x.DroneBatteryChargeState
	}
	return 0
}

type ElecPowerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkingVoltage           int32   `protobuf:"varint,1,opt,name=workingVoltage,proto3" json:"workingVoltage,omitempty"`
	WorkingCurrent           int32   `protobuf:"varint,2,opt,name=workingCurrent,proto3" json:"workingCurrent,omitempty"`
	DockBackupBatterySwitch  int32   `protobuf:"varint,3,opt,name=dockBackupBatterySwitch,proto3" json:"dockBackupBatterySwitch,omitempty"`
	DockBackupBatteryVoltage int32   `protobuf:"varint,4,opt,name=dockBackupBatteryVoltage,proto3" json:"dockBackupBatteryVoltage,omitempty"`
	DockBackupTemperature    float32 `protobuf:"fixed32,5,opt,name=dockBackupTemperature,proto3" json:"dockBackupTemperature,omitempty"`
	SupplyVoltage            int32   `protobuf:"varint,6,opt,name=supplyVoltage,proto3" json:"supplyVoltage,omitempty"`
}

func (x *ElecPowerState) Reset() {
	*x = ElecPowerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ElecPowerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ElecPowerState) ProtoMessage() {}

func (x *ElecPowerState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ElecPowerState.ProtoReflect.Descriptor instead.
func (*ElecPowerState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{11}
}

func (x *ElecPowerState) GetWorkingVoltage() int32 {
	if x != nil {
		return x.WorkingVoltage
	}
	return 0
}

func (x *ElecPowerState) GetWorkingCurrent() int32 {
	if x != nil {
		return x.WorkingCurrent
	}
	return 0
}

func (x *ElecPowerState) GetDockBackupBatterySwitch() int32 {
	if x != nil {
		return x.DockBackupBatterySwitch
	}
	return 0
}

func (x *ElecPowerState) GetDockBackupBatteryVoltage() int32 {
	if x != nil {
		return x.DockBackupBatteryVoltage
	}
	return 0
}

func (x *ElecPowerState) GetDockBackupTemperature() float32 {
	if x != nil {
		return x.DockBackupTemperature
	}
	return 0
}

func (x *ElecPowerState) GetSupplyVoltage() int32 {
	if x != nil {
		return x.SupplyVoltage
	}
	return 0
}

type StorageState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Used  int64 `protobuf:"varint,2,opt,name=used,proto3" json:"used,omitempty"`
}

func (x *StorageState) Reset() {
	*x = StorageState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StorageState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageState) ProtoMessage() {}

func (x *StorageState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageState.ProtoReflect.Descriptor instead.
func (*StorageState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{12}
}

func (x *StorageState) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *StorageState) GetUsed() int64 {
	if x != nil {
		return x.Used
	}
	return 0
}

type EnvironmentState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rainfall    int32   `protobuf:"varint,1,opt,name=rainfall,proto3" json:"rainfall,omitempty"`
	WindSpeed   float32 `protobuf:"fixed32,2,opt,name=windSpeed,proto3" json:"windSpeed,omitempty"`
	Temperature float32 `protobuf:"fixed32,3,opt,name=temperature,proto3" json:"temperature,omitempty"`
	Humidity    float32 `protobuf:"fixed32,4,opt,name=humidity,proto3" json:"humidity,omitempty"`
}

func (x *EnvironmentState) Reset() {
	*x = EnvironmentState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnvironmentState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvironmentState) ProtoMessage() {}

func (x *EnvironmentState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvironmentState.ProtoReflect.Descriptor instead.
func (*EnvironmentState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{13}
}

func (x *EnvironmentState) GetRainfall() int32 {
	if x != nil {
		return x.Rainfall
	}
	return 0
}

func (x *EnvironmentState) GetWindSpeed() float32 {
	if x != nil {
		return x.WindSpeed
	}
	return 0
}

func (x *EnvironmentState) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *EnvironmentState) GetHumidity() float32 {
	if x != nil {
		return x.Humidity
	}
	return 0
}

type DroneFlightState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HorizontalSpeed           float32 `protobuf:"fixed32,1,opt,name=horizontalSpeed,proto3" json:"horizontalSpeed,omitempty"`
	VerticalSpeed             float32 `protobuf:"fixed32,2,opt,name=verticalSpeed,proto3" json:"verticalSpeed,omitempty"`
	Longitude                 float64 `protobuf:"fixed64,3,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude                  float64 `protobuf:"fixed64,4,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Height                    float32 `protobuf:"fixed32,5,opt,name=height,proto3" json:"height,omitempty"`
	Elevation                 float32 `protobuf:"fixed32,6,opt,name=elevation,proto3" json:"elevation,omitempty"`
	AttitudePitch             float32 `protobuf:"fixed32,7,opt,name=attitudePitch,proto3" json:"attitudePitch,omitempty"`
	AttitudeRoll              float32 `protobuf:"fixed32,8,opt,name=attitudeRoll,proto3" json:"attitudeRoll,omitempty"`
	AttitudeHead              float32 `protobuf:"fixed32,9,opt,name=attitudeHead,proto3" json:"attitudeHead,omitempty"`
	TotalFlightTime           int64   `protobuf:"varint,10,opt,name=totalFlightTime,proto3" json:"totalFlightTime,omitempty"`
	WindSpeed                 float32 `protobuf:"fixed32,11,opt,name=windSpeed,proto3" json:"windSpeed,omitempty"`
	WindDirection             int32   `protobuf:"varint,12,opt,name=windDirection,proto3" json:"windDirection,omitempty"`
	IsNearHeightLimit         int32   `protobuf:"varint,13,opt,name=isNearHeightLimit,proto3" json:"isNearHeightLimit,omitempty"`
	IsNearAreaLimit           int32   `protobuf:"varint,14,opt,name=isNearAreaLimit,proto3" json:"isNearAreaLimit,omitempty"`
	IsNearIDistanceLimit      int32   `protobuf:"varint,15,opt,name=isNearIDistanceLimit,proto3" json:"isNearIDistanceLimit,omitempty"`
	NightLightsState          int32   `protobuf:"varint,16,opt,name=nightLightsState,proto3" json:"nightLightsState,omitempty"`
	HorizonObstacleAvoidance  int32   `protobuf:"varint,17,opt,name=horizonObstacleAvoidance,proto3" json:"horizonObstacleAvoidance,omitempty"`
	UpsideObstacleAvoidance   int32   `protobuf:"varint,18,opt,name=upsideObstacleAvoidance,proto3" json:"upsideObstacleAvoidance,omitempty"`
	DownsideObstacleAvoidance int32   `protobuf:"varint,19,opt,name=downsideObstacleAvoidance,proto3" json:"downsideObstacleAvoidance,omitempty"`
}

func (x *DroneFlightState) Reset() {
	*x = DroneFlightState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DroneFlightState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DroneFlightState) ProtoMessage() {}

func (x *DroneFlightState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DroneFlightState.ProtoReflect.Descriptor instead.
func (*DroneFlightState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{14}
}

func (x *DroneFlightState) GetHorizontalSpeed() float32 {
	if x != nil {
		return x.HorizontalSpeed
	}
	return 0
}

func (x *DroneFlightState) GetVerticalSpeed() float32 {
	if x != nil {
		return x.VerticalSpeed
	}
	return 0
}

func (x *DroneFlightState) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *DroneFlightState) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *DroneFlightState) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *DroneFlightState) GetElevation() float32 {
	if x != nil {
		return x.Elevation
	}
	return 0
}

func (x *DroneFlightState) GetAttitudePitch() float32 {
	if x != nil {
		return x.AttitudePitch
	}
	return 0
}

func (x *DroneFlightState) GetAttitudeRoll() float32 {
	if x != nil {
		return x.AttitudeRoll
	}
	return 0
}

func (x *DroneFlightState) GetAttitudeHead() float32 {
	if x != nil {
		return x.AttitudeHead
	}
	return 0
}

func (x *DroneFlightState) GetTotalFlightTime() int64 {
	if x != nil {
		return x.TotalFlightTime
	}
	return 0
}

func (x *DroneFlightState) GetWindSpeed() float32 {
	if x != nil {
		return x.WindSpeed
	}
	return 0
}

func (x *DroneFlightState) GetWindDirection() int32 {
	if x != nil {
		return x.WindDirection
	}
	return 0
}

func (x *DroneFlightState) GetIsNearHeightLimit() int32 {
	if x != nil {
		return x.IsNearHeightLimit
	}
	return 0
}

func (x *DroneFlightState) GetIsNearAreaLimit() int32 {
	if x != nil {
		return x.IsNearAreaLimit
	}
	return 0
}

func (x *DroneFlightState) GetIsNearIDistanceLimit() int32 {
	if x != nil {
		return x.IsNearIDistanceLimit
	}
	return 0
}

func (x *DroneFlightState) GetNightLightsState() int32 {
	if x != nil {
		return x.NightLightsState
	}
	return 0
}

func (x *DroneFlightState) GetHorizonObstacleAvoidance() int32 {
	if x != nil {
		return x.HorizonObstacleAvoidance
	}
	return 0
}

func (x *DroneFlightState) GetUpsideObstacleAvoidance() int32 {
	if x != nil {
		return x.UpsideObstacleAvoidance
	}
	return 0
}

func (x *DroneFlightState) GetDownsideObstacleAvoidance() int32 {
	if x != nil {
		return x.DownsideObstacleAvoidance
	}
	return 0
}

type DroneBatteryState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CapacityPercent  int32                        `protobuf:"varint,1,opt,name=capacityPercent,proto3" json:"capacityPercent,omitempty"`
	RemainFlightTime int32                        `protobuf:"varint,2,opt,name=remainFlightTime,proto3" json:"remainFlightTime,omitempty"`
	ReturnHomePower  int32                        `protobuf:"varint,3,opt,name=returnHomePower,proto3" json:"returnHomePower,omitempty"`
	LandingPower     int32                        `protobuf:"varint,4,opt,name=landingPower,proto3" json:"landingPower,omitempty"`
	Batteries        []*DroneBatteryState_Battery `protobuf:"bytes,5,rep,name=batteries,proto3" json:"batteries,omitempty"`
}

func (x *DroneBatteryState) Reset() {
	*x = DroneBatteryState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DroneBatteryState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DroneBatteryState) ProtoMessage() {}

func (x *DroneBatteryState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DroneBatteryState.ProtoReflect.Descriptor instead.
func (*DroneBatteryState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{15}
}

func (x *DroneBatteryState) GetCapacityPercent() int32 {
	if x != nil {
		return x.CapacityPercent
	}
	return 0
}

func (x *DroneBatteryState) GetRemainFlightTime() int32 {
	if x != nil {
		return x.RemainFlightTime
	}
	return 0
}

func (x *DroneBatteryState) GetReturnHomePower() int32 {
	if x != nil {
		return x.ReturnHomePower
	}
	return 0
}

func (x *DroneBatteryState) GetLandingPower() int32 {
	if x != nil {
		return x.LandingPower
	}
	return 0
}

func (x *DroneBatteryState) GetBatteries() []*DroneBatteryState_Battery {
	if x != nil {
		return x.Batteries
	}
	return nil
}

type FlightTaskProgressEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlightId             string           `protobuf:"bytes,1,opt,name=flightId,proto3" json:"flightId,omitempty"`
	CurrentWaypointIndex int32            `protobuf:"varint,3,opt,name=currentWaypointIndex,proto3" json:"currentWaypointIndex,omitempty"`
	Status               int32            `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	Percent              int32            `protobuf:"varint,5,opt,name=percent,proto3" json:"percent,omitempty"`
	Extra                *structpb.Struct `protobuf:"bytes,6,opt,name=extra,proto3" json:"extra,omitempty"`
	Step                 int32            `protobuf:"varint,7,opt,name=step,proto3" json:"step,omitempty"`
	VoyageState          int32            `protobuf:"varint,8,opt,name=voyageState,proto3" json:"voyageState,omitempty"`
	BreakReason          int32            `protobuf:"varint,9,opt,name=breakReason,proto3" json:"breakReason,omitempty"`
}

func (x *FlightTaskProgressEvent) Reset() {
	*x = FlightTaskProgressEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlightTaskProgressEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlightTaskProgressEvent) ProtoMessage() {}

func (x *FlightTaskProgressEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlightTaskProgressEvent.ProtoReflect.Descriptor instead.
func (*FlightTaskProgressEvent) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{16}
}

func (x *FlightTaskProgressEvent) GetFlightId() string {
	if x != nil {
		return x.FlightId
	}
	return ""
}

func (x *FlightTaskProgressEvent) GetCurrentWaypointIndex() int32 {
	if x != nil {
		return x.CurrentWaypointIndex
	}
	return 0
}

func (x *FlightTaskProgressEvent) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *FlightTaskProgressEvent) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *FlightTaskProgressEvent) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *FlightTaskProgressEvent) GetStep() int32 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (x *FlightTaskProgressEvent) GetVoyageState() int32 {
	if x != nil {
		return x.VoyageState
	}
	return 0
}

func (x *FlightTaskProgressEvent) GetBreakReason() int32 {
	if x != nil {
		return x.BreakReason
	}
	return 0
}

type DockHealMonitorEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level    int32            `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Module   int32            `protobuf:"varint,2,opt,name=module,proto3" json:"module,omitempty"`
	InTheSky int32            `protobuf:"varint,3,opt,name=inTheSky,proto3" json:"inTheSky,omitempty"`
	Imminent int32            `protobuf:"varint,4,opt,name=imminent,proto3" json:"imminent,omitempty"`
	Code     string           `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	Extra    *structpb.Struct `protobuf:"bytes,6,opt,name=extra,proto3" json:"extra,omitempty"`
	Source   int32            `protobuf:"varint,7,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *DockHealMonitorEvent) Reset() {
	*x = DockHealMonitorEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DockHealMonitorEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DockHealMonitorEvent) ProtoMessage() {}

func (x *DockHealMonitorEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DockHealMonitorEvent.ProtoReflect.Descriptor instead.
func (*DockHealMonitorEvent) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{17}
}

func (x *DockHealMonitorEvent) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *DockHealMonitorEvent) GetModule() int32 {
	if x != nil {
		return x.Module
	}
	return 0
}

func (x *DockHealMonitorEvent) GetInTheSky() int32 {
	if x != nil {
		return x.InTheSky
	}
	return 0
}

func (x *DockHealMonitorEvent) GetImminent() int32 {
	if x != nil {
		return x.Imminent
	}
	return 0
}

func (x *DockHealMonitorEvent) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DockHealMonitorEvent) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *DockHealMonitorEvent) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

type DockTopoUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceModel string                          `protobuf:"bytes,1,opt,name=deviceModel,proto3" json:"deviceModel,omitempty"`
	Subdevices  []*DockTopoUpdateEventSubdevice `protobuf:"bytes,2,rep,name=subdevices,proto3" json:"subdevices,omitempty"`
}

func (x *DockTopoUpdateEvent) Reset() {
	*x = DockTopoUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DockTopoUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DockTopoUpdateEvent) ProtoMessage() {}

func (x *DockTopoUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DockTopoUpdateEvent.ProtoReflect.Descriptor instead.
func (*DockTopoUpdateEvent) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{18}
}

func (x *DockTopoUpdateEvent) GetDeviceModel() string {
	if x != nil {
		return x.DeviceModel
	}
	return ""
}

func (x *DockTopoUpdateEvent) GetSubdevices() []*DockTopoUpdateEventSubdevice {
	if x != nil {
		return x.Subdevices
	}
	return nil
}

type DockFlightTaskResourceRequestEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlightId string `protobuf:"bytes,1,opt,name=flightId,proto3" json:"flightId,omitempty"`
}

func (x *DockFlightTaskResourceRequestEvent) Reset() {
	*x = DockFlightTaskResourceRequestEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DockFlightTaskResourceRequestEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DockFlightTaskResourceRequestEvent) ProtoMessage() {}

func (x *DockFlightTaskResourceRequestEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DockFlightTaskResourceRequestEvent.ProtoReflect.Descriptor instead.
func (*DockFlightTaskResourceRequestEvent) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{19}
}

func (x *DockFlightTaskResourceRequestEvent) GetFlightId() string {
	if x != nil {
		return x.FlightId
	}
	return ""
}

type Version struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Firmware string `protobuf:"bytes,1,opt,name=firmware,proto3" json:"firmware,omitempty"`
}

func (x *Version) Reset() {
	*x = Version{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Version) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Version) ProtoMessage() {}

func (x *Version) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Version.ProtoReflect.Descriptor instead.
func (*Version) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{20}
}

func (x *Version) GetFirmware() string {
	if x != nil {
		return x.Firmware
	}
	return ""
}

type PilotState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CapacityPercent int32   `protobuf:"varint,1,opt,name=capacityPercent,proto3" json:"capacityPercent,omitempty"`
	Longitude       float64 `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude        float64 `protobuf:"fixed64,3,opt,name=latitude,proto3" json:"latitude,omitempty"`
}

func (x *PilotState) Reset() {
	*x = PilotState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PilotState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PilotState) ProtoMessage() {}

func (x *PilotState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PilotState.ProtoReflect.Descriptor instead.
func (*PilotState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{21}
}

func (x *PilotState) GetCapacityPercent() int32 {
	if x != nil {
		return x.CapacityPercent
	}
	return 0
}

func (x *PilotState) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *PilotState) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

type DroneSpeakerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index             string `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Name              string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Sn                string `protobuf:"bytes,3,opt,name=sn,proto3" json:"sn,omitempty"`
	FirmwareVersion   string `protobuf:"bytes,4,opt,name=firmwareVersion,proto3" json:"firmwareVersion,omitempty"`
	LibVersion        string `protobuf:"bytes,5,opt,name=libVersion,proto3" json:"libVersion,omitempty"`
	Type              string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	WorkMode          int32  `protobuf:"varint,7,opt,name=workMode,proto3" json:"workMode,omitempty"`
	SystemState       int32  `protobuf:"varint,8,opt,name=systemState,proto3" json:"systemState,omitempty"`
	PlayMode          int32  `protobuf:"varint,9,opt,name=playMode,proto3" json:"playMode,omitempty"`
	PlayVolume        int32  `protobuf:"varint,10,opt,name=playVolume,proto3" json:"playVolume,omitempty"`
	PlayFileName      string `protobuf:"bytes,11,opt,name=playFileName,proto3" json:"playFileName,omitempty"`
	PlayFileSignature string `protobuf:"bytes,12,opt,name=playFileSignature,proto3" json:"playFileSignature,omitempty"`
}

func (x *DroneSpeakerState) Reset() {
	*x = DroneSpeakerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DroneSpeakerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DroneSpeakerState) ProtoMessage() {}

func (x *DroneSpeakerState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DroneSpeakerState.ProtoReflect.Descriptor instead.
func (*DroneSpeakerState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{22}
}

func (x *DroneSpeakerState) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *DroneSpeakerState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DroneSpeakerState) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *DroneSpeakerState) GetFirmwareVersion() string {
	if x != nil {
		return x.FirmwareVersion
	}
	return ""
}

func (x *DroneSpeakerState) GetLibVersion() string {
	if x != nil {
		return x.LibVersion
	}
	return ""
}

func (x *DroneSpeakerState) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DroneSpeakerState) GetWorkMode() int32 {
	if x != nil {
		return x.WorkMode
	}
	return 0
}

func (x *DroneSpeakerState) GetSystemState() int32 {
	if x != nil {
		return x.SystemState
	}
	return 0
}

func (x *DroneSpeakerState) GetPlayMode() int32 {
	if x != nil {
		return x.PlayMode
	}
	return 0
}

func (x *DroneSpeakerState) GetPlayVolume() int32 {
	if x != nil {
		return x.PlayVolume
	}
	return 0
}

func (x *DroneSpeakerState) GetPlayFileName() string {
	if x != nil {
		return x.PlayFileName
	}
	return ""
}

func (x *DroneSpeakerState) GetPlayFileSignature() string {
	if x != nil {
		return x.PlayFileSignature
	}
	return ""
}

type DroneCameraState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index                  string                          `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Mode                   int32                           `protobuf:"varint,2,opt,name=mode,proto3" json:"mode,omitempty"`
	PhotoState             int32                           `protobuf:"varint,3,opt,name=photoState,proto3" json:"photoState,omitempty"`
	RecordingState         int32                           `protobuf:"varint,4,opt,name=recordingState,proto3" json:"recordingState,omitempty"`
	ZoomFactor             float32                         `protobuf:"fixed32,5,opt,name=zoomFactor,proto3" json:"zoomFactor,omitempty"`
	IrZoomFactor           float32                         `protobuf:"fixed32,6,opt,name=irZoomFactor,proto3" json:"irZoomFactor,omitempty"`
	ZoomFocusMode          int32                           `protobuf:"varint,7,opt,name=zoomFocusMode,proto3" json:"zoomFocusMode,omitempty"`
	ZoomFocusValue         int32                           `protobuf:"varint,8,opt,name=zoomFocusValue,proto3" json:"zoomFocusValue,omitempty"`
	ZoomFocusState         int32                           `protobuf:"varint,9,opt,name=zoomFocusState,proto3" json:"zoomFocusState,omitempty"`
	IrMeteringMode         int32                           `protobuf:"varint,10,opt,name=irMeteringMode,proto3" json:"irMeteringMode,omitempty"`
	GimbalPitch            float64                         `protobuf:"fixed64,11,opt,name=gimbalPitch,proto3" json:"gimbalPitch,omitempty"`
	GimbalYaw              float64                         `protobuf:"fixed64,12,opt,name=gimbalYaw,proto3" json:"gimbalYaw,omitempty"`
	GimbalRoll             float64                         `protobuf:"fixed64,13,opt,name=gimbalRoll,proto3" json:"gimbalRoll,omitempty"`
	MeasureTargetLongitude float64                         `protobuf:"fixed64,14,opt,name=measureTargetLongitude,proto3" json:"measureTargetLongitude,omitempty"`
	MeasureTargetLatitude  float64                         `protobuf:"fixed64,15,opt,name=measureTargetLatitude,proto3" json:"measureTargetLatitude,omitempty"`
	MeasureTargetAltitude  float64                         `protobuf:"fixed64,16,opt,name=measureTargetAltitude,proto3" json:"measureTargetAltitude,omitempty"`
	MeasureTargetDistance  float64                         `protobuf:"fixed64,17,opt,name=measureTargetDistance,proto3" json:"measureTargetDistance,omitempty"`
	MeasureErrState        int32                           `protobuf:"varint,18,opt,name=measureErrState,proto3" json:"measureErrState,omitempty"`
	LiveViewWorldRegion    *DroneCameraLiveViewWorldRegion `protobuf:"bytes,19,opt,name=liveViewWorldRegion,proto3" json:"liveViewWorldRegion,omitempty"`
}

func (x *DroneCameraState) Reset() {
	*x = DroneCameraState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DroneCameraState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DroneCameraState) ProtoMessage() {}

func (x *DroneCameraState) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DroneCameraState.ProtoReflect.Descriptor instead.
func (*DroneCameraState) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{23}
}

func (x *DroneCameraState) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *DroneCameraState) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *DroneCameraState) GetPhotoState() int32 {
	if x != nil {
		return x.PhotoState
	}
	return 0
}

func (x *DroneCameraState) GetRecordingState() int32 {
	if x != nil {
		return x.RecordingState
	}
	return 0
}

func (x *DroneCameraState) GetZoomFactor() float32 {
	if x != nil {
		return x.ZoomFactor
	}
	return 0
}

func (x *DroneCameraState) GetIrZoomFactor() float32 {
	if x != nil {
		return x.IrZoomFactor
	}
	return 0
}

func (x *DroneCameraState) GetZoomFocusMode() int32 {
	if x != nil {
		return x.ZoomFocusMode
	}
	return 0
}

func (x *DroneCameraState) GetZoomFocusValue() int32 {
	if x != nil {
		return x.ZoomFocusValue
	}
	return 0
}

func (x *DroneCameraState) GetZoomFocusState() int32 {
	if x != nil {
		return x.ZoomFocusState
	}
	return 0
}

func (x *DroneCameraState) GetIrMeteringMode() int32 {
	if x != nil {
		return x.IrMeteringMode
	}
	return 0
}

func (x *DroneCameraState) GetGimbalPitch() float64 {
	if x != nil {
		return x.GimbalPitch
	}
	return 0
}

func (x *DroneCameraState) GetGimbalYaw() float64 {
	if x != nil {
		return x.GimbalYaw
	}
	return 0
}

func (x *DroneCameraState) GetGimbalRoll() float64 {
	if x != nil {
		return x.GimbalRoll
	}
	return 0
}

func (x *DroneCameraState) GetMeasureTargetLongitude() float64 {
	if x != nil {
		return x.MeasureTargetLongitude
	}
	return 0
}

func (x *DroneCameraState) GetMeasureTargetLatitude() float64 {
	if x != nil {
		return x.MeasureTargetLatitude
	}
	return 0
}

func (x *DroneCameraState) GetMeasureTargetAltitude() float64 {
	if x != nil {
		return x.MeasureTargetAltitude
	}
	return 0
}

func (x *DroneCameraState) GetMeasureTargetDistance() float64 {
	if x != nil {
		return x.MeasureTargetDistance
	}
	return 0
}

func (x *DroneCameraState) GetMeasureErrState() int32 {
	if x != nil {
		return x.MeasureErrState
	}
	return 0
}

func (x *DroneCameraState) GetLiveViewWorldRegion() *DroneCameraLiveViewWorldRegion {
	if x != nil {
		return x.LiveViewWorldRegion
	}
	return nil
}

type DroneCameraLiveViewWorldRegion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Top    float64 `protobuf:"fixed64,1,opt,name=top,proto3" json:"top,omitempty"`
	Bottom float64 `protobuf:"fixed64,2,opt,name=bottom,proto3" json:"bottom,omitempty"`
	Left   float64 `protobuf:"fixed64,3,opt,name=left,proto3" json:"left,omitempty"`
	Right  float64 `protobuf:"fixed64,4,opt,name=right,proto3" json:"right,omitempty"`
}

func (x *DroneCameraLiveViewWorldRegion) Reset() {
	*x = DroneCameraLiveViewWorldRegion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DroneCameraLiveViewWorldRegion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DroneCameraLiveViewWorldRegion) ProtoMessage() {}

func (x *DroneCameraLiveViewWorldRegion) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DroneCameraLiveViewWorldRegion.ProtoReflect.Descriptor instead.
func (*DroneCameraLiveViewWorldRegion) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{24}
}

func (x *DroneCameraLiveViewWorldRegion) GetTop() float64 {
	if x != nil {
		return x.Top
	}
	return 0
}

func (x *DroneCameraLiveViewWorldRegion) GetBottom() float64 {
	if x != nil {
		return x.Bottom
	}
	return 0
}

func (x *DroneCameraLiveViewWorldRegion) GetLeft() float64 {
	if x != nil {
		return x.Left
	}
	return 0
}

func (x *DroneCameraLiveViewWorldRegion) GetRight() float64 {
	if x != nil {
		return x.Right
	}
	return 0
}

type CommonReplyCommonData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CommonReplyCommonData) Reset() {
	*x = CommonReplyCommonData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReplyCommonData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReplyCommonData) ProtoMessage() {}

func (x *CommonReplyCommonData) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReplyCommonData.ProtoReflect.Descriptor instead.
func (*CommonReplyCommonData) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CommonReplyCommonData) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type DroneBatteryState_Battery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index           int32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	CapacityPercent int32   `protobuf:"varint,2,opt,name=capacityPercent,proto3" json:"capacityPercent,omitempty"`
	Voltage         int32   `protobuf:"varint,3,opt,name=voltage,proto3" json:"voltage,omitempty"`
	Temperature     float32 `protobuf:"fixed32,4,opt,name=temperature,proto3" json:"temperature,omitempty"`
	Loop            int32   `protobuf:"varint,5,opt,name=loop,proto3" json:"loop,omitempty"`
	Sn              string  `protobuf:"bytes,6,opt,name=sn,proto3" json:"sn,omitempty"`
}

func (x *DroneBatteryState_Battery) Reset() {
	*x = DroneBatteryState_Battery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DroneBatteryState_Battery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DroneBatteryState_Battery) ProtoMessage() {}

func (x *DroneBatteryState_Battery) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DroneBatteryState_Battery.ProtoReflect.Descriptor instead.
func (*DroneBatteryState_Battery) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{15, 0}
}

func (x *DroneBatteryState_Battery) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *DroneBatteryState_Battery) GetCapacityPercent() int32 {
	if x != nil {
		return x.CapacityPercent
	}
	return 0
}

func (x *DroneBatteryState_Battery) GetVoltage() int32 {
	if x != nil {
		return x.Voltage
	}
	return 0
}

func (x *DroneBatteryState_Battery) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *DroneBatteryState_Battery) GetLoop() int32 {
	if x != nil {
		return x.Loop
	}
	return 0
}

func (x *DroneBatteryState_Battery) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

type DockTopoUpdateEventSubdevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn     string           `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn,omitempty"`
	Type   string           `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Index  string           `protobuf:"bytes,3,opt,name=index,proto3" json:"index,omitempty"`
	Extra  *structpb.Struct `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	Domain int32            `protobuf:"varint,5,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *DockTopoUpdateEventSubdevice) Reset() {
	*x = DockTopoUpdateEventSubdevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connects_v1_connect_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DockTopoUpdateEventSubdevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DockTopoUpdateEventSubdevice) ProtoMessage() {}

func (x *DockTopoUpdateEventSubdevice) ProtoReflect() protoreflect.Message {
	mi := &file_api_connects_v1_connect_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DockTopoUpdateEventSubdevice.ProtoReflect.Descriptor instead.
func (*DockTopoUpdateEventSubdevice) Descriptor() ([]byte, []int) {
	return file_api_connects_v1_connect_proto_rawDescGZIP(), []int{18, 0}
}

func (x *DockTopoUpdateEventSubdevice) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *DockTopoUpdateEventSubdevice) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DockTopoUpdateEventSubdevice) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *DockTopoUpdateEventSubdevice) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *DockTopoUpdateEventSubdevice) GetDomain() int32 {
	if x != nil {
		return x.Domain
	}
	return 0
}

var File_api_connects_v1_connect_proto protoreflect.FileDescriptor

var file_api_connects_v1_connect_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x24, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xdb, 0x03, 0x0a, 0x0e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x55, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x73,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x78, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x78, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x40,
	0x0a, 0x06, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x37, 0x0a, 0x03, 0x68, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x6c, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x68, 0x6d, 0x73, 0x12, 0x38, 0x0a, 0x04, 0x74, 0x6f, 0x70,
	0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x63, 0x6b, 0x54, 0x6f,
	0x70, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x74,
	0x6f, 0x70, 0x6f, 0x12, 0x55, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x63, 0x6b, 0x46,
	0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x69, 0x0a, 0x0c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x55,
	0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0xf7, 0x08, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x55, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x02, 0x73, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x53,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x53, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x6f, 0x63, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x6f,
	0x63, 0x6b, 0x12, 0x31, 0x0a, 0x05, 0x70, 0x69, 0x6c, 0x6f, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x6c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x70, 0x69, 0x6c, 0x6f, 0x74, 0x12, 0x37, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x3a,
	0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x03, 0x65, 0x6e,
	0x76, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f,
	0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12,
	0x40, 0x0a, 0x0a, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x12, 0x33, 0x0a, 0x04, 0x65, 0x6c, 0x65, 0x63, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6c, 0x65, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x04, 0x65, 0x6c, 0x65, 0x63, 0x12, 0x37, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12,
	0x39, 0x0a, 0x06, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x06, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x12, 0x3c, 0x0a, 0x07, 0x62, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x72,
	0x6f, 0x6e, 0x65, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x07, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x77, 0x69, 0x72, 0x65, 0x6c, 0x65, 0x73,
	0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69,
	0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0c, 0x77, 0x69, 0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x32, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x2d, 0x0a, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x12, 0x3c, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x3b,
	0x0a, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x22, 0xc5, 0x02, 0x0a, 0x13,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x73, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x72, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x22, 0xc9, 0x03, 0x0a, 0x09, 0x44, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x75, 0x74, 0x74, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x75,
	0x74, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b,
	0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68,
	0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x68,
	0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x55, 0x73, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x55, 0x73, 0x65, 0x64, 0x12, 0x2a, 0x0a,
	0x10, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x69, 0x72,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x61, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x73,
	0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x73, 0x75, 0x70, 0x70, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22,
	0xa5, 0x01, 0x0a, 0x0d, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x46, 0x69, 0x78, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x69, 0x73, 0x46, 0x69, 0x78, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x69,
	0x73, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x69, 0x73, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x67,
	0x70, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x67, 0x70, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x74, 0x6b,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x74,
	0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x3c, 0x0a, 0x0c, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x71,
	0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x71, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x22, 0xe3, 0x01, 0x0a, 0x11, 0x57, 0x69, 0x72, 0x65, 0x6c, 0x65,
	0x73, 0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x74, 0x65, 0x34, 0x47, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x74, 0x65, 0x34, 0x47, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x53, 0x44, 0x52, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x53, 0x44, 0x52, 0x12, 0x1e, 0x0a, 0x0a, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x53, 0x44, 0x52, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x53, 0x44, 0x52, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x34, 0x47, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x34, 0x47, 0x12, 0x22, 0x0a, 0x0c, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x34, 0x47,
	0x55, 0x61, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x34, 0x47, 0x55, 0x61, 0x76, 0x12, 0x22, 0x0a, 0x0c, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x34, 0x47, 0x47, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x71,
	0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x34, 0x47, 0x47, 0x6e, 0x64, 0x22, 0xdb, 0x01, 0x0a, 0x0f,
	0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x73, 0x61, 0x66, 0x65, 0x4c, 0x61, 0x6e, 0x64, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x73, 0x61, 0x66, 0x65, 0x4c, 0x61, 0x6e,
	0x64, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x61, 0x66, 0x65, 0x4c,
	0x61, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x12, 0x73, 0x61, 0x66, 0x65, 0x4c, 0x61, 0x6e, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x72, 0x6f, 0x6e, 0x65,
	0x49, 0x6e, 0x44, 0x6f, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x72,
	0x6f, 0x6e, 0x65, 0x49, 0x6e, 0x44, 0x6f, 0x63, 0x6b, 0x22, 0x80, 0x01, 0x0a, 0x12, 0x42, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x30, 0x0a, 0x13, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x64,
	0x72, 0x6f, 0x6e, 0x65, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x12, 0x38, 0x0a, 0x17, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x42, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x17, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0xb2, 0x02, 0x0a,
	0x0e, 0x45, 0x6c, 0x65, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x38, 0x0a, 0x17, 0x64, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x42, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x17, 0x64, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x42, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x3a, 0x0a, 0x18, 0x64, 0x6f, 0x63,
	0x6b, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x56, 0x6f,
	0x6c, 0x74, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x64, 0x6f, 0x63,
	0x6b, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x56, 0x6f,
	0x6c, 0x74, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x64, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x63,
	0x6b, 0x75, 0x70, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x64, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73,
	0x75, 0x70, 0x70, 0x6c, 0x79, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x79, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67,
	0x65, 0x22, 0x38, 0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x10,
	0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x69, 0x6e, 0x66, 0x61, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x69, 0x6e, 0x66, 0x61, 0x6c, 0x6c, 0x12, 0x1c, 0x0a, 0x09,
	0x77, 0x69, 0x6e, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x09, 0x77, 0x69, 0x6e, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08,
	0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x22, 0x9a, 0x06, 0x0a, 0x10, 0x44, 0x72, 0x6f,
	0x6e, 0x65, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74,
	0x61, 0x6c, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x63, 0x61, 0x6c, 0x53, 0x70, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x09, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a,
	0x0d, 0x61, 0x74, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x50, 0x69, 0x74, 0x63, 0x68, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x50, 0x69,
	0x74, 0x63, 0x68, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x52,
	0x6f, 0x6c, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x52, 0x6f, 0x6c, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x48, 0x65, 0x61, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x61,
	0x74, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x48, 0x65, 0x61, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x6c, 0x69, 0x67, 0x68,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69, 0x6e, 0x64, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x77, 0x69, 0x6e, 0x64, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x69, 0x6e, 0x64, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x77, 0x69, 0x6e, 0x64,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x73, 0x4e,
	0x65, 0x61, 0x72, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x69, 0x73, 0x4e, 0x65, 0x61, 0x72, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x73, 0x4e, 0x65, 0x61,
	0x72, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x69, 0x73, 0x4e, 0x65, 0x61, 0x72, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x32, 0x0a, 0x14, 0x69, 0x73, 0x4e, 0x65, 0x61, 0x72, 0x49, 0x44, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x14, 0x69, 0x73, 0x4e, 0x65, 0x61, 0x72, 0x49, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x4c, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x3a, 0x0a, 0x18, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x4f, 0x62, 0x73, 0x74,
	0x61, 0x63, 0x6c, 0x65, 0x41, 0x76, 0x6f, 0x69, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x18, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x4f, 0x62, 0x73, 0x74,
	0x61, 0x63, 0x6c, 0x65, 0x41, 0x76, 0x6f, 0x69, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x38, 0x0a,
	0x17, 0x75, 0x70, 0x73, 0x69, 0x64, 0x65, 0x4f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x41,
	0x76, 0x6f, 0x69, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x17,
	0x75, 0x70, 0x73, 0x69, 0x64, 0x65, 0x4f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x41, 0x76,
	0x6f, 0x69, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3c, 0x0a, 0x19, 0x64, 0x6f, 0x77, 0x6e, 0x73,
	0x69, 0x64, 0x65, 0x4f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x41, 0x76, 0x6f, 0x69, 0x64,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x64, 0x6f, 0x77, 0x6e,
	0x73, 0x69, 0x64, 0x65, 0x4f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x41, 0x76, 0x6f, 0x69,
	0x64, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xad, 0x03, 0x0a, 0x11, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x50, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x46,
	0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x48, 0x6f, 0x6d, 0x65, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x6c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12,
	0x48, 0x0a, 0x09, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x09,
	0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x69, 0x65, 0x73, 0x1a, 0xa9, 0x01, 0x0a, 0x07, 0x42, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x28, 0x0a, 0x0f, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x50, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x6f, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x6c, 0x6f, 0x6f, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x73, 0x6e, 0x22, 0xa2, 0x02, 0x0a, 0x17, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x49, 0x64, 0x12, 0x32, 0x0a,
	0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x76, 0x6f, 0x79,
	0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x72, 0x65, 0x61,
	0x6b, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62,
	0x72, 0x65, 0x61, 0x6b, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xd7, 0x01, 0x0a, 0x14, 0x44,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x6c, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x54, 0x68, 0x65, 0x53, 0x6b, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x54, 0x68, 0x65, 0x53, 0x6b, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x69, 0x6d, 0x6d, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x69, 0x6d, 0x6d, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x22, 0x96, 0x02, 0x0a, 0x13, 0x44, 0x6f, 0x63, 0x6b, 0x54, 0x6f, 0x70,
	0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x4e,
	0x0a, 0x0a, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x63, 0x6b, 0x54, 0x6f, 0x70, 0x6f, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x8c,
	0x01, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x73, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2d, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x40, 0x0a,
	0x22, 0x44, 0x6f, 0x63, 0x6b, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x49, 0x64, 0x22,
	0x25, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69,
	0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x22, 0x70, 0x0a, 0x0a, 0x50, 0x69, 0x6c, 0x6f, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08,
	0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x22, 0xf7, 0x02, 0x0a, 0x11, 0x44, 0x72, 0x6f,
	0x6e, 0x65, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x6d,
	0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x4d, 0x6f,
	0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x69, 0x6c, 0x65,
	0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x22, 0xad, 0x06, 0x0a, 0x10, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x7a, 0x6f, 0x6f,
	0x6d, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x7a,
	0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x72, 0x5a,
	0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0c, 0x69, 0x72, 0x5a, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x24, 0x0a,
	0x0d, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x7a, 0x6f, 0x6f,
	0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x7a,
	0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x72, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x69, 0x72, 0x4d,
	0x65, 0x74, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x50, 0x69, 0x74, 0x63, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x50, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1c, 0x0a,
	0x09, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x59, 0x61, 0x77, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x59, 0x61, 0x77, 0x12, 0x1e, 0x0a, 0x0a, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x52, 0x6f, 0x6c, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x52, 0x6f, 0x6c, 0x6c, 0x12, 0x36, 0x0a, 0x16, 0x6d,
	0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x6f, 0x6e, 0x67,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x6d, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x15, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x6d, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x6c, 0x74, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72,
	0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x34, 0x0a, 0x15, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15,
	0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x45, 0x72, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x45, 0x72, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x61, 0x0a, 0x13, 0x6c, 0x69, 0x76, 0x65, 0x56, 0x69, 0x65, 0x77, 0x57, 0x6f, 0x72, 0x6c, 0x64,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x72, 0x6f, 0x6e, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x76, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x6c,
	0x69, 0x76, 0x65, 0x56, 0x69, 0x65, 0x77, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x22, 0x74, 0x0a, 0x1e, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x4c, 0x69, 0x76, 0x65, 0x56, 0x69, 0x65, 0x77, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x6f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x74, 0x6f, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x12, 0x12,
	0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x6c, 0x65,
	0x66, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x32, 0x82, 0x03, 0x0a, 0x07, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x12, 0x76, 0x0a, 0x07, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x12,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x1a, 0x20, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b,
	0x73, 0x6e, 0x7d, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x7e, 0x0a, 0x0a,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x55, 0x70, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x55, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x28, 0x1a, 0x23, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x73, 0x6e, 0x7d,
	0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x7f, 0x0a, 0x0c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x1a, 0x20, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x7b, 0x73, 0x6e, 0x7d, 0x2f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x3a, 0x01, 0x2a, 0x42, 0x44, 0x0a,
	0x0f, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_connects_v1_connect_proto_rawDescOnce sync.Once
	file_api_connects_v1_connect_proto_rawDescData = file_api_connects_v1_connect_proto_rawDesc
)

func file_api_connects_v1_connect_proto_rawDescGZIP() []byte {
	file_api_connects_v1_connect_proto_rawDescOnce.Do(func() {
		file_api_connects_v1_connect_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_connects_v1_connect_proto_rawDescData)
	})
	return file_api_connects_v1_connect_proto_rawDescData
}

var file_api_connects_v1_connect_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_api_connects_v1_connect_proto_goTypes = []interface{}{
	(*CommonReply)(nil),                        // 0: api.connects.v1.CommonReply
	(*EventUpRequest)(nil),                     // 1: api.connects.v1.EventUpRequest
	(*EventUpReply)(nil),                       // 2: api.connects.v1.EventUpReply
	(*PropertyUpRequest)(nil),                  // 3: api.connects.v1.PropertyUpRequest
	(*ServiceReplyRequest)(nil),                // 4: api.connects.v1.ServiceReplyRequest
	(*DockState)(nil),                          // 5: api.connects.v1.DockState
	(*PositionState)(nil),                      // 6: api.connects.v1.PositionState
	(*NetworkState)(nil),                       // 7: api.connects.v1.NetworkState
	(*WirelessLinkState)(nil),                  // 8: api.connects.v1.WirelessLinkState
	(*FlightTaskState)(nil),                    // 9: api.connects.v1.FlightTaskState
	(*BatteryChargeState)(nil),                 // 10: api.connects.v1.BatteryChargeState
	(*ElecPowerState)(nil),                     // 11: api.connects.v1.ElecPowerState
	(*StorageState)(nil),                       // 12: api.connects.v1.StorageState
	(*EnvironmentState)(nil),                   // 13: api.connects.v1.EnvironmentState
	(*DroneFlightState)(nil),                   // 14: api.connects.v1.DroneFlightState
	(*DroneBatteryState)(nil),                  // 15: api.connects.v1.DroneBatteryState
	(*FlightTaskProgressEvent)(nil),            // 16: api.connects.v1.FlightTaskProgressEvent
	(*DockHealMonitorEvent)(nil),               // 17: api.connects.v1.DockHealMonitorEvent
	(*DockTopoUpdateEvent)(nil),                // 18: api.connects.v1.DockTopoUpdateEvent
	(*DockFlightTaskResourceRequestEvent)(nil), // 19: api.connects.v1.DockFlightTaskResourceRequestEvent
	(*Version)(nil),                            // 20: api.connects.v1.Version
	(*PilotState)(nil),                         // 21: api.connects.v1.PilotState
	(*DroneSpeakerState)(nil),                  // 22: api.connects.v1.DroneSpeakerState
	(*DroneCameraState)(nil),                   // 23: api.connects.v1.DroneCameraState
	(*DroneCameraLiveViewWorldRegion)(nil),     // 24: api.connects.v1.DroneCameraLiveViewWorldRegion
	(*CommonReplyCommonData)(nil),              // 25: api.connects.v1.CommonReply.commonData
	(*DroneBatteryState_Battery)(nil),          // 26: api.connects.v1.DroneBatteryState.Battery
	(*DockTopoUpdateEventSubdevice)(nil),       // 27: api.connects.v1.DockTopoUpdateEvent.subdevice
	(*structpb.Struct)(nil),                    // 28: google.protobuf.Struct
}
var file_api_connects_v1_connect_proto_depIdxs = []int32{
	25, // 0: api.connects.v1.CommonReply.data:type_name -> api.connects.v1.CommonReply.commonData
	16, // 1: api.connects.v1.EventUpRequest.flight:type_name -> api.connects.v1.FlightTaskProgressEvent
	17, // 2: api.connects.v1.EventUpRequest.hms:type_name -> api.connects.v1.DockHealMonitorEvent
	18, // 3: api.connects.v1.EventUpRequest.topo:type_name -> api.connects.v1.DockTopoUpdateEvent
	19, // 4: api.connects.v1.EventUpRequest.resourceReq:type_name -> api.connects.v1.DockFlightTaskResourceRequestEvent
	28, // 5: api.connects.v1.EventUpReply.data:type_name -> google.protobuf.Struct
	5,  // 6: api.connects.v1.PropertyUpRequest.dock:type_name -> api.connects.v1.DockState
	21, // 7: api.connects.v1.PropertyUpRequest.pilot:type_name -> api.connects.v1.PilotState
	7,  // 8: api.connects.v1.PropertyUpRequest.network:type_name -> api.connects.v1.NetworkState
	6,  // 9: api.connects.v1.PropertyUpRequest.position:type_name -> api.connects.v1.PositionState
	13, // 10: api.connects.v1.PropertyUpRequest.env:type_name -> api.connects.v1.EnvironmentState
	9,  // 11: api.connects.v1.PropertyUpRequest.flightTask:type_name -> api.connects.v1.FlightTaskState
	11, // 12: api.connects.v1.PropertyUpRequest.elec:type_name -> api.connects.v1.ElecPowerState
	12, // 13: api.connects.v1.PropertyUpRequest.storage:type_name -> api.connects.v1.StorageState
	14, // 14: api.connects.v1.PropertyUpRequest.flight:type_name -> api.connects.v1.DroneFlightState
	15, // 15: api.connects.v1.PropertyUpRequest.battery:type_name -> api.connects.v1.DroneBatteryState
	10, // 16: api.connects.v1.PropertyUpRequest.charge:type_name -> api.connects.v1.BatteryChargeState
	8,  // 17: api.connects.v1.PropertyUpRequest.wirelessLink:type_name -> api.connects.v1.WirelessLinkState
	20, // 18: api.connects.v1.PropertyUpRequest.version:type_name -> api.connects.v1.Version
	28, // 19: api.connects.v1.PropertyUpRequest.other:type_name -> google.protobuf.Struct
	22, // 20: api.connects.v1.PropertyUpRequest.speaker:type_name -> api.connects.v1.DroneSpeakerState
	23, // 21: api.connects.v1.PropertyUpRequest.cameras:type_name -> api.connects.v1.DroneCameraState
	28, // 22: api.connects.v1.ServiceReplyRequest.data:type_name -> google.protobuf.Struct
	26, // 23: api.connects.v1.DroneBatteryState.batteries:type_name -> api.connects.v1.DroneBatteryState.Battery
	28, // 24: api.connects.v1.FlightTaskProgressEvent.extra:type_name -> google.protobuf.Struct
	28, // 25: api.connects.v1.DockHealMonitorEvent.extra:type_name -> google.protobuf.Struct
	27, // 26: api.connects.v1.DockTopoUpdateEvent.subdevices:type_name -> api.connects.v1.DockTopoUpdateEvent.subdevice
	24, // 27: api.connects.v1.DroneCameraState.liveViewWorldRegion:type_name -> api.connects.v1.DroneCameraLiveViewWorldRegion
	28, // 28: api.connects.v1.DockTopoUpdateEvent.subdevice.extra:type_name -> google.protobuf.Struct
	1,  // 29: api.connects.v1.Connect.EventUp:input_type -> api.connects.v1.EventUpRequest
	3,  // 30: api.connects.v1.Connect.PropertyUp:input_type -> api.connects.v1.PropertyUpRequest
	4,  // 31: api.connects.v1.Connect.ServiceReply:input_type -> api.connects.v1.ServiceReplyRequest
	2,  // 32: api.connects.v1.Connect.EventUp:output_type -> api.connects.v1.EventUpReply
	0,  // 33: api.connects.v1.Connect.PropertyUp:output_type -> api.connects.v1.CommonReply
	0,  // 34: api.connects.v1.Connect.ServiceReply:output_type -> api.connects.v1.CommonReply
	32, // [32:35] is the sub-list for method output_type
	29, // [29:32] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_api_connects_v1_connect_proto_init() }
func file_api_connects_v1_connect_proto_init() {
	if File_api_connects_v1_connect_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_connects_v1_connect_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventUpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventUpReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropertyUpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceReplyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DockState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WirelessLinkState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlightTaskState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatteryChargeState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ElecPowerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StorageState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnvironmentState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DroneFlightState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DroneBatteryState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlightTaskProgressEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DockHealMonitorEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DockTopoUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DockFlightTaskResourceRequestEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Version); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PilotState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DroneSpeakerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DroneCameraState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DroneCameraLiveViewWorldRegion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReplyCommonData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DroneBatteryState_Battery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connects_v1_connect_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DockTopoUpdateEventSubdevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_connects_v1_connect_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_connects_v1_connect_proto_goTypes,
		DependencyIndexes: file_api_connects_v1_connect_proto_depIdxs,
		MessageInfos:      file_api_connects_v1_connect_proto_msgTypes,
	}.Build()
	File_api_connects_v1_connect_proto = out.File
	file_api_connects_v1_connect_proto_rawDesc = nil
	file_api_connects_v1_connect_proto_goTypes = nil
	file_api_connects_v1_connect_proto_depIdxs = nil
}
