// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type ConnectHTTPServer interface {
	EventUp(context.Context, *EventUpRequest) (*EventUpReply, error)
	PropertyUp(context.Context, *PropertyUpRequest) (*CommonReply, error)
	ServiceReply(context.Context, *ServiceReplyRequest) (*CommonReply, error)
}

func RegisterConnectHTTPServer(s *http.Server, srv ConnectHTTPServer) {
	r := s.Route("/")
	r.PUT("/internal/v1/connects/{sn}/event", _Connect_EventUp0_HTTP_Handler(srv))
	r.PUT("/internal/v1/connects/{sn}/property", _Connect_PropertyUp0_HTTP_Handler(srv))
	r.PUT("/internal/v1/connects/{sn}/reply", _Connect_ServiceReply0_HTTP_Handler(srv))
}

func _Connect_EventUp0_HTTP_Handler(srv ConnectHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EventUpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.connects.v1.Connect/EventUp")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EventUp(ctx, req.(*EventUpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EventUpReply)
		return ctx.Result(200, reply)
	}
}

func _Connect_PropertyUp0_HTTP_Handler(srv ConnectHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PropertyUpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.connects.v1.Connect/PropertyUp")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PropertyUp(ctx, req.(*PropertyUpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Connect_ServiceReply0_HTTP_Handler(srv ConnectHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ServiceReplyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.connects.v1.Connect/ServiceReply")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ServiceReply(ctx, req.(*ServiceReplyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type ConnectHTTPClient interface {
	EventUp(ctx context.Context, req *EventUpRequest, opts ...http.CallOption) (rsp *EventUpReply, err error)
	PropertyUp(ctx context.Context, req *PropertyUpRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	ServiceReply(ctx context.Context, req *ServiceReplyRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type ConnectHTTPClientImpl struct {
	cc *http.Client
}

func NewConnectHTTPClient(client *http.Client) ConnectHTTPClient {
	return &ConnectHTTPClientImpl{client}
}

func (c *ConnectHTTPClientImpl) EventUp(ctx context.Context, in *EventUpRequest, opts ...http.CallOption) (*EventUpReply, error) {
	var out EventUpReply
	pattern := "/internal/v1/connects/{sn}/event"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.connects.v1.Connect/EventUp"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ConnectHTTPClientImpl) PropertyUp(ctx context.Context, in *PropertyUpRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/internal/v1/connects/{sn}/property"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.connects.v1.Connect/PropertyUp"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ConnectHTTPClientImpl) ServiceReply(ctx context.Context, in *ServiceReplyRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/internal/v1/connects/{sn}/reply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.connects.v1.Connect/ServiceReply"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
