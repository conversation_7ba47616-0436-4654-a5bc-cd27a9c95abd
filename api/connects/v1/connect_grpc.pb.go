// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/connects/v1/connect.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ConnectClient is the client API for Connect service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConnectClient interface {
	EventUp(ctx context.Context, in *EventUpRequest, opts ...grpc.CallOption) (*EventUpReply, error)
	PropertyUp(ctx context.Context, in *PropertyUpRequest, opts ...grpc.CallOption) (*CommonReply, error)
	ServiceReply(ctx context.Context, in *ServiceReplyRequest, opts ...grpc.CallOption) (*CommonReply, error)
}

type connectClient struct {
	cc grpc.ClientConnInterface
}

func NewConnectClient(cc grpc.ClientConnInterface) ConnectClient {
	return &connectClient{cc}
}

func (c *connectClient) EventUp(ctx context.Context, in *EventUpRequest, opts ...grpc.CallOption) (*EventUpReply, error) {
	out := new(EventUpReply)
	err := c.cc.Invoke(ctx, "/api.connects.v1.Connect/EventUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectClient) PropertyUp(ctx context.Context, in *PropertyUpRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.connects.v1.Connect/PropertyUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectClient) ServiceReply(ctx context.Context, in *ServiceReplyRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.connects.v1.Connect/ServiceReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConnectServer is the server API for Connect service.
// All implementations must embed UnimplementedConnectServer
// for forward compatibility
type ConnectServer interface {
	EventUp(context.Context, *EventUpRequest) (*EventUpReply, error)
	PropertyUp(context.Context, *PropertyUpRequest) (*CommonReply, error)
	ServiceReply(context.Context, *ServiceReplyRequest) (*CommonReply, error)
	mustEmbedUnimplementedConnectServer()
}

// UnimplementedConnectServer must be embedded to have forward compatible implementations.
type UnimplementedConnectServer struct {
}

func (UnimplementedConnectServer) EventUp(context.Context, *EventUpRequest) (*EventUpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EventUp not implemented")
}
func (UnimplementedConnectServer) PropertyUp(context.Context, *PropertyUpRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PropertyUp not implemented")
}
func (UnimplementedConnectServer) ServiceReply(context.Context, *ServiceReplyRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServiceReply not implemented")
}
func (UnimplementedConnectServer) mustEmbedUnimplementedConnectServer() {}

// UnsafeConnectServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConnectServer will
// result in compilation errors.
type UnsafeConnectServer interface {
	mustEmbedUnimplementedConnectServer()
}

func RegisterConnectServer(s grpc.ServiceRegistrar, srv ConnectServer) {
	s.RegisterService(&Connect_ServiceDesc, srv)
}

func _Connect_EventUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EventUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectServer).EventUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.connects.v1.Connect/EventUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectServer).EventUp(ctx, req.(*EventUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Connect_PropertyUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PropertyUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectServer).PropertyUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.connects.v1.Connect/PropertyUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectServer).PropertyUp(ctx, req.(*PropertyUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Connect_ServiceReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectServer).ServiceReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.connects.v1.Connect/ServiceReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectServer).ServiceReply(ctx, req.(*ServiceReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Connect_ServiceDesc is the grpc.ServiceDesc for Connect service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Connect_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.connects.v1.Connect",
	HandlerType: (*ConnectServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EventUp",
			Handler:    _Connect_EventUp_Handler,
		},
		{
			MethodName: "PropertyUp",
			Handler:    _Connect_PropertyUp_Handler,
		},
		{
			MethodName: "ServiceReply",
			Handler:    _Connect_ServiceReply_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/connects/v1/connect.proto",
}
