package v1

import (
	"encoding/json"
	time "time"

	"github.com/asmarques/geodist"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"google.golang.org/protobuf/types/known/structpb"
)

func TobizDeviceProperty(data *biz.DeviceProperty, pu *PropertyUpRequest) *biz.DeviceProperty {
	// 本次上行和上次上行时间差，非正表示乱序则为无效上行
	updiff := pu.Timestamp - data.UppedTime
	if updiff <= 0 {
		data.MsgType = 0
		return data
	}
	// 空包消息标识，为true则无效上行
	isEmpty := true
	data.UppedTime = pu.Timestamp
	// 机库状态数据
	if pu.Dock != nil {
		isEmpty = false
		data.Capacity = -1
		data.DockState = &pu.Dock.Mode
		data.DockHeight = &pu.Dock.Height
		data.Humidity = &pu.Dock.Humidity
		data.Tmperature = &pu.Dock.Temperature
		data.CoverState = &pu.Dock.CoverState
		data.PutterState = &pu.Dock.PutterState
		data.AirCondState = &pu.Dock.AirConditionerState
		data.UsedStorage = lo.ToPtr(conv.Vtoi(pu.Dock.StorageUsed) >> 20)
		data.TotalStorage = lo.ToPtr(conv.Vtoi(pu.Dock.StorageTotal) >> 20)
		data.DroneOnlineState = &pu.Dock.DroneOnlineState
	}
	// 遥控状态数据
	if pu.Pilot != nil {
		isEmpty = false
		// TODO: 遥控暂无网络数据，临时写死[Wi-Fi]
		data.NetworkType = 3
		data.SignalQuality = "NORMAL"
		data.Capacity = pu.Pilot.CapacityPercent
		data.PilotLatitude = &pu.Pilot.Latitude
		data.PilotLongitude = &pu.Pilot.Longitude
	}
	// 网关设备网络数据
	if pu.Network != nil {
		isEmpty = false
		data.NetworkType = pu.Network.Type
		qualityMap := map[string]string{"-1": "NONE", "0": "BAD", "1": "NORMAL", "2": "GOOD"}
		data.SignalQuality = qualityMap[conv.Vtoa(pu.Network.Quality)]
	}
	// 网关设备版本数据
	if pu.Version != nil {
		isEmpty = false
		data.FirmwareVersion = &pu.Version.Firmware
	}
	// 机库电池数据
	if pu.Elec != nil {
		isEmpty = false
		data.SupplyVoltage = &pu.Elec.SupplyVoltage
		data.WorkingVoltage = lo.ToPtr(pu.Elec.WorkingVoltage / 1000)
		data.WorkingCurrent = lo.ToPtr(pu.Elec.WorkingCurrent / 1000)
		data.BackupBatteryVoltage = lo.ToPtr(pu.Elec.DockBackupBatteryVoltage / 1000)
	}
	if pu.Storage != nil {
		isEmpty = false
		data.UsedStorage = lo.ToPtr(conv.Vtoi(pu.Storage.Used) >> 20)
		data.TotalStorage = lo.ToPtr(conv.Vtoi(pu.Storage.Total) >> 20)
	}
	// 气象环境数据
	if pu.Env != nil && pu.DroneSn == "" {
		isEmpty = false
		data.Rainfall = &pu.Env.Rainfall
		data.WindSpeed = &pu.Env.WindSpeed
		data.EnvHumidity = &pu.Env.Humidity
		data.EnvTemperature = &pu.Env.Temperature
		data.IsRain = lo.Ternary(pu.Env.Rainfall == 0, false, true)
	}
	// 网关设备图传数据
	if pu.WirelessLink != nil {
		isEmpty = false
		data.WirelessMode = &pu.WirelessLink.Mode
		data.Quality4G = &pu.WirelessLink.Quality4G
		data.QualitySDR = &pu.WirelessLink.QualitySDR
		data.QualityIT = lo.ToPtr(lo.Max([]int32{*data.Quality4G, *data.QualitySDR}))
	}
	// 网关设备搜星数据
	if pu.Position != nil {
		isEmpty = false
		data.IsFixed = &pu.Position.IsFixed
		data.GPSNumber = &pu.Position.GpsNumber
		data.RTKNumber = &pu.Position.RtkNumber
	}
	// 飞机任务数据
	if pu.FlightTask != nil {
		isEmpty = false
		data.CabinStatus = &pu.FlightTask.DroneInDock
	}
	// 飞机充电数据
	if pu.Charge != nil {
		isEmpty = false
		data.ChargeState = &pu.Charge.DroneBatteryChargeState
		// 防电量0-突变：如果上次充电量的大于20，本次充电量为0，则为无效电量
		if !(data.ChargePercent != nil && *data.ChargePercent >= 20 && pu.Charge.DroneBatteryPercent == 0) {
			data.ChargePercent = &pu.Charge.DroneBatteryPercent
		}
	}
	// 飞机电量数据
	if pu.Battery != nil {
		isEmpty = false
		// 左右电池有效时，保持充电电量和飞机电量一致
		if len(pu.Battery.Batteries) == 2 {
			chargePercent := pu.Battery.CapacityPercent
			avgPercent := int32((pu.Battery.Batteries[0].CapacityPercent + pu.Battery.Batteries[1].CapacityPercent) / 2)
			// 如果飞机电量为0，则取左右电池的平均值
			if chargePercent <= 0 && avgPercent > 0 {
				chargePercent = avgPercent
			}
			data.ChargePercent = &chargePercent
		}
		data.ReturnCapacity = &pu.Battery.ReturnHomePower
		data.BatteryCapacity = &pu.Battery.CapacityPercent
		data.RemainTime = lo.ToPtr(int64(pu.Battery.RemainFlightTime * 1000))
		data.ChildrenCapacity = lo.Map(pu.Battery.Batteries, func(b *DroneBatteryState_Battery, _ int) any {
			return b.CapacityPercent
		})
	}
	// 飞机状态数据
	if pu.Mode > -1 {
		isEmpty = false
		data.MsgType = 3
		data.LastDroneState = data.DroneState
		data.DroneState = &pu.Mode
	}
	// 飞机飞行数据
	if pu.Flight != nil {
		isEmpty = false
		data.MsgType = 3
		data.VerticalSpeed = &pu.Flight.VerticalSpeed
		data.HorizontalSpeed = &pu.Flight.HorizontalSpeed
		data.Height = &pu.Flight.Height
		data.Elevation = &pu.Flight.Elevation
		data.Yaw = &pu.Flight.AttitudeHead
		data.Roll = &pu.Flight.AttitudeRoll
		data.Pitch = &pu.Flight.AttitudePitch
		data.WindSpeed = &pu.Flight.WindSpeed
		data.TotalRuntime = &pu.Flight.TotalFlightTime
		data.WindDirection = &pu.Flight.WindDirection
		// 因为飞机刚开机会报[0, 0]的经纬度，需要pass处理
		if pu.Flight.Longitude > 0 && pu.Flight.Latitude > 0 {
			// 如果上次经纬度和本次都不为空，则飞行里程累加上两点的哈弗森距离
			if data.Longitude != nil && data.Latitude != nil {
				lastPoint := geodist.Point{Long: *data.Longitude, Lat: *data.Latitude}
				currPoint := geodist.Point{Long: pu.Flight.Longitude, Lat: pu.Flight.Latitude}
				data.Mileage = data.Mileage + int32(geodist.HaversineDistance(lastPoint, currPoint)*1000)
			}
			data.Longitude = &pu.Flight.Longitude
			data.Latitude = &pu.Flight.Latitude
		}
		// 飞行相机数组
		if pu.Cameras != nil {
			data.AeroCameras = lo.Map(pu.Cameras, func(camera *DroneCameraState, _ int) *biz.AeroCamera {
				aeroCamera := &biz.AeroCamera{}
				copier.Copy(aeroCamera, camera)
				return aeroCamera
			})
		}
	}
	// 喊话器信息
	if pu.Speaker != nil {
		isEmpty = false
		data.MsgType = 4
		data.Speaker = &biz.SpeakerWidget{
			Sn:              pu.Speaker.Sn,
			Name:            pu.Speaker.Name,
			Type:            pu.Speaker.Type,
			Index:           pu.Speaker.Index,
			Mode:            pu.Speaker.WorkMode,
			Status:          pu.Speaker.SystemState,
			Volume:          pu.Speaker.PlayVolume,
			Playmode:        pu.Speaker.PlayMode,
			FirmwareVersion: pu.Speaker.FirmwareVersion,
		}
	}
	if isEmpty {
		data.MsgType = 0
	}
	return data
}

func BuildDockPropertyUpRequest(dp *biz.DockProperties) *PropertyUpRequest {
	p := &PropertyUpRequest{
		Id:        dp.Id,
		Sn:        dp.Sn,
		Mode:      -1,
		DeviceId:  dp.DeviceId,
		RxTime:    dp.RxTime.UnixMilli(),
		Timestamp: dp.Timestamp.UnixMilli(),
	}
	if dp.State != nil {
		p.Dock = BuildDockState(dp.State)
	}
	if dp.PositionState != nil {
		p.Position = BuildPosition(dp.PositionState)
	}
	if dp.NetworkState != nil {
		p.Network = BuildNetworkState(dp.NetworkState)
	}
	if dp.VersionState != nil {
		p.Version = BuildVersionState(dp.VersionState)
	}
	if dp.FlightTaskState != nil {
		p.FlightTask = BuildFlightTaskState(dp.FlightTaskState)
	}
	if dp.EnvironmentState != nil {
		p.Env = BuildEnvrionmentState(dp.EnvironmentState)
	}
	if dp.WirelessLinkState != nil {
		p.WirelessLink = BuildWirelessLinkState(dp.WirelessLinkState)
	}
	if dp.ElecPowerState != nil {
		p.Elec = BuildElecPowerState(dp.ElecPowerState)
	}
	if dp.BatteryChargeState != nil {
		p.Charge = BuildBatteryChargeState(dp.BatteryChargeState)
	}
	if len(dp.Other) > 0 {
		p.Other, _ = structpb.NewStruct(dp.Other)
	}
	return p
}

func BuildDockDronePropertyUpRequest(dp *biz.DroneProperties) *PropertyUpRequest {
	p := &PropertyUpRequest{
		Id:        dp.Id,
		Sn:        dp.Sn,
		DeviceId:  dp.DeviceId,
		RxTime:    dp.RxTime.UnixMilli(),
		Timestamp: dp.Timestamp.UnixMilli(),
		DroneSn:   dp.DroneSn,
		DroneType: dp.DroneType,
		Mode:      dp.Mode,
	}

	if dp.FlightState != nil {
		p.Flight = BuildDroneFlightState(dp.FlightState)
	}
	if dp.Battery != nil {
		p.Battery = BuildDroneBatteryState(dp.Battery)
	}
	if dp.Storage != nil {
		p.Storage = BuildStorageState(dp.Storage)
	}
	if dp.PositionState != nil {
		p.Position = BuildPosition(dp.PositionState)
	}
	if len(dp.Other) > 0 {
		p.Other, _ = structpb.NewStruct(dp.Other)
	}
	if dp.Speaker != nil {
		p.Speaker = BuildDroneSpeakerState(dp.Speaker)
	}
	if len(dp.Cameras) > 0 {
		p.Cameras = lo.Map(dp.Cameras, func(c *biz.DroneCameraState, _ int) *DroneCameraState {
			return BuildDroneCameraState(c)
		})
	}
	return p
}

func TobizDockFlightTaskProgressEvent(eu *EventUpRequest) *biz.AutoFlightTaskProgressEvent {
	be := &biz.AutoFlightTaskProgressEvent{
		ThingEvent: biz.ThingEvent{
			Id:           eu.Id,
			Sn:           eu.Sn,
			RxTime:       time.UnixMilli(eu.RxTime),
			OccurredTime: time.UnixMilli(eu.Timestamp),
			DeviceId:     eu.DeviceId,
			Type:         biz.ThingModelEventType(eu.Type),
		},
		FlightId:             eu.Flight.FlightId,
		CurrentWaypointIndex: eu.Flight.CurrentWaypointIndex,
		Status:               eu.Flight.Status,
		Percent:              eu.Flight.Percent,
		Step:                 eu.Flight.Step,
		VoyageState:          eu.Flight.VoyageState,
		BreakReason:          eu.Flight.BreakReason,
	}
	if eu.Flight.Extra != nil {
		be.Extra = eu.Flight.Extra.AsMap()
	}
	return be
}

func BuildDockFlightTaskProgressEvent(be *biz.AutoFlightTaskProgressEvent) *EventUpRequest {
	eu := &EventUpRequest{
		Id:        be.Id,
		Sn:        be.Sn,
		RxTime:    be.RxTime.UnixMilli(),
		Timestamp: be.OccurredTime.UnixMilli(),
		DeviceId:  be.DeviceId,
		Type:      int32(be.Type),
		Flight: &FlightTaskProgressEvent{
			FlightId:             be.FlightId,
			CurrentWaypointIndex: be.CurrentWaypointIndex,
			Status:               be.Status,
			Percent:              be.Percent,
			Step:                 be.Step,
			VoyageState:          be.VoyageState,
			BreakReason:          be.BreakReason,
		},
	}
	if len(be.Extra) > 0 {
		eu.Flight.Extra, _ = structpb.NewStruct(be.Extra)
	}
	return eu
}

func TobizDockHealMoniterEvent(eu *EventUpRequest) *biz.DockHealMonitorEvent {
	return &biz.DockHealMonitorEvent{
		ThingEvent: biz.ThingEvent{
			Id:           eu.Id,
			Sn:           eu.Sn,
			RxTime:       time.UnixMilli(eu.RxTime),
			OccurredTime: time.UnixMilli(eu.Timestamp),
			DeviceId:     eu.DeviceId,
			Type:         biz.ThingModelEventType(eu.Type),
		},
		Level:    eu.Hms.Level,
		Module:   eu.Hms.Module,
		InTheSky: eu.Hms.InTheSky,
		Imminent: eu.Hms.Imminent,
		Code:     eu.Hms.Code,
		Extra:    eu.Hms.Extra.AsMap(),
		Source:   eu.Hms.Source,
	}
}

func BuildDockHealMoniterEvent(be *biz.DockHealMonitorEvent) *EventUpRequest {
	var extra *structpb.Struct
	if len(be.Extra) > 0 {
		extra, _ = structpb.NewStruct(be.Extra)
	}
	return &EventUpRequest{
		Id:        be.Id,
		Sn:        be.Sn,
		RxTime:    be.RxTime.UnixMilli(),
		Timestamp: be.OccurredTime.UnixMilli(),
		DeviceId:  be.DeviceId,
		Type:      int32(be.Type),
		Hms: &DockHealMonitorEvent{
			Level:    be.Level,
			Module:   be.Module,
			InTheSky: be.InTheSky,
			Imminent: be.Imminent,
			Code:     be.Code,
			Extra:    extra,
			Source:   be.Source,
		},
	}
}

func TobizDockTopoUpdateEvent(eu *EventUpRequest) *biz.DockTopoUpdateEvent {
	return &biz.DockTopoUpdateEvent{
		ThingEvent: biz.ThingEvent{
			Id:           eu.Id,
			Sn:           eu.Sn,
			RxTime:       time.UnixMilli(eu.RxTime),
			OccurredTime: time.UnixMilli(eu.Timestamp),
			DeviceId:     eu.DeviceId,
			Type:         biz.ThingModelEventType(eu.Type),
		},
		DeviceModel: eu.Topo.DeviceModel,
		Subdevices: lo.Map(eu.Topo.Subdevices, func(d *DockTopoUpdateEventSubdevice, _ int) *biz.DockSubdevice {
			return &biz.DockSubdevice{
				Sn:     d.Sn,
				Domain: biz.SubDeviceDomain(d.Domain),
				Index:  d.Index,
				Type:   d.Type,
				Extra:  d.Extra.AsMap(),
			}
		}),
	}
}

func BuildDockTopoUpdateEvent(be *biz.DockTopoUpdateEvent) *EventUpRequest {
	return &EventUpRequest{
		Id:        be.Id,
		Sn:        be.Sn,
		RxTime:    be.RxTime.UnixMilli(),
		Timestamp: be.OccurredTime.UnixMilli(),
		DeviceId:  be.DeviceId,
		Type:      int32(be.Type),
		Topo: &DockTopoUpdateEvent{
			DeviceModel: be.DeviceModel,
			Subdevices: lo.Map(be.Subdevices, func(d *biz.DockSubdevice, _ int) *DockTopoUpdateEventSubdevice {
				extra := &structpb.Struct{}
				if d.Extra != nil {
					// 	extra, _ = structpb.NewValue(d.Extra)
					ev, err := json.Marshal(d.Extra)
					if err == nil {
						extra.UnmarshalJSON(ev)
					}
				}
				return &DockTopoUpdateEventSubdevice{
					Sn:     d.Sn,
					Domain: int32(d.Domain),
					Index:  d.Index,
					Type:   d.Type,
					Extra:  extra,
				}
			}),
		},
	}
}

func TobizDockFlightTaskResourceRequestEvent(eu *EventUpRequest) *biz.DockFlightTaskResourceRequestEvent {
	return &biz.DockFlightTaskResourceRequestEvent{
		ThingEvent: biz.ThingEvent{
			Id:           eu.Id,
			Sn:           eu.Sn,
			RxTime:       time.UnixMilli(eu.RxTime),
			OccurredTime: time.UnixMilli(eu.Timestamp),
			DeviceId:     eu.DeviceId,
			Type:         biz.ThingModelEventType(eu.Type),
		},
		FlightId: eu.ResourceReq.FlightId,
	}
}

func BuildDockFlightTaskResourceRequestEvent(be *biz.DockFlightTaskResourceRequestEvent) *EventUpRequest {
	return &EventUpRequest{
		Id:        be.Id,
		Sn:        be.Sn,
		RxTime:    be.RxTime.UnixMilli(),
		Timestamp: be.OccurredTime.UnixMilli(),
		DeviceId:  be.DeviceId,
		Type:      int32(be.Type),
		ResourceReq: &DockFlightTaskResourceRequestEvent{
			FlightId: be.FlightId,
		},
	}
}

func BuildDockFlytoPointProgressEvent(be *biz.DockFlightTaskResourceRequestEvent) *EventUpRequest {
	return &EventUpRequest{
		Id:        be.Id,
		Sn:        be.Sn,
		RxTime:    be.RxTime.UnixMilli(),
		Timestamp: be.OccurredTime.UnixMilli(),
		DeviceId:  be.DeviceId,
		Type:      int32(be.Type),
		ResourceReq: &DockFlightTaskResourceRequestEvent{
			FlightId: be.FlightId,
		},
	}
}

func BuildSimlepleEvent(be biz.ThingEvent, value any) *EventUpRequest {
	ev, _ := json.Marshal(value)
	return &EventUpRequest{
		Id:               be.Id,
		Sn:               be.Sn,
		RxTime:           be.RxTime.UnixMilli(),
		Timestamp:        be.OccurredTime.UnixMilli(),
		DeviceId:         be.DeviceId,
		Type:             int32(be.Type),
		SimpleEventValue: ev,
	}
}

func ToSimpleBizEvent(eu *EventUpRequest, be any) (*biz.ThingEvent, error) {
	if err := json.Unmarshal(eu.SimpleEventValue, be); err != nil {
		return nil, err
	}
	e := &biz.ThingEvent{
		Id:           eu.Id,
		Sn:           eu.Sn,
		RxTime:       time.UnixMilli(eu.RxTime),
		OccurredTime: time.UnixMilli(eu.Timestamp),
		DeviceId:     eu.DeviceId,
		Type:         biz.ThingModelEventType(eu.Type),
	}
	return e, nil
}

func TobizServiceReply(sr *ServiceReplyRequest) *biz.DockServiceReply {
	return &biz.DockServiceReply{
		Code:       sr.Code,
		Id:         sr.Id,
		Sn:         sr.Sn,
		Timestamp:  time.UnixMilli(sr.Timestamp),
		DeviceId:   sr.DeviceId,
		ServiceId:  sr.ServiceId,
		Identifier: sr.Identifier,
		Status:     biz.ServiceReplyStatus(sr.Status),
		Message:    sr.Message,
		Data:       sr.Data.AsMap(),
	}
}

func BuildServiceReply(sr *biz.DockServiceReply) *ServiceReplyRequest {
	data, _ := structpb.NewStruct(sr.Data)
	return &ServiceReplyRequest{
		Code:       sr.Code,
		Id:         sr.Id,
		Sn:         sr.Sn,
		Timestamp:  sr.Timestamp.UnixMilli(),
		DeviceId:   sr.DeviceId,
		ServiceId:  sr.ServiceId,
		Identifier: sr.Identifier,
		Status:     sr.Status.String(),
		Message:    sr.Message,
		Data:       data,
	}
}

func BuildDockState(s *biz.DockState) *DockState {
	return &DockState{
		Mode:                 s.Mode,
		Longitude:            s.Longitude,
		Latitude:             s.Latitude,
		CoverState:           s.CoverState,
		PutterState:          s.PutterState,
		SupplementLightState: s.SupplementLightState,
		Temperature:          s.Temperature,
		Humidity:             s.Humidity,
		Height:               s.Height,
		StorageTotal:         s.StorageTotal,
		StorageUsed:          s.StorageUsed,
		AirConditionerState:  s.AirConditionerState,
		DroneOnlineState:     s.DroneOnlineState,
	}
}

func BuildPosition(s *biz.PositionState) *PositionState {
	return &PositionState{
		IsFixed:       s.IsFixed,
		IsCalibration: s.IsCalibration,
		Quality:       s.Quality,
		GpsNumber:     s.GpsNumber,
		RtkNumber:     s.RtkNumber,
	}
}

func BuildNetworkState(s *biz.NetworkState) *NetworkState {
	return &NetworkState{
		Type:    s.Type,
		Quality: s.Quality,
	}
}

func BuildFlightTaskState(s *biz.FlightTaskState) *FlightTaskState {
	return &FlightTaskState{
		// Count:              s.Count,
		Longitude:          s.Longitude,
		Latitude:           s.Latitude,
		SafeLandHeight:     s.SafeLandHeight,
		SafeLandConfigured: s.SafeLandConfigured,
		DroneInDock:        s.DroneInDock,
	}
}

func BuildEnvrionmentState(s *biz.EnvironmentState) *EnvironmentState {
	return &EnvironmentState{
		Rainfall:    s.Rainfall,
		WindSpeed:   s.WindSpeed,
		Temperature: s.Temperature,
		Humidity:    s.Humidity,
	}
}

func BuildWirelessLinkState(s *biz.WirelessLinkState) *WirelessLinkState {
	return &WirelessLinkState{
		Mode:         s.Mode,
		State4G:      s.State4G,
		StateSDR:     s.StateSDR,
		QualitySDR:   s.QualitySDR,
		Quality4G:    s.Quality4G,
		Quality4GUav: s.Quality4GUav,
		Quality4GGnd: s.Quality4GGnd,
	}
}

func BuildBatteryChargeState(s *biz.BatteryChargeState) *BatteryChargeState {
	return &BatteryChargeState{
		DroneBatteryPercent:     s.DroneBatteryPercent,
		DroneBatteryChargeState: s.DroneBatteryChargeState,
	}
}

func BuildElecPowerState(s *biz.ElecPowerState) *ElecPowerState {
	return &ElecPowerState{
		DockBackupBatterySwitch:  s.DockBackupBatterySwitch,
		DockBackupBatteryVoltage: s.DockBackupBatteryVoltage,
		DockBackupTemperature:    s.DockBackupTemperature,
		SupplyVoltage:            s.SupplyVoltage,
		WorkingVoltage:           s.WorkingVoltage,
		WorkingCurrent:           s.WorkingCurrent,
	}
}

func BuildStorageState(s *biz.Storage) *StorageState {
	return &StorageState{
		Total: s.Total,
		Used:  s.Used,
	}
}

func BuildDroneFlightState(s *biz.DroneFlightState) *DroneFlightState {
	return &DroneFlightState{
		HorizontalSpeed:           s.HorizontalSpeed,
		VerticalSpeed:             s.VerticalSpeed,
		Longitude:                 s.Longitude,
		Latitude:                  s.Latitude,
		Height:                    s.Height,
		Elevation:                 s.Elevation,
		AttitudePitch:             s.AttitudePitch,
		AttitudeRoll:              s.AttitudeRoll,
		AttitudeHead:              s.AttitudeHead,
		TotalFlightTime:           s.TotalFlightTime,
		WindSpeed:                 s.WindSpeed,
		WindDirection:             s.WindDirection,
		IsNearHeightLimit:         s.IsNearHeightLimit,
		IsNearAreaLimit:           s.IsNearAreaLimit,
		IsNearIDistanceLimit:      s.IsNearDistanceLimit,
		NightLightsState:          s.NightLightsState,
		HorizonObstacleAvoidance:  s.HorizonObstacleAvoidance,
		UpsideObstacleAvoidance:   s.UpsideObstacleAvoidance,
		DownsideObstacleAvoidance: s.DownsideObstacleAvoidance,
	}
}

func BuildDroneBatteryState(s *biz.DroneBatteryState) *DroneBatteryState {
	return &DroneBatteryState{
		CapacityPercent:  s.CapacityPercent,
		RemainFlightTime: s.RemainFlightTime,
		ReturnHomePower:  s.ReturnHomePower,
		LandingPower:     s.LandingPower,
		Batteries: lo.Map(s.Batteries, func(b *biz.DroneBattery, _ int) *DroneBatteryState_Battery {
			return &DroneBatteryState_Battery{
				Index:           b.Index,
				CapacityPercent: b.CapacityPercent,
				Voltage:         b.Voltage,
				Temperature:     b.Temperature,
				Loop:            b.Loop,
				Sn:              b.Sn,
			}
		}),
	}
}

func BuildVersionState(s *biz.VersionState) *Version {
	return &Version{
		Firmware: s.Firmware,
	}
}

func BuildPilotState(s *biz.RemoteControllerState) *PilotState {
	return &PilotState{
		CapacityPercent: s.CapacityPercent,
		Longitude:       s.Longitude,
		Latitude:        s.Latitude,
	}
}

func BuildControllerPropertyUpRequest(cp *biz.RemoteControllerProperties) *PropertyUpRequest {
	p := &PropertyUpRequest{
		Id:        cp.Id,
		Sn:        cp.Sn,
		Mode:      -1,
		DeviceId:  cp.DeviceId,
		RxTime:    cp.RxTime.UnixMilli(),
		Timestamp: cp.Timestamp.UnixMilli(),
	}
	if cp.State != nil {
		p.Pilot = BuildPilotState(cp.State)
	}
	if cp.VersionState != nil {
		p.Version = BuildVersionState(cp.VersionState)
	}
	if cp.WirelessLinkState != nil {
		p.WirelessLink = BuildWirelessLinkState(cp.WirelessLinkState)
	}
	return p
}

func BuildDroneSpeakerState(bs *biz.DroneSpeakerState) *DroneSpeakerState {
	return &DroneSpeakerState{
		Index:             bs.Index,
		Name:              bs.Name,
		Sn:                bs.Sn,
		FirmwareVersion:   bs.FirmwareVersion,
		LibVersion:        bs.LibVersion,
		Type:              bs.Type,
		WorkMode:          int32(bs.WorkMode),
		SystemState:       int32(bs.SystemState),
		PlayMode:          int32(bs.PlayMode),
		PlayVolume:        int32(bs.PlayVolume),
		PlayFileName:      bs.PlayFileName,
		PlayFileSignature: bs.PlayFileSignature,
	}
}

func BuildDroneCameraState(cs *biz.DroneCameraState) *DroneCameraState {
	return &DroneCameraState{
		Index:                  cs.Index,
		Mode:                   int32(cs.Mode),
		PhotoState:             cs.PhotoState,
		RecordingState:         cs.RecordingState,
		ZoomFactor:             cs.ZoomFactor,
		IrZoomFactor:           cs.IRZoomFactor,
		ZoomFocusMode:          cs.ZoomFocusMode,
		ZoomFocusValue:         cs.ZoomFocusValue,
		ZoomFocusState:         cs.ZoomFocusState,
		IrMeteringMode:         cs.IRMeteringMode,
		GimbalPitch:            cs.GimbalPitch,
		GimbalYaw:              cs.GimbalYaw,
		GimbalRoll:             cs.GimbalRoll,
		MeasureTargetLongitude: cs.MeasureTargetLongitude,
		MeasureTargetLatitude:  cs.MeasureTargetLatitude,
		MeasureTargetAltitude:  cs.MeasureTargetAltitude,
		MeasureTargetDistance:  cs.MeasureTargetAltitude,
		MeasureErrState:        cs.MeasureErrState,
		LiveViewWorldRegion: &DroneCameraLiveViewWorldRegion{
			Left:   cs.LiveViewWorldRegion.Left,
			Top:    cs.LiveViewWorldRegion.Top,
			Right:  cs.LiveViewWorldRegion.Right,
			Bottom: cs.LiveViewWorldRegion.Bottom,
		},
	}
}

func ToBizDroneCameraState(cs *DroneCameraState) *biz.DroneCameraState {
	s := &biz.DroneCameraState{
		Index:                  cs.Index,
		Mode:                   cs.Mode,
		PhotoState:             cs.PhotoState,
		RecordingState:         cs.RecordingState,
		ZoomFactor:             cs.ZoomFactor,
		IRZoomFactor:           cs.IrZoomFactor,
		ZoomFocusMode:          cs.ZoomFocusMode,
		ZoomFocusValue:         cs.ZoomFocusValue,
		ZoomFocusState:         cs.ZoomFocusState,
		IRMeteringMode:         cs.IrMeteringMode,
		GimbalPitch:            cs.GimbalPitch,
		GimbalYaw:              cs.GimbalYaw,
		GimbalRoll:             cs.GimbalRoll,
		MeasureTargetLongitude: cs.MeasureTargetLongitude,
		MeasureTargetLatitude:  cs.MeasureTargetLatitude,
		MeasureTargetAltitude:  cs.MeasureTargetAltitude,
		MeasureErrState:        cs.MeasureErrState,
	}
	if cs.LiveViewWorldRegion != nil {
		s.LiveViewWorldRegion = biz.CameraLiveViewWorldRegion{
			Left:   cs.LiveViewWorldRegion.Left,
			Top:    cs.LiveViewWorldRegion.Top,
			Right:  cs.LiveViewWorldRegion.Right,
			Bottom: cs.LiveViewWorldRegion.Bottom,
		}
	}
	return s
}
