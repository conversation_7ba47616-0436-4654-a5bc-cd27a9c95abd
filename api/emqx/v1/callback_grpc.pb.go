// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/emqx/v1/callback.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EmqxCallbackClient is the client API for EmqxCallback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EmqxCallbackClient interface {
	AuthClient(ctx context.Context, in *AuthMqttClientRequest, opts ...grpc.CallOption) (*AuthMqttClientReply, error)
	ACL(ctx context.Context, in *ACLRequest, opts ...grpc.CallOption) (*ACLReply, error)
}

type emqxCallbackClient struct {
	cc grpc.ClientConnInterface
}

func NewEmqxCallbackClient(cc grpc.ClientConnInterface) EmqxCallbackClient {
	return &emqxCallbackClient{cc}
}

func (c *emqxCallbackClient) AuthClient(ctx context.Context, in *AuthMqttClientRequest, opts ...grpc.CallOption) (*AuthMqttClientReply, error) {
	out := new(AuthMqttClientReply)
	err := c.cc.Invoke(ctx, "/api.emqx.v1.EmqxCallback/AuthClient", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emqxCallbackClient) ACL(ctx context.Context, in *ACLRequest, opts ...grpc.CallOption) (*ACLReply, error) {
	out := new(ACLReply)
	err := c.cc.Invoke(ctx, "/api.emqx.v1.EmqxCallback/ACL", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmqxCallbackServer is the server API for EmqxCallback service.
// All implementations must embed UnimplementedEmqxCallbackServer
// for forward compatibility
type EmqxCallbackServer interface {
	AuthClient(context.Context, *AuthMqttClientRequest) (*AuthMqttClientReply, error)
	ACL(context.Context, *ACLRequest) (*ACLReply, error)
	mustEmbedUnimplementedEmqxCallbackServer()
}

// UnimplementedEmqxCallbackServer must be embedded to have forward compatible implementations.
type UnimplementedEmqxCallbackServer struct {
}

func (UnimplementedEmqxCallbackServer) AuthClient(context.Context, *AuthMqttClientRequest) (*AuthMqttClientReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthClient not implemented")
}
func (UnimplementedEmqxCallbackServer) ACL(context.Context, *ACLRequest) (*ACLReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ACL not implemented")
}
func (UnimplementedEmqxCallbackServer) mustEmbedUnimplementedEmqxCallbackServer() {}

// UnsafeEmqxCallbackServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EmqxCallbackServer will
// result in compilation errors.
type UnsafeEmqxCallbackServer interface {
	mustEmbedUnimplementedEmqxCallbackServer()
}

func RegisterEmqxCallbackServer(s grpc.ServiceRegistrar, srv EmqxCallbackServer) {
	s.RegisterService(&EmqxCallback_ServiceDesc, srv)
}

func _EmqxCallback_AuthClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthMqttClientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmqxCallbackServer).AuthClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.emqx.v1.EmqxCallback/AuthClient",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmqxCallbackServer).AuthClient(ctx, req.(*AuthMqttClientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmqxCallback_ACL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ACLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmqxCallbackServer).ACL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.emqx.v1.EmqxCallback/ACL",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmqxCallbackServer).ACL(ctx, req.(*ACLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EmqxCallback_ServiceDesc is the grpc.ServiceDesc for EmqxCallback service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EmqxCallback_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.emqx.v1.EmqxCallback",
	HandlerType: (*EmqxCallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AuthClient",
			Handler:    _EmqxCallback_AuthClient_Handler,
		},
		{
			MethodName: "ACL",
			Handler:    _EmqxCallback_ACL_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/emqx/v1/callback.proto",
}
