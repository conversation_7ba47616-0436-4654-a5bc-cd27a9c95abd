{"swagger": "2.0", "info": {"title": "api/emqx/v1/callback.proto", "version": "version not set"}, "tags": [{"name": "EmqxCallback"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/internal/v1/emqx/acl": {"post": {"operationId": "EmqxCallback_ACL", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ACLReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1ACLRequest"}}], "tags": ["EmqxCallback"]}}, "/internal/v1/emqx/auth": {"post": {"operationId": "EmqxCallback_AuthClient", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1AuthMqttClientReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1AuthMqttClientRequest"}}], "tags": ["EmqxCallback"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1ACLReply": {"type": "object", "properties": {"result": {"type": "string", "title": "可选 \"allow\" | \"deny\" | \"ignore\"(继续执行授权链)"}}}, "v1ACLRequest": {"type": "object", "properties": {"username": {"type": "string"}, "clientId": {"type": "string"}, "action": {"type": "string", "title": "publish, subscribe"}, "topic": {"type": "string"}}}, "v1AuthMqttClientReply": {"type": "object", "properties": {"result": {"type": "string", "title": "可选 \"allow\" | \"deny\" | \"ignore\"(继续执行授权链)"}, "isSuperuser": {"type": "boolean"}}}, "v1AuthMqttClientRequest": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "clientId": {"type": "string"}, "peerhost": {"type": "string"}}}}}