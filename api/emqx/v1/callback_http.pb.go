// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type EmqxCallbackHTTPServer interface {
	ACL(context.Context, *ACLRequest) (*ACLReply, error)
	AuthClient(context.Context, *AuthMqttClientRequest) (*AuthMqttClientReply, error)
}

func RegisterEmqxCallbackHTTPServer(s *http.Server, srv EmqxCallbackHTTPServer) {
	r := s.Route("/")
	r.POST("/internal/v1/emqx/auth", _EmqxCallback_AuthClient0_HTTP_Handler(srv))
	r.POST("/internal/v1/emqx/acl", _EmqxCallback_ACL0_HTTP_Handler(srv))
}

func _EmqxCallback_AuthClient0_HTTP_Handler(srv EmqxCallbackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AuthMqttClientRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.emqx.v1.EmqxCallback/AuthClient")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AuthClient(ctx, req.(*AuthMqttClientRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AuthMqttClientReply)
		return ctx.Result(200, reply)
	}
}

func _EmqxCallback_ACL0_HTTP_Handler(srv EmqxCallbackHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ACLRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.emqx.v1.EmqxCallback/ACL")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ACL(ctx, req.(*ACLRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ACLReply)
		return ctx.Result(200, reply)
	}
}

type EmqxCallbackHTTPClient interface {
	ACL(ctx context.Context, req *ACLRequest, opts ...http.CallOption) (rsp *ACLReply, err error)
	AuthClient(ctx context.Context, req *AuthMqttClientRequest, opts ...http.CallOption) (rsp *AuthMqttClientReply, err error)
}

type EmqxCallbackHTTPClientImpl struct {
	cc *http.Client
}

func NewEmqxCallbackHTTPClient(client *http.Client) EmqxCallbackHTTPClient {
	return &EmqxCallbackHTTPClientImpl{client}
}

func (c *EmqxCallbackHTTPClientImpl) ACL(ctx context.Context, in *ACLRequest, opts ...http.CallOption) (*ACLReply, error) {
	var out ACLReply
	pattern := "/internal/v1/emqx/acl"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.emqx.v1.EmqxCallback/ACL"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *EmqxCallbackHTTPClientImpl) AuthClient(ctx context.Context, in *AuthMqttClientRequest, opts ...http.CallOption) (*AuthMqttClientReply, error) {
	var out AuthMqttClientReply
	pattern := "/internal/v1/emqx/auth"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.emqx.v1.EmqxCallback/AuthClient"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
