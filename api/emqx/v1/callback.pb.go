// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/emqx/v1/callback.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthMqttClientRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	ClientId string `protobuf:"bytes,3,opt,name=clientId,proto3" json:"clientId,omitempty"`
	Peerhost string `protobuf:"bytes,4,opt,name=peerhost,proto3" json:"peerhost,omitempty"`
}

func (x *AuthMqttClientRequest) Reset() {
	*x = AuthMqttClientRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_emqx_v1_callback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthMqttClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthMqttClientRequest) ProtoMessage() {}

func (x *AuthMqttClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_emqx_v1_callback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthMqttClientRequest.ProtoReflect.Descriptor instead.
func (*AuthMqttClientRequest) Descriptor() ([]byte, []int) {
	return file_api_emqx_v1_callback_proto_rawDescGZIP(), []int{0}
}

func (x *AuthMqttClientRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *AuthMqttClientRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *AuthMqttClientRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *AuthMqttClientRequest) GetPeerhost() string {
	if x != nil {
		return x.Peerhost
	}
	return ""
}

type AuthMqttClientReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 可选 "allow" | "deny" | "ignore"(继续执行授权链)
	Result      string `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	IsSuperuser bool   `protobuf:"varint,2,opt,name=isSuperuser,json=is_superuser,proto3" json:"isSuperuser,omitempty"`
}

func (x *AuthMqttClientReply) Reset() {
	*x = AuthMqttClientReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_emqx_v1_callback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthMqttClientReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthMqttClientReply) ProtoMessage() {}

func (x *AuthMqttClientReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_emqx_v1_callback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthMqttClientReply.ProtoReflect.Descriptor instead.
func (*AuthMqttClientReply) Descriptor() ([]byte, []int) {
	return file_api_emqx_v1_callback_proto_rawDescGZIP(), []int{1}
}

func (x *AuthMqttClientReply) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *AuthMqttClientReply) GetIsSuperuser() bool {
	if x != nil {
		return x.IsSuperuser
	}
	return false
}

type ACLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	ClientId string `protobuf:"bytes,2,opt,name=clientId,proto3" json:"clientId,omitempty"`
	// publish, subscribe
	Action string `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	Topic  string `protobuf:"bytes,4,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *ACLRequest) Reset() {
	*x = ACLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_emqx_v1_callback_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ACLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ACLRequest) ProtoMessage() {}

func (x *ACLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_emqx_v1_callback_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ACLRequest.ProtoReflect.Descriptor instead.
func (*ACLRequest) Descriptor() ([]byte, []int) {
	return file_api_emqx_v1_callback_proto_rawDescGZIP(), []int{2}
}

func (x *ACLRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ACLRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ACLRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ACLRequest) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type ACLReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 可选 "allow" | "deny" | "ignore"(继续执行授权链)
	Result string `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *ACLReply) Reset() {
	*x = ACLReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_emqx_v1_callback_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ACLReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ACLReply) ProtoMessage() {}

func (x *ACLReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_emqx_v1_callback_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ACLReply.ProtoReflect.Descriptor instead.
func (*ACLReply) Descriptor() ([]byte, []int) {
	return file_api_emqx_v1_callback_proto_rawDescGZIP(), []int{3}
}

func (x *ACLReply) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

var File_api_emqx_v1_callback_proto protoreflect.FileDescriptor

var file_api_emqx_v1_callback_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6d, 0x71, 0x78, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x6d, 0x71, 0x78, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x01, 0x0a, 0x15, 0x41, 0x75, 0x74, 0x68,
	0x4d, 0x71, 0x74, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x65, 0x65, 0x72, 0x68, 0x6f, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x65, 0x72, 0x68, 0x6f, 0x73,
	0x74, 0x22, 0x50, 0x0a, 0x13, 0x41, 0x75, 0x74, 0x68, 0x4d, 0x71, 0x74, 0x74, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x21, 0x0a, 0x0b, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x70, 0x65, 0x72, 0x75,
	0x73, 0x65, 0x72, 0x22, 0x72, 0x0a, 0x0a, 0x41, 0x43, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x22, 0x0a, 0x08, 0x41, 0x43, 0x4c, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xde, 0x01, 0x0a, 0x0c,
	0x45, 0x6d, 0x71, 0x78, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x75, 0x0a, 0x0a,
	0x41, 0x75, 0x74, 0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x6d, 0x71, 0x78, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x4d, 0x71, 0x74,
	0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6d, 0x71, 0x78, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74,
	0x68, 0x4d, 0x71, 0x74, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x22, 0x16, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d, 0x71, 0x78, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x3a, 0x01, 0x2a, 0x12, 0x57, 0x0a, 0x03, 0x41, 0x43, 0x4c, 0x12, 0x17, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x6d, 0x71, 0x78, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6d, 0x71, 0x78, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x43, 0x4c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1a, 0x22, 0x15, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x6d, 0x71, 0x78, 0x2f, 0x61, 0x63, 0x6c, 0x3a, 0x01, 0x2a, 0x42, 0x3c, 0x0a, 0x0b,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x6d, 0x71, 0x78, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2b, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x65, 0x6d, 0x71, 0x78, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_emqx_v1_callback_proto_rawDescOnce sync.Once
	file_api_emqx_v1_callback_proto_rawDescData = file_api_emqx_v1_callback_proto_rawDesc
)

func file_api_emqx_v1_callback_proto_rawDescGZIP() []byte {
	file_api_emqx_v1_callback_proto_rawDescOnce.Do(func() {
		file_api_emqx_v1_callback_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_emqx_v1_callback_proto_rawDescData)
	})
	return file_api_emqx_v1_callback_proto_rawDescData
}

var file_api_emqx_v1_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_emqx_v1_callback_proto_goTypes = []interface{}{
	(*AuthMqttClientRequest)(nil), // 0: api.emqx.v1.AuthMqttClientRequest
	(*AuthMqttClientReply)(nil),   // 1: api.emqx.v1.AuthMqttClientReply
	(*ACLRequest)(nil),            // 2: api.emqx.v1.ACLRequest
	(*ACLReply)(nil),              // 3: api.emqx.v1.ACLReply
}
var file_api_emqx_v1_callback_proto_depIdxs = []int32{
	0, // 0: api.emqx.v1.EmqxCallback.AuthClient:input_type -> api.emqx.v1.AuthMqttClientRequest
	2, // 1: api.emqx.v1.EmqxCallback.ACL:input_type -> api.emqx.v1.ACLRequest
	1, // 2: api.emqx.v1.EmqxCallback.AuthClient:output_type -> api.emqx.v1.AuthMqttClientReply
	3, // 3: api.emqx.v1.EmqxCallback.ACL:output_type -> api.emqx.v1.ACLReply
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_emqx_v1_callback_proto_init() }
func file_api_emqx_v1_callback_proto_init() {
	if File_api_emqx_v1_callback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_emqx_v1_callback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthMqttClientRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_emqx_v1_callback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthMqttClientReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_emqx_v1_callback_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ACLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_emqx_v1_callback_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ACLReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_emqx_v1_callback_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_emqx_v1_callback_proto_goTypes,
		DependencyIndexes: file_api_emqx_v1_callback_proto_depIdxs,
		MessageInfos:      file_api_emqx_v1_callback_proto_msgTypes,
	}.Build()
	File_api_emqx_v1_callback_proto = out.File
	file_api_emqx_v1_callback_proto_rawDesc = nil
	file_api_emqx_v1_callback_proto_goTypes = nil
	file_api_emqx_v1_callback_proto_depIdxs = nil
}
