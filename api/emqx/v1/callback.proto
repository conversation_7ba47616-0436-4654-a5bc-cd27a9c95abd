syntax = "proto3";

package api.emqx.v1;

import "google/api/annotations.proto";


option go_package = "gitlab.sensoro.com/skai/skai/api/emqx/v1;v1";
option java_multiple_files = true;
option java_package = "api.emqx.v1";

service EmqxCallback {
	rpc AuthClient(AuthMqttClientRequest) returns (AuthMqttClientReply){
		option (google.api.http) = {
      post: "/internal/v1/emqx/auth"
			body: "*"
    };
	}

	rpc ACL(ACLRequest) returns (ACLReply) {
		option (google.api.http) = {
      post: "/internal/v1/emqx/acl"
			body: "*"
    };
	}

}

message AuthMqttClientRequest {
	string username = 1;
	string password = 2;
	string clientId = 3;
	string peerhost = 4;
}
message AuthMqttClientReply {
	// 可选 "allow" | "deny" | "ignore"(继续执行授权链)
	string result = 1;
	bool isSuperuser = 2 [json_name="is_superuser"];
}

message ACLRequest {
	string username = 1;
	string clientId = 2;
	// publish, subscribe
	string action = 3;
	string topic = 4;
}
message ACLReply {
	// 可选 "allow" | "deny" | "ignore"(继续执行授权链)
	string result = 1;
}
