{"swagger": "2.0", "info": {"title": "api/admin/v1/admin.proto", "version": "version not set"}, "tags": [{"name": "Admin"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/admin/devices": {"post": {"operationId": "Admin_RegisterDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiadminv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1RegisterRequest"}}], "tags": ["Admin"]}}, "/api/v1/admin/devices/{id}": {"delete": {"operationId": "Admin_DeleteDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiadminv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Admin"]}}, "/api/v1/admin/devices/{sn}/event": {"put": {"operationId": "Admin_EventUp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiadminv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "sn", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"id": {"type": "string"}, "rxTime": {"type": "string", "format": "int64"}, "timestamp": {"type": "string", "format": "int64"}, "deviceId": {"type": "string", "format": "int64"}, "type": {"type": "integer", "format": "int32"}, "flight": {"$ref": "#/definitions/v1FlightTaskProgressEvent"}, "hms": {"$ref": "#/definitions/v1DockHealMonitorEvent"}, "topo": {"$ref": "#/definitions/v1DockTopoUpdateEvent"}, "resourceReq": {"$ref": "#/definitions/v1DockFlightTaskResourceRequestEvent"}, "simpleEventValue": {"type": "string", "format": "byte"}}}}], "tags": ["Admin"]}}, "/api/v1/admin/devices/{sn}/property": {"put": {"operationId": "Admin_PropertyUp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiadminv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "sn", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"id": {"type": "string"}, "rxTime": {"type": "string", "format": "int64"}, "timestamp": {"type": "string", "format": "int64"}, "deviceId": {"type": "string", "format": "int64"}, "droneSn": {"type": "string"}, "droneType": {"type": "string"}, "mode": {"type": "integer", "format": "int32"}, "dock": {"$ref": "#/definitions/v1DockState"}, "pilot": {"$ref": "#/definitions/v1PilotState"}, "network": {"$ref": "#/definitions/v1NetworkState"}, "position": {"$ref": "#/definitions/v1PositionState"}, "env": {"$ref": "#/definitions/v1EnvironmentState"}, "flightTask": {"$ref": "#/definitions/v1FlightTaskState"}, "elec": {"$ref": "#/definitions/v1ElecPowerState"}, "storage": {"$ref": "#/definitions/v1StorageState"}, "flight": {"$ref": "#/definitions/v1DroneFlightState"}, "battery": {"$ref": "#/definitions/v1DroneBatteryState"}, "charge": {"$ref": "#/definitions/v1BatteryChargeState"}, "wirelessLink": {"$ref": "#/definitions/v1WirelessLinkState"}, "version": {"$ref": "#/definitions/v1Version"}, "other": {"type": "object"}, "speaker": {"$ref": "#/definitions/v1DroneSpeakerState"}, "cameras": {"type": "array", "items": {"$ref": "#/definitions/v1DroneCameraState"}}}}}], "tags": ["Admin"]}}, "/api/v1/admin/devices/{sn}/reply": {"put": {"operationId": "Admin_ServiceReply", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiadminv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "sn", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"id": {"type": "string"}, "rxTime": {"type": "string", "format": "int64"}, "timestamp": {"type": "string", "format": "int64"}, "deviceId": {"type": "string", "format": "int64"}, "serviceId": {"type": "string", "format": "int64"}, "identifier": {"type": "string"}, "status": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}}}}], "tags": ["Admin"]}}}, "definitions": {"DockTopoUpdateEventsubdevice": {"type": "object", "properties": {"sn": {"type": "string"}, "type": {"type": "string"}, "index": {"type": "string"}, "extra": {"type": "object"}, "domain": {"type": "integer", "format": "int32"}}}, "DroneBatteryStateBattery": {"type": "object", "properties": {"index": {"type": "integer", "format": "int32"}, "capacityPercent": {"type": "integer", "format": "int32"}, "voltage": {"type": "integer", "format": "int32"}, "temperature": {"type": "number", "format": "float"}, "loop": {"type": "integer", "format": "int32"}, "sn": {"type": "string"}}}, "apiadminv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apiadminv1CommonReplycommonData"}}}, "apiadminv1CommonReplycommonData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\n The JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1BatteryChargeState": {"type": "object", "properties": {"droneBatteryPercent": {"type": "integer", "format": "int32"}, "droneBatteryChargeState": {"type": "integer", "format": "int32"}}}, "v1DockFlightTaskResourceRequestEvent": {"type": "object", "properties": {"flightId": {"type": "string"}}}, "v1DockHealMonitorEvent": {"type": "object", "properties": {"level": {"type": "integer", "format": "int32"}, "module": {"type": "integer", "format": "int32"}, "inTheSky": {"type": "integer", "format": "int32"}, "imminent": {"type": "integer", "format": "int32"}, "code": {"type": "string"}, "extra": {"type": "object"}, "source": {"type": "integer", "format": "int32"}}}, "v1DockState": {"type": "object", "properties": {"mode": {"type": "integer", "format": "int32"}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "coverState": {"type": "integer", "format": "int32"}, "putterState": {"type": "integer", "format": "int32"}, "supplementLightState": {"type": "integer", "format": "int32"}, "temperature": {"type": "number", "format": "float"}, "humidity": {"type": "number", "format": "float"}, "storageTotal": {"type": "string", "format": "int64"}, "storageUsed": {"type": "string", "format": "int64"}, "airConditionerState": {"type": "integer", "format": "int32"}, "droneOnlineState": {"type": "integer", "format": "int32"}}}, "v1DockTopoUpdateEvent": {"type": "object", "properties": {"deviceModel": {"type": "string"}, "subdevices": {"type": "array", "items": {"$ref": "#/definitions/DockTopoUpdateEventsubdevice"}}}}, "v1DroneBatteryState": {"type": "object", "properties": {"capacityPercent": {"type": "integer", "format": "int32"}, "remainFlightTime": {"type": "integer", "format": "int32"}, "returnHomePower": {"type": "integer", "format": "int32"}, "landingPower": {"type": "integer", "format": "int32"}, "batteries": {"type": "array", "items": {"$ref": "#/definitions/DroneBatteryStateBattery"}}}}, "v1DroneCameraLiveViewWorldRegion": {"type": "object", "properties": {"top": {"type": "integer", "format": "int32"}, "bottom": {"type": "integer", "format": "int32"}, "left": {"type": "integer", "format": "int32"}, "right": {"type": "integer", "format": "int32"}}}, "v1DroneCameraState": {"type": "object", "properties": {"index": {"type": "string"}, "mode": {"type": "integer", "format": "int32"}, "photoState": {"type": "integer", "format": "int32"}, "recordingState": {"type": "integer", "format": "int32"}, "zoomFactor": {"type": "number", "format": "float"}, "irZoomFactor": {"type": "number", "format": "float"}, "zoomFocusMode": {"type": "integer", "format": "int32"}, "zoomFocusValue": {"type": "integer", "format": "int32"}, "zoomFocusState": {"type": "integer", "format": "int32"}, "irMeteringMode": {"type": "integer", "format": "int32"}, "gimbalPitch": {"type": "number", "format": "double"}, "gimbalYaw": {"type": "number", "format": "double"}, "gimbalRoll": {"type": "number", "format": "double"}, "measureTargetLongitude": {"type": "number", "format": "double"}, "measureTargetLatitude": {"type": "number", "format": "double"}, "measureTargetAltitude": {"type": "number", "format": "double"}, "measureTargetDistance": {"type": "number", "format": "double"}, "measureErrState": {"type": "integer", "format": "int32"}, "liveViewWorldRegion": {"$ref": "#/definitions/v1DroneCameraLiveViewWorldRegion"}}}, "v1DroneFlightState": {"type": "object", "properties": {"horizontalSpeed": {"type": "number", "format": "float"}, "verticalSpeed": {"type": "number", "format": "float"}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "float"}, "elevation": {"type": "number", "format": "float"}, "attitudePitch": {"type": "number", "format": "float"}, "attitudeRoll": {"type": "number", "format": "float"}, "attitudeHead": {"type": "number", "format": "float"}, "totalFlightTime": {"type": "string", "format": "int64"}, "windSpeed": {"type": "number", "format": "float"}, "windDirection": {"type": "integer", "format": "int32"}, "isNearHeightLimit": {"type": "integer", "format": "int32"}, "isNearAreaLimit": {"type": "integer", "format": "int32"}, "isNearIDistanceLimit": {"type": "integer", "format": "int32"}, "nightLightsState": {"type": "integer", "format": "int32"}, "horizonObstacleAvoidance": {"type": "integer", "format": "int32"}, "upsideObstacleAvoidance": {"type": "integer", "format": "int32"}, "downsideObstacleAvoidance": {"type": "integer", "format": "int32"}}}, "v1DroneSpeakerState": {"type": "object", "properties": {"index": {"type": "string"}, "name": {"type": "string"}, "sn": {"type": "string"}, "firmwareVersion": {"type": "string"}, "libVersion": {"type": "string"}, "type": {"type": "string"}, "workMode": {"type": "integer", "format": "int32"}, "systemState": {"type": "integer", "format": "int32"}, "playMode": {"type": "integer", "format": "int32"}, "playVolume": {"type": "integer", "format": "int32"}, "playFileName": {"type": "string"}, "playFileSignature": {"type": "string"}}}, "v1ElecPowerState": {"type": "object", "properties": {"workingVoltage": {"type": "integer", "format": "int32"}, "workingCurrent": {"type": "integer", "format": "int32"}, "dockBackupBatterySwitch": {"type": "integer", "format": "int32"}, "dockBackupBatteryVoltage": {"type": "integer", "format": "int32"}, "dockBackupTemperature": {"type": "number", "format": "float"}, "supplyVoltage": {"type": "integer", "format": "int32"}}}, "v1EnvironmentState": {"type": "object", "properties": {"rainfall": {"type": "integer", "format": "int32"}, "windSpeed": {"type": "number", "format": "float"}, "temperature": {"type": "number", "format": "float"}, "humidity": {"type": "number", "format": "float"}}}, "v1FlightTaskProgressEvent": {"type": "object", "properties": {"flightId": {"type": "string"}, "currentWaypointIndex": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "percent": {"type": "integer", "format": "int32"}, "extra": {"type": "object"}, "step": {"type": "integer", "format": "int32"}}}, "v1FlightTaskState": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "safeLandHeight": {"type": "number", "format": "float"}, "safeLandConfigured": {"type": "integer", "format": "int32"}, "droneInDock": {"type": "integer", "format": "int32"}}}, "v1NetworkState": {"type": "object", "properties": {"type": {"type": "integer", "format": "int32"}, "quality": {"type": "integer", "format": "int32"}}}, "v1PilotState": {"type": "object", "properties": {"capacityPercent": {"type": "integer", "format": "int32"}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}}}, "v1PositionState": {"type": "object", "properties": {"isFixed": {"type": "integer", "format": "int32"}, "isCalibration": {"type": "integer", "format": "int32"}, "quality": {"type": "integer", "format": "int32"}, "gpsNumber": {"type": "integer", "format": "int32"}, "rtkNumber": {"type": "integer", "format": "int32"}}}, "v1RegisterRequest": {"type": "object", "properties": {"sn": {"type": "string"}, "type": {"type": "string"}, "model": {"type": "string"}, "source": {"type": "string"}, "category": {"type": "string"}}}, "v1StorageState": {"type": "object", "properties": {"total": {"type": "string", "format": "int64"}, "used": {"type": "string", "format": "int64"}}}, "v1Version": {"type": "object", "properties": {"firmware": {"type": "string"}}}, "v1WirelessLinkState": {"type": "object", "properties": {"mode": {"type": "integer", "format": "int32"}, "state4G": {"type": "integer", "format": "int32"}, "stateSDR": {"type": "integer", "format": "int32"}, "qualitySDR": {"type": "integer", "format": "int32"}, "quality4G": {"type": "integer", "format": "int32"}, "quality4GUav": {"type": "integer", "format": "int32"}, "quality4GGnd": {"type": "integer", "format": "int32"}}}}}