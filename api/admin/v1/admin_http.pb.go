// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "gitlab.sensoro.com/skai/skai/api/connects/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type AdminHTTPServer interface {
	DeleteDevice(context.Context, *CommonRequest) (*CommonReply, error)
	EventUp(context.Context, *v1.EventUpRequest) (*CommonReply, error)
	PropertyUp(context.Context, *v1.PropertyUpRequest) (*CommonReply, error)
	RegisterDevice(context.Context, *RegisterRequest) (*CommonReply, error)
	ServiceReply(context.Context, *v1.ServiceReplyRequest) (*CommonReply, error)
}

func RegisterAdminHTTPServer(s *http.Server, srv AdminHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/admin/devices", _Admin_RegisterDevice0_HTTP_Handler(srv))
	r.DELETE("/api/v1/admin/devices/{id}", _Admin_DeleteDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/admin/devices/{sn}/event", _Admin_EventUp0_HTTP_Handler(srv))
	r.PUT("/api/v1/admin/devices/{sn}/property", _Admin_PropertyUp0_HTTP_Handler(srv))
	r.PUT("/api/v1/admin/devices/{sn}/reply", _Admin_ServiceReply0_HTTP_Handler(srv))
}

func _Admin_RegisterDevice0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegisterRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.admin.v1.Admin/RegisterDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegisterDevice(ctx, req.(*RegisterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_DeleteDevice0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.admin.v1.Admin/DeleteDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDevice(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_EventUp0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.EventUpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.admin.v1.Admin/EventUp")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EventUp(ctx, req.(*v1.EventUpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_PropertyUp0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PropertyUpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.admin.v1.Admin/PropertyUp")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PropertyUp(ctx, req.(*v1.PropertyUpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ServiceReply0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.ServiceReplyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.admin.v1.Admin/ServiceReply")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ServiceReply(ctx, req.(*v1.ServiceReplyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type AdminHTTPClient interface {
	DeleteDevice(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	EventUp(ctx context.Context, req *v1.EventUpRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	PropertyUp(ctx context.Context, req *v1.PropertyUpRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	RegisterDevice(ctx context.Context, req *RegisterRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	ServiceReply(ctx context.Context, req *v1.ServiceReplyRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type AdminHTTPClientImpl struct {
	cc *http.Client
}

func NewAdminHTTPClient(client *http.Client) AdminHTTPClient {
	return &AdminHTTPClientImpl{client}
}

func (c *AdminHTTPClientImpl) DeleteDevice(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/admin/devices/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.admin.v1.Admin/DeleteDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AdminHTTPClientImpl) EventUp(ctx context.Context, in *v1.EventUpRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/admin/devices/{sn}/event"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.admin.v1.Admin/EventUp"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AdminHTTPClientImpl) PropertyUp(ctx context.Context, in *v1.PropertyUpRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/admin/devices/{sn}/property"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.admin.v1.Admin/PropertyUp"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AdminHTTPClientImpl) RegisterDevice(ctx context.Context, in *RegisterRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/admin/devices"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.admin.v1.Admin/RegisterDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AdminHTTPClientImpl) ServiceReply(ctx context.Context, in *v1.ServiceReplyRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/admin/devices/{sn}/reply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.admin.v1.Admin/ServiceReply"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
