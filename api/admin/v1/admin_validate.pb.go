// Code generated by protoc-gen-go-http. DO NOT EDIT.

package v1

import (
	context "context"
	v10 "github.com/go-playground/validator/v10"
	validator "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
)

var _ = new(context.Context)
var _ = new(v10.Validate)
var _ = new(validator.ValidateError)

func (m *CommonRequest) Validate() error {
	ctx := context.TODO()
	v := v10.New()
	if err := v.VarCtx(ctx, m.Id, "required"); err != nil {
		return validator.WrapValidatorError("Id", err)
	}
	return nil
}
