syntax = "proto3";

package api.admin.v1;
import "google/api/annotations.proto";
import "validator/validator.proto";
import "api/connects/v1/connect.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/admin/v1;v1";
option java_multiple_files = true;
option java_package = "api.admin.v1";

service Admin {
	rpc RegisterDevice (RegisterRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/admin/devices"
			body: "*"
    };
  };
  rpc DeleteDevice (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      delete: "/api/v1/admin/devices/{id}"
    };
  };
	rpc EventUp (api.connects.v1.EventUpRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/admin/devices/{sn}/event"
			body: "*"
    };
  };
	rpc PropertyUp (api.connects.v1.PropertyUpRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/admin/devices/{sn}/property"
			body: "*"
    };
  };
	rpc ServiceReply (api.connects.v1.ServiceReplyRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/admin/devices/{sn}/reply"
			body: "*"
    };
  };
}

message CommonRequest {
  int64 id = 1 [(validator.rules) = "required"];
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message RegisterRequest {
	string sn = 1;
	string type = 2;
	string model = 3;
  string source = 4;
	string category = 5;
}
