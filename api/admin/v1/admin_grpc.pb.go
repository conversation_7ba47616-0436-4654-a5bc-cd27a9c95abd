// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/admin/v1/admin.proto

package v1

import (
	context "context"
	v1 "gitlab.sensoro.com/skai/skai/api/connects/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AdminClient is the client API for Admin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdminClient interface {
	RegisterDevice(ctx context.Context, in *RegisterRequest, opts ...grpc.CallOption) (*CommonReply, error)
	DeleteDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	EventUp(ctx context.Context, in *v1.EventUpRequest, opts ...grpc.CallOption) (*CommonReply, error)
	PropertyUp(ctx context.Context, in *v1.PropertyUpRequest, opts ...grpc.CallOption) (*CommonReply, error)
	ServiceReply(ctx context.Context, in *v1.ServiceReplyRequest, opts ...grpc.CallOption) (*CommonReply, error)
}

type adminClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminClient(cc grpc.ClientConnInterface) AdminClient {
	return &adminClient{cc}
}

func (c *adminClient) RegisterDevice(ctx context.Context, in *RegisterRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.admin.v1.Admin/RegisterDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.admin.v1.Admin/DeleteDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) EventUp(ctx context.Context, in *v1.EventUpRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.admin.v1.Admin/EventUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) PropertyUp(ctx context.Context, in *v1.PropertyUpRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.admin.v1.Admin/PropertyUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ServiceReply(ctx context.Context, in *v1.ServiceReplyRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.admin.v1.Admin/ServiceReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminServer is the server API for Admin service.
// All implementations must embed UnimplementedAdminServer
// for forward compatibility
type AdminServer interface {
	RegisterDevice(context.Context, *RegisterRequest) (*CommonReply, error)
	DeleteDevice(context.Context, *CommonRequest) (*CommonReply, error)
	EventUp(context.Context, *v1.EventUpRequest) (*CommonReply, error)
	PropertyUp(context.Context, *v1.PropertyUpRequest) (*CommonReply, error)
	ServiceReply(context.Context, *v1.ServiceReplyRequest) (*CommonReply, error)
	mustEmbedUnimplementedAdminServer()
}

// UnimplementedAdminServer must be embedded to have forward compatible implementations.
type UnimplementedAdminServer struct {
}

func (UnimplementedAdminServer) RegisterDevice(context.Context, *RegisterRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterDevice not implemented")
}
func (UnimplementedAdminServer) DeleteDevice(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDevice not implemented")
}
func (UnimplementedAdminServer) EventUp(context.Context, *v1.EventUpRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EventUp not implemented")
}
func (UnimplementedAdminServer) PropertyUp(context.Context, *v1.PropertyUpRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PropertyUp not implemented")
}
func (UnimplementedAdminServer) ServiceReply(context.Context, *v1.ServiceReplyRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServiceReply not implemented")
}
func (UnimplementedAdminServer) mustEmbedUnimplementedAdminServer() {}

// UnsafeAdminServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminServer will
// result in compilation errors.
type UnsafeAdminServer interface {
	mustEmbedUnimplementedAdminServer()
}

func RegisterAdminServer(s grpc.ServiceRegistrar, srv AdminServer) {
	s.RegisterService(&Admin_ServiceDesc, srv)
}

func _Admin_RegisterDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).RegisterDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.admin.v1.Admin/RegisterDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).RegisterDevice(ctx, req.(*RegisterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.admin.v1.Admin/DeleteDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteDevice(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_EventUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.EventUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).EventUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.admin.v1.Admin/EventUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).EventUp(ctx, req.(*v1.EventUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_PropertyUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PropertyUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).PropertyUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.admin.v1.Admin/PropertyUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).PropertyUp(ctx, req.(*v1.PropertyUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ServiceReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.ServiceReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ServiceReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.admin.v1.Admin/ServiceReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ServiceReply(ctx, req.(*v1.ServiceReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Admin_ServiceDesc is the grpc.ServiceDesc for Admin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Admin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.admin.v1.Admin",
	HandlerType: (*AdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegisterDevice",
			Handler:    _Admin_RegisterDevice_Handler,
		},
		{
			MethodName: "DeleteDevice",
			Handler:    _Admin_DeleteDevice_Handler,
		},
		{
			MethodName: "EventUp",
			Handler:    _Admin_EventUp_Handler,
		},
		{
			MethodName: "PropertyUp",
			Handler:    _Admin_PropertyUp_Handler,
		},
		{
			MethodName: "ServiceReply",
			Handler:    _Admin_ServiceReply_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/admin/v1/admin.proto",
}
