// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type OnemapHTTPServer interface {
	GetDevice(context.Context, *CommonRequest) (*DeviceReply, error)
	ListDevice(context.Context, *ListRequest) (*ListReply, error)
	ListJoint(context.Context, *JointRequest) (*ListReply, error)
	Statistics(context.Context, *StatisticsRequest) (*StatisticsReply, error)
}

func RegisterOnemapHTTPServer(s *http.Server, srv OnemapHTTPServer) {
	r := s.Route("/")
	r.GET("/api/v1/onemap/statistics", _Onemap_Statistics0_HTTP_Handler(srv))
	r.GET("/api/v1/onemap/devices", _Onemap_ListDevice0_HTTP_Handler(srv))
	r.GET("/api/v1/onemap/devices/{id}", _Onemap_GetDevice0_HTTP_Handler(srv))
	r.GET("/api/v1/onemap/joints", _Onemap_ListJoint0_HTTP_Handler(srv))
}

func _Onemap_Statistics0_HTTP_Handler(srv OnemapHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StatisticsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.map.v1.Onemap/Statistics")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Statistics(ctx, req.(*StatisticsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StatisticsReply)
		return ctx.Result(200, reply)
	}
}

func _Onemap_ListDevice0_HTTP_Handler(srv OnemapHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.map.v1.Onemap/ListDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDevice(ctx, req.(*ListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListReply)
		return ctx.Result(200, reply)
	}
}

func _Onemap_GetDevice0_HTTP_Handler(srv OnemapHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.map.v1.Onemap/GetDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDevice(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeviceReply)
		return ctx.Result(200, reply)
	}
}

func _Onemap_ListJoint0_HTTP_Handler(srv OnemapHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JointRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.map.v1.Onemap/ListJoint")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListJoint(ctx, req.(*JointRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListReply)
		return ctx.Result(200, reply)
	}
}

type OnemapHTTPClient interface {
	GetDevice(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *DeviceReply, err error)
	ListDevice(ctx context.Context, req *ListRequest, opts ...http.CallOption) (rsp *ListReply, err error)
	ListJoint(ctx context.Context, req *JointRequest, opts ...http.CallOption) (rsp *ListReply, err error)
	Statistics(ctx context.Context, req *StatisticsRequest, opts ...http.CallOption) (rsp *StatisticsReply, err error)
}

type OnemapHTTPClientImpl struct {
	cc *http.Client
}

func NewOnemapHTTPClient(client *http.Client) OnemapHTTPClient {
	return &OnemapHTTPClientImpl{client}
}

func (c *OnemapHTTPClientImpl) GetDevice(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*DeviceReply, error) {
	var out DeviceReply
	pattern := "/api/v1/onemap/devices/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.map.v1.Onemap/GetDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *OnemapHTTPClientImpl) ListDevice(ctx context.Context, in *ListRequest, opts ...http.CallOption) (*ListReply, error) {
	var out ListReply
	pattern := "/api/v1/onemap/devices"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.map.v1.Onemap/ListDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *OnemapHTTPClientImpl) ListJoint(ctx context.Context, in *JointRequest, opts ...http.CallOption) (*ListReply, error) {
	var out ListReply
	pattern := "/api/v1/onemap/joints"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.map.v1.Onemap/ListJoint"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *OnemapHTTPClientImpl) Statistics(ctx context.Context, in *StatisticsRequest, opts ...http.CallOption) (*StatisticsReply, error) {
	var out StatisticsReply
	pattern := "/api/v1/onemap/statistics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.map.v1.Onemap/Statistics"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
