// Code generated by protoc-gen-list. DO NOT EDIT.

package v1

import (
	list "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list"
	time "time"
)

var _ = new(list.Condition)
var _ = time.Now()

func (m *ListRequest) PageInfo() (int, int) { return int(m.Page), int(m.Size) }
func (m *ListRequest) Conditions() *list.Condition {
	cond := list.NewCondition()
	if m.Search != nil {
		cond = cond.Where("sn,deployment_name,deployment_tags", "search", m.Search)
	}
	return cond
}
