{"swagger": "2.0", "info": {"title": "api/onemap/v1/onemap.proto", "version": "version not set"}, "tags": [{"name": "Onemap"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/onemap/devices": {"get": {"operationId": "Onemap_ListDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apimapv1ListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "search", "in": "query", "required": false, "type": "string"}, {"name": "type", "in": "query", "required": false, "type": "string"}, {"name": "model", "in": "query", "required": false, "type": "string"}, {"name": "networkStatus", "in": "query", "required": false, "type": "boolean"}], "tags": ["Onemap"]}}, "/api/v1/onemap/devices/{id}": {"get": {"operationId": "Onemap_GetDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apimapv1DeviceReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Onemap"]}}, "/api/v1/onemap/statistics": {"get": {"operationId": "Onemap_Statistics", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1StatisticsReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "deviceId", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["Onemap"]}}}, "definitions": {"DeviceReplybriefAvatar": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "mobile": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}}}, "DeviceReplybriefGimbal": {"type": "object", "properties": {"index": {"type": "string"}, "mode": {"type": "integer", "format": "int32"}, "photoState": {"type": "integer", "format": "int32"}, "recordingState": {"type": "integer", "format": "int32"}, "zoomFactor": {"type": "number", "format": "float"}, "irZoomFactor": {"type": "number", "format": "float"}, "zoomFocusMode": {"type": "integer", "format": "int32"}, "zoomFocusValue": {"type": "integer", "format": "int32"}, "zoomFocusState": {"type": "integer", "format": "int32"}, "irMeteringMode": {"type": "integer", "format": "int32"}, "gimbalPitch": {"type": "number", "format": "double"}, "gimbalYaw": {"type": "number", "format": "double"}, "gimbalRoll": {"type": "number", "format": "double"}, "measureTargetLongitude": {"type": "number", "format": "double"}, "measureTargetLatitude": {"type": "number", "format": "double"}, "measureTargetAltitude": {"type": "number", "format": "double"}, "measureTargetDistance": {"type": "number", "format": "double"}, "measureErrState": {"type": "integer", "format": "int32"}}}, "DeviceReplybriefSpeaker": {"type": "object", "properties": {"sn": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "index": {"type": "string"}, "mode": {"type": "integer", "format": "int32"}, "volume": {"type": "integer", "format": "int32"}, "playmode": {"type": "integer", "format": "int32"}}}, "DeviceReplybriefVoyage": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "sn": {"type": "string"}, "isFlown": {"type": "boolean"}, "runtime": {"type": "integer", "format": "int32"}, "mileage": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "endTime": {"type": "number", "format": "double"}, "deviceId": {"type": "string", "format": "int64"}, "airlineId": {"type": "string", "format": "int64"}, "startTime": {"type": "number", "format": "double"}}}, "DeviceReplyitem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "sn": {"type": "string"}, "type": {"type": "string"}, "name": {"type": "string"}, "model": {"type": "string"}, "status": {"type": "string"}, "category": {"type": "string"}, "location": {"type": "string"}, "videoStatus": {"type": "boolean"}, "merchantId": {"type": "string", "format": "int64"}, "aeroMode": {"type": "integer", "format": "int32"}, "flyControl": {"type": "integer", "format": "int32"}, "hoverControl": {"type": "integer", "format": "int32"}, "lensControl": {"type": "integer", "format": "int32"}, "aeroControl": {"type": "integer", "format": "int32"}, "speakerControl": {"type": "integer", "format": "int32"}, "networkStatus": {"type": "boolean"}, "signalQuality": {"type": "string"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "avatarId": {"type": "string", "format": "int64"}, "airlineId": {"type": "string", "format": "int64"}, "missionId": {"type": "string", "format": "int64"}, "uppedTime": {"type": "number", "format": "double"}, "avatar": {"$ref": "#/definitions/DeviceReplybriefAvatar"}, "gimbal": {"$ref": "#/definitions/DeviceReplybriefGimbal"}, "speaker": {"$ref": "#/definitions/DeviceReplybriefSpeaker"}, "liveVideos": {"type": "array", "items": {"$ref": "#/definitions/DeviceReplyliveVideo"}}, "lastVoyage": {"$ref": "#/definitions/DeviceReplybriefVoyage"}, "propData": {"type": "object"}, "extraData": {"type": "object"}, "launchData": {"type": "object"}, "subdevices": {"type": "array", "items": {"type": "object"}}}}, "DeviceReplyliveVideo": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "url": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "index": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "key": {"type": "string"}, "clarity": {"type": "integer", "format": "int32"}, "position": {"type": "integer", "format": "int32"}, "switchable": {"type": "array", "items": {"type": "string"}}}}, "ListReplydevice": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "sn": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "model": {"type": "string"}, "status": {"type": "string"}, "category": {"type": "string"}, "location": {"type": "string"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "lockStatus": {"type": "integer", "format": "int32"}, "cabinStatus": {"type": "boolean"}, "voyageTimes": {"type": "integer", "format": "int32"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "propData": {"type": "object"}, "subdevices": {"type": "array", "items": {"type": "object"}}}}, "StatisticsReplystatData": {"type": "object", "properties": {"dockCount": {"type": "integer", "format": "int32"}, "onlineDock": {"type": "integer", "format": "int32"}, "pilotCount": {"type": "integer", "format": "int32"}, "onlinePilot": {"type": "integer", "format": "int32"}, "imageCount": {"type": "integer", "format": "int32"}, "videoCount": {"type": "integer", "format": "int32"}, "voyageTimes": {"type": "integer", "format": "int32"}, "voyageMileages": {"type": "integer", "format": "int32"}}}, "apimapv1DeviceReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/DeviceReplyitem"}}}, "apimapv1ListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apimapv1ListReplylistData"}}}, "apimapv1ListReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/ListReplydevice"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\n The JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1StatisticsReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/StatisticsReplystatData"}}}}}