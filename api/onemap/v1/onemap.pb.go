// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/onemap/v1/onemap.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list"
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CommonRequest) Reset() {
	*x = CommonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRequest) ProtoMessage() {}

func (x *CommonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRequest.ProtoReflect.Descriptor instead.
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{0}
}

func (x *CommonRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type StatisticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId *int64 `protobuf:"varint,1,opt,name=deviceId,proto3,oneof" json:"deviceId,omitempty"`
}

func (x *StatisticsRequest) Reset() {
	*x = StatisticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticsRequest) ProtoMessage() {}

func (x *StatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticsRequest.ProtoReflect.Descriptor instead.
func (*StatisticsRequest) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{1}
}

func (x *StatisticsRequest) GetDeviceId() int64 {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return 0
}

type StatisticsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *StatisticsReplyStatData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *StatisticsReply) Reset() {
	*x = StatisticsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticsReply) ProtoMessage() {}

func (x *StatisticsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticsReply.ProtoReflect.Descriptor instead.
func (*StatisticsReply) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{2}
}

func (x *StatisticsReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *StatisticsReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StatisticsReply) GetData() *StatisticsReplyStatData {
	if x != nil {
		return x.Data
	}
	return nil
}

type JointRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lnglat string `protobuf:"bytes,1,opt,name=lnglat,proto3" json:"lnglat,omitempty"`
}

func (x *JointRequest) Reset() {
	*x = JointRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JointRequest) ProtoMessage() {}

func (x *JointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JointRequest.ProtoReflect.Descriptor instead.
func (*JointRequest) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{3}
}

func (x *JointRequest) GetLnglat() string {
	if x != nil {
		return x.Lnglat
	}
	return ""
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page          int32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size          int32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Search        *string `protobuf:"bytes,3,opt,name=search,proto3,oneof" json:"search,omitempty"`
	Type          *string `protobuf:"bytes,4,opt,name=type,proto3,oneof" json:"type,omitempty"`
	Model         *string `protobuf:"bytes,5,opt,name=model,proto3,oneof" json:"model,omitempty"`
	NetworkStatus *bool   `protobuf:"varint,6,opt,name=networkStatus,proto3,oneof" json:"networkStatus,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{4}
}

func (x *ListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListRequest) GetSearch() string {
	if x != nil && x.Search != nil {
		return *x.Search
	}
	return ""
}

func (x *ListRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *ListRequest) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *ListRequest) GetNetworkStatus() bool {
	if x != nil && x.NetworkStatus != nil {
		return *x.NetworkStatus
	}
	return false
}

type ListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ListReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListReply) Reset() {
	*x = ListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReply) ProtoMessage() {}

func (x *ListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReply.ProtoReflect.Descriptor instead.
func (*ListReply) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{5}
}

func (x *ListReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListReply) GetData() *ListReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeviceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *DeviceReplyItem `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DeviceReply) Reset() {
	*x = DeviceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReply) ProtoMessage() {}

func (x *DeviceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReply.ProtoReflect.Descriptor instead.
func (*DeviceReply) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{6}
}

func (x *DeviceReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeviceReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeviceReply) GetData() *DeviceReplyItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type StatisticsReplyStatData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DockCount      int32 `protobuf:"varint,1,opt,name=dockCount,proto3" json:"dockCount,omitempty"`
	OnlineDock     int32 `protobuf:"varint,2,opt,name=onlineDock,proto3" json:"onlineDock,omitempty"`
	PilotCount     int32 `protobuf:"varint,3,opt,name=pilotCount,proto3" json:"pilotCount,omitempty"`
	OnlinePilot    int32 `protobuf:"varint,4,opt,name=onlinePilot,proto3" json:"onlinePilot,omitempty"`
	ImageCount     int32 `protobuf:"varint,5,opt,name=imageCount,proto3" json:"imageCount,omitempty"`
	VideoCount     int32 `protobuf:"varint,6,opt,name=videoCount,proto3" json:"videoCount,omitempty"`
	VoyageTimes    int32 `protobuf:"varint,7,opt,name=voyageTimes,proto3" json:"voyageTimes,omitempty"`
	VoyageMileages int32 `protobuf:"varint,8,opt,name=voyageMileages,proto3" json:"voyageMileages,omitempty"`
}

func (x *StatisticsReplyStatData) Reset() {
	*x = StatisticsReplyStatData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticsReplyStatData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticsReplyStatData) ProtoMessage() {}

func (x *StatisticsReplyStatData) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticsReplyStatData.ProtoReflect.Descriptor instead.
func (*StatisticsReplyStatData) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{2, 0}
}

func (x *StatisticsReplyStatData) GetDockCount() int32 {
	if x != nil {
		return x.DockCount
	}
	return 0
}

func (x *StatisticsReplyStatData) GetOnlineDock() int32 {
	if x != nil {
		return x.OnlineDock
	}
	return 0
}

func (x *StatisticsReplyStatData) GetPilotCount() int32 {
	if x != nil {
		return x.PilotCount
	}
	return 0
}

func (x *StatisticsReplyStatData) GetOnlinePilot() int32 {
	if x != nil {
		return x.OnlinePilot
	}
	return 0
}

func (x *StatisticsReplyStatData) GetImageCount() int32 {
	if x != nil {
		return x.ImageCount
	}
	return 0
}

func (x *StatisticsReplyStatData) GetVideoCount() int32 {
	if x != nil {
		return x.VideoCount
	}
	return 0
}

func (x *StatisticsReplyStatData) GetVoyageTimes() int32 {
	if x != nil {
		return x.VoyageTimes
	}
	return 0
}

func (x *StatisticsReplyStatData) GetVoyageMileages() int32 {
	if x != nil {
		return x.VoyageMileages
	}
	return 0
}

type ListReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32              `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32              `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32              `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*ListReplyDevice `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListReplyListData) Reset() {
	*x = ListReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReplyListData) ProtoMessage() {}

func (x *ListReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReplyListData.ProtoReflect.Descriptor instead.
func (*ListReplyListData) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ListReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListReplyListData) GetList() []*ListReplyDevice {
	if x != nil {
		return x.List
	}
	return nil
}

type ListReplyDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sn           string             `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	Name         string             `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type         string             `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Model        string             `protobuf:"bytes,5,opt,name=model,proto3" json:"model,omitempty"`
	Status       string             `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	Category     string             `protobuf:"bytes,7,opt,name=category,proto3" json:"category,omitempty"`
	Location     string             `protobuf:"bytes,8,opt,name=location,proto3" json:"location,omitempty"`
	TenantId     int64              `protobuf:"varint,9,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	MerchantId   int64              `protobuf:"varint,10,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	LockStatus   int32              `protobuf:"varint,11,opt,name=lockStatus,proto3" json:"lockStatus,omitempty"`
	CabinStatus  bool               `protobuf:"varint,12,opt,name=cabinStatus,proto3" json:"cabinStatus,omitempty"`
	VoyageTimes  int32              `protobuf:"varint,13,opt,name=voyageTimes,proto3" json:"voyageTimes,omitempty"`
	Lnglat       []float64          `protobuf:"fixed64,14,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	PropData     *structpb.Struct   `protobuf:"bytes,15,opt,name=propData,proto3" json:"propData,omitempty"`
	Subdevices   []*structpb.Struct `protobuf:"bytes,16,rep,name=subdevices,proto3" json:"subdevices,omitempty"`
	RelatedChIds []int64            `protobuf:"varint,17,rep,packed,name=relatedChIds,proto3" json:"relatedChIds,omitempty"`
}

func (x *ListReplyDevice) Reset() {
	*x = ListReplyDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReplyDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReplyDevice) ProtoMessage() {}

func (x *ListReplyDevice) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReplyDevice.ProtoReflect.Descriptor instead.
func (*ListReplyDevice) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{5, 1}
}

func (x *ListReplyDevice) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListReplyDevice) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ListReplyDevice) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListReplyDevice) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ListReplyDevice) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ListReplyDevice) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListReplyDevice) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ListReplyDevice) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ListReplyDevice) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *ListReplyDevice) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *ListReplyDevice) GetLockStatus() int32 {
	if x != nil {
		return x.LockStatus
	}
	return 0
}

func (x *ListReplyDevice) GetCabinStatus() bool {
	if x != nil {
		return x.CabinStatus
	}
	return false
}

func (x *ListReplyDevice) GetVoyageTimes() int32 {
	if x != nil {
		return x.VoyageTimes
	}
	return 0
}

func (x *ListReplyDevice) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *ListReplyDevice) GetPropData() *structpb.Struct {
	if x != nil {
		return x.PropData
	}
	return nil
}

func (x *ListReplyDevice) GetSubdevices() []*structpb.Struct {
	if x != nil {
		return x.Subdevices
	}
	return nil
}

func (x *ListReplyDevice) GetRelatedChIds() []int64 {
	if x != nil {
		return x.RelatedChIds
	}
	return nil
}

type DeviceReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime    float64                  `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime    float64                  `protobuf:"fixed64,3,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	Sn             string                   `protobuf:"bytes,4,opt,name=sn,proto3" json:"sn,omitempty"`
	Type           string                   `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Name           string                   `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Model          string                   `protobuf:"bytes,7,opt,name=model,proto3" json:"model,omitempty"`
	Status         string                   `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	Category       string                   `protobuf:"bytes,9,opt,name=category,proto3" json:"category,omitempty"`
	Location       string                   `protobuf:"bytes,10,opt,name=location,proto3" json:"location,omitempty"`
	VideoStatus    bool                     `protobuf:"varint,11,opt,name=videoStatus,proto3" json:"videoStatus,omitempty"`
	MerchantId     int64                    `protobuf:"varint,12,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	AeroMode       int32                    `protobuf:"varint,13,opt,name=aeroMode,proto3" json:"aeroMode,omitempty"`
	FlyControl     int32                    `protobuf:"varint,14,opt,name=flyControl,proto3" json:"flyControl,omitempty"`
	HoverControl   int32                    `protobuf:"varint,15,opt,name=hoverControl,proto3" json:"hoverControl,omitempty"`
	LensControl    int32                    `protobuf:"varint,16,opt,name=lensControl,proto3" json:"lensControl,omitempty"`
	AeroControl    int32                    `protobuf:"varint,17,opt,name=aeroControl,proto3" json:"aeroControl,omitempty"`
	NetworkStatus  bool                     `protobuf:"varint,18,opt,name=networkStatus,proto3" json:"networkStatus,omitempty"`
	SpeakerControl int32                    `protobuf:"varint,19,opt,name=speakerControl,proto3" json:"speakerControl,omitempty"`
	SignalQuality  string                   `protobuf:"bytes,20,opt,name=signalQuality,proto3" json:"signalQuality,omitempty"`
	Lnglat         []float64                `protobuf:"fixed64,21,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	FlyerId        *int64                   `protobuf:"varint,22,opt,name=flyerId,proto3,oneof" json:"flyerId,omitempty"`
	AvatarId       *int64                   `protobuf:"varint,23,opt,name=avatarId,proto3,oneof" json:"avatarId,omitempty"`
	AirlineId      *int64                   `protobuf:"varint,24,opt,name=airlineId,proto3,oneof" json:"airlineId,omitempty"`
	MissionId      *int64                   `protobuf:"varint,25,opt,name=missionId,proto3,oneof" json:"missionId,omitempty"`
	UppedTime      *float64                 `protobuf:"fixed64,26,opt,name=uppedTime,proto3,oneof" json:"uppedTime,omitempty"`
	Avatar         *DeviceReplyBriefAvatar  `protobuf:"bytes,27,opt,name=avatar,proto3,oneof" json:"avatar,omitempty"`
	Gimbal         *DeviceReplyBriefGimbal  `protobuf:"bytes,28,opt,name=gimbal,proto3,oneof" json:"gimbal,omitempty"`
	RelatedChIds   []int64                  `protobuf:"varint,29,rep,packed,name=relatedChIds,proto3" json:"relatedChIds,omitempty"`
	LiveVideos     []*DeviceReplyLiveVideo  `protobuf:"bytes,30,rep,name=liveVideos,proto3" json:"liveVideos,omitempty"`
	Speaker        *DeviceReplyBriefSpeaker `protobuf:"bytes,31,opt,name=speaker,proto3,oneof" json:"speaker,omitempty"`
	LastVoyage     *DeviceReplyBriefVoyage  `protobuf:"bytes,32,opt,name=lastVoyage,proto3,oneof" json:"lastVoyage,omitempty"`
	PropData       *structpb.Struct         `protobuf:"bytes,33,opt,name=propData,proto3" json:"propData,omitempty"`
	ExtraData      *structpb.Struct         `protobuf:"bytes,34,opt,name=extraData,proto3" json:"extraData,omitempty"`
	LaunchData     *structpb.Struct         `protobuf:"bytes,35,opt,name=launchData,proto3" json:"launchData,omitempty"`
	Subdevices     []*structpb.Struct       `protobuf:"bytes,36,rep,name=subdevices,proto3" json:"subdevices,omitempty"`
}

func (x *DeviceReplyItem) Reset() {
	*x = DeviceReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReplyItem) ProtoMessage() {}

func (x *DeviceReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReplyItem.ProtoReflect.Descriptor instead.
func (*DeviceReplyItem) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{6, 0}
}

func (x *DeviceReplyItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeviceReplyItem) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *DeviceReplyItem) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *DeviceReplyItem) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *DeviceReplyItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeviceReplyItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeviceReplyItem) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *DeviceReplyItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DeviceReplyItem) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *DeviceReplyItem) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *DeviceReplyItem) GetVideoStatus() bool {
	if x != nil {
		return x.VideoStatus
	}
	return false
}

func (x *DeviceReplyItem) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *DeviceReplyItem) GetAeroMode() int32 {
	if x != nil {
		return x.AeroMode
	}
	return 0
}

func (x *DeviceReplyItem) GetFlyControl() int32 {
	if x != nil {
		return x.FlyControl
	}
	return 0
}

func (x *DeviceReplyItem) GetHoverControl() int32 {
	if x != nil {
		return x.HoverControl
	}
	return 0
}

func (x *DeviceReplyItem) GetLensControl() int32 {
	if x != nil {
		return x.LensControl
	}
	return 0
}

func (x *DeviceReplyItem) GetAeroControl() int32 {
	if x != nil {
		return x.AeroControl
	}
	return 0
}

func (x *DeviceReplyItem) GetNetworkStatus() bool {
	if x != nil {
		return x.NetworkStatus
	}
	return false
}

func (x *DeviceReplyItem) GetSpeakerControl() int32 {
	if x != nil {
		return x.SpeakerControl
	}
	return 0
}

func (x *DeviceReplyItem) GetSignalQuality() string {
	if x != nil {
		return x.SignalQuality
	}
	return ""
}

func (x *DeviceReplyItem) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *DeviceReplyItem) GetFlyerId() int64 {
	if x != nil && x.FlyerId != nil {
		return *x.FlyerId
	}
	return 0
}

func (x *DeviceReplyItem) GetAvatarId() int64 {
	if x != nil && x.AvatarId != nil {
		return *x.AvatarId
	}
	return 0
}

func (x *DeviceReplyItem) GetAirlineId() int64 {
	if x != nil && x.AirlineId != nil {
		return *x.AirlineId
	}
	return 0
}

func (x *DeviceReplyItem) GetMissionId() int64 {
	if x != nil && x.MissionId != nil {
		return *x.MissionId
	}
	return 0
}

func (x *DeviceReplyItem) GetUppedTime() float64 {
	if x != nil && x.UppedTime != nil {
		return *x.UppedTime
	}
	return 0
}

func (x *DeviceReplyItem) GetAvatar() *DeviceReplyBriefAvatar {
	if x != nil {
		return x.Avatar
	}
	return nil
}

func (x *DeviceReplyItem) GetGimbal() *DeviceReplyBriefGimbal {
	if x != nil {
		return x.Gimbal
	}
	return nil
}

func (x *DeviceReplyItem) GetRelatedChIds() []int64 {
	if x != nil {
		return x.RelatedChIds
	}
	return nil
}

func (x *DeviceReplyItem) GetLiveVideos() []*DeviceReplyLiveVideo {
	if x != nil {
		return x.LiveVideos
	}
	return nil
}

func (x *DeviceReplyItem) GetSpeaker() *DeviceReplyBriefSpeaker {
	if x != nil {
		return x.Speaker
	}
	return nil
}

func (x *DeviceReplyItem) GetLastVoyage() *DeviceReplyBriefVoyage {
	if x != nil {
		return x.LastVoyage
	}
	return nil
}

func (x *DeviceReplyItem) GetPropData() *structpb.Struct {
	if x != nil {
		return x.PropData
	}
	return nil
}

func (x *DeviceReplyItem) GetExtraData() *structpb.Struct {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

func (x *DeviceReplyItem) GetLaunchData() *structpb.Struct {
	if x != nil {
		return x.LaunchData
	}
	return nil
}

func (x *DeviceReplyItem) GetSubdevices() []*structpb.Struct {
	if x != nil {
		return x.Subdevices
	}
	return nil
}

type DeviceReplyBriefAvatar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Mobile   string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Nickname string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Avatar   string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
}

func (x *DeviceReplyBriefAvatar) Reset() {
	*x = DeviceReplyBriefAvatar{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReplyBriefAvatar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReplyBriefAvatar) ProtoMessage() {}

func (x *DeviceReplyBriefAvatar) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReplyBriefAvatar.ProtoReflect.Descriptor instead.
func (*DeviceReplyBriefAvatar) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{6, 1}
}

func (x *DeviceReplyBriefAvatar) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeviceReplyBriefAvatar) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *DeviceReplyBriefAvatar) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *DeviceReplyBriefAvatar) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type DeviceReplyBriefSpeaker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn       string `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn,omitempty"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type     string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Index    string `protobuf:"bytes,4,opt,name=index,proto3" json:"index,omitempty"`
	Mode     int32  `protobuf:"varint,5,opt,name=mode,proto3" json:"mode,omitempty"`
	Volume   int32  `protobuf:"varint,6,opt,name=volume,proto3" json:"volume,omitempty"`
	Playmode int32  `protobuf:"varint,7,opt,name=playmode,proto3" json:"playmode,omitempty"`
}

func (x *DeviceReplyBriefSpeaker) Reset() {
	*x = DeviceReplyBriefSpeaker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReplyBriefSpeaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReplyBriefSpeaker) ProtoMessage() {}

func (x *DeviceReplyBriefSpeaker) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReplyBriefSpeaker.ProtoReflect.Descriptor instead.
func (*DeviceReplyBriefSpeaker) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{6, 2}
}

func (x *DeviceReplyBriefSpeaker) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *DeviceReplyBriefSpeaker) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeviceReplyBriefSpeaker) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeviceReplyBriefSpeaker) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *DeviceReplyBriefSpeaker) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *DeviceReplyBriefSpeaker) GetVolume() int32 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *DeviceReplyBriefSpeaker) GetPlaymode() int32 {
	if x != nil {
		return x.Playmode
	}
	return 0
}

type DeviceReplyLiveVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Url        string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Name       string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type       string   `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Index      string   `protobuf:"bytes,5,opt,name=index,proto3" json:"index,omitempty"`
	Status     int32    `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	Key        string   `protobuf:"bytes,7,opt,name=key,proto3" json:"key,omitempty"`
	Clarity    int32    `protobuf:"varint,8,opt,name=clarity,proto3" json:"clarity,omitempty"`
	Position   int32    `protobuf:"varint,9,opt,name=position,proto3" json:"position,omitempty"`
	Switchable []string `protobuf:"bytes,10,rep,name=switchable,proto3" json:"switchable,omitempty"`
}

func (x *DeviceReplyLiveVideo) Reset() {
	*x = DeviceReplyLiveVideo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReplyLiveVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReplyLiveVideo) ProtoMessage() {}

func (x *DeviceReplyLiveVideo) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReplyLiveVideo.ProtoReflect.Descriptor instead.
func (*DeviceReplyLiveVideo) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{6, 3}
}

func (x *DeviceReplyLiveVideo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeviceReplyLiveVideo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DeviceReplyLiveVideo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeviceReplyLiveVideo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeviceReplyLiveVideo) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *DeviceReplyLiveVideo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DeviceReplyLiveVideo) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *DeviceReplyLiveVideo) GetClarity() int32 {
	if x != nil {
		return x.Clarity
	}
	return 0
}

func (x *DeviceReplyLiveVideo) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *DeviceReplyLiveVideo) GetSwitchable() []string {
	if x != nil {
		return x.Switchable
	}
	return nil
}

type DeviceReplyBriefVoyage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sn        string  `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	IsFlown   bool    `protobuf:"varint,3,opt,name=isFlown,proto3" json:"isFlown,omitempty"`
	Runtime   int32   `protobuf:"varint,4,opt,name=runtime,proto3" json:"runtime,omitempty"`
	Mileage   int32   `protobuf:"varint,5,opt,name=mileage,proto3" json:"mileage,omitempty"`
	Status    string  `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	EndTime   float64 `protobuf:"fixed64,7,opt,name=endTime,proto3" json:"endTime,omitempty"`
	DeviceId  int64   `protobuf:"varint,8,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	AirlineId int64   `protobuf:"varint,9,opt,name=airlineId,proto3" json:"airlineId,omitempty"`
	StartTime float64 `protobuf:"fixed64,10,opt,name=startTime,proto3" json:"startTime,omitempty"`
}

func (x *DeviceReplyBriefVoyage) Reset() {
	*x = DeviceReplyBriefVoyage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReplyBriefVoyage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReplyBriefVoyage) ProtoMessage() {}

func (x *DeviceReplyBriefVoyage) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReplyBriefVoyage.ProtoReflect.Descriptor instead.
func (*DeviceReplyBriefVoyage) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{6, 4}
}

func (x *DeviceReplyBriefVoyage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeviceReplyBriefVoyage) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *DeviceReplyBriefVoyage) GetIsFlown() bool {
	if x != nil {
		return x.IsFlown
	}
	return false
}

func (x *DeviceReplyBriefVoyage) GetRuntime() int32 {
	if x != nil {
		return x.Runtime
	}
	return 0
}

func (x *DeviceReplyBriefVoyage) GetMileage() int32 {
	if x != nil {
		return x.Mileage
	}
	return 0
}

func (x *DeviceReplyBriefVoyage) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DeviceReplyBriefVoyage) GetEndTime() float64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *DeviceReplyBriefVoyage) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *DeviceReplyBriefVoyage) GetAirlineId() int64 {
	if x != nil {
		return x.AirlineId
	}
	return 0
}

func (x *DeviceReplyBriefVoyage) GetStartTime() float64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

type DeviceReplyBriefGimbal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index                  string  `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Mode                   int32   `protobuf:"varint,2,opt,name=mode,proto3" json:"mode,omitempty"`
	PhotoState             int32   `protobuf:"varint,3,opt,name=photoState,proto3" json:"photoState,omitempty"`
	RecordingState         int32   `protobuf:"varint,4,opt,name=recordingState,proto3" json:"recordingState,omitempty"`
	ZoomFactor             float32 `protobuf:"fixed32,5,opt,name=zoomFactor,proto3" json:"zoomFactor,omitempty"`
	IrZoomFactor           float32 `protobuf:"fixed32,6,opt,name=irZoomFactor,proto3" json:"irZoomFactor,omitempty"`
	ZoomFocusMode          int32   `protobuf:"varint,7,opt,name=zoomFocusMode,proto3" json:"zoomFocusMode,omitempty"`
	ZoomFocusValue         int32   `protobuf:"varint,8,opt,name=zoomFocusValue,proto3" json:"zoomFocusValue,omitempty"`
	ZoomFocusState         int32   `protobuf:"varint,9,opt,name=zoomFocusState,proto3" json:"zoomFocusState,omitempty"`
	IrMeteringMode         int32   `protobuf:"varint,10,opt,name=irMeteringMode,proto3" json:"irMeteringMode,omitempty"`
	GimbalPitch            float64 `protobuf:"fixed64,11,opt,name=gimbalPitch,proto3" json:"gimbalPitch,omitempty"`
	GimbalYaw              float64 `protobuf:"fixed64,12,opt,name=gimbalYaw,proto3" json:"gimbalYaw,omitempty"`
	GimbalRoll             float64 `protobuf:"fixed64,13,opt,name=gimbalRoll,proto3" json:"gimbalRoll,omitempty"`
	MeasureTargetLongitude float64 `protobuf:"fixed64,14,opt,name=measureTargetLongitude,proto3" json:"measureTargetLongitude,omitempty"`
	MeasureTargetLatitude  float64 `protobuf:"fixed64,15,opt,name=measureTargetLatitude,proto3" json:"measureTargetLatitude,omitempty"`
	MeasureTargetAltitude  float64 `protobuf:"fixed64,16,opt,name=measureTargetAltitude,proto3" json:"measureTargetAltitude,omitempty"`
	MeasureTargetDistance  float64 `protobuf:"fixed64,17,opt,name=measureTargetDistance,proto3" json:"measureTargetDistance,omitempty"`
	MeasureErrState        int32   `protobuf:"varint,18,opt,name=measureErrState,proto3" json:"measureErrState,omitempty"`
}

func (x *DeviceReplyBriefGimbal) Reset() {
	*x = DeviceReplyBriefGimbal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_onemap_v1_onemap_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReplyBriefGimbal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReplyBriefGimbal) ProtoMessage() {}

func (x *DeviceReplyBriefGimbal) ProtoReflect() protoreflect.Message {
	mi := &file_api_onemap_v1_onemap_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReplyBriefGimbal.ProtoReflect.Descriptor instead.
func (*DeviceReplyBriefGimbal) Descriptor() ([]byte, []int) {
	return file_api_onemap_v1_onemap_proto_rawDescGZIP(), []int{6, 5}
}

func (x *DeviceReplyBriefGimbal) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *DeviceReplyBriefGimbal) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetPhotoState() int32 {
	if x != nil {
		return x.PhotoState
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetRecordingState() int32 {
	if x != nil {
		return x.RecordingState
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetZoomFactor() float32 {
	if x != nil {
		return x.ZoomFactor
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetIrZoomFactor() float32 {
	if x != nil {
		return x.IrZoomFactor
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetZoomFocusMode() int32 {
	if x != nil {
		return x.ZoomFocusMode
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetZoomFocusValue() int32 {
	if x != nil {
		return x.ZoomFocusValue
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetZoomFocusState() int32 {
	if x != nil {
		return x.ZoomFocusState
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetIrMeteringMode() int32 {
	if x != nil {
		return x.IrMeteringMode
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetGimbalPitch() float64 {
	if x != nil {
		return x.GimbalPitch
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetGimbalYaw() float64 {
	if x != nil {
		return x.GimbalYaw
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetGimbalRoll() float64 {
	if x != nil {
		return x.GimbalRoll
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetMeasureTargetLongitude() float64 {
	if x != nil {
		return x.MeasureTargetLongitude
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetMeasureTargetLatitude() float64 {
	if x != nil {
		return x.MeasureTargetLatitude
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetMeasureTargetAltitude() float64 {
	if x != nil {
		return x.MeasureTargetAltitude
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetMeasureTargetDistance() float64 {
	if x != nil {
		return x.MeasureTargetDistance
	}
	return 0
}

func (x *DeviceReplyBriefGimbal) GetMeasureErrState() int32 {
	if x != nil {
		return x.MeasureErrState
	}
	return 0
}

var File_api_onemap_v1_onemap_proto protoreflect.FileDescriptor

var file_api_onemap_v1_onemap_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x6e, 0x65, 0x6d, 0x61, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x0f, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x2c, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x41,
	0x0a, 0x11, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x22, 0x90, 0x03, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x73,
	0x74, 0x61, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x94, 0x02,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f,
	0x63, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64,
	0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x6f, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x6f, 0x63, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x69, 0x6c, 0x6f,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x69,
	0x6c, 0x6f, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x50, 0x69, 0x6c, 0x6f, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x69, 0x6c, 0x6f, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x6f,
	0x79, 0x61, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e,
	0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x73, 0x22, 0x26, 0x0a, 0x0c, 0x4a, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x22, 0xc2, 0x02, 0x0a,
	0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d,
	0x61, 0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x4e, 0x0a,
	0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0x9a,
	0x7c, 0x2e, 0x9a, 0x43, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0xa2, 0x43, 0x22, 0x73, 0x6e,
	0x2c, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x2c, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x73,
	0x48, 0x00, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x88, 0x01,
	0x01, 0x12, 0x29, 0x0a, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x0d, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x3a, 0x03, 0x88, 0x43,
	0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xec, 0x05, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0x7a, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x30, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x80, 0x04,
	0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x62, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x63, 0x61, 0x62, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x76, 0x6f, 0x79, 0x61, 0x67,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12, 0x33,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x52, 0x0a, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x68, 0x49, 0x64, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x68, 0x49, 0x64, 0x73,
	0x22, 0x9f, 0x18, 0x0a, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x30,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x69, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0xdf, 0x0b, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x73, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x61, 0x65, 0x72, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x61, 0x65, 0x72, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x6c, 0x79,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66,
	0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x68, 0x6f, 0x76,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x68, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x20, 0x0a,
	0x0b, 0x6c, 0x65, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x6c, 0x65, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12,
	0x20, 0x0a, 0x0b, 0x61, 0x65, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x65, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12,
	0x24, 0x0a, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x51, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18,
	0x15, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12, 0x1d, 0x0a,
	0x07, 0x66, 0x6c, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x07, 0x66, 0x6c, 0x79, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01,
	0x52, 0x08, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a,
	0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x02, 0x52, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x21, 0x0a, 0x09, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x75, 0x70, 0x70, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x01, 0x48, 0x04, 0x52, 0x09, 0x75, 0x70, 0x70, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e,
	0x62, 0x72, 0x69, 0x65, 0x66, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x48, 0x05, 0x52, 0x06, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x06, 0x67, 0x69, 0x6d, 0x62,
	0x61, 0x6c, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x62, 0x72, 0x69, 0x65, 0x66, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x48, 0x06, 0x52,
	0x06, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x68, 0x49, 0x64, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x68, 0x49, 0x64, 0x73, 0x12, 0x41,
	0x0a, 0x0a, 0x6c, 0x69, 0x76, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x1e, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x0a, 0x6c, 0x69, 0x76, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x73, 0x12, 0x43, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x62, 0x72, 0x69, 0x65,
	0x66, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x48, 0x07, 0x52, 0x07, 0x73, 0x70, 0x65, 0x61,
	0x6b, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x6f,
	0x79, 0x61, 0x67, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x2e, 0x62, 0x72, 0x69, 0x65, 0x66, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x48,
	0x08, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x33, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0a,
	0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0a, 0x6c, 0x61, 0x75, 0x6e, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x18, 0x24, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x66, 0x6c, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x61, 0x69, 0x72, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x75, 0x70, 0x70, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x70, 0x65, 0x61,
	0x6b, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x6f, 0x79, 0x61,
	0x67, 0x65, 0x1a, 0x69, 0x0a, 0x0b, 0x62, 0x72, 0x69, 0x65, 0x66, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x1a, 0xa4, 0x01,
	0x0a, 0x0c, 0x62, 0x72, 0x69, 0x65, 0x66, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x0e,
	0x0a, 0x02, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x79,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79,
	0x6d, 0x6f, 0x64, 0x65, 0x1a, 0xeb, 0x01, 0x0a, 0x09, 0x6c, 0x69, 0x76, 0x65, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63,
	0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x61, 0x62,
	0x6c, 0x65, 0x1a, 0x85, 0x02, 0x0a, 0x0b, 0x62, 0x72, 0x69, 0x65, 0x66, 0x56, 0x6f, 0x79, 0x61,
	0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x73, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0xc5, 0x05, 0x0a, 0x0b, 0x62,
	0x72, 0x69, 0x65, 0x66, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0a, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0c,
	0x69, 0x72, 0x5a, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0c, 0x69, 0x72, 0x5a, 0x6f, 0x6f, 0x6d, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x12, 0x24, 0x0a, 0x0d, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x4d, 0x6f, 0x64,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63,
	0x75, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f,
	0x63, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x26,
	0x0a, 0x0e, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x7a, 0x6f, 0x6f, 0x6d, 0x46, 0x6f, 0x63, 0x75,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x72, 0x4d, 0x65, 0x74, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x69, 0x72, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x50, 0x69, 0x74, 0x63, 0x68, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x50, 0x69, 0x74, 0x63, 0x68,
	0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x59, 0x61, 0x77, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x09, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x59, 0x61, 0x77, 0x12, 0x1e,
	0x0a, 0x0a, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x52, 0x6f, 0x6c, 0x6c, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0a, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x52, 0x6f, 0x6c, 0x6c, 0x12, 0x36,
	0x0a, 0x16, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16,
	0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x6f, 0x6e,
	0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72,
	0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x15,
	0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x6c, 0x74,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x6d, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x6c, 0x74, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x15, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x45, 0x72, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x45, 0x72, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x32, 0x96, 0x03, 0x0a, 0x06, 0x4f, 0x6e, 0x65, 0x6d, 0x61, 0x70, 0x12, 0x6b, 0x0a,
	0x0a, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12,
	0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x61, 0x70, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x5c, 0x0a, 0x0a, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18,
	0x12, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x61, 0x70,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x64, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1d, 0x12, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x61,
	0x70, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x5b,
	0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x6e,
	0x65, 0x6d, 0x61, 0x70, 0x2f, 0x6a, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x40, 0x0a, 0x0d, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2d,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_onemap_v1_onemap_proto_rawDescOnce sync.Once
	file_api_onemap_v1_onemap_proto_rawDescData = file_api_onemap_v1_onemap_proto_rawDesc
)

func file_api_onemap_v1_onemap_proto_rawDescGZIP() []byte {
	file_api_onemap_v1_onemap_proto_rawDescOnce.Do(func() {
		file_api_onemap_v1_onemap_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_onemap_v1_onemap_proto_rawDescData)
	})
	return file_api_onemap_v1_onemap_proto_rawDescData
}

var file_api_onemap_v1_onemap_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_api_onemap_v1_onemap_proto_goTypes = []interface{}{
	(*CommonRequest)(nil),           // 0: api.map.v1.CommonRequest
	(*StatisticsRequest)(nil),       // 1: api.map.v1.StatisticsRequest
	(*StatisticsReply)(nil),         // 2: api.map.v1.StatisticsReply
	(*JointRequest)(nil),            // 3: api.map.v1.JointRequest
	(*ListRequest)(nil),             // 4: api.map.v1.ListRequest
	(*ListReply)(nil),               // 5: api.map.v1.ListReply
	(*DeviceReply)(nil),             // 6: api.map.v1.DeviceReply
	(*StatisticsReplyStatData)(nil), // 7: api.map.v1.StatisticsReply.statData
	(*ListReplyListData)(nil),       // 8: api.map.v1.ListReply.listData
	(*ListReplyDevice)(nil),         // 9: api.map.v1.ListReply.device
	(*DeviceReplyItem)(nil),         // 10: api.map.v1.DeviceReply.item
	(*DeviceReplyBriefAvatar)(nil),  // 11: api.map.v1.DeviceReply.briefAvatar
	(*DeviceReplyBriefSpeaker)(nil), // 12: api.map.v1.DeviceReply.briefSpeaker
	(*DeviceReplyLiveVideo)(nil),    // 13: api.map.v1.DeviceReply.liveVideo
	(*DeviceReplyBriefVoyage)(nil),  // 14: api.map.v1.DeviceReply.briefVoyage
	(*DeviceReplyBriefGimbal)(nil),  // 15: api.map.v1.DeviceReply.briefGimbal
	(*structpb.Struct)(nil),         // 16: google.protobuf.Struct
}
var file_api_onemap_v1_onemap_proto_depIdxs = []int32{
	7,  // 0: api.map.v1.StatisticsReply.data:type_name -> api.map.v1.StatisticsReply.statData
	8,  // 1: api.map.v1.ListReply.data:type_name -> api.map.v1.ListReply.listData
	10, // 2: api.map.v1.DeviceReply.data:type_name -> api.map.v1.DeviceReply.item
	9,  // 3: api.map.v1.ListReply.listData.list:type_name -> api.map.v1.ListReply.device
	16, // 4: api.map.v1.ListReply.device.propData:type_name -> google.protobuf.Struct
	16, // 5: api.map.v1.ListReply.device.subdevices:type_name -> google.protobuf.Struct
	11, // 6: api.map.v1.DeviceReply.item.avatar:type_name -> api.map.v1.DeviceReply.briefAvatar
	15, // 7: api.map.v1.DeviceReply.item.gimbal:type_name -> api.map.v1.DeviceReply.briefGimbal
	13, // 8: api.map.v1.DeviceReply.item.liveVideos:type_name -> api.map.v1.DeviceReply.liveVideo
	12, // 9: api.map.v1.DeviceReply.item.speaker:type_name -> api.map.v1.DeviceReply.briefSpeaker
	14, // 10: api.map.v1.DeviceReply.item.lastVoyage:type_name -> api.map.v1.DeviceReply.briefVoyage
	16, // 11: api.map.v1.DeviceReply.item.propData:type_name -> google.protobuf.Struct
	16, // 12: api.map.v1.DeviceReply.item.extraData:type_name -> google.protobuf.Struct
	16, // 13: api.map.v1.DeviceReply.item.launchData:type_name -> google.protobuf.Struct
	16, // 14: api.map.v1.DeviceReply.item.subdevices:type_name -> google.protobuf.Struct
	1,  // 15: api.map.v1.Onemap.Statistics:input_type -> api.map.v1.StatisticsRequest
	4,  // 16: api.map.v1.Onemap.ListDevice:input_type -> api.map.v1.ListRequest
	0,  // 17: api.map.v1.Onemap.GetDevice:input_type -> api.map.v1.CommonRequest
	3,  // 18: api.map.v1.Onemap.ListJoint:input_type -> api.map.v1.JointRequest
	2,  // 19: api.map.v1.Onemap.Statistics:output_type -> api.map.v1.StatisticsReply
	5,  // 20: api.map.v1.Onemap.ListDevice:output_type -> api.map.v1.ListReply
	6,  // 21: api.map.v1.Onemap.GetDevice:output_type -> api.map.v1.DeviceReply
	5,  // 22: api.map.v1.Onemap.ListJoint:output_type -> api.map.v1.ListReply
	19, // [19:23] is the sub-list for method output_type
	15, // [15:19] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_onemap_v1_onemap_proto_init() }
func file_api_onemap_v1_onemap_proto_init() {
	if File_api_onemap_v1_onemap_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_onemap_v1_onemap_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatisticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatisticsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JointRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatisticsReplyStatData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReplyDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReplyBriefAvatar); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReplyBriefSpeaker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReplyLiveVideo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReplyBriefVoyage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_onemap_v1_onemap_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReplyBriefGimbal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_onemap_v1_onemap_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_api_onemap_v1_onemap_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_api_onemap_v1_onemap_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_onemap_v1_onemap_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_onemap_v1_onemap_proto_goTypes,
		DependencyIndexes: file_api_onemap_v1_onemap_proto_depIdxs,
		MessageInfos:      file_api_onemap_v1_onemap_proto_msgTypes,
	}.Build()
	File_api_onemap_v1_onemap_proto = out.File
	file_api_onemap_v1_onemap_proto_rawDesc = nil
	file_api_onemap_v1_onemap_proto_goTypes = nil
	file_api_onemap_v1_onemap_proto_depIdxs = nil
}
