// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/onemap/v1/onemap.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OnemapClient is the client API for Onemap service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OnemapClient interface {
	Statistics(ctx context.Context, in *StatisticsRequest, opts ...grpc.CallOption) (*StatisticsReply, error)
	ListDevice(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error)
	GetDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DeviceReply, error)
	ListJoint(ctx context.Context, in *JointRequest, opts ...grpc.CallOption) (*ListReply, error)
}

type onemapClient struct {
	cc grpc.ClientConnInterface
}

func NewOnemapClient(cc grpc.ClientConnInterface) OnemapClient {
	return &onemapClient{cc}
}

func (c *onemapClient) Statistics(ctx context.Context, in *StatisticsRequest, opts ...grpc.CallOption) (*StatisticsReply, error) {
	out := new(StatisticsReply)
	err := c.cc.Invoke(ctx, "/api.map.v1.Onemap/Statistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onemapClient) ListDevice(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error) {
	out := new(ListReply)
	err := c.cc.Invoke(ctx, "/api.map.v1.Onemap/ListDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onemapClient) GetDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DeviceReply, error) {
	out := new(DeviceReply)
	err := c.cc.Invoke(ctx, "/api.map.v1.Onemap/GetDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onemapClient) ListJoint(ctx context.Context, in *JointRequest, opts ...grpc.CallOption) (*ListReply, error) {
	out := new(ListReply)
	err := c.cc.Invoke(ctx, "/api.map.v1.Onemap/ListJoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnemapServer is the server API for Onemap service.
// All implementations must embed UnimplementedOnemapServer
// for forward compatibility
type OnemapServer interface {
	Statistics(context.Context, *StatisticsRequest) (*StatisticsReply, error)
	ListDevice(context.Context, *ListRequest) (*ListReply, error)
	GetDevice(context.Context, *CommonRequest) (*DeviceReply, error)
	ListJoint(context.Context, *JointRequest) (*ListReply, error)
	mustEmbedUnimplementedOnemapServer()
}

// UnimplementedOnemapServer must be embedded to have forward compatible implementations.
type UnimplementedOnemapServer struct {
}

func (UnimplementedOnemapServer) Statistics(context.Context, *StatisticsRequest) (*StatisticsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Statistics not implemented")
}
func (UnimplementedOnemapServer) ListDevice(context.Context, *ListRequest) (*ListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDevice not implemented")
}
func (UnimplementedOnemapServer) GetDevice(context.Context, *CommonRequest) (*DeviceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDevice not implemented")
}
func (UnimplementedOnemapServer) ListJoint(context.Context, *JointRequest) (*ListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJoint not implemented")
}
func (UnimplementedOnemapServer) mustEmbedUnimplementedOnemapServer() {}

// UnsafeOnemapServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OnemapServer will
// result in compilation errors.
type UnsafeOnemapServer interface {
	mustEmbedUnimplementedOnemapServer()
}

func RegisterOnemapServer(s grpc.ServiceRegistrar, srv OnemapServer) {
	s.RegisterService(&Onemap_ServiceDesc, srv)
}

func _Onemap_Statistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnemapServer).Statistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.map.v1.Onemap/Statistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnemapServer).Statistics(ctx, req.(*StatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onemap_ListDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnemapServer).ListDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.map.v1.Onemap/ListDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnemapServer).ListDevice(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onemap_GetDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnemapServer).GetDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.map.v1.Onemap/GetDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnemapServer).GetDevice(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onemap_ListJoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnemapServer).ListJoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.map.v1.Onemap/ListJoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnemapServer).ListJoint(ctx, req.(*JointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Onemap_ServiceDesc is the grpc.ServiceDesc for Onemap service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Onemap_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.map.v1.Onemap",
	HandlerType: (*OnemapServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Statistics",
			Handler:    _Onemap_Statistics_Handler,
		},
		{
			MethodName: "ListDevice",
			Handler:    _Onemap_ListDevice_Handler,
		},
		{
			MethodName: "GetDevice",
			Handler:    _Onemap_GetDevice_Handler,
		},
		{
			MethodName: "ListJoint",
			Handler:    _Onemap_ListJoint_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/onemap/v1/onemap.proto",
}
