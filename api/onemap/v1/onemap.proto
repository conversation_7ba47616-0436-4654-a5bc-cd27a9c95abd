syntax = "proto3";

package api.map.v1;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "validator/validator.proto";
import "list/list.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/onemap/v1;v1";
option java_multiple_files = true;
option java_package = "api.onemap.v1";

service Onemap {
	rpc Statistics (StatisticsRequest) returns (StatisticsReply) {
    option (google.api.http) = {
      get: "/api/v1/onemap/statistics"
    };
  };
	rpc ListDevice (ListRequest) returns (ListReply) {
    option (google.api.http) = {
      get: "/api/v1/onemap/devices"
    };
  };
  rpc GetDevice (CommonRequest) returns (DeviceReply) {
    option (google.api.http) = {
      get: "/api/v1/onemap/devices/{id}"
    };
  };
  rpc ListJoint (JointRequest) returns (ListReply) {
    option (google.api.http) = {
      get: "/api/v1/onemap/joints"
    };
  };
}

message CommonRequest {
  int64 id = 1 [(validator.rules) = "required"];
}

message StatisticsRequest {
  optional int64 deviceId = 1;
}

message StatisticsReply {
  int32 code = 1;
  string message = 2;
  statData data = 3;

	message statData {
		int32 dockCount = 1;
		int32 onlineDock = 2;
		int32 pilotCount = 3;
		int32 onlinePilot = 4;
		int32 imageCount = 5;
		int32 videoCount = 6;
		int32 voyageTimes = 7;
		int32 voyageMileages = 8;
	}
}

message JointRequest {
  string lnglat = 1;
}

message ListRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  optional string search = 3 [(list.filter_options)={filter_name: "sn,deployment_name,deployment_tags", operator:"search" }];
  optional string type = 4;
  optional string model = 5;
  optional bool networkStatus = 6;
}

message ListReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated device list = 4;
  }

	message device {
		int64 id = 1;
		string sn = 2;
		string name = 3;
		string type = 4;
		string model = 5;
		string status = 6;
		string category = 7;
    string location = 8;
    int64 tenantId = 9;
    int64 merchantId = 10;
    int32 lockStatus = 11;
		bool cabinStatus = 12;
		int32 voyageTimes = 13;
    repeated double lnglat = 14;
    google.protobuf.Struct propData = 15;
		repeated google.protobuf.Struct subdevices = 16;
    repeated int64 relatedChIds = 17;
	}
}

message DeviceReply {
  int32 code = 1;
  string message = 2;
  item data = 3;

  message item {
    int64 id = 1;
    double createdTime = 2;
    double updatedTime = 3;
    string sn = 4;
    string type = 5;
    string name = 6;
    string model = 7;
    string status = 8;
    string category = 9;
    string location = 10;
    bool videoStatus = 11;
    int64 merchantId = 12;
    int32 aeroMode = 13;
    int32 flyControl = 14;
    int32 hoverControl = 15;
    int32 lensControl = 16;
    int32 aeroControl = 17;
    bool networkStatus = 18;
    int32 speakerControl = 19;
    string signalQuality = 20;
    repeated double lnglat = 21;
    optional int64 flyerId = 22;
    optional int64 avatarId = 23;
    optional int64 airlineId = 24;
    optional int64 missionId = 25;
    optional double uppedTime = 26;
    optional briefAvatar avatar = 27;
    optional briefGimbal gimbal = 28;
    repeated int64 relatedChIds = 29;
    repeated liveVideo liveVideos = 30;
    optional briefSpeaker speaker = 31;
    optional briefVoyage lastVoyage = 32;
    google.protobuf.Struct propData = 33;
    google.protobuf.Struct extraData = 34;
    google.protobuf.Struct launchData = 35;
    repeated google.protobuf.Struct subdevices = 36;
  }

  message briefAvatar {
    int64 id = 1;
    string mobile = 2;
    string nickname = 3;
    string avatar = 4;
  }

  message briefSpeaker {
    string sn = 1;
    string name = 2;
    string type = 3;
    string index = 4;
    int32 mode = 5;
    int32 volume = 6;
    int32 playmode = 7;
  }

  message liveVideo {
    int64 id = 1;
    string url = 2;
    string name = 3;
    string type = 4;
    string index = 5;
    int32 status = 6;
    string key = 7;
    int32 clarity = 8;
    int32 position = 9;
    repeated string switchable = 10;
  }

  message briefVoyage {
    int64 id = 1;
    string sn = 2;
    bool isFlown = 3;
    int32 runtime = 4;
    int32 mileage = 5;
    string status = 6;
    double endTime = 7;
    int64 deviceId = 8;
    int64 airlineId = 9;
    double startTime = 10;
  }

  message briefGimbal {
    string index = 1;
    int32 mode = 2;
    int32 photoState = 3;
    int32 recordingState = 4;
    float zoomFactor = 5;
    float irZoomFactor = 6;
    int32 zoomFocusMode = 7;
    int32 zoomFocusValue = 8;
    int32 zoomFocusState = 9;
    int32 irMeteringMode = 10;
    double gimbalPitch = 11;
    double gimbalYaw = 12;
    double gimbalRoll = 13;
    double measureTargetLongitude = 14;
    double measureTargetLatitude = 15;
    double measureTargetAltitude = 16;
    double measureTargetDistance = 17;
    int32 measureErrState = 18;
  }
}
