{"swagger": "2.0", "info": {"title": "api/cloud/v1/cloud.proto", "version": "version not set"}, "tags": [{"name": "Cloud"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/cloud/media/workspaces/{id}/fast-upload": {"post": {"operationId": "Cloud_FastUploadMedia", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apicloudv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"ext": {"$ref": "#/definitions/v1MediaFile"}, "fingerprint": {"type": "string"}, "name": {"type": "string"}, "path": {"type": "string"}}}}], "tags": ["Cloud"]}}, "/api/v1/cloud/media/workspaces/{id}/group-upload-callback": {"post": {"operationId": "Cloud_UploadedMediaGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apicloudv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"fileGroupId": {"type": "string"}, "fileCount": {"type": "integer", "format": "int32"}, "fileUploadedCount": {"type": "integer", "format": "int32"}}}}], "tags": ["Cloud"]}}, "/api/v1/cloud/media/workspaces/{id}/upload-callback": {"post": {"summary": "rpc TinyFingerprints () returns (CommonReply) {\n  option (google.api.http) = {\n    post: \"/api/v1/cloud/media/workspaces/{id}/files/tiny-fingerprints\"\n    body: \"*\"\n  };\n};", "operationId": "Cloud_UploadedMedia", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1UploadMediaReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"ext": {"$ref": "#/definitions/v1MediaFile"}, "fingerprint": {"type": "string"}, "metadata": {"$ref": "#/definitions/v1MediaFileMetadata"}, "name": {"type": "string"}, "objectKey": {"type": "string"}, "path": {"type": "string"}, "subFileType": {"type": "integer", "format": "int32", "title": "当文件为图片的时候生效0 - 普通图片1 - 全景图"}, "result": {"type": "integer", "format": "int32"}}}}], "tags": ["Cloud"]}}, "/api/v1/cloud/storage/workspaces/{id}/sts": {"post": {"operationId": "Cloud_GenerateSTS", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1STSReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["Cloud"]}}, "/api/v1/cloud/wayline/workspaces/{id}/favorites": {"delete": {"operationId": "Cloud_UnfavoriteWaylines", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apicloudv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["Cloud"]}, "post": {"operationId": "Cloud_FavoriteWaylines", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apicloudv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["Cloud"]}}, "/api/v1/cloud/wayline/workspaces/{id}/upload-callback": {"post": {"operationId": "Cloud_UploadedWayline", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apicloudv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["Cloud"]}}, "/api/v1/cloud/wayline/workspaces/{id}/waylines": {"get": {"operationId": "Cloud_ListWaylines", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1WaylineListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page_size", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["Cloud"]}}, "/api/v1/cloud/wayline/workspaces/{id}/waylines/{wid}/url": {"get": {"operationId": "Cloud_GetWaylineUrl", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1WaylineUrlReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "wid", "in": "path", "required": true, "type": "string"}], "tags": ["Cloud"]}}}, "definitions": {"MediaFileMetadataPosition": {"type": "object", "properties": {"lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}}}, "STSReplySTSReplyData": {"type": "object", "properties": {"bucket": {"type": "string"}, "credentials": {"$ref": "#/definitions/v1STSCredential"}, "endpoint": {"type": "string"}, "ObjectKeyPrefix": {"type": "string"}, "provider": {"type": "string"}, "region": {"type": "string"}}}, "apicloudv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1CommonReplyokData"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1CommonReplyokData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "v1MediaFile": {"type": "object", "properties": {"droneModelKey": {"type": "string"}, "isOriginal": {"type": "boolean"}, "payloadModelKey": {"type": "string"}, "tinyFingerprint": {"type": "string"}, "sn": {"type": "string"}, "fileGroupId": {"type": "string"}}}, "v1MediaFileMetadata": {"type": "object", "properties": {"absoluteAltitude": {"type": "number", "format": "double"}, "createdTime": {"type": "string"}, "gimbalYawDegree": {"type": "number", "format": "double", "title": "云台偏航角"}, "relativeAltitude": {"type": "number", "format": "double", "title": "拍摄相对高度(m)"}, "shootPosition": {"$ref": "#/definitions/MediaFileMetadataPosition"}}}, "v1Pagination": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}}}, "v1STSCredential": {"type": "object", "properties": {"accessKeyId": {"type": "string"}, "accessKeySecret": {"type": "string"}, "expire": {"type": "number", "format": "double"}, "securityToken": {"type": "string"}}}, "v1STSReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/STSReplySTSReplyData"}}}, "v1UploadMediaReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1UploadMediaReplyData"}}}, "v1UploadMediaReplyData": {"type": "object", "properties": {"ObjectKey": {"type": "string"}}}, "v1Wayline": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "favorited": {"type": "boolean"}, "userName": {"type": "string"}, "updatedTime": {"type": "number", "format": "double"}, "droneModelKey": {"type": "string"}, "templateTypes": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "payloadModelKeys": {"type": "array", "items": {"type": "string"}}}}, "v1WaylineListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1WaylineListReplylistData"}}}, "v1WaylineListReplylistData": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/v1Pagination"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1Wayline"}}}}, "v1WaylineUrlReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1WaylineUrlReplyokData"}}}, "v1WaylineUrlReplyokData": {"type": "object", "properties": {"url": {"type": "string"}}}}}