// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/cloud/v1/cloud.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list"
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,json=page_size,proto3" json:"pageSize,omitempty"`
	Total    int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{0}
}

func (x *Pagination) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *Pagination) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *Pagination) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type Wayline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name             string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Favorited        bool     `protobuf:"varint,3,opt,name=favorited,proto3" json:"favorited,omitempty"`
	UserName         string   `protobuf:"bytes,4,opt,name=userName,json=user_name,proto3" json:"userName,omitempty"`
	UpdatedTime      float64  `protobuf:"fixed64,5,opt,name=updatedTime,json=updated_time,proto3" json:"updatedTime,omitempty"`
	DroneModelKey    string   `protobuf:"bytes,6,opt,name=droneModelKey,json=drone_model_key,proto3" json:"droneModelKey,omitempty"`
	TemplateTypes    []int32  `protobuf:"varint,7,rep,packed,name=templateTypes,json=template_types,proto3" json:"templateTypes,omitempty"`
	PayloadModelKeys []string `protobuf:"bytes,8,rep,name=payloadModelKeys,json=payload_model_keys,proto3" json:"payloadModelKeys,omitempty"`
}

func (x *Wayline) Reset() {
	*x = Wayline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Wayline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Wayline) ProtoMessage() {}

func (x *Wayline) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Wayline.ProtoReflect.Descriptor instead.
func (*Wayline) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{1}
}

func (x *Wayline) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Wayline) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Wayline) GetFavorited() bool {
	if x != nil {
		return x.Favorited
	}
	return false
}

func (x *Wayline) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Wayline) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *Wayline) GetDroneModelKey() string {
	if x != nil {
		return x.DroneModelKey
	}
	return ""
}

func (x *Wayline) GetTemplateTypes() []int32 {
	if x != nil {
		return x.TemplateTypes
	}
	return nil
}

func (x *Wayline) GetPayloadModelKeys() []string {
	if x != nil {
		return x.PayloadModelKeys
	}
	return nil
}

type CommonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CommonRequest) Reset() {
	*x = CommonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRequest) ProtoMessage() {}

func (x *CommonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRequest.ProtoReflect.Descriptor instead.
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{2}
}

func (x *CommonRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type WaylineListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Page     int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size     int32  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	PageSize int32  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *WaylineListRequest) Reset() {
	*x = WaylineListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaylineListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaylineListRequest) ProtoMessage() {}

func (x *WaylineListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaylineListRequest.ProtoReflect.Descriptor instead.
func (*WaylineListRequest) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{3}
}

func (x *WaylineListRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WaylineListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *WaylineListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *WaylineListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type WaylineListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *WaylineListReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *WaylineListReply) Reset() {
	*x = WaylineListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaylineListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaylineListReply) ProtoMessage() {}

func (x *WaylineListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaylineListReply.ProtoReflect.Descriptor instead.
func (*WaylineListReply) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{4}
}

func (x *WaylineListReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WaylineListReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *WaylineListReply) GetData() *WaylineListReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type WaylineUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Wid string `protobuf:"bytes,2,opt,name=wid,proto3" json:"wid,omitempty"`
}

func (x *WaylineUrlRequest) Reset() {
	*x = WaylineUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaylineUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaylineUrlRequest) ProtoMessage() {}

func (x *WaylineUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaylineUrlRequest.ProtoReflect.Descriptor instead.
func (*WaylineUrlRequest) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{5}
}

func (x *WaylineUrlRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WaylineUrlRequest) GetWid() string {
	if x != nil {
		return x.Wid
	}
	return ""
}

type WaylineUrlReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *WaylineUrlReplyOkData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *WaylineUrlReply) Reset() {
	*x = WaylineUrlReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaylineUrlReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaylineUrlReply) ProtoMessage() {}

func (x *WaylineUrlReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaylineUrlReply.ProtoReflect.Descriptor instead.
func (*WaylineUrlReply) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{6}
}

func (x *WaylineUrlReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WaylineUrlReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *WaylineUrlReply) GetData() *WaylineUrlReplyOkData {
	if x != nil {
		return x.Data
	}
	return nil
}

type CommonReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *CommonReplyOkData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CommonReply) Reset() {
	*x = CommonReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReply) ProtoMessage() {}

func (x *CommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReply.ProtoReflect.Descriptor instead.
func (*CommonReply) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{7}
}

func (x *CommonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommonReply) GetData() *CommonReplyOkData {
	if x != nil {
		return x.Data
	}
	return nil
}

type MediaFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DroneModelKey   string `protobuf:"bytes,1,opt,name=droneModelKey,json=drone_model_key,proto3" json:"droneModelKey,omitempty"`
	IsOriginal      bool   `protobuf:"varint,2,opt,name=isOriginal,json=is_original,proto3" json:"isOriginal,omitempty"`
	PayloadModelKey string `protobuf:"bytes,3,opt,name=payloadModelKey,json=payload_model_key,proto3" json:"payloadModelKey,omitempty"`
	TinyFingerprint string `protobuf:"bytes,4,opt,name=tinyFingerprint,json=tinny_fingerprint,proto3" json:"tinyFingerprint,omitempty"`
	Sn              string `protobuf:"bytes,5,opt,name=sn,proto3" json:"sn,omitempty"`
	FileGroupId     string `protobuf:"bytes,6,opt,name=fileGroupId,json=file_group_id,proto3" json:"fileGroupId,omitempty"`
}

func (x *MediaFile) Reset() {
	*x = MediaFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaFile) ProtoMessage() {}

func (x *MediaFile) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaFile.ProtoReflect.Descriptor instead.
func (*MediaFile) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{8}
}

func (x *MediaFile) GetDroneModelKey() string {
	if x != nil {
		return x.DroneModelKey
	}
	return ""
}

func (x *MediaFile) GetIsOriginal() bool {
	if x != nil {
		return x.IsOriginal
	}
	return false
}

func (x *MediaFile) GetPayloadModelKey() string {
	if x != nil {
		return x.PayloadModelKey
	}
	return ""
}

func (x *MediaFile) GetTinyFingerprint() string {
	if x != nil {
		return x.TinyFingerprint
	}
	return ""
}

func (x *MediaFile) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *MediaFile) GetFileGroupId() string {
	if x != nil {
		return x.FileGroupId
	}
	return ""
}

type MediaFileMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AbsoluteAltitude float64 `protobuf:"fixed64,1,opt,name=absoluteAltitude,json=absolute_altitude,proto3" json:"absoluteAltitude,omitempty"`
	CreatedTime      string  `protobuf:"bytes,2,opt,name=createdTime,json=created_time,proto3" json:"createdTime,omitempty"`
	// 云台偏航角
	GimbalYawDegree float64 `protobuf:"fixed64,3,opt,name=gimbalYawDegree,json=gimbal_yaw_degree,proto3" json:"gimbalYawDegree,omitempty"`
	// 拍摄相对高度(m)
	RelativeAltitude float64                     `protobuf:"fixed64,4,opt,name=relativeAltitude,json=relative_altitude,proto3" json:"relativeAltitude,omitempty"`
	ShootPosition    *MediaFileMetadata_Position `protobuf:"bytes,5,opt,name=shootPosition,json=shoot_position,proto3" json:"shootPosition,omitempty"`
}

func (x *MediaFileMetadata) Reset() {
	*x = MediaFileMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaFileMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaFileMetadata) ProtoMessage() {}

func (x *MediaFileMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaFileMetadata.ProtoReflect.Descriptor instead.
func (*MediaFileMetadata) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{9}
}

func (x *MediaFileMetadata) GetAbsoluteAltitude() float64 {
	if x != nil {
		return x.AbsoluteAltitude
	}
	return 0
}

func (x *MediaFileMetadata) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *MediaFileMetadata) GetGimbalYawDegree() float64 {
	if x != nil {
		return x.GimbalYawDegree
	}
	return 0
}

func (x *MediaFileMetadata) GetRelativeAltitude() float64 {
	if x != nil {
		return x.RelativeAltitude
	}
	return 0
}

func (x *MediaFileMetadata) GetShootPosition() *MediaFileMetadata_Position {
	if x != nil {
		return x.ShootPosition
	}
	return nil
}

type FastUploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ext         *MediaFile `protobuf:"bytes,1,opt,name=ext,proto3" json:"ext,omitempty"`
	Fingerprint string     `protobuf:"bytes,2,opt,name=fingerprint,proto3" json:"fingerprint,omitempty"`
	Name        string     `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Path        string     `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`
	Id          string     `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *FastUploadRequest) Reset() {
	*x = FastUploadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FastUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FastUploadRequest) ProtoMessage() {}

func (x *FastUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FastUploadRequest.ProtoReflect.Descriptor instead.
func (*FastUploadRequest) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{10}
}

func (x *FastUploadRequest) GetExt() *MediaFile {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *FastUploadRequest) GetFingerprint() string {
	if x != nil {
		return x.Fingerprint
	}
	return ""
}

func (x *FastUploadRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FastUploadRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *FastUploadRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type STSCredential struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessKeyId     string  `protobuf:"bytes,1,opt,name=accessKeyId,json=access_key_id,proto3" json:"accessKeyId,omitempty"`
	AccessKeySecret string  `protobuf:"bytes,2,opt,name=accessKeySecret,json=access_key_secret,proto3" json:"accessKeySecret,omitempty"`
	Expire          float64 `protobuf:"fixed64,3,opt,name=expire,proto3" json:"expire,omitempty"`
	SecurityToken   string  `protobuf:"bytes,4,opt,name=securityToken,json=security_token,proto3" json:"securityToken,omitempty"`
}

func (x *STSCredential) Reset() {
	*x = STSCredential{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *STSCredential) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*STSCredential) ProtoMessage() {}

func (x *STSCredential) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use STSCredential.ProtoReflect.Descriptor instead.
func (*STSCredential) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{11}
}

func (x *STSCredential) GetAccessKeyId() string {
	if x != nil {
		return x.AccessKeyId
	}
	return ""
}

func (x *STSCredential) GetAccessKeySecret() string {
	if x != nil {
		return x.AccessKeySecret
	}
	return ""
}

func (x *STSCredential) GetExpire() float64 {
	if x != nil {
		return x.Expire
	}
	return 0
}

func (x *STSCredential) GetSecurityToken() string {
	if x != nil {
		return x.SecurityToken
	}
	return ""
}

type STSReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *STSReply_STSReplyData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *STSReply) Reset() {
	*x = STSReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *STSReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*STSReply) ProtoMessage() {}

func (x *STSReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use STSReply.ProtoReflect.Descriptor instead.
func (*STSReply) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{12}
}

func (x *STSReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *STSReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *STSReply) GetData() *STSReply_STSReplyData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UploadMediaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Ext         *MediaFile         `protobuf:"bytes,2,opt,name=ext,proto3" json:"ext,omitempty"`
	Fingerprint string             `protobuf:"bytes,3,opt,name=fingerprint,proto3" json:"fingerprint,omitempty"`
	Metadata    *MediaFileMetadata `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Name        string             `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	ObjectKey   string             `protobuf:"bytes,6,opt,name=objectKey,json=object_key,proto3" json:"objectKey,omitempty"`
	Path        string             `protobuf:"bytes,7,opt,name=path,proto3" json:"path,omitempty"`
	// 当文件为图片的时候生效0 - 普通图片1 - 全景图
	SubFileType *int32 `protobuf:"varint,8,opt,name=subFileType,json=sub_file_type,proto3,oneof" json:"subFileType,omitempty"`
	Result      int32  `protobuf:"varint,9,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UploadMediaRequest) Reset() {
	*x = UploadMediaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadMediaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMediaRequest) ProtoMessage() {}

func (x *UploadMediaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMediaRequest.ProtoReflect.Descriptor instead.
func (*UploadMediaRequest) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{13}
}

func (x *UploadMediaRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UploadMediaRequest) GetExt() *MediaFile {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *UploadMediaRequest) GetFingerprint() string {
	if x != nil {
		return x.Fingerprint
	}
	return ""
}

func (x *UploadMediaRequest) GetMetadata() *MediaFileMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UploadMediaRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UploadMediaRequest) GetObjectKey() string {
	if x != nil {
		return x.ObjectKey
	}
	return ""
}

func (x *UploadMediaRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *UploadMediaRequest) GetSubFileType() int32 {
	if x != nil && x.SubFileType != nil {
		return *x.SubFileType
	}
	return 0
}

func (x *UploadMediaRequest) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type UploadMediaReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *UploadMediaReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UploadMediaReply) Reset() {
	*x = UploadMediaReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadMediaReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMediaReply) ProtoMessage() {}

func (x *UploadMediaReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMediaReply.ProtoReflect.Descriptor instead.
func (*UploadMediaReply) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{14}
}

func (x *UploadMediaReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UploadMediaReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UploadMediaReply) GetData() *UploadMediaReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type UploadMediaGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FileGroupId       string `protobuf:"bytes,2,opt,name=fileGroupId,json=file_group_id,proto3" json:"fileGroupId,omitempty"`
	FileCount         int32  `protobuf:"varint,3,opt,name=fileCount,json=file_count,proto3" json:"fileCount,omitempty"`
	FileUploadedCount int32  `protobuf:"varint,4,opt,name=fileUploadedCount,json=file_uploaded_count,proto3" json:"fileUploadedCount,omitempty"`
}

func (x *UploadMediaGroupRequest) Reset() {
	*x = UploadMediaGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadMediaGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMediaGroupRequest) ProtoMessage() {}

func (x *UploadMediaGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMediaGroupRequest.ProtoReflect.Descriptor instead.
func (*UploadMediaGroupRequest) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{15}
}

func (x *UploadMediaGroupRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UploadMediaGroupRequest) GetFileGroupId() string {
	if x != nil {
		return x.FileGroupId
	}
	return ""
}

func (x *UploadMediaGroupRequest) GetFileCount() int32 {
	if x != nil {
		return x.FileCount
	}
	return 0
}

func (x *UploadMediaGroupRequest) GetFileUploadedCount() int32 {
	if x != nil {
		return x.FileUploadedCount
	}
	return 0
}

type WaylineListReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*Wayline  `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *WaylineListReplyListData) Reset() {
	*x = WaylineListReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaylineListReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaylineListReplyListData) ProtoMessage() {}

func (x *WaylineListReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaylineListReplyListData.ProtoReflect.Descriptor instead.
func (*WaylineListReplyListData) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{4, 0}
}

func (x *WaylineListReplyListData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *WaylineListReplyListData) GetList() []*Wayline {
	if x != nil {
		return x.List
	}
	return nil
}

type WaylineUrlReplyOkData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *WaylineUrlReplyOkData) Reset() {
	*x = WaylineUrlReplyOkData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaylineUrlReplyOkData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaylineUrlReplyOkData) ProtoMessage() {}

func (x *WaylineUrlReplyOkData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaylineUrlReplyOkData.ProtoReflect.Descriptor instead.
func (*WaylineUrlReplyOkData) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{6, 0}
}

func (x *WaylineUrlReplyOkData) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type CommonReplyOkData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CommonReplyOkData) Reset() {
	*x = CommonReplyOkData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReplyOkData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReplyOkData) ProtoMessage() {}

func (x *CommonReplyOkData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReplyOkData.ProtoReflect.Descriptor instead.
func (*CommonReplyOkData) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{7, 0}
}

func (x *CommonReplyOkData) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type MediaFileMetadata_Position struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng float64 `protobuf:"fixed64,2,opt,name=lng,proto3" json:"lng,omitempty"`
}

func (x *MediaFileMetadata_Position) Reset() {
	*x = MediaFileMetadata_Position{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaFileMetadata_Position) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaFileMetadata_Position) ProtoMessage() {}

func (x *MediaFileMetadata_Position) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaFileMetadata_Position.ProtoReflect.Descriptor instead.
func (*MediaFileMetadata_Position) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{9, 0}
}

func (x *MediaFileMetadata_Position) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *MediaFileMetadata_Position) GetLng() float64 {
	if x != nil {
		return x.Lng
	}
	return 0
}

type STSReply_STSReplyData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bucket          string         `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	Credentials     *STSCredential `protobuf:"bytes,2,opt,name=credentials,proto3" json:"credentials,omitempty"`
	Endpoint        string         `protobuf:"bytes,3,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	ObjectKeyPrefix string         `protobuf:"bytes,4,opt,name=ObjectKeyPrefix,json=object_key_prefix,proto3" json:"ObjectKeyPrefix,omitempty"`
	Provider        string         `protobuf:"bytes,5,opt,name=provider,proto3" json:"provider,omitempty"`
	Region          string         `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *STSReply_STSReplyData) Reset() {
	*x = STSReply_STSReplyData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *STSReply_STSReplyData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*STSReply_STSReplyData) ProtoMessage() {}

func (x *STSReply_STSReplyData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use STSReply_STSReplyData.ProtoReflect.Descriptor instead.
func (*STSReply_STSReplyData) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{12, 0}
}

func (x *STSReply_STSReplyData) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *STSReply_STSReplyData) GetCredentials() *STSCredential {
	if x != nil {
		return x.Credentials
	}
	return nil
}

func (x *STSReply_STSReplyData) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *STSReply_STSReplyData) GetObjectKeyPrefix() string {
	if x != nil {
		return x.ObjectKeyPrefix
	}
	return ""
}

func (x *STSReply_STSReplyData) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *STSReply_STSReplyData) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type UploadMediaReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ObjectKey string `protobuf:"bytes,1,opt,name=ObjectKey,json=object_key,proto3" json:"ObjectKey,omitempty"`
}

func (x *UploadMediaReply_Data) Reset() {
	*x = UploadMediaReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cloud_v1_cloud_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadMediaReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMediaReply_Data) ProtoMessage() {}

func (x *UploadMediaReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_cloud_v1_cloud_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMediaReply_Data.ProtoReflect.Descriptor instead.
func (*UploadMediaReply_Data) Descriptor() ([]byte, []int) {
	return file_api_cloud_v1_cloud_proto_rawDescGZIP(), []int{14, 0}
}

func (x *UploadMediaReply_Data) GetObjectKey() string {
	if x != nil {
		return x.ObjectKey
	}
	return ""
}

var File_api_cloud_v1_cloud_proto protoreflect.FileDescriptor

var file_api_cloud_v1_cloud_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f,
	0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0f, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x53, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x88, 0x02, 0x0a, 0x07, 0x57, 0x61, 0x79, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x61, 0x76, 0x6f, 0x72,
	0x69, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0d, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x72,
	0x6f, 0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6b, 0x65, 0x79, 0x12, 0x25, 0x0a,
	0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x65, 0x79, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12,
	0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6b, 0x65,
	0x79, 0x73, 0x22, 0x2c, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64,
	0x22, 0xba, 0x01, 0x0a, 0x12, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c,
	0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x3a, 0x03, 0x88, 0x43, 0x01, 0x22, 0xee, 0x01,
	0x0a, 0x10, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c,
	0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x6f, 0x0a,
	0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x4f,
	0x0a, 0x11, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x03, 0x77, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x03, 0x77, 0x69, 0x64, 0x22,
	0x95, 0x01, 0x0a, 0x0f, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x55, 0x72, 0x6c, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6f,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x1a, 0x0a, 0x06, 0x6f,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x93, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6f,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x20, 0x0a, 0x06, 0x6f,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xe0, 0x01,
	0x0a, 0x09, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0d, 0x64,
	0x72, 0x6f, 0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x6b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0a, 0x69, 0x73, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x5f, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6b, 0x65, 0x79,
	0x12, 0x2a, 0x0a, 0x0f, 0x74, 0x69, 0x6e, 0x79, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x69, 0x6e, 0x6e, 0x79,
	0x5f, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x73, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x22, 0x0a, 0x0b,
	0x66, 0x69, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x22, 0xbd, 0x02, 0x0a, 0x11, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x10, 0x61, 0x62, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x65, 0x41, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x11, 0x61, 0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x5f, 0x61, 0x6c, 0x74, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x0f, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c,
	0x59, 0x61, 0x77, 0x44, 0x65, 0x67, 0x72, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x11, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x5f, 0x79, 0x61, 0x77, 0x5f, 0x64, 0x65, 0x67, 0x72,
	0x65, 0x65, 0x12, 0x2b, 0x0a, 0x10, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x6c,
	0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x4f, 0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0e, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x1a, 0x2e, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6e, 0x67,
	0x22, 0x98, 0x01, 0x0a, 0x11, 0x46, 0x61, 0x73, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x03, 0x65, 0x78,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x9e, 0x01, 0x0a, 0x0d,
	0x53, 0x54, 0x53, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x22, 0x0a,
	0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x69,
	0x64, 0x12, 0x2a, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x25, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xd5, 0x02, 0x0a,
	0x08, 0x53, 0x54, 0x53, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x54, 0x53, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x53, 0x54,
	0x53, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0xe1, 0x01, 0x0a, 0x0c, 0x53, 0x54, 0x53, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x54,
	0x53, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x0b, 0x63, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x0f, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4b, 0x65,
	0x79, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x22, 0xc6, 0x02, 0x0a, 0x12, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x03, 0x65,
	0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6e,
	0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x46,
	0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x09, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x27, 0x0a, 0x0b,
	0x73, 0x75, 0x62, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x73, 0x75, 0x62, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa0, 0x01,
	0x0a, 0x10, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x25, 0x0a, 0x04, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1d, 0x0a, 0x09, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6b, 0x65, 0x79,
	0x22, 0x9c, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0b,
	0x66, 0x69, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2e, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x32,
	0xfe, 0x09, 0x0a, 0x05, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x12, 0x89, 0x01, 0x0a, 0x0f, 0x46, 0x61,
	0x73, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x73,
	0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x34, 0x22, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x66, 0x61, 0x73, 0x74, 0x2d, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x3a, 0x01, 0x2a, 0x12, 0x91, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x65, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x38, 0x22, 0x33, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x2d, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x3a, 0x01, 0x2a, 0x12, 0x9c, 0x01, 0x0a, 0x12, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x44, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3e, 0x22, 0x39, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x2d, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x2d, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x3a, 0x01, 0x2a, 0x12, 0x78, 0x0a, 0x0b, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x53, 0x54, 0x53, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x54, 0x53, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x34, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2e, 0x22, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x74, 0x73, 0x3a,
	0x01, 0x2a, 0x12, 0x88, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x79, 0x6c, 0x69,
	0x6e, 0x65, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x12, 0x2e, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x61, 0x79,
	0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x77, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x91, 0x01,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x40, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x12, 0x38, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x77,
	0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x77, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x72,
	0x6c, 0x12, 0x8b, 0x01, 0x0a, 0x0f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x57, 0x61,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x40, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x22, 0x35, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x2d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x3a, 0x01, 0x2a, 0x12,
	0x86, 0x01, 0x0a, 0x10, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x57, 0x61, 0x79, 0x6c,
	0x69, 0x6e, 0x65, 0x73, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x34, 0x22, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2f, 0x77, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x66, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x85, 0x01, 0x0a, 0x12, 0x55, 0x6e, 0x66,
	0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x57, 0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x2a,
	0x2f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77,
	0x61, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x66, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x73,
	0x42, 0x3e, 0x0a, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cloud_v1_cloud_proto_rawDescOnce sync.Once
	file_api_cloud_v1_cloud_proto_rawDescData = file_api_cloud_v1_cloud_proto_rawDesc
)

func file_api_cloud_v1_cloud_proto_rawDescGZIP() []byte {
	file_api_cloud_v1_cloud_proto_rawDescOnce.Do(func() {
		file_api_cloud_v1_cloud_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cloud_v1_cloud_proto_rawDescData)
	})
	return file_api_cloud_v1_cloud_proto_rawDescData
}

var file_api_cloud_v1_cloud_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_api_cloud_v1_cloud_proto_goTypes = []interface{}{
	(*Pagination)(nil),                 // 0: api.cloud.v1.Pagination
	(*Wayline)(nil),                    // 1: api.cloud.v1.Wayline
	(*CommonRequest)(nil),              // 2: api.cloud.v1.CommonRequest
	(*WaylineListRequest)(nil),         // 3: api.cloud.v1.WaylineListRequest
	(*WaylineListReply)(nil),           // 4: api.cloud.v1.WaylineListReply
	(*WaylineUrlRequest)(nil),          // 5: api.cloud.v1.WaylineUrlRequest
	(*WaylineUrlReply)(nil),            // 6: api.cloud.v1.WaylineUrlReply
	(*CommonReply)(nil),                // 7: api.cloud.v1.CommonReply
	(*MediaFile)(nil),                  // 8: api.cloud.v1.MediaFile
	(*MediaFileMetadata)(nil),          // 9: api.cloud.v1.MediaFileMetadata
	(*FastUploadRequest)(nil),          // 10: api.cloud.v1.FastUploadRequest
	(*STSCredential)(nil),              // 11: api.cloud.v1.STSCredential
	(*STSReply)(nil),                   // 12: api.cloud.v1.STSReply
	(*UploadMediaRequest)(nil),         // 13: api.cloud.v1.UploadMediaRequest
	(*UploadMediaReply)(nil),           // 14: api.cloud.v1.UploadMediaReply
	(*UploadMediaGroupRequest)(nil),    // 15: api.cloud.v1.UploadMediaGroupRequest
	(*WaylineListReplyListData)(nil),   // 16: api.cloud.v1.WaylineListReply.listData
	(*WaylineUrlReplyOkData)(nil),      // 17: api.cloud.v1.WaylineUrlReply.okData
	(*CommonReplyOkData)(nil),          // 18: api.cloud.v1.CommonReply.okData
	(*MediaFileMetadata_Position)(nil), // 19: api.cloud.v1.MediaFileMetadata.Position
	(*STSReply_STSReplyData)(nil),      // 20: api.cloud.v1.STSReply.STSReplyData
	(*UploadMediaReply_Data)(nil),      // 21: api.cloud.v1.UploadMediaReply.Data
}
var file_api_cloud_v1_cloud_proto_depIdxs = []int32{
	16, // 0: api.cloud.v1.WaylineListReply.data:type_name -> api.cloud.v1.WaylineListReply.listData
	17, // 1: api.cloud.v1.WaylineUrlReply.data:type_name -> api.cloud.v1.WaylineUrlReply.okData
	18, // 2: api.cloud.v1.CommonReply.data:type_name -> api.cloud.v1.CommonReply.okData
	19, // 3: api.cloud.v1.MediaFileMetadata.shootPosition:type_name -> api.cloud.v1.MediaFileMetadata.Position
	8,  // 4: api.cloud.v1.FastUploadRequest.ext:type_name -> api.cloud.v1.MediaFile
	20, // 5: api.cloud.v1.STSReply.data:type_name -> api.cloud.v1.STSReply.STSReplyData
	8,  // 6: api.cloud.v1.UploadMediaRequest.ext:type_name -> api.cloud.v1.MediaFile
	9,  // 7: api.cloud.v1.UploadMediaRequest.metadata:type_name -> api.cloud.v1.MediaFileMetadata
	21, // 8: api.cloud.v1.UploadMediaReply.data:type_name -> api.cloud.v1.UploadMediaReply.Data
	0,  // 9: api.cloud.v1.WaylineListReply.listData.pagination:type_name -> api.cloud.v1.Pagination
	1,  // 10: api.cloud.v1.WaylineListReply.listData.list:type_name -> api.cloud.v1.Wayline
	11, // 11: api.cloud.v1.STSReply.STSReplyData.credentials:type_name -> api.cloud.v1.STSCredential
	10, // 12: api.cloud.v1.Cloud.FastUploadMedia:input_type -> api.cloud.v1.FastUploadRequest
	13, // 13: api.cloud.v1.Cloud.UploadedMedia:input_type -> api.cloud.v1.UploadMediaRequest
	15, // 14: api.cloud.v1.Cloud.UploadedMediaGroup:input_type -> api.cloud.v1.UploadMediaGroupRequest
	2,  // 15: api.cloud.v1.Cloud.GenerateSTS:input_type -> api.cloud.v1.CommonRequest
	3,  // 16: api.cloud.v1.Cloud.ListWaylines:input_type -> api.cloud.v1.WaylineListRequest
	5,  // 17: api.cloud.v1.Cloud.GetWaylineUrl:input_type -> api.cloud.v1.WaylineUrlRequest
	2,  // 18: api.cloud.v1.Cloud.UploadedWayline:input_type -> api.cloud.v1.CommonRequest
	2,  // 19: api.cloud.v1.Cloud.FavoriteWaylines:input_type -> api.cloud.v1.CommonRequest
	2,  // 20: api.cloud.v1.Cloud.UnfavoriteWaylines:input_type -> api.cloud.v1.CommonRequest
	7,  // 21: api.cloud.v1.Cloud.FastUploadMedia:output_type -> api.cloud.v1.CommonReply
	14, // 22: api.cloud.v1.Cloud.UploadedMedia:output_type -> api.cloud.v1.UploadMediaReply
	7,  // 23: api.cloud.v1.Cloud.UploadedMediaGroup:output_type -> api.cloud.v1.CommonReply
	12, // 24: api.cloud.v1.Cloud.GenerateSTS:output_type -> api.cloud.v1.STSReply
	4,  // 25: api.cloud.v1.Cloud.ListWaylines:output_type -> api.cloud.v1.WaylineListReply
	6,  // 26: api.cloud.v1.Cloud.GetWaylineUrl:output_type -> api.cloud.v1.WaylineUrlReply
	7,  // 27: api.cloud.v1.Cloud.UploadedWayline:output_type -> api.cloud.v1.CommonReply
	7,  // 28: api.cloud.v1.Cloud.FavoriteWaylines:output_type -> api.cloud.v1.CommonReply
	7,  // 29: api.cloud.v1.Cloud.UnfavoriteWaylines:output_type -> api.cloud.v1.CommonReply
	21, // [21:30] is the sub-list for method output_type
	12, // [12:21] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_cloud_v1_cloud_proto_init() }
func file_api_cloud_v1_cloud_proto_init() {
	if File_api_cloud_v1_cloud_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cloud_v1_cloud_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pagination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Wayline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaylineListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaylineListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaylineUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaylineUrlReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaFileMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FastUploadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*STSCredential); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*STSReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadMediaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadMediaReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadMediaGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaylineListReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaylineUrlReplyOkData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReplyOkData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaFileMetadata_Position); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*STSReply_STSReplyData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cloud_v1_cloud_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadMediaReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cloud_v1_cloud_proto_msgTypes[13].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cloud_v1_cloud_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cloud_v1_cloud_proto_goTypes,
		DependencyIndexes: file_api_cloud_v1_cloud_proto_depIdxs,
		MessageInfos:      file_api_cloud_v1_cloud_proto_msgTypes,
	}.Build()
	File_api_cloud_v1_cloud_proto = out.File
	file_api_cloud_v1_cloud_proto_rawDesc = nil
	file_api_cloud_v1_cloud_proto_goTypes = nil
	file_api_cloud_v1_cloud_proto_depIdxs = nil
}
