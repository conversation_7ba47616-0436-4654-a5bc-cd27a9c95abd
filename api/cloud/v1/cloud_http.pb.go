// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type CloudHTTPServer interface {
	FastUploadMedia(context.Context, *FastUploadRequest) (*CommonReply, error)
	FavoriteWaylines(context.Context, *CommonRequest) (*CommonReply, error)
	GenerateSTS(context.Context, *CommonRequest) (*STSReply, error)
	GetWaylineUrl(context.Context, *WaylineUrlRequest) (*WaylineUrlReply, error)
	ListWaylines(context.Context, *WaylineListRequest) (*WaylineListReply, error)
	UnfavoriteWaylines(context.Context, *CommonRequest) (*CommonReply, error)
	UploadedMedia(context.Context, *UploadMediaRequest) (*UploadMediaReply, error)
	UploadedMediaGroup(context.Context, *UploadMediaGroupRequest) (*CommonReply, error)
	UploadedWayline(context.Context, *CommonRequest) (*CommonReply, error)
}

func RegisterCloudHTTPServer(s *http.Server, srv CloudHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/cloud/media/workspaces/{id}/fast-upload", _Cloud_FastUploadMedia0_HTTP_Handler(srv))
	r.POST("/api/v1/cloud/media/workspaces/{id}/upload-callback", _Cloud_UploadedMedia0_HTTP_Handler(srv))
	r.POST("/api/v1/cloud/media/workspaces/{id}/group-upload-callback", _Cloud_UploadedMediaGroup0_HTTP_Handler(srv))
	r.POST("/api/v1/cloud/storage/workspaces/{id}/sts", _Cloud_GenerateSTS0_HTTP_Handler(srv))
	r.GET("/api/v1/cloud/wayline/workspaces/{id}/waylines", _Cloud_ListWaylines0_HTTP_Handler(srv))
	r.GET("/api/v1/cloud/wayline/workspaces/{id}/waylines/{wid}/url", _Cloud_GetWaylineUrl0_HTTP_Handler(srv))
	r.POST("/api/v1/cloud/wayline/workspaces/{id}/upload-callback", _Cloud_UploadedWayline0_HTTP_Handler(srv))
	r.POST("/api/v1/cloud/wayline/workspaces/{id}/favorites", _Cloud_FavoriteWaylines0_HTTP_Handler(srv))
	r.DELETE("/api/v1/cloud/wayline/workspaces/{id}/favorites", _Cloud_UnfavoriteWaylines0_HTTP_Handler(srv))
}

func _Cloud_FastUploadMedia0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FastUploadRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/FastUploadMedia")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FastUploadMedia(ctx, req.(*FastUploadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Cloud_UploadedMedia0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadMediaRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/UploadedMedia")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadedMedia(ctx, req.(*UploadMediaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UploadMediaReply)
		return ctx.Result(200, reply)
	}
}

func _Cloud_UploadedMediaGroup0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadMediaGroupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/UploadedMediaGroup")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadedMediaGroup(ctx, req.(*UploadMediaGroupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Cloud_GenerateSTS0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/GenerateSTS")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GenerateSTS(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*STSReply)
		return ctx.Result(200, reply)
	}
}

func _Cloud_ListWaylines0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WaylineListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/ListWaylines")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWaylines(ctx, req.(*WaylineListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WaylineListReply)
		return ctx.Result(200, reply)
	}
}

func _Cloud_GetWaylineUrl0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WaylineUrlRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/GetWaylineUrl")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWaylineUrl(ctx, req.(*WaylineUrlRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WaylineUrlReply)
		return ctx.Result(200, reply)
	}
}

func _Cloud_UploadedWayline0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/UploadedWayline")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadedWayline(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Cloud_FavoriteWaylines0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/FavoriteWaylines")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FavoriteWaylines(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Cloud_UnfavoriteWaylines0_HTTP_Handler(srv CloudHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Cloud/UnfavoriteWaylines")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UnfavoriteWaylines(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type CloudHTTPClient interface {
	FastUploadMedia(ctx context.Context, req *FastUploadRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	FavoriteWaylines(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	GenerateSTS(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *STSReply, err error)
	GetWaylineUrl(ctx context.Context, req *WaylineUrlRequest, opts ...http.CallOption) (rsp *WaylineUrlReply, err error)
	ListWaylines(ctx context.Context, req *WaylineListRequest, opts ...http.CallOption) (rsp *WaylineListReply, err error)
	UnfavoriteWaylines(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UploadedMedia(ctx context.Context, req *UploadMediaRequest, opts ...http.CallOption) (rsp *UploadMediaReply, err error)
	UploadedMediaGroup(ctx context.Context, req *UploadMediaGroupRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UploadedWayline(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type CloudHTTPClientImpl struct {
	cc *http.Client
}

func NewCloudHTTPClient(client *http.Client) CloudHTTPClient {
	return &CloudHTTPClientImpl{client}
}

func (c *CloudHTTPClientImpl) FastUploadMedia(ctx context.Context, in *FastUploadRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/cloud/media/workspaces/{id}/fast-upload"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/FastUploadMedia"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CloudHTTPClientImpl) FavoriteWaylines(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/cloud/wayline/workspaces/{id}/favorites"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/FavoriteWaylines"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CloudHTTPClientImpl) GenerateSTS(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*STSReply, error) {
	var out STSReply
	pattern := "/api/v1/cloud/storage/workspaces/{id}/sts"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/GenerateSTS"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CloudHTTPClientImpl) GetWaylineUrl(ctx context.Context, in *WaylineUrlRequest, opts ...http.CallOption) (*WaylineUrlReply, error) {
	var out WaylineUrlReply
	pattern := "/api/v1/cloud/wayline/workspaces/{id}/waylines/{wid}/url"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/GetWaylineUrl"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CloudHTTPClientImpl) ListWaylines(ctx context.Context, in *WaylineListRequest, opts ...http.CallOption) (*WaylineListReply, error) {
	var out WaylineListReply
	pattern := "/api/v1/cloud/wayline/workspaces/{id}/waylines"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/ListWaylines"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CloudHTTPClientImpl) UnfavoriteWaylines(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/cloud/wayline/workspaces/{id}/favorites"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/UnfavoriteWaylines"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CloudHTTPClientImpl) UploadedMedia(ctx context.Context, in *UploadMediaRequest, opts ...http.CallOption) (*UploadMediaReply, error) {
	var out UploadMediaReply
	pattern := "/api/v1/cloud/media/workspaces/{id}/upload-callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/UploadedMedia"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CloudHTTPClientImpl) UploadedMediaGroup(ctx context.Context, in *UploadMediaGroupRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/cloud/media/workspaces/{id}/group-upload-callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/UploadedMediaGroup"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CloudHTTPClientImpl) UploadedWayline(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/cloud/wayline/workspaces/{id}/upload-callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.cloud.v1.Cloud/UploadedWayline"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
