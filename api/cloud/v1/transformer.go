package v1

import (
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func TransformAlgoEventToLinsEvent(device *biz.JointDevice, mission *biz.SnapMission, event *biz.AlgoEventDto) *biz.LinsMetaEvent {
	return &biz.LinsMetaEvent{
		Type: event.Type,
		Entry: client.Entry{
			CustomId:        conv.Itoa(utilities.MustNextID()),
			AlarmName:       event.Name + "-" + device.Deployment.Name,
			AlarmLnglat:     event.Lnglat,
			AlarmLocation:   event.Location,
			AlarmMerchantId: conv.Itoa(device.MerchantId),
			AlarmTenantId:   conv.Itoa(device.TenantId),
			OccurredTime:    event.Timestamp,
			LLMEngine:       event.LLMEngine,
			Device:          device.ToAlgo(),
			Task:            mission.ToAlgo(),
			DroneAlgoEvent:  mission.TransEvent(event.AlgType),
			Caputre: &client.EventCaptureInfo{
				CaptureTime: event.Timestamp,
				ImageUrl:    event.ImageUrl,
				SceneUrl:    event.ImageUrl,
				Objects:     event.ObjectInfo,
			},
		},
	}
}
