syntax = "proto3";

package api.cloud.v1;

import "google/api/annotations.proto";
import "validator/validator.proto";
import "list/list.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/cloud/v1;v1";
option java_multiple_files = true;
option java_package = "api.cloud.v1";

service Cloud {
	rpc FastUploadMedia (FastUploadRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/cloud/media/workspaces/{id}/fast-upload"
      body: "*"
    };
  };
  // rpc TinyFingerprints () returns (CommonReply) {
  //   option (google.api.http) = {
  //     post: "/api/v1/cloud/media/workspaces/{id}/files/tiny-fingerprints"
  //     body: "*"
  //   };
  // };
  rpc UploadedMedia (UploadMediaRequest) returns (UploadMediaReply) {
    option (google.api.http) = {
      post: "/api/v1/cloud/media/workspaces/{id}/upload-callback"
			body: "*"
    };
  };
  rpc UploadedMediaGroup(UploadMediaGroupRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/cloud/media/workspaces/{id}/group-upload-callback"
			body: "*"
    };
  }
  rpc GenerateSTS (CommonRequest) returns (STSReply) {
    option (google.api.http) = {
      post: "/api/v1/cloud/storage/workspaces/{id}/sts"
      body: "*"
    };
  };
  rpc ListWaylines (WaylineListRequest) returns (WaylineListReply) {
    option (google.api.http) = {
      get: "/api/v1/cloud/wayline/workspaces/{id}/waylines"
    };
  };
  rpc GetWaylineUrl (WaylineUrlRequest) returns (WaylineUrlReply) {
    option (google.api.http) = {
      get: "/api/v1/cloud/wayline/workspaces/{id}/waylines/{wid}/url"
    };
  };
  rpc UploadedWayline (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/cloud/wayline/workspaces/{id}/upload-callback"
			body: "*"
    };
  };
  rpc FavoriteWaylines (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/cloud/wayline/workspaces/{id}/favorites"
			body: "*"
    };
  };
  rpc UnfavoriteWaylines (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      delete: "/api/v1/cloud/wayline/workspaces/{id}/favorites"
    };
  };
}

message Pagination {
  int32 page = 1;
  int32 pageSize = 2 [json_name="page_size"];
  int32 total = 3;
}

message Wayline {
  string id = 1;
  string name = 2;
  bool favorited = 3;
  string userName = 4 [json_name="user_name"];
  double updatedTime = 5 [json_name="updated_time"];
  string droneModelKey = 6 [json_name="drone_model_key"];
  repeated int32 templateTypes = 7 [json_name="template_types"];
  repeated string payloadModelKeys = 8 [json_name="payload_model_keys"];
}

message CommonRequest {
  string id = 1 [(validator.rules) = "required"];
}

message WaylineListRequest {
  option (list.page) = true;
  string id = 1 [(validator.rules) = "required"];
	int32 page = 2 [(validator.rules) = "required,min=1"];
  int32 size = 3 [(validator.rules) = "required,max=5000"];
	int32 page_size = 4 [(validator.rules) = "required,max=5000"];
}

message WaylineListReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    Pagination pagination = 1;
    repeated Wayline list = 2;
  }
}

message WaylineUrlRequest {
  string id = 1 [(validator.rules) = "required"];
  string wid = 2 [(validator.rules) = "required"];
}

message WaylineUrlReply {
	int32 code = 1;
	string message = 2;
	okData data = 3;

  message okData {
    string url = 1;
  }
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	okData data = 3;

  message okData {
    bool status = 1;
  }
}


message MediaFile {
  string droneModelKey = 1 [json_name="drone_model_key"];
  bool isOriginal = 2 [json_name="is_original"];
  string payloadModelKey = 3 [json_name="payload_model_key"];
  string tinyFingerprint = 4 [json_name="tinny_fingerprint"];
  string sn = 5;
  string fileGroupId = 6 [json_name="file_group_id"];
}

message MediaFileMetadata {
  double absoluteAltitude = 1[json_name="absolute_altitude"];
  string createdTime = 2 [json_name="created_time"];
  // 云台偏航角
  double gimbalYawDegree = 3 [json_name="gimbal_yaw_degree"];
  // 拍摄相对高度(m)
  double relativeAltitude = 4 [json_name="relative_altitude"];
  message Position {
    double lat = 1;
    double lng = 2;
  }
  Position shootPosition = 5 [json_name="shoot_position"];
}

message FastUploadRequest {
  MediaFile ext = 1;
  string fingerprint = 2;
  string name = 3;
  string path = 4;
  string id = 5;
}



message STSCredential {
  string accessKeyId = 1 [json_name="access_key_id"];
  string accessKeySecret = 2 [json_name="access_key_secret"];
  double expire = 3;
  string securityToken = 4 [json_name="security_token"];
}

message STSReply {
  int32 code = 1;
  string message = 2;
  message STSReplyData {
    string bucket = 1;
    STSCredential credentials = 2;
    string endpoint = 3;
    string ObjectKeyPrefix = 4 [json_name="object_key_prefix"];
    string provider = 5;
    string region = 6;
  }
  STSReplyData data = 3;
}

message UploadMediaRequest {
  string id = 1;
  MediaFile ext = 2;
  string fingerprint = 3;
  MediaFileMetadata metadata=4;
  string name = 5;
  string objectKey = 6 [json_name="object_key"];
  string path = 7;
  // 当文件为图片的时候生效0 - 普通图片1 - 全景图
  optional int32 subFileType = 8 [json_name="sub_file_type"];
  int32 result =9;
}

message UploadMediaReply {
  int32 code = 1;
  string message = 2;
  message Data {
    string ObjectKey = 1 [json_name="object_key"];
  }
  Data data = 3;
}


message UploadMediaGroupRequest {
  string id = 1;
  string fileGroupId = 2 [json_name="file_group_id"];
  int32 fileCount = 3 [json_name="file_count"];
  int32 fileUploadedCount = 4 [json_name="file_uploaded_count"];
}