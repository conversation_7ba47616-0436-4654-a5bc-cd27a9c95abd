// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/cloud/v1/cloud.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CloudClient is the client API for Cloud service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CloudClient interface {
	FastUploadMedia(ctx context.Context, in *FastUploadRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// rpc TinyFingerprints () returns (CommonReply) {
	//   option (google.api.http) = {
	//     post: "/api/v1/cloud/media/workspaces/{id}/files/tiny-fingerprints"
	//     body: "*"
	//   };
	// };
	UploadedMedia(ctx context.Context, in *UploadMediaRequest, opts ...grpc.CallOption) (*UploadMediaReply, error)
	UploadedMediaGroup(ctx context.Context, in *UploadMediaGroupRequest, opts ...grpc.CallOption) (*CommonReply, error)
	GenerateSTS(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*STSReply, error)
	ListWaylines(ctx context.Context, in *WaylineListRequest, opts ...grpc.CallOption) (*WaylineListReply, error)
	GetWaylineUrl(ctx context.Context, in *WaylineUrlRequest, opts ...grpc.CallOption) (*WaylineUrlReply, error)
	UploadedWayline(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	FavoriteWaylines(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	UnfavoriteWaylines(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
}

type cloudClient struct {
	cc grpc.ClientConnInterface
}

func NewCloudClient(cc grpc.ClientConnInterface) CloudClient {
	return &cloudClient{cc}
}

func (c *cloudClient) FastUploadMedia(ctx context.Context, in *FastUploadRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/FastUploadMedia", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudClient) UploadedMedia(ctx context.Context, in *UploadMediaRequest, opts ...grpc.CallOption) (*UploadMediaReply, error) {
	out := new(UploadMediaReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/UploadedMedia", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudClient) UploadedMediaGroup(ctx context.Context, in *UploadMediaGroupRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/UploadedMediaGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudClient) GenerateSTS(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*STSReply, error) {
	out := new(STSReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/GenerateSTS", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudClient) ListWaylines(ctx context.Context, in *WaylineListRequest, opts ...grpc.CallOption) (*WaylineListReply, error) {
	out := new(WaylineListReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/ListWaylines", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudClient) GetWaylineUrl(ctx context.Context, in *WaylineUrlRequest, opts ...grpc.CallOption) (*WaylineUrlReply, error) {
	out := new(WaylineUrlReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/GetWaylineUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudClient) UploadedWayline(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/UploadedWayline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudClient) FavoriteWaylines(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/FavoriteWaylines", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cloudClient) UnfavoriteWaylines(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Cloud/UnfavoriteWaylines", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CloudServer is the server API for Cloud service.
// All implementations must embed UnimplementedCloudServer
// for forward compatibility
type CloudServer interface {
	FastUploadMedia(context.Context, *FastUploadRequest) (*CommonReply, error)
	// rpc TinyFingerprints () returns (CommonReply) {
	//   option (google.api.http) = {
	//     post: "/api/v1/cloud/media/workspaces/{id}/files/tiny-fingerprints"
	//     body: "*"
	//   };
	// };
	UploadedMedia(context.Context, *UploadMediaRequest) (*UploadMediaReply, error)
	UploadedMediaGroup(context.Context, *UploadMediaGroupRequest) (*CommonReply, error)
	GenerateSTS(context.Context, *CommonRequest) (*STSReply, error)
	ListWaylines(context.Context, *WaylineListRequest) (*WaylineListReply, error)
	GetWaylineUrl(context.Context, *WaylineUrlRequest) (*WaylineUrlReply, error)
	UploadedWayline(context.Context, *CommonRequest) (*CommonReply, error)
	FavoriteWaylines(context.Context, *CommonRequest) (*CommonReply, error)
	UnfavoriteWaylines(context.Context, *CommonRequest) (*CommonReply, error)
	mustEmbedUnimplementedCloudServer()
}

// UnimplementedCloudServer must be embedded to have forward compatible implementations.
type UnimplementedCloudServer struct {
}

func (UnimplementedCloudServer) FastUploadMedia(context.Context, *FastUploadRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FastUploadMedia not implemented")
}
func (UnimplementedCloudServer) UploadedMedia(context.Context, *UploadMediaRequest) (*UploadMediaReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadedMedia not implemented")
}
func (UnimplementedCloudServer) UploadedMediaGroup(context.Context, *UploadMediaGroupRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadedMediaGroup not implemented")
}
func (UnimplementedCloudServer) GenerateSTS(context.Context, *CommonRequest) (*STSReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateSTS not implemented")
}
func (UnimplementedCloudServer) ListWaylines(context.Context, *WaylineListRequest) (*WaylineListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWaylines not implemented")
}
func (UnimplementedCloudServer) GetWaylineUrl(context.Context, *WaylineUrlRequest) (*WaylineUrlReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWaylineUrl not implemented")
}
func (UnimplementedCloudServer) UploadedWayline(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadedWayline not implemented")
}
func (UnimplementedCloudServer) FavoriteWaylines(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavoriteWaylines not implemented")
}
func (UnimplementedCloudServer) UnfavoriteWaylines(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnfavoriteWaylines not implemented")
}
func (UnimplementedCloudServer) mustEmbedUnimplementedCloudServer() {}

// UnsafeCloudServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CloudServer will
// result in compilation errors.
type UnsafeCloudServer interface {
	mustEmbedUnimplementedCloudServer()
}

func RegisterCloudServer(s grpc.ServiceRegistrar, srv CloudServer) {
	s.RegisterService(&Cloud_ServiceDesc, srv)
}

func _Cloud_FastUploadMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FastUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).FastUploadMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/FastUploadMedia",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).FastUploadMedia(ctx, req.(*FastUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cloud_UploadedMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadMediaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).UploadedMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/UploadedMedia",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).UploadedMedia(ctx, req.(*UploadMediaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cloud_UploadedMediaGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadMediaGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).UploadedMediaGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/UploadedMediaGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).UploadedMediaGroup(ctx, req.(*UploadMediaGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cloud_GenerateSTS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).GenerateSTS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/GenerateSTS",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).GenerateSTS(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cloud_ListWaylines_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WaylineListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).ListWaylines(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/ListWaylines",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).ListWaylines(ctx, req.(*WaylineListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cloud_GetWaylineUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WaylineUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).GetWaylineUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/GetWaylineUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).GetWaylineUrl(ctx, req.(*WaylineUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cloud_UploadedWayline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).UploadedWayline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/UploadedWayline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).UploadedWayline(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cloud_FavoriteWaylines_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).FavoriteWaylines(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/FavoriteWaylines",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).FavoriteWaylines(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cloud_UnfavoriteWaylines_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CloudServer).UnfavoriteWaylines(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Cloud/UnfavoriteWaylines",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CloudServer).UnfavoriteWaylines(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Cloud_ServiceDesc is the grpc.ServiceDesc for Cloud service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Cloud_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.cloud.v1.Cloud",
	HandlerType: (*CloudServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FastUploadMedia",
			Handler:    _Cloud_FastUploadMedia_Handler,
		},
		{
			MethodName: "UploadedMedia",
			Handler:    _Cloud_UploadedMedia_Handler,
		},
		{
			MethodName: "UploadedMediaGroup",
			Handler:    _Cloud_UploadedMediaGroup_Handler,
		},
		{
			MethodName: "GenerateSTS",
			Handler:    _Cloud_GenerateSTS_Handler,
		},
		{
			MethodName: "ListWaylines",
			Handler:    _Cloud_ListWaylines_Handler,
		},
		{
			MethodName: "GetWaylineUrl",
			Handler:    _Cloud_GetWaylineUrl_Handler,
		},
		{
			MethodName: "UploadedWayline",
			Handler:    _Cloud_UploadedWayline_Handler,
		},
		{
			MethodName: "FavoriteWaylines",
			Handler:    _Cloud_FavoriteWaylines_Handler,
		},
		{
			MethodName: "UnfavoriteWaylines",
			Handler:    _Cloud_UnfavoriteWaylines_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/cloud/v1/cloud.proto",
}
