{"swagger": "2.0", "info": {"title": "api/voyages/v1/voyage.proto", "version": "version not set"}, "tags": [{"name": "Voyage"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/voyages": {"get": {"operationId": "Voyage_ListVoyage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apivoyagesv1ListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "deviceId", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "airlineId", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "startTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "endTime", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["Voyage"]}}, "/api/v1/voyages/{id}": {"get": {"operationId": "Voyage_GetVoyage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1VoyageReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Voyage"]}, "put": {"operationId": "Voyage_UpdateVoyage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apivoyagesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"avatarId": {"type": "string", "format": "int64"}}}}], "tags": ["Voyage"]}}, "/api/v1/voyages/{id}/path": {"get": {"operationId": "Voyage_GetPath", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1VoyagePathReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Voyage"]}}}, "definitions": {"VoyageItembriefAirline": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "sn": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "speed": {"type": "number", "format": "float"}, "height": {"type": "number", "format": "float"}}}, "apivoyagesv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apivoyagesv1CommonReplycommonData"}}}, "apivoyagesv1CommonReplycommonData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "apivoyagesv1ListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apivoyagesv1ListReplylistData"}}}, "apivoyagesv1ListReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1VoyageItem"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1VoyageFlightPoint": {"type": "object", "properties": {"timestamp": {"type": "number", "format": "double"}, "point": {"type": "array", "items": {"type": "number", "format": "double"}}, "horizon": {"type": "number", "format": "float"}, "vertical": {"type": "number", "format": "float"}, "height": {"type": "number", "format": "float"}, "elevation": {"type": "number", "format": "float"}}}, "v1VoyageItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "sn": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string"}, "deviceId": {"type": "string", "format": "int64"}, "tenantId": {"type": "string", "format": "int64"}, "airlineId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "isFlown": {"type": "boolean"}, "runtime": {"type": "integer", "format": "int32"}, "mileage": {"type": "integer", "format": "int32"}, "endTime": {"type": "number", "format": "double"}, "startTime": {"type": "number", "format": "double"}, "airline": {"$ref": "#/definitions/VoyageItembriefAirline"}, "guidePoints": {"type": "array", "items": {"$ref": "#/definitions/v1VoyageFlightPoint"}}}}, "v1VoyagePathReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1VoyagePathReplyData"}}}, "v1VoyagePathReplyData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1VoyageFlightPoint"}}}}, "v1VoyageReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1VoyageItem"}}}}}