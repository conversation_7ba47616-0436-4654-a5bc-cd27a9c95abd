syntax = "proto3";

package api.voyages.v1;
import "google/api/annotations.proto";
import "validator/validator.proto";
import "list/list.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/voyages/v1;v1";
option java_multiple_files = true;
option java_package = "api.voyages.v1";

service Voyage {
	rpc ListVoyage (ListRequest) returns (ListReply) {
    option (google.api.http) = {
      get: "/api/v1/voyages"
    };
  };
  rpc GetVoyage (CommonRequest) returns (VoyageReply) {
    option (google.api.http) = {
      get: "/api/v1/voyages/{id}"
    };
  };
	rpc UpdateVoyage (UpdateRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/voyages/{id}"
      body: "*"
    };
  };
  rpc GetPath(CommonRequest) returns (VoyagePathReply) {
    option (google.api.http) = {
      get: "/api/v1/voyages/{id}/path"
    };
  }
}

message CommonRequest {
  int64 id = 1 [(validator.rules) = "required"];
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message UpdateRequest {
  int64 id = 1;
	optional int64 avatarId = 3;
}

message VoyageItem {
  int64 id = 1;
  double createdTime = 2;
  double updatedTime = 3;
  string sn = 4;
  string name = 5;
  string status = 6;
  int64 deviceId = 7;
  int64 tenantId = 8;
  int64 airlineId = 9;
  int64 merchantId = 10;
  bool isFlown = 11;
  int32 runtime = 12;
  int32 mileage = 13;
  double endTime = 14;
  double startTime = 15;
  briefAirline airline = 16;
  repeated VoyageFlightPoint guidePoints = 17;

  message briefAirline {
    int64 id = 1;
    string sn = 2;
    string name = 3;
    string type = 4;
    float speed = 5;
    float height = 6;
  }
}

message VoyageReply {
  int32 code = 1;
  string message = 2;
  VoyageItem data = 3;
}

message ListRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  optional int64 deviceId = 4;
  optional int64 airlineId = 5;
  int64 startTime = 6;
  int64 endTime = 7;
}

message ListReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated VoyageItem list = 4;
  }
}

message VoyageFlightPoint {
  double timestamp = 1;
  repeated double point = 2;
  float horizon = 3;
  float vertical = 4;
  float height = 5;
  float elevation = 6;
}

message VoyagePathReply {
  int32 code = 1;
  string message = 2;
  message Data {
    int32 total = 1;
    repeated VoyageFlightPoint list = 2;
  }
  Data data = 3;
}