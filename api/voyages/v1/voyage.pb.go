// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/voyages/v1/voyage.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list"
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CommonRequest) Reset() {
	*x = CommonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRequest) ProtoMessage() {}

func (x *CommonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRequest.ProtoReflect.Descriptor instead.
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{0}
}

func (x *CommonRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type CommonReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *CommonReplyCommonData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CommonReply) Reset() {
	*x = CommonReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReply) ProtoMessage() {}

func (x *CommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReply.ProtoReflect.Descriptor instead.
func (*CommonReply) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{1}
}

func (x *CommonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommonReply) GetData() *CommonReplyCommonData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AvatarId *int64 `protobuf:"varint,3,opt,name=avatarId,proto3,oneof" json:"avatarId,omitempty"`
}

func (x *UpdateRequest) Reset() {
	*x = UpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRequest) ProtoMessage() {}

func (x *UpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRequest.ProtoReflect.Descriptor instead.
func (*UpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateRequest) GetAvatarId() int64 {
	if x != nil && x.AvatarId != nil {
		return *x.AvatarId
	}
	return 0
}

type VoyageItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime float64                 `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime float64                 `protobuf:"fixed64,3,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	Sn          string                  `protobuf:"bytes,4,opt,name=sn,proto3" json:"sn,omitempty"`
	Name        string                  `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Status      string                  `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	DeviceId    int64                   `protobuf:"varint,7,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	TenantId    int64                   `protobuf:"varint,8,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	AirlineId   int64                   `protobuf:"varint,9,opt,name=airlineId,proto3" json:"airlineId,omitempty"`
	MerchantId  int64                   `protobuf:"varint,10,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	IsFlown     bool                    `protobuf:"varint,11,opt,name=isFlown,proto3" json:"isFlown,omitempty"`
	Runtime     int32                   `protobuf:"varint,12,opt,name=runtime,proto3" json:"runtime,omitempty"`
	Mileage     int32                   `protobuf:"varint,13,opt,name=mileage,proto3" json:"mileage,omitempty"`
	EndTime     float64                 `protobuf:"fixed64,14,opt,name=endTime,proto3" json:"endTime,omitempty"`
	StartTime   float64                 `protobuf:"fixed64,15,opt,name=startTime,proto3" json:"startTime,omitempty"`
	Airline     *VoyageItemBriefAirline `protobuf:"bytes,16,opt,name=airline,proto3" json:"airline,omitempty"`
	GuidePoints []*VoyageFlightPoint    `protobuf:"bytes,17,rep,name=guidePoints,proto3" json:"guidePoints,omitempty"`
}

func (x *VoyageItem) Reset() {
	*x = VoyageItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoyageItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoyageItem) ProtoMessage() {}

func (x *VoyageItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoyageItem.ProtoReflect.Descriptor instead.
func (*VoyageItem) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{3}
}

func (x *VoyageItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VoyageItem) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *VoyageItem) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *VoyageItem) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *VoyageItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VoyageItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *VoyageItem) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *VoyageItem) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *VoyageItem) GetAirlineId() int64 {
	if x != nil {
		return x.AirlineId
	}
	return 0
}

func (x *VoyageItem) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *VoyageItem) GetIsFlown() bool {
	if x != nil {
		return x.IsFlown
	}
	return false
}

func (x *VoyageItem) GetRuntime() int32 {
	if x != nil {
		return x.Runtime
	}
	return 0
}

func (x *VoyageItem) GetMileage() int32 {
	if x != nil {
		return x.Mileage
	}
	return 0
}

func (x *VoyageItem) GetEndTime() float64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *VoyageItem) GetStartTime() float64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *VoyageItem) GetAirline() *VoyageItemBriefAirline {
	if x != nil {
		return x.Airline
	}
	return nil
}

func (x *VoyageItem) GetGuidePoints() []*VoyageFlightPoint {
	if x != nil {
		return x.GuidePoints
	}
	return nil
}

type VoyageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *VoyageItem `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *VoyageReply) Reset() {
	*x = VoyageReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoyageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoyageReply) ProtoMessage() {}

func (x *VoyageReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoyageReply.ProtoReflect.Descriptor instead.
func (*VoyageReply) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{4}
}

func (x *VoyageReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VoyageReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *VoyageReply) GetData() *VoyageItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size      int32  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	DeviceId  *int64 `protobuf:"varint,4,opt,name=deviceId,proto3,oneof" json:"deviceId,omitempty"`
	AirlineId *int64 `protobuf:"varint,5,opt,name=airlineId,proto3,oneof" json:"airlineId,omitempty"`
	StartTime int64  `protobuf:"varint,6,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   int64  `protobuf:"varint,7,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{5}
}

func (x *ListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListRequest) GetDeviceId() int64 {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return 0
}

func (x *ListRequest) GetAirlineId() int64 {
	if x != nil && x.AirlineId != nil {
		return *x.AirlineId
	}
	return 0
}

func (x *ListRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type ListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ListReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListReply) Reset() {
	*x = ListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReply) ProtoMessage() {}

func (x *ListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReply.ProtoReflect.Descriptor instead.
func (*ListReply) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{6}
}

func (x *ListReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListReply) GetData() *ListReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type VoyageFlightPoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp float64   `protobuf:"fixed64,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Point     []float64 `protobuf:"fixed64,2,rep,packed,name=point,proto3" json:"point,omitempty"`
	Horizon   float32   `protobuf:"fixed32,3,opt,name=horizon,proto3" json:"horizon,omitempty"`
	Vertical  float32   `protobuf:"fixed32,4,opt,name=vertical,proto3" json:"vertical,omitempty"`
	Height    float32   `protobuf:"fixed32,5,opt,name=height,proto3" json:"height,omitempty"`
	Elevation float32   `protobuf:"fixed32,6,opt,name=elevation,proto3" json:"elevation,omitempty"`
}

func (x *VoyageFlightPoint) Reset() {
	*x = VoyageFlightPoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoyageFlightPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoyageFlightPoint) ProtoMessage() {}

func (x *VoyageFlightPoint) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoyageFlightPoint.ProtoReflect.Descriptor instead.
func (*VoyageFlightPoint) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{7}
}

func (x *VoyageFlightPoint) GetTimestamp() float64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *VoyageFlightPoint) GetPoint() []float64 {
	if x != nil {
		return x.Point
	}
	return nil
}

func (x *VoyageFlightPoint) GetHorizon() float32 {
	if x != nil {
		return x.Horizon
	}
	return 0
}

func (x *VoyageFlightPoint) GetVertical() float32 {
	if x != nil {
		return x.Vertical
	}
	return 0
}

func (x *VoyageFlightPoint) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VoyageFlightPoint) GetElevation() float32 {
	if x != nil {
		return x.Elevation
	}
	return 0
}

type VoyagePathReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *VoyagePathReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *VoyagePathReply) Reset() {
	*x = VoyagePathReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoyagePathReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoyagePathReply) ProtoMessage() {}

func (x *VoyagePathReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoyagePathReply.ProtoReflect.Descriptor instead.
func (*VoyagePathReply) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{8}
}

func (x *VoyagePathReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VoyagePathReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *VoyagePathReply) GetData() *VoyagePathReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CommonReplyCommonData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CommonReplyCommonData) Reset() {
	*x = CommonReplyCommonData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReplyCommonData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReplyCommonData) ProtoMessage() {}

func (x *CommonReplyCommonData) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReplyCommonData.ProtoReflect.Descriptor instead.
func (*CommonReplyCommonData) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CommonReplyCommonData) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type VoyageItemBriefAirline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sn     string  `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	Name   string  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type   string  `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Speed  float32 `protobuf:"fixed32,5,opt,name=speed,proto3" json:"speed,omitempty"`
	Height float32 `protobuf:"fixed32,6,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *VoyageItemBriefAirline) Reset() {
	*x = VoyageItemBriefAirline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoyageItemBriefAirline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoyageItemBriefAirline) ProtoMessage() {}

func (x *VoyageItemBriefAirline) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoyageItemBriefAirline.ProtoReflect.Descriptor instead.
func (*VoyageItemBriefAirline) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{3, 0}
}

func (x *VoyageItemBriefAirline) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VoyageItemBriefAirline) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *VoyageItemBriefAirline) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VoyageItemBriefAirline) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *VoyageItemBriefAirline) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *VoyageItemBriefAirline) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type ListReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32         `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32         `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32         `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*VoyageItem `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListReplyListData) Reset() {
	*x = ListReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReplyListData) ProtoMessage() {}

func (x *ListReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReplyListData.ProtoReflect.Descriptor instead.
func (*ListReplyListData) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ListReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListReplyListData) GetList() []*VoyageItem {
	if x != nil {
		return x.List
	}
	return nil
}

type VoyagePathReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*VoyageFlightPoint `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *VoyagePathReply_Data) Reset() {
	*x = VoyagePathReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_voyages_v1_voyage_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoyagePathReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoyagePathReply_Data) ProtoMessage() {}

func (x *VoyagePathReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_voyages_v1_voyage_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoyagePathReply_Data.ProtoReflect.Descriptor instead.
func (*VoyagePathReply_Data) Descriptor() ([]byte, []int) {
	return file_api_voyages_v1_voyage_proto_rawDescGZIP(), []int{8, 0}
}

func (x *VoyagePathReply_Data) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *VoyagePathReply_Data) GetList() []*VoyageFlightPoint {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_voyages_v1_voyage_proto protoreflect.FileDescriptor

var file_api_voyages_v1_voyage_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x6c, 0x69, 0x73,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2c, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a,
	0x24, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4d, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x08, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x49, 0x64, 0x22, 0xa7, 0x05, 0x0a, 0x0a, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x46,
	0x6c, 0x6f, 0x77, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x46, 0x6c,
	0x6f, 0x77, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x41, 0x0a, 0x07, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x62, 0x72, 0x69,
	0x65, 0x66, 0x41, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x07, 0x61, 0x69, 0x72, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x43, 0x0a, 0x0b, 0x67, 0x75, 0x69, 0x64, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f,
	0x79, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x46,
	0x6c, 0x69, 0x67, 0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0b, 0x67, 0x75, 0x69, 0x64,
	0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x1a, 0x84, 0x01, 0x0a, 0x0c, 0x62, 0x72, 0x69, 0x65,
	0x66, 0x41, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x6b,
	0x0a, 0x0b, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xfa, 0x01, 0x0a, 0x0b,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61,
	0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x08,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a,
	0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x01, 0x52, 0x09, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x3a, 0x03, 0x88, 0x43, 0x01, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x61,
	0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x22, 0xeb, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x78, 0x0a, 0x08,
	0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb3, 0x01, 0x0a, 0x11, 0x56, 0x6f, 0x79, 0x61, 0x67,
	0x65, 0x46, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x07, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xce, 0x01, 0x0a,
	0x0f, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x38,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f,
	0x79, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x53, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x35, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x46, 0x6c, 0x69, 0x67,
	0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0xa9, 0x03,
	0x0a, 0x06, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x12, 0x5d, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74,
	0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x17,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x12, 0x65, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x56, 0x6f,
	0x79, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x6b,
	0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x12, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x19, 0x1a, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x6f, 0x79, 0x61,
	0x67, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x3a, 0x01, 0x2a, 0x12, 0x6c, 0x0a, 0x07, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6f, 0x79, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x70, 0x61, 0x74, 0x68, 0x42, 0x42, 0x0a, 0x0e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2e, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_voyages_v1_voyage_proto_rawDescOnce sync.Once
	file_api_voyages_v1_voyage_proto_rawDescData = file_api_voyages_v1_voyage_proto_rawDesc
)

func file_api_voyages_v1_voyage_proto_rawDescGZIP() []byte {
	file_api_voyages_v1_voyage_proto_rawDescOnce.Do(func() {
		file_api_voyages_v1_voyage_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_voyages_v1_voyage_proto_rawDescData)
	})
	return file_api_voyages_v1_voyage_proto_rawDescData
}

var file_api_voyages_v1_voyage_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_voyages_v1_voyage_proto_goTypes = []interface{}{
	(*CommonRequest)(nil),          // 0: api.voyages.v1.CommonRequest
	(*CommonReply)(nil),            // 1: api.voyages.v1.CommonReply
	(*UpdateRequest)(nil),          // 2: api.voyages.v1.UpdateRequest
	(*VoyageItem)(nil),             // 3: api.voyages.v1.VoyageItem
	(*VoyageReply)(nil),            // 4: api.voyages.v1.VoyageReply
	(*ListRequest)(nil),            // 5: api.voyages.v1.ListRequest
	(*ListReply)(nil),              // 6: api.voyages.v1.ListReply
	(*VoyageFlightPoint)(nil),      // 7: api.voyages.v1.VoyageFlightPoint
	(*VoyagePathReply)(nil),        // 8: api.voyages.v1.VoyagePathReply
	(*CommonReplyCommonData)(nil),  // 9: api.voyages.v1.CommonReply.commonData
	(*VoyageItemBriefAirline)(nil), // 10: api.voyages.v1.VoyageItem.briefAirline
	(*ListReplyListData)(nil),      // 11: api.voyages.v1.ListReply.listData
	(*VoyagePathReply_Data)(nil),   // 12: api.voyages.v1.VoyagePathReply.Data
}
var file_api_voyages_v1_voyage_proto_depIdxs = []int32{
	9,  // 0: api.voyages.v1.CommonReply.data:type_name -> api.voyages.v1.CommonReply.commonData
	10, // 1: api.voyages.v1.VoyageItem.airline:type_name -> api.voyages.v1.VoyageItem.briefAirline
	7,  // 2: api.voyages.v1.VoyageItem.guidePoints:type_name -> api.voyages.v1.VoyageFlightPoint
	3,  // 3: api.voyages.v1.VoyageReply.data:type_name -> api.voyages.v1.VoyageItem
	11, // 4: api.voyages.v1.ListReply.data:type_name -> api.voyages.v1.ListReply.listData
	12, // 5: api.voyages.v1.VoyagePathReply.data:type_name -> api.voyages.v1.VoyagePathReply.Data
	3,  // 6: api.voyages.v1.ListReply.listData.list:type_name -> api.voyages.v1.VoyageItem
	7,  // 7: api.voyages.v1.VoyagePathReply.Data.list:type_name -> api.voyages.v1.VoyageFlightPoint
	5,  // 8: api.voyages.v1.Voyage.ListVoyage:input_type -> api.voyages.v1.ListRequest
	0,  // 9: api.voyages.v1.Voyage.GetVoyage:input_type -> api.voyages.v1.CommonRequest
	2,  // 10: api.voyages.v1.Voyage.UpdateVoyage:input_type -> api.voyages.v1.UpdateRequest
	0,  // 11: api.voyages.v1.Voyage.GetPath:input_type -> api.voyages.v1.CommonRequest
	6,  // 12: api.voyages.v1.Voyage.ListVoyage:output_type -> api.voyages.v1.ListReply
	4,  // 13: api.voyages.v1.Voyage.GetVoyage:output_type -> api.voyages.v1.VoyageReply
	1,  // 14: api.voyages.v1.Voyage.UpdateVoyage:output_type -> api.voyages.v1.CommonReply
	8,  // 15: api.voyages.v1.Voyage.GetPath:output_type -> api.voyages.v1.VoyagePathReply
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_voyages_v1_voyage_proto_init() }
func file_api_voyages_v1_voyage_proto_init() {
	if File_api_voyages_v1_voyage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_voyages_v1_voyage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoyageItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoyageReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoyageFlightPoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoyagePathReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReplyCommonData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoyageItemBriefAirline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_voyages_v1_voyage_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoyagePathReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_voyages_v1_voyage_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_api_voyages_v1_voyage_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_voyages_v1_voyage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_voyages_v1_voyage_proto_goTypes,
		DependencyIndexes: file_api_voyages_v1_voyage_proto_depIdxs,
		MessageInfos:      file_api_voyages_v1_voyage_proto_msgTypes,
	}.Build()
	File_api_voyages_v1_voyage_proto = out.File
	file_api_voyages_v1_voyage_proto_rawDesc = nil
	file_api_voyages_v1_voyage_proto_goTypes = nil
	file_api_voyages_v1_voyage_proto_depIdxs = nil
}
