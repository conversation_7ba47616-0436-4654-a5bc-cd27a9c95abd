// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/voyages/v1/voyage.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// VoyageClient is the client API for Voyage service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VoyageClient interface {
	ListVoyage(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error)
	GetVoyage(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*VoyageReply, error)
	UpdateVoyage(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (*CommonReply, error)
	GetPath(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*VoyagePathReply, error)
}

type voyageClient struct {
	cc grpc.ClientConnInterface
}

func NewVoyageClient(cc grpc.ClientConnInterface) VoyageClient {
	return &voyageClient{cc}
}

func (c *voyageClient) ListVoyage(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error) {
	out := new(ListReply)
	err := c.cc.Invoke(ctx, "/api.voyages.v1.Voyage/ListVoyage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voyageClient) GetVoyage(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*VoyageReply, error) {
	out := new(VoyageReply)
	err := c.cc.Invoke(ctx, "/api.voyages.v1.Voyage/GetVoyage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voyageClient) UpdateVoyage(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.voyages.v1.Voyage/UpdateVoyage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voyageClient) GetPath(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*VoyagePathReply, error) {
	out := new(VoyagePathReply)
	err := c.cc.Invoke(ctx, "/api.voyages.v1.Voyage/GetPath", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VoyageServer is the server API for Voyage service.
// All implementations must embed UnimplementedVoyageServer
// for forward compatibility
type VoyageServer interface {
	ListVoyage(context.Context, *ListRequest) (*ListReply, error)
	GetVoyage(context.Context, *CommonRequest) (*VoyageReply, error)
	UpdateVoyage(context.Context, *UpdateRequest) (*CommonReply, error)
	GetPath(context.Context, *CommonRequest) (*VoyagePathReply, error)
	mustEmbedUnimplementedVoyageServer()
}

// UnimplementedVoyageServer must be embedded to have forward compatible implementations.
type UnimplementedVoyageServer struct {
}

func (UnimplementedVoyageServer) ListVoyage(context.Context, *ListRequest) (*ListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVoyage not implemented")
}
func (UnimplementedVoyageServer) GetVoyage(context.Context, *CommonRequest) (*VoyageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVoyage not implemented")
}
func (UnimplementedVoyageServer) UpdateVoyage(context.Context, *UpdateRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVoyage not implemented")
}
func (UnimplementedVoyageServer) GetPath(context.Context, *CommonRequest) (*VoyagePathReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPath not implemented")
}
func (UnimplementedVoyageServer) mustEmbedUnimplementedVoyageServer() {}

// UnsafeVoyageServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VoyageServer will
// result in compilation errors.
type UnsafeVoyageServer interface {
	mustEmbedUnimplementedVoyageServer()
}

func RegisterVoyageServer(s grpc.ServiceRegistrar, srv VoyageServer) {
	s.RegisterService(&Voyage_ServiceDesc, srv)
}

func _Voyage_ListVoyage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoyageServer).ListVoyage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.voyages.v1.Voyage/ListVoyage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoyageServer).ListVoyage(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Voyage_GetVoyage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoyageServer).GetVoyage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.voyages.v1.Voyage/GetVoyage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoyageServer).GetVoyage(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Voyage_UpdateVoyage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoyageServer).UpdateVoyage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.voyages.v1.Voyage/UpdateVoyage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoyageServer).UpdateVoyage(ctx, req.(*UpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Voyage_GetPath_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoyageServer).GetPath(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.voyages.v1.Voyage/GetPath",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoyageServer).GetPath(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Voyage_ServiceDesc is the grpc.ServiceDesc for Voyage service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Voyage_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.voyages.v1.Voyage",
	HandlerType: (*VoyageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListVoyage",
			Handler:    _Voyage_ListVoyage_Handler,
		},
		{
			MethodName: "GetVoyage",
			Handler:    _Voyage_GetVoyage_Handler,
		},
		{
			MethodName: "UpdateVoyage",
			Handler:    _Voyage_UpdateVoyage_Handler,
		},
		{
			MethodName: "GetPath",
			Handler:    _Voyage_GetPath_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/voyages/v1/voyage.proto",
}
