// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type VoyageHTTPServer interface {
	GetPath(context.Context, *CommonRequest) (*VoyagePathReply, error)
	GetVoyage(context.Context, *CommonRequest) (*VoyageReply, error)
	ListVoyage(context.Context, *ListRequest) (*ListReply, error)
	UpdateVoyage(context.Context, *UpdateRequest) (*CommonReply, error)
}

func RegisterVoyageHTTPServer(s *http.Server, srv VoyageHTTPServer) {
	r := s.Route("/")
	r.GET("/api/v1/voyages", _Voyage_ListVoyage0_HTTP_Handler(srv))
	r.GET("/api/v1/voyages/{id}", _Voyage_GetVoyage0_HTTP_Handler(srv))
	r.PUT("/api/v1/voyages/{id}", _Voyage_UpdateVoyage0_HTTP_Handler(srv))
	r.GET("/api/v1/voyages/{id}/path", _Voyage_GetPath0_HTTP_Handler(srv))
}

func _Voyage_ListVoyage0_HTTP_Handler(srv VoyageHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.voyages.v1.Voyage/ListVoyage")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListVoyage(ctx, req.(*ListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListReply)
		return ctx.Result(200, reply)
	}
}

func _Voyage_GetVoyage0_HTTP_Handler(srv VoyageHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.voyages.v1.Voyage/GetVoyage")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVoyage(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VoyageReply)
		return ctx.Result(200, reply)
	}
}

func _Voyage_UpdateVoyage0_HTTP_Handler(srv VoyageHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.voyages.v1.Voyage/UpdateVoyage")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateVoyage(ctx, req.(*UpdateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Voyage_GetPath0_HTTP_Handler(srv VoyageHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.voyages.v1.Voyage/GetPath")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPath(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VoyagePathReply)
		return ctx.Result(200, reply)
	}
}

type VoyageHTTPClient interface {
	GetPath(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *VoyagePathReply, err error)
	GetVoyage(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *VoyageReply, err error)
	ListVoyage(ctx context.Context, req *ListRequest, opts ...http.CallOption) (rsp *ListReply, err error)
	UpdateVoyage(ctx context.Context, req *UpdateRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type VoyageHTTPClientImpl struct {
	cc *http.Client
}

func NewVoyageHTTPClient(client *http.Client) VoyageHTTPClient {
	return &VoyageHTTPClientImpl{client}
}

func (c *VoyageHTTPClientImpl) GetPath(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*VoyagePathReply, error) {
	var out VoyagePathReply
	pattern := "/api/v1/voyages/{id}/path"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.voyages.v1.Voyage/GetPath"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VoyageHTTPClientImpl) GetVoyage(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*VoyageReply, error) {
	var out VoyageReply
	pattern := "/api/v1/voyages/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.voyages.v1.Voyage/GetVoyage"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VoyageHTTPClientImpl) ListVoyage(ctx context.Context, in *ListRequest, opts ...http.CallOption) (*ListReply, error) {
	var out ListReply
	pattern := "/api/v1/voyages"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.voyages.v1.Voyage/ListVoyage"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *VoyageHTTPClientImpl) UpdateVoyage(ctx context.Context, in *UpdateRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/voyages/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.voyages.v1.Voyage/UpdateVoyage"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
