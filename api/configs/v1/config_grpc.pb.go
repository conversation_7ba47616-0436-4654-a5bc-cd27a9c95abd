// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/configs/v1/config.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ConfigClient is the client API for Config service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfigClient interface {
	AccessConfig(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*AccessConfigReply, error)
	ThingConfig(ctx context.Context, in *SNThingRequest, opts ...grpc.CallOption) (*ThingConfigReply, error)
	OperationConfig(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*OperationConfigReply, error)
}

type configClient struct {
	cc grpc.ClientConnInterface
}

func NewConfigClient(cc grpc.ClientConnInterface) ConfigClient {
	return &configClient{cc}
}

func (c *configClient) AccessConfig(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*AccessConfigReply, error) {
	out := new(AccessConfigReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Config/AccessConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configClient) ThingConfig(ctx context.Context, in *SNThingRequest, opts ...grpc.CallOption) (*ThingConfigReply, error) {
	out := new(ThingConfigReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Config/ThingConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configClient) OperationConfig(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*OperationConfigReply, error) {
	out := new(OperationConfigReply)
	err := c.cc.Invoke(ctx, "/api.cloud.v1.Config/OperationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfigServer is the server API for Config service.
// All implementations must embed UnimplementedConfigServer
// for forward compatibility
type ConfigServer interface {
	AccessConfig(context.Context, *EmptyRequest) (*AccessConfigReply, error)
	ThingConfig(context.Context, *SNThingRequest) (*ThingConfigReply, error)
	OperationConfig(context.Context, *EmptyRequest) (*OperationConfigReply, error)
	mustEmbedUnimplementedConfigServer()
}

// UnimplementedConfigServer must be embedded to have forward compatible implementations.
type UnimplementedConfigServer struct {
}

func (UnimplementedConfigServer) AccessConfig(context.Context, *EmptyRequest) (*AccessConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccessConfig not implemented")
}
func (UnimplementedConfigServer) ThingConfig(context.Context, *SNThingRequest) (*ThingConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThingConfig not implemented")
}
func (UnimplementedConfigServer) OperationConfig(context.Context, *EmptyRequest) (*OperationConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperationConfig not implemented")
}
func (UnimplementedConfigServer) mustEmbedUnimplementedConfigServer() {}

// UnsafeConfigServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfigServer will
// result in compilation errors.
type UnsafeConfigServer interface {
	mustEmbedUnimplementedConfigServer()
}

func RegisterConfigServer(s grpc.ServiceRegistrar, srv ConfigServer) {
	s.RegisterService(&Config_ServiceDesc, srv)
}

func _Config_AccessConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServer).AccessConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Config/AccessConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServer).AccessConfig(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Config_ThingConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SNThingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServer).ThingConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Config/ThingConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServer).ThingConfig(ctx, req.(*SNThingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Config_OperationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServer).OperationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.cloud.v1.Config/OperationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServer).OperationConfig(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Config_ServiceDesc is the grpc.ServiceDesc for Config service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Config_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.cloud.v1.Config",
	HandlerType: (*ConfigServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AccessConfig",
			Handler:    _Config_AccessConfig_Handler,
		},
		{
			MethodName: "ThingConfig",
			Handler:    _Config_ThingConfig_Handler,
		},
		{
			MethodName: "OperationConfig",
			Handler:    _Config_OperationConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/configs/v1/config.proto",
}
