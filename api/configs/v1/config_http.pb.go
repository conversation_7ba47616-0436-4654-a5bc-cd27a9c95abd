// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type ConfigHTTPServer interface {
	AccessConfig(context.Context, *EmptyRequest) (*AccessConfigReply, error)
	OperationConfig(context.Context, *EmptyRequest) (*OperationConfigReply, error)
	ThingConfig(context.Context, *SNThingRequest) (*ThingConfigReply, error)
}

func RegisterConfigHTTPServer(s *http.Server, srv ConfigHTTPServer) {
	r := s.Route("/")
	r.GET("/api/v1/configs/cloud/access", _Config_AccessConfig0_HTTP_Handler(srv))
	r.GET("/api/v1/configs/cloud/{sn}/thing", _Config_ThingConfig0_HTTP_Handler(srv))
	r.GET("/api/v1/configs/operation/types", _Config_OperationConfig0_HTTP_Handler(srv))
}

func _Config_AccessConfig0_HTTP_Handler(srv ConfigHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Config/AccessConfig")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AccessConfig(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AccessConfigReply)
		return ctx.Result(200, reply)
	}
}

func _Config_ThingConfig0_HTTP_Handler(srv ConfigHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SNThingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Config/ThingConfig")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ThingConfig(ctx, req.(*SNThingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ThingConfigReply)
		return ctx.Result(200, reply)
	}
}

func _Config_OperationConfig0_HTTP_Handler(srv ConfigHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.cloud.v1.Config/OperationConfig")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OperationConfig(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationConfigReply)
		return ctx.Result(200, reply)
	}
}

type ConfigHTTPClient interface {
	AccessConfig(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *AccessConfigReply, err error)
	OperationConfig(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *OperationConfigReply, err error)
	ThingConfig(ctx context.Context, req *SNThingRequest, opts ...http.CallOption) (rsp *ThingConfigReply, err error)
}

type ConfigHTTPClientImpl struct {
	cc *http.Client
}

func NewConfigHTTPClient(client *http.Client) ConfigHTTPClient {
	return &ConfigHTTPClientImpl{client}
}

func (c *ConfigHTTPClientImpl) AccessConfig(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*AccessConfigReply, error) {
	var out AccessConfigReply
	pattern := "/api/v1/configs/cloud/access"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.cloud.v1.Config/AccessConfig"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ConfigHTTPClientImpl) OperationConfig(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*OperationConfigReply, error) {
	var out OperationConfigReply
	pattern := "/api/v1/configs/operation/types"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.cloud.v1.Config/OperationConfig"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ConfigHTTPClientImpl) ThingConfig(ctx context.Context, in *SNThingRequest, opts ...http.CallOption) (*ThingConfigReply, error) {
	var out ThingConfigReply
	pattern := "/api/v1/configs/cloud/{sn}/thing"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.cloud.v1.Config/ThingConfig"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
