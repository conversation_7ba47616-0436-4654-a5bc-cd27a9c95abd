syntax = "proto3";

package api.cloud.v1;

import "google/api/annotations.proto";
import "validator/validator.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/configs/v1;v1";
option java_multiple_files = true;
option java_package = "api.configs.v1";

service Config {
  rpc AccessConfig (EmptyRequest) returns (AccessConfigReply) {
    option (google.api.http) = {
      get: "/api/v1/configs/cloud/access"
    };
  };
  rpc ThingConfig (SNThingRequest) returns (ThingConfigReply) {
    option (google.api.http) = {
      get: "/api/v1/configs/cloud/{sn}/thing"
    };
  };
  rpc OperationConfig (EmptyRequest) returns (OperationConfigReply) {
    option (google.api.http) = {
      get: "/api/v1/configs/operation/types"
    };
  };
}

message EmptyRequest {}

message SNThingRequest {
  string sn = 1 [(validator.rules) = "required"];
}

message AccessConfigReply {
	int32 code = 1;
	string message = 2;
	okData data = 3;

  message okData {
    string appId = 1;
    string appKey = 2;
    string appLicense = 3;
  }
}

message ThingConfigReply {
	int32 code = 1;
	string message = 2;
	okData data = 3;

  message okData {
    aconnect api = 1;
    mconnect mqtt = 2;
    ispace workspace = 3;
  }

  message aconnect {
    string host = 1;
    string token = 2;
  }

  message mconnect {
    string host = 1;
    string username = 2;
    string password = 3;
  }

  message ispace {
    string id = 1;
    string name = 2;
  }
}

message OperationConfigReply {
	int32 code = 1;
	string message = 2;
	listData data = 3;

  message listData {
    int32 total = 1;
    repeated optype list = 2;
  }

  message optype {
    string type = 1;
    string name = 2;
  }
}