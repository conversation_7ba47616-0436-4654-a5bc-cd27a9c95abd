// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/configs/v1/config.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{0}
}

type SNThingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn string `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn,omitempty"`
}

func (x *SNThingRequest) Reset() {
	*x = SNThingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SNThingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SNThingRequest) ProtoMessage() {}

func (x *SNThingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SNThingRequest.ProtoReflect.Descriptor instead.
func (*SNThingRequest) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{1}
}

func (x *SNThingRequest) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

type AccessConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *AccessConfigReplyOkData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AccessConfigReply) Reset() {
	*x = AccessConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessConfigReply) ProtoMessage() {}

func (x *AccessConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessConfigReply.ProtoReflect.Descriptor instead.
func (*AccessConfigReply) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{2}
}

func (x *AccessConfigReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AccessConfigReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AccessConfigReply) GetData() *AccessConfigReplyOkData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ThingConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                  `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ThingConfigReplyOkData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ThingConfigReply) Reset() {
	*x = ThingConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThingConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThingConfigReply) ProtoMessage() {}

func (x *ThingConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThingConfigReply.ProtoReflect.Descriptor instead.
func (*ThingConfigReply) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{3}
}

func (x *ThingConfigReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ThingConfigReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ThingConfigReply) GetData() *ThingConfigReplyOkData {
	if x != nil {
		return x.Data
	}
	return nil
}

type OperationConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *OperationConfigReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *OperationConfigReply) Reset() {
	*x = OperationConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationConfigReply) ProtoMessage() {}

func (x *OperationConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationConfigReply.ProtoReflect.Descriptor instead.
func (*OperationConfigReply) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{4}
}

func (x *OperationConfigReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *OperationConfigReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *OperationConfigReply) GetData() *OperationConfigReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccessConfigReplyOkData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	AppKey     string `protobuf:"bytes,2,opt,name=appKey,proto3" json:"appKey,omitempty"`
	AppLicense string `protobuf:"bytes,3,opt,name=appLicense,proto3" json:"appLicense,omitempty"`
}

func (x *AccessConfigReplyOkData) Reset() {
	*x = AccessConfigReplyOkData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessConfigReplyOkData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessConfigReplyOkData) ProtoMessage() {}

func (x *AccessConfigReplyOkData) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessConfigReplyOkData.ProtoReflect.Descriptor instead.
func (*AccessConfigReplyOkData) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{2, 0}
}

func (x *AccessConfigReplyOkData) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AccessConfigReplyOkData) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *AccessConfigReplyOkData) GetAppLicense() string {
	if x != nil {
		return x.AppLicense
	}
	return ""
}

type ThingConfigReplyOkData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Api       *ThingConfigReplyAconnect `protobuf:"bytes,1,opt,name=api,proto3" json:"api,omitempty"`
	Mqtt      *ThingConfigReplyMconnect `protobuf:"bytes,2,opt,name=mqtt,proto3" json:"mqtt,omitempty"`
	Workspace *ThingConfigReplyIspace   `protobuf:"bytes,3,opt,name=workspace,proto3" json:"workspace,omitempty"`
}

func (x *ThingConfigReplyOkData) Reset() {
	*x = ThingConfigReplyOkData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThingConfigReplyOkData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThingConfigReplyOkData) ProtoMessage() {}

func (x *ThingConfigReplyOkData) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThingConfigReplyOkData.ProtoReflect.Descriptor instead.
func (*ThingConfigReplyOkData) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ThingConfigReplyOkData) GetApi() *ThingConfigReplyAconnect {
	if x != nil {
		return x.Api
	}
	return nil
}

func (x *ThingConfigReplyOkData) GetMqtt() *ThingConfigReplyMconnect {
	if x != nil {
		return x.Mqtt
	}
	return nil
}

func (x *ThingConfigReplyOkData) GetWorkspace() *ThingConfigReplyIspace {
	if x != nil {
		return x.Workspace
	}
	return nil
}

type ThingConfigReplyAconnect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host  string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *ThingConfigReplyAconnect) Reset() {
	*x = ThingConfigReplyAconnect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThingConfigReplyAconnect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThingConfigReplyAconnect) ProtoMessage() {}

func (x *ThingConfigReplyAconnect) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThingConfigReplyAconnect.ProtoReflect.Descriptor instead.
func (*ThingConfigReplyAconnect) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{3, 1}
}

func (x *ThingConfigReplyAconnect) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ThingConfigReplyAconnect) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type ThingConfigReplyMconnect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host     string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *ThingConfigReplyMconnect) Reset() {
	*x = ThingConfigReplyMconnect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThingConfigReplyMconnect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThingConfigReplyMconnect) ProtoMessage() {}

func (x *ThingConfigReplyMconnect) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThingConfigReplyMconnect.ProtoReflect.Descriptor instead.
func (*ThingConfigReplyMconnect) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{3, 2}
}

func (x *ThingConfigReplyMconnect) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ThingConfigReplyMconnect) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ThingConfigReplyMconnect) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type ThingConfigReplyIspace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ThingConfigReplyIspace) Reset() {
	*x = ThingConfigReplyIspace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThingConfigReplyIspace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThingConfigReplyIspace) ProtoMessage() {}

func (x *ThingConfigReplyIspace) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThingConfigReplyIspace.ProtoReflect.Descriptor instead.
func (*ThingConfigReplyIspace) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{3, 3}
}

func (x *ThingConfigReplyIspace) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ThingConfigReplyIspace) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type OperationConfigReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                         `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*OperationConfigReplyOptype `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *OperationConfigReplyListData) Reset() {
	*x = OperationConfigReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationConfigReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationConfigReplyListData) ProtoMessage() {}

func (x *OperationConfigReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationConfigReplyListData.ProtoReflect.Descriptor instead.
func (*OperationConfigReplyListData) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{4, 0}
}

func (x *OperationConfigReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *OperationConfigReplyListData) GetList() []*OperationConfigReplyOptype {
	if x != nil {
		return x.List
	}
	return nil
}

type OperationConfigReplyOptype struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *OperationConfigReplyOptype) Reset() {
	*x = OperationConfigReplyOptype{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_configs_v1_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationConfigReplyOptype) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationConfigReplyOptype) ProtoMessage() {}

func (x *OperationConfigReplyOptype) ProtoReflect() protoreflect.Message {
	mi := &file_api_configs_v1_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationConfigReplyOptype.ProtoReflect.Descriptor instead.
func (*OperationConfigReplyOptype) Descriptor() ([]byte, []int) {
	return file_api_configs_v1_config_proto_rawDescGZIP(), []int{4, 1}
}

func (x *OperationConfigReplyOptype) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *OperationConfigReplyOptype) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_api_configs_v1_config_proto protoreflect.FileDescriptor

var file_api_configs_v1_config_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x2d, 0x0a, 0x0e, 0x53, 0x4e, 0x54, 0x68, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x02, 0x73, 0x6e, 0x22, 0xd5, 0x01, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6f, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x56, 0x0a, 0x06, 0x6f, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x61,
	0x70, 0x70, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x22, 0xff, 0x03, 0x0a, 0x10,
	0x54, 0x68, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x68, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6f, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xc5, 0x01, 0x0a, 0x06, 0x6f, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x03, 0x61, 0x70, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x68, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x61, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x03, 0x61, 0x70, 0x69, 0x12,
	0x3b, 0x0a, 0x04, 0x6d, 0x71, 0x74, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x68, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6d, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x04, 0x6d, 0x71, 0x74, 0x74, 0x12, 0x43, 0x0a, 0x09,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x68, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e,
	0x69, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x1a, 0x34, 0x0a, 0x08, 0x61, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0x56, 0x0a, 0x08, 0x6d, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x1a,
	0x2c, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x98, 0x02,
	0x0a, 0x14, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x5f, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6f, 0x70, 0x74, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x30, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0xee, 0x02, 0x0a, 0x06, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x71, 0x0a, 0x0c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x75, 0x0a, 0x0b, 0x54, 0x68, 0x69, 0x6e, 0x67, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4e, 0x54, 0x68, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x68, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2f, 0x7b, 0x73, 0x6e, 0x7d, 0x2f, 0x74, 0x68, 0x69, 0x6e, 0x67, 0x12, 0x7a, 0x0a,
	0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x42, 0x42, 0x0a, 0x0e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2e, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_configs_v1_config_proto_rawDescOnce sync.Once
	file_api_configs_v1_config_proto_rawDescData = file_api_configs_v1_config_proto_rawDesc
)

func file_api_configs_v1_config_proto_rawDescGZIP() []byte {
	file_api_configs_v1_config_proto_rawDescOnce.Do(func() {
		file_api_configs_v1_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_configs_v1_config_proto_rawDescData)
	})
	return file_api_configs_v1_config_proto_rawDescData
}

var file_api_configs_v1_config_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_configs_v1_config_proto_goTypes = []interface{}{
	(*EmptyRequest)(nil),                 // 0: api.cloud.v1.EmptyRequest
	(*SNThingRequest)(nil),               // 1: api.cloud.v1.SNThingRequest
	(*AccessConfigReply)(nil),            // 2: api.cloud.v1.AccessConfigReply
	(*ThingConfigReply)(nil),             // 3: api.cloud.v1.ThingConfigReply
	(*OperationConfigReply)(nil),         // 4: api.cloud.v1.OperationConfigReply
	(*AccessConfigReplyOkData)(nil),      // 5: api.cloud.v1.AccessConfigReply.okData
	(*ThingConfigReplyOkData)(nil),       // 6: api.cloud.v1.ThingConfigReply.okData
	(*ThingConfigReplyAconnect)(nil),     // 7: api.cloud.v1.ThingConfigReply.aconnect
	(*ThingConfigReplyMconnect)(nil),     // 8: api.cloud.v1.ThingConfigReply.mconnect
	(*ThingConfigReplyIspace)(nil),       // 9: api.cloud.v1.ThingConfigReply.ispace
	(*OperationConfigReplyListData)(nil), // 10: api.cloud.v1.OperationConfigReply.listData
	(*OperationConfigReplyOptype)(nil),   // 11: api.cloud.v1.OperationConfigReply.optype
}
var file_api_configs_v1_config_proto_depIdxs = []int32{
	5,  // 0: api.cloud.v1.AccessConfigReply.data:type_name -> api.cloud.v1.AccessConfigReply.okData
	6,  // 1: api.cloud.v1.ThingConfigReply.data:type_name -> api.cloud.v1.ThingConfigReply.okData
	10, // 2: api.cloud.v1.OperationConfigReply.data:type_name -> api.cloud.v1.OperationConfigReply.listData
	7,  // 3: api.cloud.v1.ThingConfigReply.okData.api:type_name -> api.cloud.v1.ThingConfigReply.aconnect
	8,  // 4: api.cloud.v1.ThingConfigReply.okData.mqtt:type_name -> api.cloud.v1.ThingConfigReply.mconnect
	9,  // 5: api.cloud.v1.ThingConfigReply.okData.workspace:type_name -> api.cloud.v1.ThingConfigReply.ispace
	11, // 6: api.cloud.v1.OperationConfigReply.listData.list:type_name -> api.cloud.v1.OperationConfigReply.optype
	0,  // 7: api.cloud.v1.Config.AccessConfig:input_type -> api.cloud.v1.EmptyRequest
	1,  // 8: api.cloud.v1.Config.ThingConfig:input_type -> api.cloud.v1.SNThingRequest
	0,  // 9: api.cloud.v1.Config.OperationConfig:input_type -> api.cloud.v1.EmptyRequest
	2,  // 10: api.cloud.v1.Config.AccessConfig:output_type -> api.cloud.v1.AccessConfigReply
	3,  // 11: api.cloud.v1.Config.ThingConfig:output_type -> api.cloud.v1.ThingConfigReply
	4,  // 12: api.cloud.v1.Config.OperationConfig:output_type -> api.cloud.v1.OperationConfigReply
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_configs_v1_config_proto_init() }
func file_api_configs_v1_config_proto_init() {
	if File_api_configs_v1_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_configs_v1_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SNThingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThingConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessConfigReplyOkData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThingConfigReplyOkData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThingConfigReplyAconnect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThingConfigReplyMconnect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThingConfigReplyIspace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationConfigReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_configs_v1_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationConfigReplyOptype); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_configs_v1_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_configs_v1_config_proto_goTypes,
		DependencyIndexes: file_api_configs_v1_config_proto_depIdxs,
		MessageInfos:      file_api_configs_v1_config_proto_msgTypes,
	}.Build()
	File_api_configs_v1_config_proto = out.File
	file_api_configs_v1_config_proto_rawDesc = nil
	file_api_configs_v1_config_proto_goTypes = nil
	file_api_configs_v1_config_proto_depIdxs = nil
}
