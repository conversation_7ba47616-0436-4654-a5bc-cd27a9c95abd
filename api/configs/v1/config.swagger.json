{"swagger": "2.0", "info": {"title": "api/configs/v1/config.proto", "version": "version not set"}, "tags": [{"name": "Config"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/configs/cloud/access": {"get": {"operationId": "Config_AccessConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1AccessConfigReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["Config"]}}, "/api/v1/configs/cloud/{sn}/thing": {"get": {"operationId": "Config_ThingConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ThingConfigReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "sn", "in": "path", "required": true, "type": "string"}], "tags": ["Config"]}}, "/api/v1/configs/operation/types": {"get": {"operationId": "Config_OperationConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationConfigReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["Config"]}}}, "definitions": {"OperationConfigReplyoptype": {"type": "object", "properties": {"type": {"type": "string"}, "name": {"type": "string"}}}, "ThingConfigReplyaconnect": {"type": "object", "properties": {"host": {"type": "string"}, "token": {"type": "string"}}}, "ThingConfigReplyispace": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "ThingConfigReplymconnect": {"type": "object", "properties": {"host": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1AccessConfigReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1AccessConfigReplyokData"}}}, "v1AccessConfigReplyokData": {"type": "object", "properties": {"appId": {"type": "string"}, "appKey": {"type": "string"}, "appLicense": {"type": "string"}}}, "v1OperationConfigReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1OperationConfigReplylistData"}}}, "v1OperationConfigReplylistData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/OperationConfigReplyoptype"}}}}, "v1ThingConfigReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1ThingConfigReplyokData"}}}, "v1ThingConfigReplyokData": {"type": "object", "properties": {"api": {"$ref": "#/definitions/ThingConfigReplyaconnect"}, "mqtt": {"$ref": "#/definitions/ThingConfigReplymconnect"}, "workspace": {"$ref": "#/definitions/ThingConfigReplyispace"}}}}}