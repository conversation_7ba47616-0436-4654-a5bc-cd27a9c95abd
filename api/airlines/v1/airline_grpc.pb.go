// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/airlines/v1/airline.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AirlineClient is the client API for Airline service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AirlineClient interface {
	CreateAirline(ctx context.Context, in *AddRequest, opts ...grpc.CallOption) (*CommonReply, error)
	ListAirline(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error)
	GetAirline(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*AirlineReply, error)
	UpdateAirline(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (*CommonReply, error)
	DeleteAirline(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	DownloadAirline(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DownloadReply, error)
}

type airlineClient struct {
	cc grpc.ClientConnInterface
}

func NewAirlineClient(cc grpc.ClientConnInterface) AirlineClient {
	return &airlineClient{cc}
}

func (c *airlineClient) CreateAirline(ctx context.Context, in *AddRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.airlines.v1.Airline/CreateAirline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *airlineClient) ListAirline(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error) {
	out := new(ListReply)
	err := c.cc.Invoke(ctx, "/api.airlines.v1.Airline/ListAirline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *airlineClient) GetAirline(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*AirlineReply, error) {
	out := new(AirlineReply)
	err := c.cc.Invoke(ctx, "/api.airlines.v1.Airline/GetAirline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *airlineClient) UpdateAirline(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.airlines.v1.Airline/UpdateAirline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *airlineClient) DeleteAirline(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.airlines.v1.Airline/DeleteAirline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *airlineClient) DownloadAirline(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DownloadReply, error) {
	out := new(DownloadReply)
	err := c.cc.Invoke(ctx, "/api.airlines.v1.Airline/DownloadAirline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AirlineServer is the server API for Airline service.
// All implementations must embed UnimplementedAirlineServer
// for forward compatibility
type AirlineServer interface {
	CreateAirline(context.Context, *AddRequest) (*CommonReply, error)
	ListAirline(context.Context, *ListRequest) (*ListReply, error)
	GetAirline(context.Context, *GetRequest) (*AirlineReply, error)
	UpdateAirline(context.Context, *UpdateRequest) (*CommonReply, error)
	DeleteAirline(context.Context, *CommonRequest) (*CommonReply, error)
	DownloadAirline(context.Context, *CommonRequest) (*DownloadReply, error)
	mustEmbedUnimplementedAirlineServer()
}

// UnimplementedAirlineServer must be embedded to have forward compatible implementations.
type UnimplementedAirlineServer struct {
}

func (UnimplementedAirlineServer) CreateAirline(context.Context, *AddRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAirline not implemented")
}
func (UnimplementedAirlineServer) ListAirline(context.Context, *ListRequest) (*ListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAirline not implemented")
}
func (UnimplementedAirlineServer) GetAirline(context.Context, *GetRequest) (*AirlineReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAirline not implemented")
}
func (UnimplementedAirlineServer) UpdateAirline(context.Context, *UpdateRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAirline not implemented")
}
func (UnimplementedAirlineServer) DeleteAirline(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAirline not implemented")
}
func (UnimplementedAirlineServer) DownloadAirline(context.Context, *CommonRequest) (*DownloadReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadAirline not implemented")
}
func (UnimplementedAirlineServer) mustEmbedUnimplementedAirlineServer() {}

// UnsafeAirlineServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AirlineServer will
// result in compilation errors.
type UnsafeAirlineServer interface {
	mustEmbedUnimplementedAirlineServer()
}

func RegisterAirlineServer(s grpc.ServiceRegistrar, srv AirlineServer) {
	s.RegisterService(&Airline_ServiceDesc, srv)
}

func _Airline_CreateAirline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AirlineServer).CreateAirline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.airlines.v1.Airline/CreateAirline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AirlineServer).CreateAirline(ctx, req.(*AddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Airline_ListAirline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AirlineServer).ListAirline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.airlines.v1.Airline/ListAirline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AirlineServer).ListAirline(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Airline_GetAirline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AirlineServer).GetAirline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.airlines.v1.Airline/GetAirline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AirlineServer).GetAirline(ctx, req.(*GetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Airline_UpdateAirline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AirlineServer).UpdateAirline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.airlines.v1.Airline/UpdateAirline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AirlineServer).UpdateAirline(ctx, req.(*UpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Airline_DeleteAirline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AirlineServer).DeleteAirline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.airlines.v1.Airline/DeleteAirline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AirlineServer).DeleteAirline(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Airline_DownloadAirline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AirlineServer).DownloadAirline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.airlines.v1.Airline/DownloadAirline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AirlineServer).DownloadAirline(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Airline_ServiceDesc is the grpc.ServiceDesc for Airline service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Airline_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.airlines.v1.Airline",
	HandlerType: (*AirlineServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAirline",
			Handler:    _Airline_CreateAirline_Handler,
		},
		{
			MethodName: "ListAirline",
			Handler:    _Airline_ListAirline_Handler,
		},
		{
			MethodName: "GetAirline",
			Handler:    _Airline_GetAirline_Handler,
		},
		{
			MethodName: "UpdateAirline",
			Handler:    _Airline_UpdateAirline_Handler,
		},
		{
			MethodName: "DeleteAirline",
			Handler:    _Airline_DeleteAirline_Handler,
		},
		{
			MethodName: "DownloadAirline",
			Handler:    _Airline_DownloadAirline_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/airlines/v1/airline.proto",
}
