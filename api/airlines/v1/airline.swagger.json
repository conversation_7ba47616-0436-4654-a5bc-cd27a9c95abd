{"swagger": "2.0", "info": {"title": "api/airlines/v1/airline.proto", "version": "version not set"}, "tags": [{"name": "Airline"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/airlines": {"get": {"operationId": "Airline_ListAirline", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiairlinesv1ListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "endTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "startTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "deviceIds", "in": "query", "required": false, "type": "string"}, {"name": "type", "in": "query", "required": false, "type": "string"}, {"name": "deviceId", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "search", "in": "query", "required": false, "type": "string"}], "tags": ["Airline"]}, "post": {"operationId": "Airline_CreateAirline", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiairlinesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/apiairlinesv1AddRequest"}}], "tags": ["Airline"]}}, "/api/v1/airlines/{id}": {"get": {"operationId": "Airline_GetAirline", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1AirlineReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "scope", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "withDevice", "in": "query", "required": false, "type": "boolean"}], "tags": ["Airline"]}, "delete": {"operationId": "Airline_DeleteAirline", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiairlinesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Airline"]}, "put": {"operationId": "Airline_UpdateAirline", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiairlinesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "deviceIds": {"type": "array", "items": {"type": "string", "format": "int64"}}}}}], "tags": ["Airline"]}}, "/api/v1/airlines/{id}/download": {"get": {"operationId": "Airline_DownloadAirline", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apiairlinesv1DownloadReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Airline"]}}}, "definitions": {"AirlineItembriefDevice": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "sn": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "model": {"type": "string"}, "status": {"type": "string"}, "category": {"type": "string"}, "location": {"type": "string"}, "networkStatus": {"type": "boolean"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}, "DownloadReplykmzFile": {"type": "object", "properties": {"url": {"type": "string"}, "fingerprint": {"type": "string"}}}, "apiairlinesv1AddRequest": {"type": "object", "properties": {"name": {"type": "string"}, "path": {"type": "string"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "deviceIds": {"type": "array", "items": {"type": "string", "format": "int64"}}}}, "apiairlinesv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apiairlinesv1CommonReplycommonData"}}}, "apiairlinesv1CommonReplycommonData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "apiairlinesv1DownloadReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/DownloadReplykmzFile"}}}, "apiairlinesv1ListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apiairlinesv1ListReplylistData"}}}, "apiairlinesv1ListReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1AirlineItem"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\n The JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1AirlineItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "name": {"type": "string"}, "type": {"type": "string"}, "speed": {"type": "number", "format": "float"}, "tranSpeed": {"type": "number", "format": "float"}, "height": {"type": "number", "format": "float"}, "returnHeight": {"type": "number", "format": "float"}, "securityHeight": {"type": "number", "format": "float"}, "waypointCount": {"type": "integer", "format": "int32"}, "estimateMileage": {"type": "number", "format": "float"}, "flytoMode": {"type": "string"}, "finishAction": {"type": "string"}, "exitOnRCLost": {"type": "string"}, "rCLostAction": {"type": "string"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "deviceIds": {"type": "array", "items": {"type": "string", "format": "int64"}}, "devices": {"type": "array", "items": {"$ref": "#/definitions/AirlineItembriefDevice"}}, "fenceArea": {"type": "object"}}}, "v1AirlineReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1AirlineItem"}}}}}