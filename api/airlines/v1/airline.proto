syntax = "proto3";

package api.airlines.v1;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "validator/validator.proto";
import "list/list.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/airlines/v1;v1";
option java_multiple_files = true;
option java_package = "api.airlines.v1";

service Airline {
  rpc CreateAirline (AddRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/airlines"
			body: "*"
    };
  };
	rpc ListAirline (ListRequest) returns (ListReply) {
    option (google.api.http) = {
      get: "/api/v1/airlines"
    };
  };
  rpc GetAirline (GetRequest) returns (AirlineReply) {
    option (google.api.http) = {
      get: "/api/v1/airlines/{id}"
    };
  };
	rpc UpdateAirline (UpdateRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/airlines/{id}"
      body: "*"
    };
  };
  rpc DeleteAirline (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      delete: "/api/v1/airlines/{id}"
    };
  };
  rpc DownloadAirline (CommonRequest) returns (DownloadReply) {
    option (google.api.http) = {
      get: "/api/v1/airlines/{id}/download"
    };
  };
}

message CommonRequest {
  int64 id = 1 [(validator.rules) = "required"];
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message AddRequest {
  string name = 1;
  string path = 2;
  string description = 3;
  repeated string tags = 4;
  repeated int64 deviceIds = 5;
}

message GetRequest {
  int64 id = 1 [(validator.rules) = "required"];
  int32 scope = 2;
  bool withDevice = 3;
}

message UpdateRequest {
  int64 id = 1 [(validator.rules) = "required"];
  string name = 2;
  string description = 3;
  repeated string tags = 4;
  repeated int64 deviceIds = 5;
}

message AirlineItem {
  int64 id = 1;
  double createdTime = 2;
  double updatedTime = 3;
  string name = 4;
  string type = 5;
  float speed = 6;
  float tranSpeed = 7;
  float height = 8;
  float returnHeight = 9;
  float securityHeight = 10;
  int32 waypointCount = 11;
  float estimateMileage = 12;
  string flytoMode = 13;
  string finishAction = 14;
  string exitOnRCLost = 15;
  string rCLostAction = 16;
  int64 tenantId = 17;
  int64 merchantId = 18;
  string description = 19;
  repeated string tags = 20;
  repeated int64 deviceIds = 21;
  repeated briefDevice devices = 22;
  google.protobuf.Struct fenceArea = 23;

  message briefDevice {
    int64 id = 1;
		string sn = 2;
		string name = 3;
		string type = 4;
		string model = 5;
		string status = 6;
		string category = 7;
    string location = 8;
    bool networkStatus = 9;
    repeated double lnglat = 10;
  }
}

message AirlineReply {
  int32 code = 1;
  string message = 2;
  AirlineItem data = 3;
}

message ListRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  int64 endTime = 3;
  int64 startTime = 4;
  string deviceIds = 5;
  optional string type = 6;
  optional int64 deviceId = 7;
  optional string search = 8 [(list.filter_options)={filter_name: "name,tags", operator:"search" }];
}

message ListReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated AirlineItem list = 4;
  }
}

message DownloadReply {
	int32 code = 1;
	string message = 2;
	kmzFile data = 3;

  message kmzFile {
    string url = 1;
    string fingerprint = 2;
  }
}
