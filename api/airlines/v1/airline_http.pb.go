// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type AirlineHTTPServer interface {
	CreateAirline(context.Context, *AddRequest) (*CommonReply, error)
	DeleteAirline(context.Context, *CommonRequest) (*CommonReply, error)
	DownloadAirline(context.Context, *CommonRequest) (*DownloadReply, error)
	GetAirline(context.Context, *GetRequest) (*AirlineReply, error)
	ListAirline(context.Context, *ListRequest) (*ListReply, error)
	UpdateAirline(context.Context, *UpdateRequest) (*CommonReply, error)
}

func RegisterAirlineHTTPServer(s *http.Server, srv AirlineHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/airlines", _Airline_CreateAirline0_HTTP_Handler(srv))
	r.GET("/api/v1/airlines", _Airline_ListAirline0_HTTP_Handler(srv))
	r.GET("/api/v1/airlines/{id}", _Airline_GetAirline0_HTTP_Handler(srv))
	r.PUT("/api/v1/airlines/{id}", _Airline_UpdateAirline0_HTTP_Handler(srv))
	r.DELETE("/api/v1/airlines/{id}", _Airline_DeleteAirline0_HTTP_Handler(srv))
	r.GET("/api/v1/airlines/{id}/download", _Airline_DownloadAirline0_HTTP_Handler(srv))
}

func _Airline_CreateAirline0_HTTP_Handler(srv AirlineHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.airlines.v1.Airline/CreateAirline")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAirline(ctx, req.(*AddRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Airline_ListAirline0_HTTP_Handler(srv AirlineHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.airlines.v1.Airline/ListAirline")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAirline(ctx, req.(*ListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListReply)
		return ctx.Result(200, reply)
	}
}

func _Airline_GetAirline0_HTTP_Handler(srv AirlineHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.airlines.v1.Airline/GetAirline")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAirline(ctx, req.(*GetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AirlineReply)
		return ctx.Result(200, reply)
	}
}

func _Airline_UpdateAirline0_HTTP_Handler(srv AirlineHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.airlines.v1.Airline/UpdateAirline")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAirline(ctx, req.(*UpdateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Airline_DeleteAirline0_HTTP_Handler(srv AirlineHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.airlines.v1.Airline/DeleteAirline")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAirline(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Airline_DownloadAirline0_HTTP_Handler(srv AirlineHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.airlines.v1.Airline/DownloadAirline")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DownloadAirline(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DownloadReply)
		return ctx.Result(200, reply)
	}
}

type AirlineHTTPClient interface {
	CreateAirline(ctx context.Context, req *AddRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	DeleteAirline(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	DownloadAirline(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *DownloadReply, err error)
	GetAirline(ctx context.Context, req *GetRequest, opts ...http.CallOption) (rsp *AirlineReply, err error)
	ListAirline(ctx context.Context, req *ListRequest, opts ...http.CallOption) (rsp *ListReply, err error)
	UpdateAirline(ctx context.Context, req *UpdateRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
}

type AirlineHTTPClientImpl struct {
	cc *http.Client
}

func NewAirlineHTTPClient(client *http.Client) AirlineHTTPClient {
	return &AirlineHTTPClientImpl{client}
}

func (c *AirlineHTTPClientImpl) CreateAirline(ctx context.Context, in *AddRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/airlines"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.airlines.v1.Airline/CreateAirline"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AirlineHTTPClientImpl) DeleteAirline(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/airlines/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.airlines.v1.Airline/DeleteAirline"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AirlineHTTPClientImpl) DownloadAirline(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*DownloadReply, error) {
	var out DownloadReply
	pattern := "/api/v1/airlines/{id}/download"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.airlines.v1.Airline/DownloadAirline"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AirlineHTTPClientImpl) GetAirline(ctx context.Context, in *GetRequest, opts ...http.CallOption) (*AirlineReply, error) {
	var out AirlineReply
	pattern := "/api/v1/airlines/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.airlines.v1.Airline/GetAirline"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AirlineHTTPClientImpl) ListAirline(ctx context.Context, in *ListRequest, opts ...http.CallOption) (*ListReply, error) {
	var out ListReply
	pattern := "/api/v1/airlines"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.airlines.v1.Airline/ListAirline"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AirlineHTTPClientImpl) UpdateAirline(ctx context.Context, in *UpdateRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/airlines/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.airlines.v1.Airline/UpdateAirline"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
