// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v5.29.2
// source: api/devices/v1/device_operation.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceOperationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Action string `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *DeviceOperationRequest) Reset() {
	*x = DeviceOperationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_operation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceOperationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceOperationRequest) ProtoMessage() {}

func (x *DeviceOperationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_operation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceOperationRequest.ProtoReflect.Descriptor instead.
func (*DeviceOperationRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_operation_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceOperationRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeviceOperationRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type DeviceForceCoverRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 只允许为close
	Action string `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *DeviceForceCoverRequest) Reset() {
	*x = DeviceForceCoverRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_operation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceForceCoverRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceForceCoverRequest) ProtoMessage() {}

func (x *DeviceForceCoverRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_operation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceForceCoverRequest.ProtoReflect.Descriptor instead.
func (*DeviceForceCoverRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_operation_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceForceCoverRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeviceForceCoverRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type DeviceLiveCameraRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	LiveId   int64 `protobuf:"varint,2,opt,name=liveId,proto3" json:"liveId,omitempty"`
	Position int32 `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`
}

func (x *DeviceLiveCameraRequest) Reset() {
	*x = DeviceLiveCameraRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_operation_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceLiveCameraRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceLiveCameraRequest) ProtoMessage() {}

func (x *DeviceLiveCameraRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_operation_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceLiveCameraRequest.ProtoReflect.Descriptor instead.
func (*DeviceLiveCameraRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_operation_proto_rawDescGZIP(), []int{2}
}

func (x *DeviceLiveCameraRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeviceLiveCameraRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

func (x *DeviceLiveCameraRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

var File_api_devices_v1_device_operation_proto protoreflect.FileDescriptor

var file_api_devices_v1_device_operation_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x62, 0x0a,
	0x16, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x3d, 0x6f,
	0x70, 0x65, 0x6e, 0x20, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x4e, 0x0a, 0x17, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x8f, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x76, 0x65,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x06, 0x6c, 0x69,
	0x76, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12,
	0x32, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x16, 0xfa, 0x42, 0x13, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x3d, 0x31, 0x30, 0x30, 0x30,
	0x30, 0x31, 0x20, 0x31, 0x30, 0x30, 0x30, 0x30, 0x32, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0x9f, 0x09, 0x0a, 0x0f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x89, 0x01, 0x0a, 0x09, 0x44, 0x65, 0x62, 0x75,
	0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x34, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x1a, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x0c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x1a, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x8a,
	0x01, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x62, 0x6f, 0x6f, 0x74,
	0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b,
	0x3a, 0x01, 0x2a, 0x1a, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x72, 0x65, 0x62, 0x6f, 0x6f, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x12,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x44, 0x72, 0x6f, 0x6e, 0x65, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x30, 0x3a, 0x01, 0x2a, 0x1a, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x12, 0x8a, 0x01, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x74, 0x74, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x1a, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x70, 0x75, 0x74, 0x74, 0x65, 0x72, 0x12,
	0x94, 0x01, 0x0a, 0x11, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x46, 0x6f, 0x72,
	0x63, 0x65, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x36,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x3a, 0x01, 0x2a, 0x1a, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x64, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x92, 0x01, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x4c, 0x69, 0x76, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x3a, 0x01, 0x2a, 0x1a,
	0x2a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6c, 0x69, 0x76, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x97, 0x01, 0x0a, 0x14,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x31, 0x3a, 0x01, 0x2a, 0x1a, 0x2c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x42, 0x0a, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b,
	0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_devices_v1_device_operation_proto_rawDescOnce sync.Once
	file_api_devices_v1_device_operation_proto_rawDescData = file_api_devices_v1_device_operation_proto_rawDesc
)

func file_api_devices_v1_device_operation_proto_rawDescGZIP() []byte {
	file_api_devices_v1_device_operation_proto_rawDescOnce.Do(func() {
		file_api_devices_v1_device_operation_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_devices_v1_device_operation_proto_rawDescData)
	})
	return file_api_devices_v1_device_operation_proto_rawDescData
}

var file_api_devices_v1_device_operation_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_devices_v1_device_operation_proto_goTypes = []interface{}{
	(*DeviceOperationRequest)(nil),  // 0: api.devices.v1.DeviceOperationRequest
	(*DeviceForceCoverRequest)(nil), // 1: api.devices.v1.DeviceForceCoverRequest
	(*DeviceLiveCameraRequest)(nil), // 2: api.devices.v1.DeviceLiveCameraRequest
	(*OperationReply)(nil),          // 3: api.devices.v1.OperationReply
}
var file_api_devices_v1_device_operation_proto_depIdxs = []int32{
	0, // 0: api.devices.v1.DeviceOperation.DebugMode:input_type -> api.devices.v1.DeviceOperationRequest
	0, // 1: api.devices.v1.DeviceOperation.OperateCover:input_type -> api.devices.v1.DeviceOperationRequest
	0, // 2: api.devices.v1.DeviceOperation.OperateReboot:input_type -> api.devices.v1.DeviceOperationRequest
	0, // 3: api.devices.v1.DeviceOperation.OperateDroneSwitch:input_type -> api.devices.v1.DeviceOperationRequest
	0, // 4: api.devices.v1.DeviceOperation.OperatePutter:input_type -> api.devices.v1.DeviceOperationRequest
	1, // 5: api.devices.v1.DeviceOperation.ForceOperateCover:input_type -> api.devices.v1.DeviceForceCoverRequest
	2, // 6: api.devices.v1.DeviceOperation.ChangeLiveCamera:input_type -> api.devices.v1.DeviceLiveCameraRequest
	0, // 7: api.devices.v1.DeviceOperation.OperateBatteryCharge:input_type -> api.devices.v1.DeviceOperationRequest
	3, // 8: api.devices.v1.DeviceOperation.DebugMode:output_type -> api.devices.v1.OperationReply
	3, // 9: api.devices.v1.DeviceOperation.OperateCover:output_type -> api.devices.v1.OperationReply
	3, // 10: api.devices.v1.DeviceOperation.OperateReboot:output_type -> api.devices.v1.OperationReply
	3, // 11: api.devices.v1.DeviceOperation.OperateDroneSwitch:output_type -> api.devices.v1.OperationReply
	3, // 12: api.devices.v1.DeviceOperation.OperatePutter:output_type -> api.devices.v1.OperationReply
	3, // 13: api.devices.v1.DeviceOperation.ForceOperateCover:output_type -> api.devices.v1.OperationReply
	3, // 14: api.devices.v1.DeviceOperation.ChangeLiveCamera:output_type -> api.devices.v1.OperationReply
	3, // 15: api.devices.v1.DeviceOperation.OperateBatteryCharge:output_type -> api.devices.v1.OperationReply
	8, // [8:16] is the sub-list for method output_type
	0, // [0:8] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_devices_v1_device_operation_proto_init() }
func file_api_devices_v1_device_operation_proto_init() {
	if File_api_devices_v1_device_operation_proto != nil {
		return
	}
	file_api_devices_v1_device_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_devices_v1_device_operation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceOperationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_operation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceForceCoverRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_operation_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceLiveCameraRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_devices_v1_device_operation_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_devices_v1_device_operation_proto_goTypes,
		DependencyIndexes: file_api_devices_v1_device_operation_proto_depIdxs,
		MessageInfos:      file_api_devices_v1_device_operation_proto_msgTypes,
	}.Build()
	File_api_devices_v1_device_operation_proto = out.File
	file_api_devices_v1_device_operation_proto_rawDesc = nil
	file_api_devices_v1_device_operation_proto_goTypes = nil
	file_api_devices_v1_device_operation_proto_depIdxs = nil
}
