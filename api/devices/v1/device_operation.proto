syntax = "proto3";

package api.devices.v1;

option go_package = "gitlab.sensoro.com/skai/skai/api/devices/v1;v1";
option java_multiple_files = true;
option java_package = "api.devices.v1";

import "google/api/annotations.proto";
import "validator/validator.proto";
import "api/devices/v1/device.proto";

service DeviceOperation {
	rpc DebugMode(DeviceOperationRequest) returns (api.devices.v1.OperationReply){
		option (google.api.http) = {
			put: "/api/v1/devices/{id}/operations/debugMode"
			body: "*"
		};
	};

	rpc OperateCover(DeviceOperationRequest) returns (api.devices.v1.OperationReply){
		option (google.api.http) = {
			put: "/api/v1/devices/{id}/operations/cover"
			body: "*"
		};
	}

	rpc OperateReboot(DeviceOperationRequest) returns (api.devices.v1.OperationReply){
		option (google.api.http) = {
			put: "/api/v1/devices/{id}/operations/reboot"
			body: "*"
		};
	}

	rpc OperateDroneSwitch(DeviceOperationRequest) returns (api.devices.v1.OperationReply){
		option (google.api.http) = {
			put: "/api/v1/devices/{id}/operations/droneSwitch"
			body: "*"
		};
	}

	rpc OperatePutter(DeviceOperationRequest) returns (api.devices.v1.OperationReply){
		option (google.api.http) = {
			put: "/api/v1/devices/{id}/operations/putter"
			body: "*"
		};
	}

	rpc ForceOperateCover(DeviceForceCoverRequest) returns (api.devices.v1.OperationReply){
		option (google.api.http) = {
			put: "/api/v1/devices/{id}/operations/forcedCover"
			body: "*"
		};
	}

	rpc ChangeLiveCamera(DeviceLiveCameraRequest) returns (api.devices.v1.OperationReply){
		option (google.api.http) = {
			put: "/api/v1/devices/{id}/operations/liveCamera"
			body: "*"
		};
	}

	rpc OperateBatteryCharge(DeviceOperationRequest) returns (api.devices.v1.OperationReply){
		option (google.api.http) = {
			put: "/api/v1/devices/{id}/operations/chargeSwitch"
			body: "*"
		};
	}

}

message DeviceOperationRequest {
	int64 id = 1 [(validator.rules)="required"];
	string action = 2 [(validator.rules) = "oneof=open close"];
}


message DeviceForceCoverRequest {
	int64 id = 1 [(validator.rules)="required"];
	// 只允许为close
	string action = 2;
}


message DeviceLiveCameraRequest {
	int64 id = 1 [(validator.rules)="required"];
	int64 liveId = 2 [(validator.rules)="required"];
	int32 position = 3 [(validator.rules) = "oneof=100001 100002"];
}