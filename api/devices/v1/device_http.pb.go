// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type DeviceHTTPServer interface {
	AddDevice(context.Context, *AddRequest) (*CommonReply, error)
	AeromodeDevice(context.Context, *AeromodeRequest) (*CommonReply, error)
	AlgflowDevice(context.Context, *AlgflowRequest) (*CommonReply, error)
	AutobackDevice(context.Context, *ReturnRequest) (*OperationReply, error)
	CallbackDevice(context.Context, *CallbackRequest) (*CommonReply, error)
	ClarityDevice(context.Context, *ClarityRequest) (*OperationReply, error)
	ControlDevice(context.Context, *ControlRequest) (*OperationReply, error)
	CrashDevice(context.Context, *CommonRequest) (*OperationReply, error)
	DeviceDatalogs(context.Context, *DatalogsRequest) (*DatalogsReply, error)
	DeviceEvents(context.Context, *EventsRequest) (*EventsReply, error)
	DeviceOperations(context.Context, *OperationsRequest) (*OperationsReply, error)
	DeviceSchedules(context.Context, *SchedulesRequest) (*SchedulesReply, error)
	DownloadLogfile(context.Context, *DownloadLogfilesRequest) (*DownloadReply, error)
	DriveDevice(context.Context, *DriveRequest) (*OperationReply, error)
	ExecuteDevice(context.Context, *CallbackRequest) (*CommonReply, error)
	FreeDevice(context.Context, *FreeRequest) (*OperationReply, error)
	GetDevice(context.Context, *CommonRequest) (*DeviceReply, error)
	GimbalDevice(context.Context, *GimbalRequest) (*OperationReply, error)
	LaunchDevice(context.Context, *LaunchRequest) (*OperationReply, error)
	LensDevice(context.Context, *LensRequest) (*OperationReply, error)
	ListDevice(context.Context, *ListRequest) (*ListReply, error)
	ListLogfiles(context.Context, *ListLogfilesRequest) (*LogfilesReply, error)
	LookatDevice(context.Context, *LookatRequest) (*OperationReply, error)
	OrbeedDevice(context.Context, *OrbeedRequest) (*OperationReply, error)
	OrbitDevice(context.Context, *PointRequest) (*OperationReply, error)
	PictureDevice(context.Context, *PictureRequest) (*OperationReply, error)
	PlaymodeDevice(context.Context, *PlaymodeRequest) (*OperationReply, error)
	PointDevice(context.Context, *PointRequest) (*OperationReply, error)
	PropertyDevice(context.Context, *PropertyRequest) (*OperationReply, error)
	ReleaseDevice(context.Context, *ControlRequest) (*OperationReply, error)
	RemoteLogfiles(context.Context, *RemoteLogfilesRequest) (*OperationReply, error)
	RemoveDevice(context.Context, *CommonRequest) (*CommonReply, error)
	RepointDevice(context.Context, *PointRequest) (*OperationReply, error)
	ReturnDevice(context.Context, *ReturnRequest) (*OperationReply, error)
	RouteDevice(context.Context, *ReturnRequest) (*OperationReply, error)
	ShoutDevice(context.Context, *ReturnRequest) (*OperationReply, error)
	SpeakDevice(context.Context, *SpeakRequest) (*OperationReply, error)
	TakeoffDevice(context.Context, *TakeoffRequest) (*OperationReply, error)
	UpdateDevice(context.Context, *UpdateRequest) (*CommonReply, error)
	UploadLogfiles(context.Context, *UploadLogfilesRequest) (*OperationReply, error)
	VideoDevice(context.Context, *VideoRequest) (*OperationReply, error)
	VolumeDevice(context.Context, *VolumeRequest) (*OperationReply, error)
	ZoomDevice(context.Context, *ZoomRequest) (*OperationReply, error)
}

func RegisterDeviceHTTPServer(s *http.Server, srv DeviceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/devices", _Device_AddDevice0_HTTP_Handler(srv))
	r.GET("/api/v1/devices", _Device_ListDevice0_HTTP_Handler(srv))
	r.GET("/api/v1/devices/{id}", _Device_GetDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/deployment", _Device_UpdateDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/property", _Device_PropertyDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/takeoff", _Device_TakeoffDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/launch", _Device_LaunchDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/crash", _Device_CrashDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/return", _Device_ReturnDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/autoback", _Device_AutobackDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/route", _Device_RouteDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/control", _Device_ControlDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/release", _Device_ReleaseDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/aeromode", _Device_AeromodeDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/lens", _Device_LensDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/zoom", _Device_ZoomDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/gimbal", _Device_GimbalDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/clarity", _Device_ClarityDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/free", _Device_FreeDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/point", _Device_PointDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/repoint", _Device_RepointDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/orbit", _Device_OrbitDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/orbeed", _Device_OrbeedDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/lookat", _Device_LookatDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/picture", _Device_PictureDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/video", _Device_VideoDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/drive", _Device_DriveDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/shout", _Device_ShoutDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/speak", _Device_SpeakDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/volume", _Device_VolumeDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/playmode", _Device_PlaymodeDevice0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/algflow", _Device_AlgflowDevice0_HTTP_Handler(srv))
	r.DELETE("/api/v1/devices/{id}", _Device_RemoveDevice0_HTTP_Handler(srv))
	r.GET("/api/v1/devices/{id}/events", _Device_DeviceEvents0_HTTP_Handler(srv))
	r.GET("/api/v1/devices/{id}/datalogs", _Device_DeviceDatalogs0_HTTP_Handler(srv))
	r.GET("/api/v1/devices/{id}/schedules", _Device_DeviceSchedules0_HTTP_Handler(srv))
	r.GET("/api/v1/devices/{id}/operations", _Device_DeviceOperations0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/logfiles", _Device_RemoteLogfiles0_HTTP_Handler(srv))
	r.POST("/api/v1/devices/{id}/logfiles", _Device_UploadLogfiles0_HTTP_Handler(srv))
	r.GET("/api/v1/devices/{id}/logfiles", _Device_ListLogfiles0_HTTP_Handler(srv))
	r.GET("/api/v1/devices/{id}/logfiles/{fileId}", _Device_DownloadLogfile0_HTTP_Handler(srv))
	r.POST("/internal/v1/devices/{did}/callback", _Device_CallbackDevice0_HTTP_Handler(srv))
	r.POST("/internal/v1/devices/{did}/executions", _Device_ExecuteDevice0_HTTP_Handler(srv))
}

func _Device_AddDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/AddDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddDevice(ctx, req.(*AddRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ListDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ListDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDevice(ctx, req.(*ListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListReply)
		return ctx.Result(200, reply)
	}
}

func _Device_GetDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/GetDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDevice(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeviceReply)
		return ctx.Result(200, reply)
	}
}

func _Device_UpdateDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRequest
		if err := ctx.Bind(&in.Deployment); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/UpdateDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDevice(ctx, req.(*UpdateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Device_PropertyDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PropertyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/PropertyDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PropertyDevice(ctx, req.(*PropertyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_TakeoffDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TakeoffRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/TakeoffDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TakeoffDevice(ctx, req.(*TakeoffRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_LaunchDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LaunchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/LaunchDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LaunchDevice(ctx, req.(*LaunchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_CrashDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/CrashDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CrashDevice(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ReturnDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReturnRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ReturnDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReturnDevice(ctx, req.(*ReturnRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_AutobackDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReturnRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/AutobackDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AutobackDevice(ctx, req.(*ReturnRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_RouteDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReturnRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/RouteDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RouteDevice(ctx, req.(*ReturnRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ControlDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ControlRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ControlDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ControlDevice(ctx, req.(*ControlRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ReleaseDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ControlRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ReleaseDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReleaseDevice(ctx, req.(*ControlRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_AeromodeDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AeromodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/AeromodeDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AeromodeDevice(ctx, req.(*AeromodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Device_LensDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LensRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/LensDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LensDevice(ctx, req.(*LensRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ZoomDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ZoomRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ZoomDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ZoomDevice(ctx, req.(*ZoomRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_GimbalDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GimbalRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/GimbalDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GimbalDevice(ctx, req.(*GimbalRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ClarityDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ClarityRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ClarityDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ClarityDevice(ctx, req.(*ClarityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_FreeDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FreeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/FreeDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FreeDevice(ctx, req.(*FreeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_PointDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PointRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/PointDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PointDevice(ctx, req.(*PointRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_RepointDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PointRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/RepointDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RepointDevice(ctx, req.(*PointRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_OrbitDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PointRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/OrbitDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OrbitDevice(ctx, req.(*PointRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_OrbeedDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrbeedRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/OrbeedDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OrbeedDevice(ctx, req.(*OrbeedRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_LookatDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LookatRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/LookatDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LookatDevice(ctx, req.(*LookatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_PictureDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PictureRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/PictureDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PictureDevice(ctx, req.(*PictureRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_VideoDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in VideoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/VideoDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.VideoDevice(ctx, req.(*VideoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_DriveDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DriveRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/DriveDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DriveDevice(ctx, req.(*DriveRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ShoutDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReturnRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ShoutDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ShoutDevice(ctx, req.(*ReturnRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_SpeakDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SpeakRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/SpeakDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SpeakDevice(ctx, req.(*SpeakRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_VolumeDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in VolumeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/VolumeDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.VolumeDevice(ctx, req.(*VolumeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_PlaymodeDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PlaymodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/PlaymodeDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PlaymodeDevice(ctx, req.(*PlaymodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_AlgflowDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AlgflowRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/AlgflowDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AlgflowDevice(ctx, req.(*AlgflowRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Device_RemoveDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/RemoveDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveDevice(ctx, req.(*CommonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Device_DeviceEvents0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EventsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/DeviceEvents")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeviceEvents(ctx, req.(*EventsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EventsReply)
		return ctx.Result(200, reply)
	}
}

func _Device_DeviceDatalogs0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DatalogsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/DeviceDatalogs")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeviceDatalogs(ctx, req.(*DatalogsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DatalogsReply)
		return ctx.Result(200, reply)
	}
}

func _Device_DeviceSchedules0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchedulesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/DeviceSchedules")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeviceSchedules(ctx, req.(*SchedulesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchedulesReply)
		return ctx.Result(200, reply)
	}
}

func _Device_DeviceOperations0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OperationsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/DeviceOperations")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeviceOperations(ctx, req.(*OperationsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationsReply)
		return ctx.Result(200, reply)
	}
}

func _Device_RemoteLogfiles0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoteLogfilesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/RemoteLogfiles")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoteLogfiles(ctx, req.(*RemoteLogfilesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_UploadLogfiles0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadLogfilesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/UploadLogfiles")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadLogfiles(ctx, req.(*UploadLogfilesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ListLogfiles0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLogfilesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ListLogfiles")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLogfiles(ctx, req.(*ListLogfilesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LogfilesReply)
		return ctx.Result(200, reply)
	}
}

func _Device_DownloadLogfile0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DownloadLogfilesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/DownloadLogfile")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DownloadLogfile(ctx, req.(*DownloadLogfilesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DownloadReply)
		return ctx.Result(200, reply)
	}
}

func _Device_CallbackDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/CallbackDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CallbackDevice(ctx, req.(*CallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Device_ExecuteDevice0_HTTP_Handler(srv DeviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.devices.v1.Device/ExecuteDevice")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExecuteDevice(ctx, req.(*CallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type DeviceHTTPClient interface {
	AddDevice(ctx context.Context, req *AddRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	AeromodeDevice(ctx context.Context, req *AeromodeRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	AlgflowDevice(ctx context.Context, req *AlgflowRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	AutobackDevice(ctx context.Context, req *ReturnRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	CallbackDevice(ctx context.Context, req *CallbackRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	ClarityDevice(ctx context.Context, req *ClarityRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	ControlDevice(ctx context.Context, req *ControlRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	CrashDevice(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	DeviceDatalogs(ctx context.Context, req *DatalogsRequest, opts ...http.CallOption) (rsp *DatalogsReply, err error)
	DeviceEvents(ctx context.Context, req *EventsRequest, opts ...http.CallOption) (rsp *EventsReply, err error)
	DeviceOperations(ctx context.Context, req *OperationsRequest, opts ...http.CallOption) (rsp *OperationsReply, err error)
	DeviceSchedules(ctx context.Context, req *SchedulesRequest, opts ...http.CallOption) (rsp *SchedulesReply, err error)
	DownloadLogfile(ctx context.Context, req *DownloadLogfilesRequest, opts ...http.CallOption) (rsp *DownloadReply, err error)
	DriveDevice(ctx context.Context, req *DriveRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	ExecuteDevice(ctx context.Context, req *CallbackRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	FreeDevice(ctx context.Context, req *FreeRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	GetDevice(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *DeviceReply, err error)
	GimbalDevice(ctx context.Context, req *GimbalRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	LaunchDevice(ctx context.Context, req *LaunchRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	LensDevice(ctx context.Context, req *LensRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	ListDevice(ctx context.Context, req *ListRequest, opts ...http.CallOption) (rsp *ListReply, err error)
	ListLogfiles(ctx context.Context, req *ListLogfilesRequest, opts ...http.CallOption) (rsp *LogfilesReply, err error)
	LookatDevice(ctx context.Context, req *LookatRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	OrbeedDevice(ctx context.Context, req *OrbeedRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	OrbitDevice(ctx context.Context, req *PointRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	PictureDevice(ctx context.Context, req *PictureRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	PlaymodeDevice(ctx context.Context, req *PlaymodeRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	PointDevice(ctx context.Context, req *PointRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	PropertyDevice(ctx context.Context, req *PropertyRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	ReleaseDevice(ctx context.Context, req *ControlRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	RemoteLogfiles(ctx context.Context, req *RemoteLogfilesRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	RemoveDevice(ctx context.Context, req *CommonRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	RepointDevice(ctx context.Context, req *PointRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	ReturnDevice(ctx context.Context, req *ReturnRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	RouteDevice(ctx context.Context, req *ReturnRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	ShoutDevice(ctx context.Context, req *ReturnRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	SpeakDevice(ctx context.Context, req *SpeakRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	TakeoffDevice(ctx context.Context, req *TakeoffRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	UpdateDevice(ctx context.Context, req *UpdateRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UploadLogfiles(ctx context.Context, req *UploadLogfilesRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	VideoDevice(ctx context.Context, req *VideoRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	VolumeDevice(ctx context.Context, req *VolumeRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	ZoomDevice(ctx context.Context, req *ZoomRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
}

type DeviceHTTPClientImpl struct {
	cc *http.Client
}

func NewDeviceHTTPClient(client *http.Client) DeviceHTTPClient {
	return &DeviceHTTPClientImpl{client}
}

func (c *DeviceHTTPClientImpl) AddDevice(ctx context.Context, in *AddRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/devices"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/AddDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) AeromodeDevice(ctx context.Context, in *AeromodeRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/devices/{id}/aeromode"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/AeromodeDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) AlgflowDevice(ctx context.Context, in *AlgflowRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/devices/{id}/algflow"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/AlgflowDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) AutobackDevice(ctx context.Context, in *ReturnRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/autoback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/AutobackDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) CallbackDevice(ctx context.Context, in *CallbackRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/internal/v1/devices/{did}/callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/CallbackDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ClarityDevice(ctx context.Context, in *ClarityRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/clarity"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ClarityDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ControlDevice(ctx context.Context, in *ControlRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/control"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ControlDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) CrashDevice(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/crash"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/CrashDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) DeviceDatalogs(ctx context.Context, in *DatalogsRequest, opts ...http.CallOption) (*DatalogsReply, error) {
	var out DatalogsReply
	pattern := "/api/v1/devices/{id}/datalogs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/DeviceDatalogs"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) DeviceEvents(ctx context.Context, in *EventsRequest, opts ...http.CallOption) (*EventsReply, error) {
	var out EventsReply
	pattern := "/api/v1/devices/{id}/events"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/DeviceEvents"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) DeviceOperations(ctx context.Context, in *OperationsRequest, opts ...http.CallOption) (*OperationsReply, error) {
	var out OperationsReply
	pattern := "/api/v1/devices/{id}/operations"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/DeviceOperations"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) DeviceSchedules(ctx context.Context, in *SchedulesRequest, opts ...http.CallOption) (*SchedulesReply, error) {
	var out SchedulesReply
	pattern := "/api/v1/devices/{id}/schedules"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/DeviceSchedules"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) DownloadLogfile(ctx context.Context, in *DownloadLogfilesRequest, opts ...http.CallOption) (*DownloadReply, error) {
	var out DownloadReply
	pattern := "/api/v1/devices/{id}/logfiles/{fileId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/DownloadLogfile"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) DriveDevice(ctx context.Context, in *DriveRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/drive"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/DriveDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ExecuteDevice(ctx context.Context, in *CallbackRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/internal/v1/devices/{did}/executions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ExecuteDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) FreeDevice(ctx context.Context, in *FreeRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/free"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/FreeDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) GetDevice(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*DeviceReply, error) {
	var out DeviceReply
	pattern := "/api/v1/devices/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/GetDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) GimbalDevice(ctx context.Context, in *GimbalRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/gimbal"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/GimbalDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) LaunchDevice(ctx context.Context, in *LaunchRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/launch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/LaunchDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) LensDevice(ctx context.Context, in *LensRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/lens"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/LensDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ListDevice(ctx context.Context, in *ListRequest, opts ...http.CallOption) (*ListReply, error) {
	var out ListReply
	pattern := "/api/v1/devices"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ListDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ListLogfiles(ctx context.Context, in *ListLogfilesRequest, opts ...http.CallOption) (*LogfilesReply, error) {
	var out LogfilesReply
	pattern := "/api/v1/devices/{id}/logfiles"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ListLogfiles"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) LookatDevice(ctx context.Context, in *LookatRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/lookat"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/LookatDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) OrbeedDevice(ctx context.Context, in *OrbeedRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/orbeed"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/OrbeedDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) OrbitDevice(ctx context.Context, in *PointRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/orbit"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/OrbitDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) PictureDevice(ctx context.Context, in *PictureRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/picture"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/PictureDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) PlaymodeDevice(ctx context.Context, in *PlaymodeRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/playmode"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/PlaymodeDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) PointDevice(ctx context.Context, in *PointRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/point"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/PointDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) PropertyDevice(ctx context.Context, in *PropertyRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/property"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/PropertyDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ReleaseDevice(ctx context.Context, in *ControlRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/release"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ReleaseDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) RemoteLogfiles(ctx context.Context, in *RemoteLogfilesRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/logfiles"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/RemoteLogfiles"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) RemoveDevice(ctx context.Context, in *CommonRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/devices/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.devices.v1.Device/RemoveDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) RepointDevice(ctx context.Context, in *PointRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/repoint"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/RepointDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ReturnDevice(ctx context.Context, in *ReturnRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/return"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ReturnDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) RouteDevice(ctx context.Context, in *ReturnRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/route"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/RouteDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ShoutDevice(ctx context.Context, in *ReturnRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/shout"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ShoutDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) SpeakDevice(ctx context.Context, in *SpeakRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/speak"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/SpeakDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) TakeoffDevice(ctx context.Context, in *TakeoffRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/takeoff"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/TakeoffDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) UpdateDevice(ctx context.Context, in *UpdateRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/api/v1/devices/{id}/deployment"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/UpdateDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in.Deployment, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) UploadLogfiles(ctx context.Context, in *UploadLogfilesRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/logfiles"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/UploadLogfiles"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) VideoDevice(ctx context.Context, in *VideoRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/video"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/VideoDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) VolumeDevice(ctx context.Context, in *VolumeRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/volume"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/VolumeDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceHTTPClientImpl) ZoomDevice(ctx context.Context, in *ZoomRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/zoom"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.devices.v1.Device/ZoomDevice"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
