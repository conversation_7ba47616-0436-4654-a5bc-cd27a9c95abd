// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.6.1
// - protoc             v5.29.2
// source: api/devices/v1/device_operation.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDeviceOperationChangeLiveCamera = "/api.devices.v1.DeviceOperation/ChangeLiveCamera"
const OperationDeviceOperationDebugMode = "/api.devices.v1.DeviceOperation/DebugMode"
const OperationDeviceOperationForceOperateCover = "/api.devices.v1.DeviceOperation/ForceOperateCover"
const OperationDeviceOperationOperateBatteryCharge = "/api.devices.v1.DeviceOperation/OperateBatteryCharge"
const OperationDeviceOperationOperateCover = "/api.devices.v1.DeviceOperation/OperateCover"
const OperationDeviceOperationOperateDroneSwitch = "/api.devices.v1.DeviceOperation/OperateDroneSwitch"
const OperationDeviceOperationOperatePutter = "/api.devices.v1.DeviceOperation/OperatePutter"
const OperationDeviceOperationOperateReboot = "/api.devices.v1.DeviceOperation/OperateReboot"

type DeviceOperationHTTPServer interface {
	ChangeLiveCamera(context.Context, *DeviceLiveCameraRequest) (*OperationReply, error)
	DebugMode(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	ForceOperateCover(context.Context, *DeviceForceCoverRequest) (*OperationReply, error)
	OperateBatteryCharge(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	OperateCover(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	OperateDroneSwitch(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	OperatePutter(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	OperateReboot(context.Context, *DeviceOperationRequest) (*OperationReply, error)
}

func RegisterDeviceOperationHTTPServer(s *http.Server, srv DeviceOperationHTTPServer) {
	r := s.Route("/")
	r.PUT("/api/v1/devices/{id}/operations/debugMode", _DeviceOperation_DebugMode0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/operations/cover", _DeviceOperation_OperateCover0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/operations/reboot", _DeviceOperation_OperateReboot0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/operations/droneSwitch", _DeviceOperation_OperateDroneSwitch0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/operations/putter", _DeviceOperation_OperatePutter0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/operations/forcedCover", _DeviceOperation_ForceOperateCover0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/operations/liveCamera", _DeviceOperation_ChangeLiveCamera0_HTTP_Handler(srv))
	r.PUT("/api/v1/devices/{id}/operations/chargeSwitch", _DeviceOperation_OperateBatteryCharge0_HTTP_Handler(srv))
}

func _DeviceOperation_DebugMode0_HTTP_Handler(srv DeviceOperationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeviceOperationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDeviceOperationDebugMode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DebugMode(ctx, req.(*DeviceOperationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _DeviceOperation_OperateCover0_HTTP_Handler(srv DeviceOperationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeviceOperationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDeviceOperationOperateCover)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OperateCover(ctx, req.(*DeviceOperationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _DeviceOperation_OperateReboot0_HTTP_Handler(srv DeviceOperationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeviceOperationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDeviceOperationOperateReboot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OperateReboot(ctx, req.(*DeviceOperationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _DeviceOperation_OperateDroneSwitch0_HTTP_Handler(srv DeviceOperationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeviceOperationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDeviceOperationOperateDroneSwitch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OperateDroneSwitch(ctx, req.(*DeviceOperationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _DeviceOperation_OperatePutter0_HTTP_Handler(srv DeviceOperationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeviceOperationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDeviceOperationOperatePutter)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OperatePutter(ctx, req.(*DeviceOperationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _DeviceOperation_ForceOperateCover0_HTTP_Handler(srv DeviceOperationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeviceForceCoverRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDeviceOperationForceOperateCover)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ForceOperateCover(ctx, req.(*DeviceForceCoverRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _DeviceOperation_ChangeLiveCamera0_HTTP_Handler(srv DeviceOperationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeviceLiveCameraRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDeviceOperationChangeLiveCamera)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeLiveCamera(ctx, req.(*DeviceLiveCameraRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

func _DeviceOperation_OperateBatteryCharge0_HTTP_Handler(srv DeviceOperationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeviceOperationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDeviceOperationOperateBatteryCharge)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OperateBatteryCharge(ctx, req.(*DeviceOperationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OperationReply)
		return ctx.Result(200, reply)
	}
}

type DeviceOperationHTTPClient interface {
	ChangeLiveCamera(ctx context.Context, req *DeviceLiveCameraRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	DebugMode(ctx context.Context, req *DeviceOperationRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	ForceOperateCover(ctx context.Context, req *DeviceForceCoverRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	OperateBatteryCharge(ctx context.Context, req *DeviceOperationRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	OperateCover(ctx context.Context, req *DeviceOperationRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	OperateDroneSwitch(ctx context.Context, req *DeviceOperationRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	OperatePutter(ctx context.Context, req *DeviceOperationRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
	OperateReboot(ctx context.Context, req *DeviceOperationRequest, opts ...http.CallOption) (rsp *OperationReply, err error)
}

type DeviceOperationHTTPClientImpl struct {
	cc *http.Client
}

func NewDeviceOperationHTTPClient(client *http.Client) DeviceOperationHTTPClient {
	return &DeviceOperationHTTPClientImpl{client}
}

func (c *DeviceOperationHTTPClientImpl) ChangeLiveCamera(ctx context.Context, in *DeviceLiveCameraRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/operations/liveCamera"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDeviceOperationChangeLiveCamera))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceOperationHTTPClientImpl) DebugMode(ctx context.Context, in *DeviceOperationRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/operations/debugMode"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDeviceOperationDebugMode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceOperationHTTPClientImpl) ForceOperateCover(ctx context.Context, in *DeviceForceCoverRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/operations/forcedCover"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDeviceOperationForceOperateCover))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceOperationHTTPClientImpl) OperateBatteryCharge(ctx context.Context, in *DeviceOperationRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/operations/chargeSwitch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDeviceOperationOperateBatteryCharge))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceOperationHTTPClientImpl) OperateCover(ctx context.Context, in *DeviceOperationRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/operations/cover"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDeviceOperationOperateCover))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceOperationHTTPClientImpl) OperateDroneSwitch(ctx context.Context, in *DeviceOperationRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/operations/droneSwitch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDeviceOperationOperateDroneSwitch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceOperationHTTPClientImpl) OperatePutter(ctx context.Context, in *DeviceOperationRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/operations/putter"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDeviceOperationOperatePutter))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DeviceOperationHTTPClientImpl) OperateReboot(ctx context.Context, in *DeviceOperationRequest, opts ...http.CallOption) (*OperationReply, error) {
	var out OperationReply
	pattern := "/api/v1/devices/{id}/operations/reboot"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDeviceOperationOperateReboot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
