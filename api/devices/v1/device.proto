syntax = "proto3";

package api.devices.v1;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "validator/validator.proto";
import "list/list.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/devices/v1;v1";
option java_multiple_files = true;
option java_package = "api.devices.v1";

service Device {
	rpc AddDevice (AddRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/api/v1/devices"
			body: "*"
    };
  };
	rpc ListDevice (ListRequest) returns (ListReply) {
    option (google.api.http) = {
      get: "/api/v1/devices"
    };
  };
  rpc GetDevice (CommonRequest) returns (DeviceReply) {
    option (google.api.http) = {
      get: "/api/v1/devices/{id}"
    };
  };
	rpc UpdateDevice (UpdateRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/deployment"
      body: "deployment"
    };
  };
  rpc PropertyDevice (PropertyRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/property"
      body: "*"
    };
  };
  rpc TakeoffDevice (TakeoffRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/takeoff"
      body: "*"
    };
  };
  rpc LaunchDevice (LaunchRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/launch"
      body: "*"
    };
  };
  rpc CrashDevice (CommonRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/crash"
      body: "*"
    };
  };
  rpc ReturnDevice (ReturnRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/return"
      body: "*"
    };
  };
  rpc AutobackDevice (ReturnRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/autoback"
      body: "*"
    };
  };
  rpc RouteDevice (ReturnRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/route"
      body: "*"
    };
  };
  rpc ControlDevice (ControlRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/control"
      body: "*"
    };
  };
  rpc ReleaseDevice (ControlRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/release"
      body: "*"
    };
  };
  rpc AeromodeDevice (AeromodeRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/aeromode"
      body: "*"
    };
  };
  rpc LensDevice (LensRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/lens"
      body: "*"
    };
  };
  rpc ZoomDevice (ZoomRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/zoom"
      body: "*"
    };
  };
  rpc GimbalDevice (GimbalRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/gimbal"
      body: "*"
    };
  };
  rpc ClarityDevice (ClarityRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/clarity"
      body: "*"
    };
  };
  rpc FreeDevice (FreeRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/free"
      body: "*"
    };
  };
  rpc PointDevice (PointRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/point"
      body: "*"
    };
  };
  rpc RepointDevice (PointRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/repoint"
      body: "*"
    };
  };
  rpc OrbitDevice (PointRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/orbit"
      body: "*"
    };
  };
  rpc OrbeedDevice (OrbeedRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/orbeed"
      body: "*"
    };
  };
  rpc LookatDevice (LookatRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/lookat"
      body: "*"
    };
  };
  rpc PictureDevice (PictureRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/picture"
      body: "*"
    };
  };
  rpc VideoDevice (VideoRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/video"
      body: "*"
    };
  };
  rpc DriveDevice (DriveRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/drive"
      body: "*"
    };
  };
  rpc ShoutDevice (ReturnRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/shout"
      body: "*"
    };
  };
  rpc SpeakDevice (SpeakRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/speak"
      body: "*"
    };
  };
  rpc VolumeDevice (VolumeRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/volume"
      body: "*"
    };
  };
  rpc PlaymodeDevice (PlaymodeRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/playmode"
      body: "*"
    };
  };
  rpc AlgflowDevice (AlgflowRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/algflow"
      body: "*"
    };
  };
  rpc RemoveDevice (CommonRequest) returns (CommonReply) {
    option (google.api.http) = {
      delete: "/api/v1/devices/{id}"
    };
  };
  rpc DeviceEvents (EventsRequest) returns (EventsReply) {
    option (google.api.http) = {
      get: "/api/v1/devices/{id}/events"
    };
  };
  rpc DeviceDatalogs (DatalogsRequest) returns (DatalogsReply) {
    option (google.api.http) = {
      get: "/api/v1/devices/{id}/datalogs"
    };
  };
  rpc DeviceSchedules (SchedulesRequest) returns (SchedulesReply) {
    option (google.api.http) = {
      get: "/api/v1/devices/{id}/schedules"
    };
  };
  rpc DeviceOperations (OperationsRequest) returns (OperationsReply) {
    option (google.api.http) = {
      get: "/api/v1/devices/{id}/operations"
    };
  };
  rpc RemoteLogfiles (RemoteLogfilesRequest) returns (OperationReply) {
    option (google.api.http) = {
      put: "/api/v1/devices/{id}/logfiles"
      body: "*"
    };
  };
  rpc UploadLogfiles (UploadLogfilesRequest) returns (OperationReply) {
    option (google.api.http) = {
      post: "/api/v1/devices/{id}/logfiles"
      body: "*"
    };
  };
  rpc ListLogfiles (ListLogfilesRequest) returns (LogfilesReply) {
    option (google.api.http) = {
      get: "/api/v1/devices/{id}/logfiles"
    };
  };
  rpc DownloadLogfile (DownloadLogfilesRequest) returns (DownloadReply) {
    option (google.api.http) = {
      get: "/api/v1/devices/{id}/logfiles/{fileId}"
    };
  };
  rpc CallbackDevice (CallbackRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/internal/v1/devices/{did}/callback"
      body: "*"
    };
  };
  rpc ExecuteDevice (CallbackRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/internal/v1/devices/{did}/executions"
      body: "*"
    };
  };
}

message Avatar {
  int64 id = 1;
  string mobile = 2;
  string nickname = 3;
  string avatar = 4;
  int32 roleType = 5;
}

message CommonRequest {
  int64 id = 1 [(validator.rules) = "required"];
}

message CommonReply {
	int32 code = 1;
	string message = 2;
	commonData data = 3;

  message commonData {
    bool status = 1;
  }
}

message Property  {
  string key = 1;
  string name = 2;
  int32 series = 3;
  google.protobuf.Value value = 4;
}

message Deployment {
	string name = 1;
	image images = 2;
  float altitude = 3;
	string location = 4;
	repeated string tags = 5;
	optional bool status = 6;
	optional double time = 7;
	repeated double lnglat = 8;
	repeated contact contacts = 9;
  repeated int64 relatedChIds = 10;
  repeated relatedCh relatedChs = 11;

	message image {
		string deviceImg = 1;
		string envImg = 2;
		optional string shopImg = 3;
	}
	message contact {
		string name = 1;
		string contact = 2;
	}
  message relatedCh {
    int64 id = 1;
    string name = 2;
  }
}

message AddRequest {
	string sn = 1;
	string type = 2;
	string model = 3;
	Deployment deployment = 4;
}

message UpdateRequest {
  int64 id = 1;
  Deployment deployment = 2;
}

message PropertyRequest {
  int64 id = 1;
  string name = 2;
	int32 value = 3;
}

message TakeoffRequest {
  int64 id = 1;
	int64 airlineId = 2;
	string algorithm = 3;
  optional simulator simulateMission = 4;

  message simulator {
    bool isEnable = 1;
    repeated double lnglat = 2;
  }
}

message ReturnRequest {
  int64 id = 1;
	bool enable = 2;
}

message LaunchRequest {
  int64 id = 1;
  float speed = 2;
  float height = 3;
  string algorithm = 4;
  int32 rCLostAction = 5;
  float cmderHeight = 6;
  float securityHeight = 7;
  float returnAltitude = 8;
	repeated double lnglat = 9;
  optional simulator simulateMission = 10;

  message simulator {
    bool isEnable = 1;
    repeated double lnglat = 2;
  }
}

message ControlRequest {
  int64 id = 1;
	string operate = 2;
  other others = 3;

  message other {
    optional string index = 1;
  }
}

message AeromodeRequest {
  int64 id = 1;
	bool enable = 2;
  int32 aeroMode = 3;
}

message LensRequest {
  int64 id = 1;
	string lens = 2;
  string key = 3;
  int64 liveId = 4;
}

message ZoomRequest {
  int64 id = 1;
  string key = 2;
  string lens = 3;
  int64 liveId = 4;
	string index = 5;
  double factor = 6;
}

message GimbalRequest {
  int64 id = 1;
  int32 mode = 2;
  string index = 3;
	int64 liveId = 4;
}

message ClarityRequest {
  int64 id = 1;
	int32 clarity = 2;
  string key = 3;
  int64 liveId = 4;
}

message FreeRequest {
  int64 id = 1;
  bool enable = 2;
  int32 frequency = 3;
}

message PointRequest {
  int64 id = 1;
  bool enable = 2;
  float speed = 3;
  float radius = 4;
	float height = 5;
  repeated double lnglat = 6;
}

message OrbeedRequest {
  int64 id = 1;
  float speed = 2;
}

message LookatRequest {
  int64 id = 1;
  bool locked = 2;
	float height = 3;
  string index = 4;
  repeated double lnglat = 5;
}

message PictureRequest {
  int64 id = 1;
  string index = 2;
	int64 liveId = 3;
}

message VideoRequest {
  int64 id = 1;
  string index = 2;
	int64 liveId = 3;
  string action = 4;
}

message DriveRequest {
  int64 id = 1;
  optional float xSpeed = 2;
  optional float ySpeed = 3;
  optional float hSpeed = 4;
  optional float wSpeed = 5;
}

message SpeakRequest {
  int64 id = 1;
  string index = 2;
	int32 mode = 3;
  string name = 4;
  string content = 5;
  string action = 6;
}

message VolumeRequest {
  int64 id = 1;
  string index = 2;
	int32 volume = 3;
}

message PlaymodeRequest {
  int64 id = 1;
  string index = 2;
	int32 playmode = 3;
}

message AlgflowRequest {
  int64 id = 1;
  bool algflowStatus = 2;
}

message OperationReply {
	int32 code = 1;
	string message = 2;
	Operation data = 3;
}

message DeviceItem {
  int64 id = 1;
  double createdTime = 2;
  double updatedTime = 3;
  string sn = 4;
  string type = 5;
  string model = 6;
  string category = 7;
  string status = 8;
  string source = 9;
  string sourceSn = 10;
  int64 tenantId = 11;
  int64 merchantId = 12;
  int32 networkType = 13;
  bool cabinStatus = 14;
  int32 lockStatus = 15;
  bool networkStatus = 16;
  string signalQuality = 17;
  Deployment deployment = 18;
  string firmwareVersion = 19;
  optional double uppedTime = 20;
  google.protobuf.Struct propData = 21;
  google.protobuf.Struct extraData = 22;
  repeated google.protobuf.Struct subdevices = 23;
}

message DeviceReply {
  int32 code = 1;
  string message = 2;
  DeviceItem data = 3;
}

message ListRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  optional string search = 3 [(list.filter_options)={filter_name: "sn,deployment_name,deployment_tags", operator:"search" }];
  optional string type = 4;
  optional string category = 5;
  optional bool networkStatus = 6;
}

message ListReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated DeviceItem list = 4;
  }
}

message EventsRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  int64 id = 3 [(validator.rules) = "required"];
  int64 startTime = 4;
  int64 endTime = 5;
  string types = 6;
  string levels = 7;
}

message EventsReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated event list = 4;

		message event {
      int64 id = 1;
      string sn = 2;
      string type = 3;
      string code = 4;
      int32 level = 5;
      string description = 6;
      double createdTime = 7;
      double updatedTime = 8;
      double occurredTime = 9;
    }
  }
}

message DatalogsRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  int64 id = 3 [(validator.rules) = "required"];
  int64 startTime = 4;
  int64 endTime = 5;
}

message DatalogsReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated DeviceDatalog list = 4;
  }
}

message DeviceDatalog {
  string id = 1;
  string sn = 2;
  string realTs = 3;
  double timestamp = 4;
  repeated Property data = 5;
}

message SchedulesRequest {
  option (list.page) = false;
  int64 id = 1 [(validator.rules) = "required"];
  int32 type = 2;
  int64 startTime = 3;
  int64 endTime = 4;
  optional int64 missionId = 5;
}

message SchedulesReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 total = 1;
    repeated timer list = 2;
  }

  message timer {
    int32 runday = 1;
    repeated int32 moments = 2;
  }
}

message OperationsRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  int64 id = 3 [(validator.rules) = "required"];
  int64 startTime = 4;
  int64 endTime = 5;
  optional string type = 6;
}

message Operation {
  int64 id = 1;
  string type = 2;
  string status = 3;
  int64 sourceId = 4;
  int64 tenantId = 5;
  int64 merchantId = 6;
  int64 avatarId = 7;
  Avatar avatar = 8;
  string from = 9;
  google.protobuf.Struct content = 10;
  double createdTime = 13;
  double updatedTime = 14;
}

message OperationsReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated Operation list = 4;
  }
}

message RemoteLogfilesRequest {
  int64 id = 1;
  repeated string modules = 2;
}

message UploadLogfilesRequest {
  int64 id = 1;
  repeated file files = 2;
  message file {
    string module = 1;
    int32 bootIndex = 2;
    int64 size = 3;
    int64 startTime = 4;
    int64 endTime = 5;
  }
}

message ListLogfilesRequest {
  option (list.page) = true;
	int32 page = 1 [(validator.rules) = "required,min=1"];
	int32 size = 2 [(validator.rules) = "required,max=5000"];
  int64 id = 3 [(validator.rules) = "required"];
  int64 startTime = 4;
  int64 endTime = 5;
  string modules = 6;
}

message Logfile {
  int64 id = 1;
  string module = 2;
  int32 bootIndex = 3;
  int32 status = 4;
  int64 deviceId = 5;
  int64 size = 6;
  int64 tenantId = 7;
  int64 merchantId = 8;
  double endTime = 9;
  double startTime = 10;
  double createdTime = 11;
  double updatedTime = 12;
}

message LogfilesReply {
  int32 code = 1;
  string message = 2;
  listData data = 3;

  message listData {
    int32 page = 1;
    int32 size = 2;
    int32 total = 3;
    repeated Logfile list = 4;
  }
}

message DownloadLogfilesRequest {
  int64 id = 1 [(validator.rules) = "required"];
  int64 fileId = 2;
}

message DownloadReply {
	int32 code = 1;
	string message = 2;
	downdata data = 3;

  message downdata {
    string url = 1;
    string fingerprint = 2;
  }
}

message CallbackRequest {
  int64 did = 1 [(validator.rules) = "required"];
  string id = 2;
  string clientId = 3;
  int64 executeTime = 4;
  payload body = 5;

  message payload {
    string type = 1;
    string action = 2;
    string sourceId = 3;
  }
}
