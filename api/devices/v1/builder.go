package v1

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/stoewer/go-strcase"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"google.golang.org/protobuf/types/known/structpb"
)

func BuildDeviceItem(bd *biz.Device) *DeviceItem {
	contacts := lo.Map(bd.Deployment.Contacts, func(c biz.Contact, _ int) *DeploymentContact {
		return &DeploymentContact{
			Name:    c.Name,
			Contact: c.Contact,
		}
	})
	di := &DeviceItem{
		Id:              bd.Id,
		Sn:              bd.Sn,
		Type:            bd.Type,
		Model:           bd.Model.String(),
		Category:        bd.Category.String(),
		Status:          bd.Status.String(),
		Source:          bd.Source.String(),
		SourceSn:        bd.SourceSn,
		TenantId:        bd.TenantId,
		MerchantId:      bd.MerchantId,
		CreatedTime:     float64(bd.CreatedTime.UnixMilli()),
		UpdatedTime:     float64(bd.UpdatedTime.UnixMilli()),
		LockStatus:      int32(bd.LockStatus),
		CabinStatus:     bd.CabinStatus,
		NetworkType:     bd.NetworkType,
		NetworkStatus:   bd.NetworkStatus,
		SignalQuality:   bd.SignalQuality,
		FirmwareVersion: bd.FirmwareVersion,
		Deployment: &Deployment{
			Name: bd.Deployment.Name,
			Tags: bd.Deployment.Tags,
			Images: &DeploymentImage{
				DeviceImg: bd.Deployment.Images.DeviceImg,
				EnvImg:    bd.Deployment.Images.EnvImg,
				ShopImg:   bd.Deployment.Images.ShopImg,
			},
			Contacts:     contacts,
			Lnglat:       bd.Deployment.Lnglat,
			Altitude:     bd.Deployment.Altitude,
			Location:     bd.Deployment.Location,
			Status:       &bd.Deployment.Status,
			Time:         lo.ToPtr(float64(bd.Deployment.Time.UnixMilli())),
			RelatedChIds: bd.Deployment.RelatedChIds,
		},
	}
	di.ExtraData, _ = structpb.NewStruct(bd.ExtraData)
	di.PropData, _ = structpb.NewStruct(bd.PropData.Convert())
	if bd.UppedTime != nil {
		di.UppedTime = lo.ToPtr(float64(bd.UppedTime.UnixMilli()))
	}
	di.Subdevices = lo.Map(bd.Subdevices, func(device *biz.DockSubdevice, _ int) *structpb.Struct {
		data, _ := structpb.NewStruct(map[string]interface{}{
			"sn":     device.Sn,
			"type":   device.Type,
			"index":  device.Index,
			"extra":  device.Extra,
			"domain": int(device.Domain),
			"status": device.Status,
		})
		return data
	})
	return di
}

var exportTimeLocaltion, _ = time.LoadLocation("Asia/Shanghai")
var ExportLogHeaders = []string{"上行时间", "机场状态", "空调状态", "舱内温度(℃)", "舱内湿度", "存储容量/总容量(G)", "天气", "风速", "环境温度(℃)", "环境湿度", "GPS搜星数量", "RTK搜星数量", "4G信号质量", "SDR信号质量", "机场图传链路模式", "飞机状态", "工作电压(V)", "工作电流(A)", "市电电压(V)"}
var dockExportLogKeys = lo.Map(
	[]string{"DockState", "AirCondState", "Tmperature", "Humidity", "Storage", "Rainfall", "WindSpeed", "EnvTemperature", "EnvHumidity", "GPSNumber", "RTKNumber", "Quality4G", "QualitySDR", "WirelessMode", "CabinStatus", "WorkingVoltage", "WorkingCurrent", "SupplyVoltage"},
	func(it string, _ int) string { return strcase.LowerCamelCase(it) },
)

var ControllerExportLogHeaders = []string{
	"上行时间", "飞机状态", "风速", "风向", "图传质量", "GPS搜星数", "RTK搜星数",
	"已使用容量/总容量", "是否接近限飞区", "是否接近设定的限制距离", "夜航灯状态",
	"水平避障状态", "上视避障状态", "下视避障状态", "遥控剩余电量", "电池1剩余电量", "电池2剩余电量",
}

var controllerExportLogKeys = lo.Map([]string{
	"DroneState", "WindSpeed", "WindDirection", "QualityIT", "GPSNumber", "RTKNumber",
	"Storage", "IsNearAreaLimit", "IsNearIDistanceLimit", "NightLightsState",
	"HorizonObstacleAvoidance", "UpsideObstacleAvoidance", "DownsideObstacleAvoidance",
	"ControllerBattery", "DroneBattery1", "DroneBattery2",
}, func(it string, _ int) string { return strcase.LowerCamelCase(it) })

func BuildControllerDeviceDataLogRow(p any) []any {
	dl := BuildControllerDeviceDatalog(p)
	if dl == nil {
		return nil
	}
	row := make([]interface{}, len(ControllerExportLogHeaders))
	row[0] = time.UnixMilli(int64(dl.Timestamp)).In(exportTimeLocaltion).Format("2006-01-02 15:04:05")
	for i := 0; i < len(controllerExportLogKeys); i++ {
		row[i+1] = "-"
		for _, it := range dl.Data {
			if it.Key == controllerExportLogKeys[i] {
				row[i+1] = it.Value.AsInterface()
				break
			}
		}
	}
	return row
}

func BuildDockDeviceDataLogRow(p *biz.DockProperties) []any {
	dl := BuildDockDeviceDatalog(p)
	if dl == nil {
		return nil
	}
	row := make([]interface{}, len(ExportLogHeaders))
	row[0] = p.RxTime.In(exportTimeLocaltion).Format("2006-01-02 15:04:05")
	for i := 0; i < len(dockExportLogKeys); i++ {
		row[i+1] = "-"
		for _, it := range dl.Data {
			if it.Key == dockExportLogKeys[i] {
				row[i+1] = it.Value.AsInterface()
				break
			}
		}
	}
	return row
}

func BuildDockDeviceDatalog(p *biz.DockProperties) *DeviceDatalog {
	if p == nil {
		return nil
	}
	l := &DeviceDatalog{
		Sn:        p.Sn,
		Id:        p.Id,
		Timestamp: float64(p.RxTime.UnixMilli()),
		RealTs:    p.Timestamp.Format("2006-01-02T15:04:05.999Z07:00"),
		Data:      make([]*Property, 0, 20),
	}
	if s := p.State; s != nil {
		l.Data = append(l.Data,
			newDeviceDataItem("DockState", "机场状态", s.Mode),
			newDeviceDataItem("AirCondState", "空调状态", s.AirConditionerState),
			newDeviceDataItem("Tmperature", "舱内温度(℃)", s.Temperature),
			newDeviceDataItem("Humidity", "舱内湿度", s.Humidity),
			newDeviceDataItem("Storage", "存储容量/总容量(G)", NewStorage(s.StorageUsed, s.StorageTotal)),
		)
	}
	if s := p.EnvironmentState; s != nil {
		l.Data = append(l.Data,
			newDeviceDataItem("Rainfall", "天气", s.Rainfall),
			newDeviceDataItem("WindSpeed", "风速", NewWindSpeed(s.WindSpeed)),
			//newDeviceDataItem("WindSpeed", "风速", s.WindSpeed),
			newDeviceDataItem("EnvTemperature", "环境温度(℃)", s.Temperature),
			newDeviceDataItem("EnvHumidity", "环境湿度", s.Humidity),
		)
	}
	if s := p.PositionState; s != nil {
		l.Data = append(l.Data, newDeviceDataItem("GPSNumber", "GPS搜星数量", s.GpsNumber))
		l.Data = append(l.Data, newDeviceDataItem("RTKNumber", "RTK搜星数量", s.RtkNumber))
	}
	if s := p.WirelessLinkState; s != nil {
		l.Data = append(l.Data, newDeviceDataItem("Quality4G", "4G信号质量", s.Quality4G))
		l.Data = append(l.Data, newDeviceDataItem("QualitySDR", "SDR信号质量", s.QualitySDR))
		l.Data = append(l.Data, newDeviceDataItem("WirelessMode", "机场图传链路模式", s.Mode))
	}
	if s := p.FlightTaskState; s != nil {
		l.Data = append(l.Data, newDeviceDataItem("CabinStatus", "飞机状态", s.DroneInDock))
	}
	if s := p.ElecPowerState; s != nil {
		l.Data = append(l.Data, newDeviceDataItem("WorkingVoltage", "工作电压(V)", s.WorkingVoltage/1000))
		l.Data = append(l.Data, newDeviceDataItem("WorkingCurrent", "工作电流(A)", s.WorkingCurrent/1000))
		l.Data = append(l.Data, newDeviceDataItem("SupplyVoltage", "市电电压(V)", s.SupplyVoltage))
	}

	return l
}

func BuildControllerDeviceDatalog(p any) *DeviceDatalog {
	if p == nil {
		return nil
	}
	if v, ok := p.(*biz.RemoteControllerProperties); ok {
		return BuildControllerDeviceDatalogWithCP(v)
	}
	if v, ok := p.(*biz.DroneProperties); ok {
		return BuildControllerDeviceDatalogWithDP(v)
	}
	return nil
}

func BuildControllerDeviceDatalogWithCP(p *biz.RemoteControllerProperties) *DeviceDatalog {
	l := &DeviceDatalog{
		Sn:        p.Sn,
		Id:        p.Id,
		Timestamp: float64(p.RxTime.UnixMilli()),
		RealTs:    p.Timestamp.Format("2006-01-02T15:04:05.999Z07:00"),
		Data:      make([]*Property, 0, 2),
	}
	if p.State != nil {
		l.Data = append(l.Data, newDeviceDataItem("ControllerBattery", "遥控剩余电量(%)", p.State.CapacityPercent))
	}
	if p.WirelessLinkState != nil {
		l.Data = append(l.Data, newDeviceDataItem("QualityIT", "图传质量", lo.Max([]int32{
			p.WirelessLinkState.Quality4G, p.WirelessLinkState.QualitySDR,
		})))
	}
	return l
}

func newStateDataItem(v int32) string {
	if v == 1 {
		return "打开"
	}
	return "关闭"
}

func newOnOffDataItem(v int32) string {
	if v == 1 {
		return "是"
	}
	return "否"
}
func BuildControllerDeviceDatalogWithDP(p *biz.DroneProperties) *DeviceDatalog {
	l := &DeviceDatalog{
		Sn:        p.Sn,
		Id:        p.Id,
		Timestamp: float64(p.RxTime.UnixMilli()),
		RealTs:    p.Timestamp.Format("2006-01-02T15:04:05.999Z07:00"),
		Data:      make([]*Property, 0, 15),
	}
	if p.Mode != -1 {
		l.Data = append(l.Data, newDeviceDataItem("DroneState", "飞机状态", p.Mode))
	}
	if s := p.FlightState; s != nil {
		l.Data = append(l.Data, newDeviceDataItem("WindSpeed", "风速", NewWindSpeed(s.WindSpeed)))
		l.Data = append(l.Data, newDeviceDataItem("WindDirection", "风向", s.WindDirection))
		l.Data = append(l.Data, newDeviceDataItem("IsNearAreaLimit", "是否接近限飞区", newOnOffDataItem(s.IsNearAreaLimit)))
		l.Data = append(l.Data, newDeviceDataItem("IsNearIDistanceLimit", "是否接近设定限制距离", newOnOffDataItem(s.IsNearDistanceLimit)))
		l.Data = append(l.Data, newDeviceDataItem("NightLightsState", "夜航灯状态", newStateDataItem(s.NightLightsState)))
		l.Data = append(l.Data, newDeviceDataItem("HorizonObstacleAvoidance", "水平避障状态", newStateDataItem(s.HorizonObstacleAvoidance)))
		l.Data = append(l.Data, newDeviceDataItem("UpsideObstacleAvoidance", "上视避障状态", newStateDataItem(s.UpsideObstacleAvoidance)))
		l.Data = append(l.Data, newDeviceDataItem("DownsideObstacleAvoidance", "下视避障状态", newStateDataItem(s.DownsideObstacleAvoidance)))
	}
	if s := p.PositionState; s != nil {
		l.Data = append(l.Data, newDeviceDataItem("GPSNumber", "GPS搜星数量", s.GpsNumber))
		l.Data = append(l.Data, newDeviceDataItem("RTKNumber", "RTK搜星数量", s.RtkNumber))
	}
	if s := p.Storage; s != nil {
		l.Data = append(l.Data, newDeviceDataItem("Storage", "存储容量/总容量(G)", NewStorage(s.Used, s.Total)))
	}
	if s := p.Battery; s != nil {
		bn := len(s.Batteries)
		for i := 0; i < bn; i++ {
			bIndex := i + 1
			l.Data = append(l.Data, newDeviceDataItem(
				"DroneBattery"+strconv.Itoa(bIndex),
				fmt.Sprintf("电池%d剩余电量", bIndex),
				s.Batteries[i].CapacityPercent,
			))
		}
	}
	return l
}

func newDeviceDataItem(key, name string, value any) *Property {
	var v *structpb.Value
	// var s int32
	if pc, ok := biz.DevicePropertyMap[key]; ok && pc.Dictionary != nil {
		name = pc.Name
		// s = pc.Series
		vk := conv.Vtoa(value)
		if dv, ok := pc.Dictionary[vk]; ok {
			v = structpb.NewStringValue(dv)
		} else {
			v, _ = structpb.NewValue(value)
		}
	} else {
		v, _ = structpb.NewValue(value)
	}
	item := &Property{
		Key:  strcase.LowerCamelCase(key),
		Name: name,
		// Series: s,
		Value: v,
	}

	return item
}

func NewWindSpeed(windSpeed float32) string {
	return fmt.Sprintf("%.2fm/s", windSpeed)
	// var windSpeedScopes = []float32{0.3, 1.6, 3.4, 5.5, 8.0, 10.8, 13.9, 17.2, 20.8, 24.5, 28.5}
	// for i, v := range windSpeedScopes {
	// 	if windSpeed < v {
	// 		return fmt.Sprintf("%d级", i)
	// 	}
	// }
	// return "13级"
}

// NewStorage 参数单位KB, 返回格式10/30 单位GB
func NewStorage(used, total int64) string {
	return fmt.Sprintf("%d/%d", used>>20, total>>20)
	// usedInGig := float64(used) / 1048576
	// totalInGig := float64(total) / 1048576
	// return fmt.Sprintf("%.2f/%.2f", usedInGig, totalInGig)
}

func BuildDeviceOperation(bo *biz.Operation) *Operation {
	o := &Operation{}
	copier.CopyWithOption(o, bo, copier.Option{
		IgnoreEmpty: true,
		DeepCopy:    true,
		Converters:  []copier.TypeConverter{biz.TimeCopierConverter},
	})
	o.Status = bo.Status.Simplify()
	// 任务巡航操作人默认为系统
	if bo.Type == biz.TypeCruise {
		o.AvatarId = 0
		o.Avatar = nil
	}
	var toContent biz.AnyMap
	fromContent, _ := json.Marshal(bo.Content)
	json.Unmarshal(fromContent, &toContent)
	if content, err := structpb.NewStruct(toContent); err == nil {
		o.Content = content
	} else {
		o.Content = &structpb.Struct{}
	}
	return o
}

func BuildDeviceEvent(a *biz.Event) *EventsReplyListDataEvent {
	if a == nil {
		return nil
	}
	return &EventsReplyListDataEvent{
		Id:           a.Id,
		Sn:           a.Sn,
		Code:         a.Code,
		Level:        a.Level,
		Type:         a.Trans(),
		Description:  a.Describe(),
		CreatedTime:  float64(a.CreatedTime.UnixMilli()),
		UpdatedTime:  float64(a.UpdatedTime.UnixMilli()),
		OccurredTime: float64(a.OccurredTime.UnixMilli()),
	}
}
