{"swagger": "2.0", "info": {"title": "api/devices/v1/device.proto", "version": "version not set"}, "tags": [{"name": "<PERSON><PERSON>"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/devices": {"get": {"operationId": "Device_ListDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1ListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "search", "in": "query", "required": false, "type": "string"}, {"name": "type", "in": "query", "required": false, "type": "string"}, {"name": "model", "in": "query", "required": false, "type": "string"}, {"name": "networkStatus", "in": "query", "required": false, "type": "boolean"}], "tags": ["<PERSON><PERSON>"]}, "post": {"operationId": "Device_AddDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/apidevicesv1AddRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}": {"get": {"operationId": "Device_GetDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1DeviceReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["<PERSON><PERSON>"]}, "delete": {"operationId": "Device_RemoveDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/aeromode": {"put": {"operationId": "Device_AeromodeDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enable": {"type": "boolean"}, "aeroMode": {"type": "integer", "format": "int32"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/algflow": {"put": {"operationId": "Device_AlgflowDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"algflowStatus": {"type": "boolean"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/clarity": {"put": {"operationId": "Device_ClarityDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"clarity": {"type": "integer", "format": "int32"}, "key": {"type": "string"}, "liveId": {"type": "string", "format": "int64"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/control": {"put": {"operationId": "Device_ControlDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"operate": {"type": "string"}, "others": {"$ref": "#/definitions/ControlRequestother"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/crash": {"put": {"operationId": "Device_CrashDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/datalogs": {"get": {"operationId": "Device_DeviceDatalogs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1DatalogsReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "startTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "endTime", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/deployment": {"put": {"operationId": "Device_UpdateDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "deployment", "in": "body", "required": true, "schema": {"$ref": "#/definitions/apidevicesv1Deployment"}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/drive": {"put": {"operationId": "Device_DriveDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"xSpeed": {"type": "number", "format": "float"}, "ySpeed": {"type": "number", "format": "float"}, "hSpeed": {"type": "number", "format": "float"}, "wSpeed": {"type": "number", "format": "float"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/events": {"get": {"operationId": "Device_DeviceEvents", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1EventsReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "startTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "endTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "levels", "in": "query", "required": false, "type": "string"}, {"name": "type", "in": "query", "required": false, "type": "string"}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/free": {"put": {"operationId": "Device_FreeDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enable": {"type": "boolean"}, "frequency": {"type": "integer", "format": "int32"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/gimbal": {"put": {"operationId": "Device_GimbalDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"mode": {"type": "integer", "format": "int32"}, "index": {"type": "string"}, "liveId": {"type": "string", "format": "int64"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/launch": {"put": {"operationId": "Device_LaunchDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"speed": {"type": "number", "format": "float"}, "height": {"type": "number", "format": "float"}, "algorithm": {"type": "string"}, "rCLostAction": {"type": "integer", "format": "int32"}, "cmderHeight": {"type": "number", "format": "float"}, "securityHeight": {"type": "number", "format": "float"}, "returnAltitude": {"type": "number", "format": "float"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "simulateMission": {"$ref": "#/definitions/v1LaunchRequestsimulator"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/lens": {"put": {"operationId": "Device_LensDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"lens": {"type": "string"}, "key": {"type": "string"}, "liveId": {"type": "string", "format": "int64"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/logfiles": {"get": {"operationId": "Device_ListLogfiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1LogfilesReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "startTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "endTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "modules", "in": "query", "required": false, "type": "string"}], "tags": ["<PERSON><PERSON>"]}, "post": {"operationId": "Device_UploadLogfiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"$ref": "#/definitions/UploadLogfilesRequestfile"}}}}}], "tags": ["<PERSON><PERSON>"]}, "put": {"operationId": "Device_RemoteLogfiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"modules": {"type": "array", "items": {"type": "string"}}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/logfiles/{fileId}": {"get": {"operationId": "Device_DownloadLogfile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1DownloadReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "fileId", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/lookat": {"put": {"operationId": "Device_LookatDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"locked": {"type": "boolean"}, "height": {"type": "number", "format": "float"}, "index": {"type": "string"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/operations": {"get": {"operationId": "Device_DeviceOperations", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationsReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "page", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "startTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "endTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "type", "in": "query", "required": false, "type": "string"}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/operations/reboot": {"put": {"operationId": "Device_RebootDevice2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/orbeed": {"put": {"operationId": "Device_OrbeedDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"speed": {"type": "number", "format": "float"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/orbit": {"put": {"operationId": "Device_OrbitDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enable": {"type": "boolean"}, "speed": {"type": "number", "format": "float"}, "radius": {"type": "number", "format": "float"}, "height": {"type": "number", "format": "float"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/picture": {"put": {"operationId": "Device_PictureDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"index": {"type": "string"}, "liveId": {"type": "string", "format": "int64"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/playmode": {"put": {"operationId": "Device_PlaymodeDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"index": {"type": "string"}, "playmode": {"type": "integer", "format": "int32"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/point": {"put": {"operationId": "Device_PointDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enable": {"type": "boolean"}, "speed": {"type": "number", "format": "float"}, "radius": {"type": "number", "format": "float"}, "height": {"type": "number", "format": "float"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/property": {"put": {"operationId": "Device_PropertyDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "integer", "format": "int32"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/reboot": {"put": {"operationId": "Device_RebootDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/release": {"put": {"operationId": "Device_ReleaseDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"operate": {"type": "string"}, "others": {"$ref": "#/definitions/ControlRequestother"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/repoint": {"put": {"operationId": "Device_RepointDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enable": {"type": "boolean"}, "speed": {"type": "number", "format": "float"}, "radius": {"type": "number", "format": "float"}, "height": {"type": "number", "format": "float"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/return": {"put": {"operationId": "Device_ReturnDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enable": {"type": "boolean"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/route": {"put": {"operationId": "Device_RouteDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enable": {"type": "boolean"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/schedules": {"get": {"operationId": "Device_DeviceSchedules", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1SchedulesReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "type", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "startTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "endTime", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "missionId", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/shout": {"put": {"operationId": "Device_ShoutDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"enable": {"type": "boolean"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/speak": {"put": {"operationId": "Device_SpeakDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"index": {"type": "string"}, "mode": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "content": {"type": "string"}, "action": {"type": "string"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/takeoff": {"put": {"operationId": "Device_TakeoffDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"airlineId": {"type": "string", "format": "int64"}, "algorithm": {"type": "string"}, "simulateMission": {"$ref": "#/definitions/v1TakeoffRequestsimulator"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/video": {"put": {"operationId": "Device_VideoDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"index": {"type": "string"}, "liveId": {"type": "string", "format": "int64"}, "action": {"type": "string"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/volume": {"put": {"operationId": "Device_VolumeDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"index": {"type": "string"}, "volume": {"type": "integer", "format": "int32"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/devices/{id}/zoom": {"put": {"operationId": "Device_ZoomDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"key": {"type": "string"}, "lens": {"type": "string"}, "liveId": {"type": "string", "format": "int64"}, "index": {"type": "string"}, "factor": {"type": "number", "format": "double"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/internal/v1/devices/{id}/callback": {"put": {"operationId": "Device_CallbackDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"type": {"type": "string"}, "action": {"type": "string"}, "sourceId": {"type": "string"}}}}], "tags": ["<PERSON><PERSON>"]}}, "/internal/v1/devices/{id}/executions": {"put": {"operationId": "Device_ExecuteDevice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/apidevicesv1CommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"type": {"type": "string"}, "action": {"type": "string"}, "sourceId": {"type": "string"}}}}], "tags": ["<PERSON><PERSON>"]}}}, "definitions": {"ControlRequestother": {"type": "object", "properties": {"index": {"type": "string"}}}, "Deploymentimage": {"type": "object", "properties": {"deviceImg": {"type": "string"}, "envImg": {"type": "string"}, "shopImg": {"type": "string"}}}, "DownloadReplydowndata": {"type": "object", "properties": {"url": {"type": "string"}, "fingerprint": {"type": "string"}}}, "SchedulesReplytimer": {"type": "object", "properties": {"runday": {"type": "integer", "format": "int32"}, "moments": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "UploadLogfilesRequestfile": {"type": "object", "properties": {"module": {"type": "string"}, "bootIndex": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startTime": {"type": "number", "format": "double"}, "endTime": {"type": "number", "format": "double"}}}, "apidevicesv1AddRequest": {"type": "object", "properties": {"sn": {"type": "string"}, "type": {"type": "string"}, "model": {"type": "string"}, "deployment": {"$ref": "#/definitions/apidevicesv1Deployment"}}}, "apidevicesv1CommonReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apidevicesv1CommonReplycommonData"}}}, "apidevicesv1CommonReplycommonData": {"type": "object", "properties": {"status": {"type": "boolean"}}}, "apidevicesv1Deployment": {"type": "object", "properties": {"name": {"type": "string"}, "images": {"$ref": "#/definitions/Deploymentimage"}, "location": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "contacts": {"type": "array", "items": {"$ref": "#/definitions/v1Deploymentcontact"}}, "status": {"type": "boolean"}, "time": {"type": "number", "format": "double"}}}, "apidevicesv1DeviceReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1DeviceItem"}}}, "apidevicesv1DownloadReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/DownloadReplydowndata"}}}, "apidevicesv1ListReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/apidevicesv1ListReplylistData"}}}, "apidevicesv1ListReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1DeviceItem"}}}}, "listDataevent": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "sn": {"type": "string"}, "type": {"type": "string"}, "code": {"type": "string"}, "level": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "occurredTime": {"type": "number", "format": "double"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\n The JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1Avatar": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "mobile": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}, "roleType": {"type": "integer", "format": "int32"}}}, "v1DatalogsReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1DatalogsReplylistData"}}}, "v1DatalogsReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1DeviceDatalog"}}}}, "v1Deploymentcontact": {"type": "object", "properties": {"name": {"type": "string"}, "contact": {"type": "string"}}}, "v1DeviceDatalog": {"type": "object", "properties": {"id": {"type": "string"}, "sn": {"type": "string"}, "realTs": {"type": "string"}, "timestamp": {"type": "number", "format": "double"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1Property"}}}}, "v1DeviceItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}, "sn": {"type": "string"}, "type": {"type": "string"}, "model": {"type": "string"}, "category": {"type": "string"}, "status": {"type": "string"}, "source": {"type": "string"}, "sourceSn": {"type": "string"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "networkType": {"type": "integer", "format": "int32"}, "cabinStatus": {"type": "boolean"}, "lockStatus": {"type": "integer", "format": "int32"}, "networkStatus": {"type": "boolean"}, "signalQuality": {"type": "string"}, "deployment": {"$ref": "#/definitions/apidevicesv1Deployment"}, "firmwareVersion": {"type": "string"}, "uppedTime": {"type": "number", "format": "double"}, "propData": {"type": "object"}, "extraData": {"type": "object"}, "subdevices": {"type": "array", "items": {"type": "object"}}}}, "v1EventsReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1EventsReplylistData"}}}, "v1EventsReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/listDataevent"}}}}, "v1LaunchRequestsimulator": {"type": "object", "properties": {"isEnable": {"type": "boolean"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}, "v1Logfile": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "module": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "deviceId": {"type": "string", "format": "int64"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "startTime": {"type": "number", "format": "double"}, "endTime": {"type": "number", "format": "double"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}}}, "v1LogfilesReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1LogfilesReplylistData"}}}, "v1LogfilesReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1Logfile"}}}}, "v1Operation": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "type": {"type": "string"}, "status": {"type": "string"}, "sourceId": {"type": "string", "format": "int64"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "avatarId": {"type": "string", "format": "int64"}, "avatar": {"$ref": "#/definitions/v1Avatar"}, "from": {"type": "string"}, "content": {"type": "object"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}}}, "v1OperationReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1Operation"}}}, "v1OperationsReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1OperationsReplylistData"}}}, "v1OperationsReplylistData": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1Operation"}}}}, "v1Property": {"type": "object", "properties": {"key": {"type": "string"}, "name": {"type": "string"}, "series": {"type": "integer", "format": "int32"}, "value": {"type": "object"}}}, "v1SchedulesReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1SchedulesReplylistData"}}}, "v1SchedulesReplylistData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/SchedulesReplytimer"}}}}, "v1TakeoffRequestsimulator": {"type": "object", "properties": {"isEnable": {"type": "boolean"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}}}}}