// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.29.2
// source: api/devices/v1/device_operation.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DeviceOperation_DebugMode_FullMethodName            = "/api.devices.v1.DeviceOperation/DebugMode"
	DeviceOperation_OperateCover_FullMethodName         = "/api.devices.v1.DeviceOperation/OperateCover"
	DeviceOperation_OperateReboot_FullMethodName        = "/api.devices.v1.DeviceOperation/OperateReboot"
	DeviceOperation_OperateDroneSwitch_FullMethodName   = "/api.devices.v1.DeviceOperation/OperateDroneSwitch"
	DeviceOperation_OperatePutter_FullMethodName        = "/api.devices.v1.DeviceOperation/OperatePutter"
	DeviceOperation_ForceOperateCover_FullMethodName    = "/api.devices.v1.DeviceOperation/ForceOperateCover"
	DeviceOperation_ChangeLiveCamera_FullMethodName     = "/api.devices.v1.DeviceOperation/ChangeLiveCamera"
	DeviceOperation_OperateBatteryCharge_FullMethodName = "/api.devices.v1.DeviceOperation/OperateBatteryCharge"
)

// DeviceOperationClient is the client API for DeviceOperation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DeviceOperationClient interface {
	DebugMode(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error)
	OperateCover(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error)
	OperateReboot(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error)
	OperateDroneSwitch(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error)
	OperatePutter(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ForceOperateCover(ctx context.Context, in *DeviceForceCoverRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ChangeLiveCamera(ctx context.Context, in *DeviceLiveCameraRequest, opts ...grpc.CallOption) (*OperationReply, error)
	OperateBatteryCharge(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error)
}

type deviceOperationClient struct {
	cc grpc.ClientConnInterface
}

func NewDeviceOperationClient(cc grpc.ClientConnInterface) DeviceOperationClient {
	return &deviceOperationClient{cc}
}

func (c *deviceOperationClient) DebugMode(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, DeviceOperation_DebugMode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceOperationClient) OperateCover(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, DeviceOperation_OperateCover_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceOperationClient) OperateReboot(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, DeviceOperation_OperateReboot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceOperationClient) OperateDroneSwitch(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, DeviceOperation_OperateDroneSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceOperationClient) OperatePutter(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, DeviceOperation_OperatePutter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceOperationClient) ForceOperateCover(ctx context.Context, in *DeviceForceCoverRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, DeviceOperation_ForceOperateCover_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceOperationClient) ChangeLiveCamera(ctx context.Context, in *DeviceLiveCameraRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, DeviceOperation_ChangeLiveCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceOperationClient) OperateBatteryCharge(ctx context.Context, in *DeviceOperationRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, DeviceOperation_OperateBatteryCharge_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeviceOperationServer is the server API for DeviceOperation service.
// All implementations must embed UnimplementedDeviceOperationServer
// for forward compatibility
type DeviceOperationServer interface {
	DebugMode(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	OperateCover(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	OperateReboot(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	OperateDroneSwitch(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	OperatePutter(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	ForceOperateCover(context.Context, *DeviceForceCoverRequest) (*OperationReply, error)
	ChangeLiveCamera(context.Context, *DeviceLiveCameraRequest) (*OperationReply, error)
	OperateBatteryCharge(context.Context, *DeviceOperationRequest) (*OperationReply, error)
	mustEmbedUnimplementedDeviceOperationServer()
}

// UnimplementedDeviceOperationServer must be embedded to have forward compatible implementations.
type UnimplementedDeviceOperationServer struct {
}

func (UnimplementedDeviceOperationServer) DebugMode(context.Context, *DeviceOperationRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DebugMode not implemented")
}
func (UnimplementedDeviceOperationServer) OperateCover(context.Context, *DeviceOperationRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperateCover not implemented")
}
func (UnimplementedDeviceOperationServer) OperateReboot(context.Context, *DeviceOperationRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperateReboot not implemented")
}
func (UnimplementedDeviceOperationServer) OperateDroneSwitch(context.Context, *DeviceOperationRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperateDroneSwitch not implemented")
}
func (UnimplementedDeviceOperationServer) OperatePutter(context.Context, *DeviceOperationRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperatePutter not implemented")
}
func (UnimplementedDeviceOperationServer) ForceOperateCover(context.Context, *DeviceForceCoverRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceOperateCover not implemented")
}
func (UnimplementedDeviceOperationServer) ChangeLiveCamera(context.Context, *DeviceLiveCameraRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeLiveCamera not implemented")
}
func (UnimplementedDeviceOperationServer) OperateBatteryCharge(context.Context, *DeviceOperationRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperateBatteryCharge not implemented")
}
func (UnimplementedDeviceOperationServer) mustEmbedUnimplementedDeviceOperationServer() {}

// UnsafeDeviceOperationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeviceOperationServer will
// result in compilation errors.
type UnsafeDeviceOperationServer interface {
	mustEmbedUnimplementedDeviceOperationServer()
}

func RegisterDeviceOperationServer(s grpc.ServiceRegistrar, srv DeviceOperationServer) {
	s.RegisterService(&DeviceOperation_ServiceDesc, srv)
}

func _DeviceOperation_DebugMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceOperationServer).DebugMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceOperation_DebugMode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceOperationServer).DebugMode(ctx, req.(*DeviceOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceOperation_OperateCover_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceOperationServer).OperateCover(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceOperation_OperateCover_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceOperationServer).OperateCover(ctx, req.(*DeviceOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceOperation_OperateReboot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceOperationServer).OperateReboot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceOperation_OperateReboot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceOperationServer).OperateReboot(ctx, req.(*DeviceOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceOperation_OperateDroneSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceOperationServer).OperateDroneSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceOperation_OperateDroneSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceOperationServer).OperateDroneSwitch(ctx, req.(*DeviceOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceOperation_OperatePutter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceOperationServer).OperatePutter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceOperation_OperatePutter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceOperationServer).OperatePutter(ctx, req.(*DeviceOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceOperation_ForceOperateCover_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceForceCoverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceOperationServer).ForceOperateCover(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceOperation_ForceOperateCover_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceOperationServer).ForceOperateCover(ctx, req.(*DeviceForceCoverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceOperation_ChangeLiveCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceLiveCameraRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceOperationServer).ChangeLiveCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceOperation_ChangeLiveCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceOperationServer).ChangeLiveCamera(ctx, req.(*DeviceLiveCameraRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceOperation_OperateBatteryCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceOperationServer).OperateBatteryCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceOperation_OperateBatteryCharge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceOperationServer).OperateBatteryCharge(ctx, req.(*DeviceOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DeviceOperation_ServiceDesc is the grpc.ServiceDesc for DeviceOperation service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DeviceOperation_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devices.v1.DeviceOperation",
	HandlerType: (*DeviceOperationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DebugMode",
			Handler:    _DeviceOperation_DebugMode_Handler,
		},
		{
			MethodName: "OperateCover",
			Handler:    _DeviceOperation_OperateCover_Handler,
		},
		{
			MethodName: "OperateReboot",
			Handler:    _DeviceOperation_OperateReboot_Handler,
		},
		{
			MethodName: "OperateDroneSwitch",
			Handler:    _DeviceOperation_OperateDroneSwitch_Handler,
		},
		{
			MethodName: "OperatePutter",
			Handler:    _DeviceOperation_OperatePutter_Handler,
		},
		{
			MethodName: "ForceOperateCover",
			Handler:    _DeviceOperation_ForceOperateCover_Handler,
		},
		{
			MethodName: "ChangeLiveCamera",
			Handler:    _DeviceOperation_ChangeLiveCamera_Handler,
		},
		{
			MethodName: "OperateBatteryCharge",
			Handler:    _DeviceOperation_OperateBatteryCharge_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/devices/v1/device_operation.proto",
}
