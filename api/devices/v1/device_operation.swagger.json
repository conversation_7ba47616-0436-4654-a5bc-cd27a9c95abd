{"swagger": "2.0", "info": {"title": "api/devices/v1/device_operation.proto", "version": "version not set"}, "tags": [{"name": "DeviceOperation"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/devices/{id}/operations/cover": {"put": {"operationId": "DeviceOperation_OperateCover", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"action": {"type": "string"}}}}], "tags": ["DeviceOperation"]}}, "/api/v1/devices/{id}/operations/debugMode": {"put": {"operationId": "DeviceOperation_DebugMode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"action": {"type": "string"}}}}], "tags": ["DeviceOperation"]}}, "/api/v1/devices/{id}/operations/droneSwitch": {"put": {"operationId": "DeviceOperation_OperateDroneSwitch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"action": {"type": "string"}}}}], "tags": ["DeviceOperation"]}}, "/api/v1/devices/{id}/operations/forcedCover": {"put": {"operationId": "DeviceOperation_ForceOperateCover", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"action": {"type": "string", "title": "只允许为close"}}}}], "tags": ["DeviceOperation"]}}, "/api/v1/devices/{id}/operations/putter": {"put": {"operationId": "DeviceOperation_OperatePutter", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1OperationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"action": {"type": "string"}}}}], "tags": ["DeviceOperation"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\n The JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1Avatar": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "mobile": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}, "roleType": {"type": "integer", "format": "int32"}}}, "v1Operation": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "type": {"type": "string"}, "status": {"type": "string"}, "sourceId": {"type": "string", "format": "int64"}, "tenantId": {"type": "string", "format": "int64"}, "merchantId": {"type": "string", "format": "int64"}, "avatarId": {"type": "string", "format": "int64"}, "avatar": {"$ref": "#/definitions/v1Avatar"}, "from": {"type": "string"}, "content": {"type": "object"}, "createdTime": {"type": "number", "format": "double"}, "updatedTime": {"type": "number", "format": "double"}}}, "v1OperationReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1Operation"}}}}}