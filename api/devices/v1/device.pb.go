// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/devices/v1/device.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list"
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Avatar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Mobile   string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Nickname string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Avatar   string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	RoleType int32  `protobuf:"varint,5,opt,name=roleType,proto3" json:"roleType,omitempty"`
}

func (x *Avatar) Reset() {
	*x = Avatar{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Avatar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Avatar) ProtoMessage() {}

func (x *Avatar) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Avatar.ProtoReflect.Descriptor instead.
func (*Avatar) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{0}
}

func (x *Avatar) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Avatar) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *Avatar) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *Avatar) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Avatar) GetRoleType() int32 {
	if x != nil {
		return x.RoleType
	}
	return 0
}

type CommonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CommonRequest) Reset() {
	*x = CommonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRequest) ProtoMessage() {}

func (x *CommonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRequest.ProtoReflect.Descriptor instead.
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{1}
}

func (x *CommonRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type CommonReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *CommonReplyCommonData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CommonReply) Reset() {
	*x = CommonReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReply) ProtoMessage() {}

func (x *CommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReply.ProtoReflect.Descriptor instead.
func (*CommonReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{2}
}

func (x *CommonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommonReply) GetData() *CommonReplyCommonData {
	if x != nil {
		return x.Data
	}
	return nil
}

type Property struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key    string          `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Name   string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Series int32           `protobuf:"varint,3,opt,name=series,proto3" json:"series,omitempty"`
	Value  *structpb.Value `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Property) Reset() {
	*x = Property{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Property) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Property) ProtoMessage() {}

func (x *Property) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Property.ProtoReflect.Descriptor instead.
func (*Property) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{3}
}

func (x *Property) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Property) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Property) GetSeries() int32 {
	if x != nil {
		return x.Series
	}
	return 0
}

func (x *Property) GetValue() *structpb.Value {
	if x != nil {
		return x.Value
	}
	return nil
}

type Deployment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Images       *DeploymentImage       `protobuf:"bytes,2,opt,name=images,proto3" json:"images,omitempty"`
	Altitude     float32                `protobuf:"fixed32,3,opt,name=altitude,proto3" json:"altitude,omitempty"`
	Location     string                 `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	Tags         []string               `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty"`
	Status       *bool                  `protobuf:"varint,6,opt,name=status,proto3,oneof" json:"status,omitempty"`
	Time         *float64               `protobuf:"fixed64,7,opt,name=time,proto3,oneof" json:"time,omitempty"`
	Lnglat       []float64              `protobuf:"fixed64,8,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	Contacts     []*DeploymentContact   `protobuf:"bytes,9,rep,name=contacts,proto3" json:"contacts,omitempty"`
	RelatedChIds []int64                `protobuf:"varint,10,rep,packed,name=relatedChIds,proto3" json:"relatedChIds,omitempty"`
	RelatedChs   []*DeploymentRelatedCh `protobuf:"bytes,11,rep,name=relatedChs,proto3" json:"relatedChs,omitempty"`
}

func (x *Deployment) Reset() {
	*x = Deployment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment) ProtoMessage() {}

func (x *Deployment) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment.ProtoReflect.Descriptor instead.
func (*Deployment) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{4}
}

func (x *Deployment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Deployment) GetImages() *DeploymentImage {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *Deployment) GetAltitude() float32 {
	if x != nil {
		return x.Altitude
	}
	return 0
}

func (x *Deployment) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Deployment) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Deployment) GetStatus() bool {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return false
}

func (x *Deployment) GetTime() float64 {
	if x != nil && x.Time != nil {
		return *x.Time
	}
	return 0
}

func (x *Deployment) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *Deployment) GetContacts() []*DeploymentContact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *Deployment) GetRelatedChIds() []int64 {
	if x != nil {
		return x.RelatedChIds
	}
	return nil
}

func (x *Deployment) GetRelatedChs() []*DeploymentRelatedCh {
	if x != nil {
		return x.RelatedChs
	}
	return nil
}

type AddRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn         string      `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn,omitempty"`
	Type       string      `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Model      string      `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	Deployment *Deployment `protobuf:"bytes,4,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *AddRequest) Reset() {
	*x = AddRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRequest) ProtoMessage() {}

func (x *AddRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRequest.ProtoReflect.Descriptor instead.
func (*AddRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{5}
}

func (x *AddRequest) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *AddRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AddRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *AddRequest) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type UpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Deployment *Deployment `protobuf:"bytes,2,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *UpdateRequest) Reset() {
	*x = UpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRequest) ProtoMessage() {}

func (x *UpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRequest.ProtoReflect.Descriptor instead.
func (*UpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateRequest) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type PropertyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Value int32  `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PropertyRequest) Reset() {
	*x = PropertyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyRequest) ProtoMessage() {}

func (x *PropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyRequest.ProtoReflect.Descriptor instead.
func (*PropertyRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{7}
}

func (x *PropertyRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PropertyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PropertyRequest) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type TakeoffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AirlineId       int64                    `protobuf:"varint,2,opt,name=airlineId,proto3" json:"airlineId,omitempty"`
	Algorithm       string                   `protobuf:"bytes,3,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
	SimulateMission *TakeoffRequestSimulator `protobuf:"bytes,4,opt,name=simulateMission,proto3,oneof" json:"simulateMission,omitempty"`
}

func (x *TakeoffRequest) Reset() {
	*x = TakeoffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TakeoffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TakeoffRequest) ProtoMessage() {}

func (x *TakeoffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TakeoffRequest.ProtoReflect.Descriptor instead.
func (*TakeoffRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{8}
}

func (x *TakeoffRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TakeoffRequest) GetAirlineId() int64 {
	if x != nil {
		return x.AirlineId
	}
	return 0
}

func (x *TakeoffRequest) GetAlgorithm() string {
	if x != nil {
		return x.Algorithm
	}
	return ""
}

func (x *TakeoffRequest) GetSimulateMission() *TakeoffRequestSimulator {
	if x != nil {
		return x.SimulateMission
	}
	return nil
}

type ReturnRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Enable bool  `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *ReturnRequest) Reset() {
	*x = ReturnRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReturnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReturnRequest) ProtoMessage() {}

func (x *ReturnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReturnRequest.ProtoReflect.Descriptor instead.
func (*ReturnRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{9}
}

func (x *ReturnRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReturnRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type LaunchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Speed           float32                 `protobuf:"fixed32,2,opt,name=speed,proto3" json:"speed,omitempty"`
	Height          float32                 `protobuf:"fixed32,3,opt,name=height,proto3" json:"height,omitempty"`
	Algorithm       string                  `protobuf:"bytes,4,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
	RCLostAction    int32                   `protobuf:"varint,5,opt,name=rCLostAction,proto3" json:"rCLostAction,omitempty"`
	CmderHeight     float32                 `protobuf:"fixed32,6,opt,name=cmderHeight,proto3" json:"cmderHeight,omitempty"`
	SecurityHeight  float32                 `protobuf:"fixed32,7,opt,name=securityHeight,proto3" json:"securityHeight,omitempty"`
	ReturnAltitude  float32                 `protobuf:"fixed32,8,opt,name=returnAltitude,proto3" json:"returnAltitude,omitempty"`
	Lnglat          []float64               `protobuf:"fixed64,9,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	SimulateMission *LaunchRequestSimulator `protobuf:"bytes,10,opt,name=simulateMission,proto3,oneof" json:"simulateMission,omitempty"`
}

func (x *LaunchRequest) Reset() {
	*x = LaunchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaunchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaunchRequest) ProtoMessage() {}

func (x *LaunchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaunchRequest.ProtoReflect.Descriptor instead.
func (*LaunchRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{10}
}

func (x *LaunchRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LaunchRequest) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *LaunchRequest) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *LaunchRequest) GetAlgorithm() string {
	if x != nil {
		return x.Algorithm
	}
	return ""
}

func (x *LaunchRequest) GetRCLostAction() int32 {
	if x != nil {
		return x.RCLostAction
	}
	return 0
}

func (x *LaunchRequest) GetCmderHeight() float32 {
	if x != nil {
		return x.CmderHeight
	}
	return 0
}

func (x *LaunchRequest) GetSecurityHeight() float32 {
	if x != nil {
		return x.SecurityHeight
	}
	return 0
}

func (x *LaunchRequest) GetReturnAltitude() float32 {
	if x != nil {
		return x.ReturnAltitude
	}
	return 0
}

func (x *LaunchRequest) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *LaunchRequest) GetSimulateMission() *LaunchRequestSimulator {
	if x != nil {
		return x.SimulateMission
	}
	return nil
}

type ControlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Operate string               `protobuf:"bytes,2,opt,name=operate,proto3" json:"operate,omitempty"`
	Others  *ControlRequestOther `protobuf:"bytes,3,opt,name=others,proto3" json:"others,omitempty"`
}

func (x *ControlRequest) Reset() {
	*x = ControlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlRequest) ProtoMessage() {}

func (x *ControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlRequest.ProtoReflect.Descriptor instead.
func (*ControlRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{11}
}

func (x *ControlRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ControlRequest) GetOperate() string {
	if x != nil {
		return x.Operate
	}
	return ""
}

func (x *ControlRequest) GetOthers() *ControlRequestOther {
	if x != nil {
		return x.Others
	}
	return nil
}

type AeromodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Enable   bool  `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	AeroMode int32 `protobuf:"varint,3,opt,name=aeroMode,proto3" json:"aeroMode,omitempty"`
}

func (x *AeromodeRequest) Reset() {
	*x = AeromodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AeromodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AeromodeRequest) ProtoMessage() {}

func (x *AeromodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AeromodeRequest.ProtoReflect.Descriptor instead.
func (*AeromodeRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{12}
}

func (x *AeromodeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AeromodeRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *AeromodeRequest) GetAeroMode() int32 {
	if x != nil {
		return x.AeroMode
	}
	return 0
}

type LensRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Lens   string `protobuf:"bytes,2,opt,name=lens,proto3" json:"lens,omitempty"`
	Key    string `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	LiveId int64  `protobuf:"varint,4,opt,name=liveId,proto3" json:"liveId,omitempty"`
}

func (x *LensRequest) Reset() {
	*x = LensRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensRequest) ProtoMessage() {}

func (x *LensRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensRequest.ProtoReflect.Descriptor instead.
func (*LensRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{13}
}

func (x *LensRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LensRequest) GetLens() string {
	if x != nil {
		return x.Lens
	}
	return ""
}

func (x *LensRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *LensRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

type ZoomRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Key    string  `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Lens   string  `protobuf:"bytes,3,opt,name=lens,proto3" json:"lens,omitempty"`
	LiveId int64   `protobuf:"varint,4,opt,name=liveId,proto3" json:"liveId,omitempty"`
	Index  string  `protobuf:"bytes,5,opt,name=index,proto3" json:"index,omitempty"`
	Factor float64 `protobuf:"fixed64,6,opt,name=factor,proto3" json:"factor,omitempty"`
}

func (x *ZoomRequest) Reset() {
	*x = ZoomRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZoomRequest) ProtoMessage() {}

func (x *ZoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZoomRequest.ProtoReflect.Descriptor instead.
func (*ZoomRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{14}
}

func (x *ZoomRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ZoomRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ZoomRequest) GetLens() string {
	if x != nil {
		return x.Lens
	}
	return ""
}

func (x *ZoomRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

func (x *ZoomRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *ZoomRequest) GetFactor() float64 {
	if x != nil {
		return x.Factor
	}
	return 0
}

type GimbalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Mode   int32  `protobuf:"varint,2,opt,name=mode,proto3" json:"mode,omitempty"`
	Index  string `protobuf:"bytes,3,opt,name=index,proto3" json:"index,omitempty"`
	LiveId int64  `protobuf:"varint,4,opt,name=liveId,proto3" json:"liveId,omitempty"`
}

func (x *GimbalRequest) Reset() {
	*x = GimbalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GimbalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GimbalRequest) ProtoMessage() {}

func (x *GimbalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GimbalRequest.ProtoReflect.Descriptor instead.
func (*GimbalRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{15}
}

func (x *GimbalRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GimbalRequest) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *GimbalRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *GimbalRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

type ClarityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Clarity int32  `protobuf:"varint,2,opt,name=clarity,proto3" json:"clarity,omitempty"`
	Key     string `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	LiveId  int64  `protobuf:"varint,4,opt,name=liveId,proto3" json:"liveId,omitempty"`
}

func (x *ClarityRequest) Reset() {
	*x = ClarityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClarityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClarityRequest) ProtoMessage() {}

func (x *ClarityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClarityRequest.ProtoReflect.Descriptor instead.
func (*ClarityRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{16}
}

func (x *ClarityRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClarityRequest) GetClarity() int32 {
	if x != nil {
		return x.Clarity
	}
	return 0
}

func (x *ClarityRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ClarityRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

type FreeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Enable    bool  `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	Frequency int32 `protobuf:"varint,3,opt,name=frequency,proto3" json:"frequency,omitempty"`
}

func (x *FreeRequest) Reset() {
	*x = FreeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FreeRequest) ProtoMessage() {}

func (x *FreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FreeRequest.ProtoReflect.Descriptor instead.
func (*FreeRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{17}
}

func (x *FreeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FreeRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *FreeRequest) GetFrequency() int32 {
	if x != nil {
		return x.Frequency
	}
	return 0
}

type PointRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Enable bool      `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	Speed  float32   `protobuf:"fixed32,3,opt,name=speed,proto3" json:"speed,omitempty"`
	Radius float32   `protobuf:"fixed32,4,opt,name=radius,proto3" json:"radius,omitempty"`
	Height float32   `protobuf:"fixed32,5,opt,name=height,proto3" json:"height,omitempty"`
	Lnglat []float64 `protobuf:"fixed64,6,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
}

func (x *PointRequest) Reset() {
	*x = PointRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PointRequest) ProtoMessage() {}

func (x *PointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PointRequest.ProtoReflect.Descriptor instead.
func (*PointRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{18}
}

func (x *PointRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PointRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *PointRequest) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *PointRequest) GetRadius() float32 {
	if x != nil {
		return x.Radius
	}
	return 0
}

func (x *PointRequest) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *PointRequest) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

type OrbeedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Speed float32 `protobuf:"fixed32,2,opt,name=speed,proto3" json:"speed,omitempty"`
}

func (x *OrbeedRequest) Reset() {
	*x = OrbeedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrbeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrbeedRequest) ProtoMessage() {}

func (x *OrbeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrbeedRequest.ProtoReflect.Descriptor instead.
func (*OrbeedRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{19}
}

func (x *OrbeedRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrbeedRequest) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type LookatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Locked bool      `protobuf:"varint,2,opt,name=locked,proto3" json:"locked,omitempty"`
	Height float32   `protobuf:"fixed32,3,opt,name=height,proto3" json:"height,omitempty"`
	Index  string    `protobuf:"bytes,4,opt,name=index,proto3" json:"index,omitempty"`
	Lnglat []float64 `protobuf:"fixed64,5,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
}

func (x *LookatRequest) Reset() {
	*x = LookatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LookatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LookatRequest) ProtoMessage() {}

func (x *LookatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LookatRequest.ProtoReflect.Descriptor instead.
func (*LookatRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{20}
}

func (x *LookatRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LookatRequest) GetLocked() bool {
	if x != nil {
		return x.Locked
	}
	return false
}

func (x *LookatRequest) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *LookatRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *LookatRequest) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

type PictureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Index  string `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
	LiveId int64  `protobuf:"varint,3,opt,name=liveId,proto3" json:"liveId,omitempty"`
}

func (x *PictureRequest) Reset() {
	*x = PictureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PictureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PictureRequest) ProtoMessage() {}

func (x *PictureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PictureRequest.ProtoReflect.Descriptor instead.
func (*PictureRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{21}
}

func (x *PictureRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PictureRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *PictureRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

type VideoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Index  string `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
	LiveId int64  `protobuf:"varint,3,opt,name=liveId,proto3" json:"liveId,omitempty"`
	Action string `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *VideoRequest) Reset() {
	*x = VideoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoRequest) ProtoMessage() {}

func (x *VideoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoRequest.ProtoReflect.Descriptor instead.
func (*VideoRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{22}
}

func (x *VideoRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *VideoRequest) GetLiveId() int64 {
	if x != nil {
		return x.LiveId
	}
	return 0
}

func (x *VideoRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type DriveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XSpeed *float32 `protobuf:"fixed32,2,opt,name=xSpeed,proto3,oneof" json:"xSpeed,omitempty"`
	YSpeed *float32 `protobuf:"fixed32,3,opt,name=ySpeed,proto3,oneof" json:"ySpeed,omitempty"`
	HSpeed *float32 `protobuf:"fixed32,4,opt,name=hSpeed,proto3,oneof" json:"hSpeed,omitempty"`
	WSpeed *float32 `protobuf:"fixed32,5,opt,name=wSpeed,proto3,oneof" json:"wSpeed,omitempty"`
}

func (x *DriveRequest) Reset() {
	*x = DriveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriveRequest) ProtoMessage() {}

func (x *DriveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriveRequest.ProtoReflect.Descriptor instead.
func (*DriveRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{23}
}

func (x *DriveRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DriveRequest) GetXSpeed() float32 {
	if x != nil && x.XSpeed != nil {
		return *x.XSpeed
	}
	return 0
}

func (x *DriveRequest) GetYSpeed() float32 {
	if x != nil && x.YSpeed != nil {
		return *x.YSpeed
	}
	return 0
}

func (x *DriveRequest) GetHSpeed() float32 {
	if x != nil && x.HSpeed != nil {
		return *x.HSpeed
	}
	return 0
}

func (x *DriveRequest) GetWSpeed() float32 {
	if x != nil && x.WSpeed != nil {
		return *x.WSpeed
	}
	return 0
}

type SpeakRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Index   string `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
	Mode    int32  `protobuf:"varint,3,opt,name=mode,proto3" json:"mode,omitempty"`
	Name    string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Action  string `protobuf:"bytes,6,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *SpeakRequest) Reset() {
	*x = SpeakRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpeakRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeakRequest) ProtoMessage() {}

func (x *SpeakRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeakRequest.ProtoReflect.Descriptor instead.
func (*SpeakRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{24}
}

func (x *SpeakRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SpeakRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *SpeakRequest) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *SpeakRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SpeakRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SpeakRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type VolumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Index  string `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
	Volume int32  `protobuf:"varint,3,opt,name=volume,proto3" json:"volume,omitempty"`
}

func (x *VolumeRequest) Reset() {
	*x = VolumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeRequest) ProtoMessage() {}

func (x *VolumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeRequest.ProtoReflect.Descriptor instead.
func (*VolumeRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{25}
}

func (x *VolumeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VolumeRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *VolumeRequest) GetVolume() int32 {
	if x != nil {
		return x.Volume
	}
	return 0
}

type PlaymodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Index    string `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
	Playmode int32  `protobuf:"varint,3,opt,name=playmode,proto3" json:"playmode,omitempty"`
}

func (x *PlaymodeRequest) Reset() {
	*x = PlaymodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaymodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaymodeRequest) ProtoMessage() {}

func (x *PlaymodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaymodeRequest.ProtoReflect.Descriptor instead.
func (*PlaymodeRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{26}
}

func (x *PlaymodeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PlaymodeRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *PlaymodeRequest) GetPlaymode() int32 {
	if x != nil {
		return x.Playmode
	}
	return 0
}

type AlgflowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AlgflowStatus bool  `protobuf:"varint,2,opt,name=algflowStatus,proto3" json:"algflowStatus,omitempty"`
}

func (x *AlgflowRequest) Reset() {
	*x = AlgflowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlgflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlgflowRequest) ProtoMessage() {}

func (x *AlgflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlgflowRequest.ProtoReflect.Descriptor instead.
func (*AlgflowRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{27}
}

func (x *AlgflowRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AlgflowRequest) GetAlgflowStatus() bool {
	if x != nil {
		return x.AlgflowStatus
	}
	return false
}

type OperationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string     `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *Operation `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *OperationReply) Reset() {
	*x = OperationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationReply) ProtoMessage() {}

func (x *OperationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationReply.ProtoReflect.Descriptor instead.
func (*OperationReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{28}
}

func (x *OperationReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *OperationReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *OperationReply) GetData() *Operation {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeviceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime     float64            `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime     float64            `protobuf:"fixed64,3,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	Sn              string             `protobuf:"bytes,4,opt,name=sn,proto3" json:"sn,omitempty"`
	Type            string             `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Model           string             `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`
	Category        string             `protobuf:"bytes,7,opt,name=category,proto3" json:"category,omitempty"`
	Status          string             `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	Source          string             `protobuf:"bytes,9,opt,name=source,proto3" json:"source,omitempty"`
	SourceSn        string             `protobuf:"bytes,10,opt,name=sourceSn,proto3" json:"sourceSn,omitempty"`
	TenantId        int64              `protobuf:"varint,11,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	MerchantId      int64              `protobuf:"varint,12,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	NetworkType     int32              `protobuf:"varint,13,opt,name=networkType,proto3" json:"networkType,omitempty"`
	CabinStatus     bool               `protobuf:"varint,14,opt,name=cabinStatus,proto3" json:"cabinStatus,omitempty"`
	LockStatus      int32              `protobuf:"varint,15,opt,name=lockStatus,proto3" json:"lockStatus,omitempty"`
	NetworkStatus   bool               `protobuf:"varint,16,opt,name=networkStatus,proto3" json:"networkStatus,omitempty"`
	SignalQuality   string             `protobuf:"bytes,17,opt,name=signalQuality,proto3" json:"signalQuality,omitempty"`
	Deployment      *Deployment        `protobuf:"bytes,18,opt,name=deployment,proto3" json:"deployment,omitempty"`
	FirmwareVersion string             `protobuf:"bytes,19,opt,name=firmwareVersion,proto3" json:"firmwareVersion,omitempty"`
	UppedTime       *float64           `protobuf:"fixed64,20,opt,name=uppedTime,proto3,oneof" json:"uppedTime,omitempty"`
	PropData        *structpb.Struct   `protobuf:"bytes,21,opt,name=propData,proto3" json:"propData,omitempty"`
	ExtraData       *structpb.Struct   `protobuf:"bytes,22,opt,name=extraData,proto3" json:"extraData,omitempty"`
	Subdevices      []*structpb.Struct `protobuf:"bytes,23,rep,name=subdevices,proto3" json:"subdevices,omitempty"`
}

func (x *DeviceItem) Reset() {
	*x = DeviceItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceItem) ProtoMessage() {}

func (x *DeviceItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceItem.ProtoReflect.Descriptor instead.
func (*DeviceItem) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{29}
}

func (x *DeviceItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeviceItem) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *DeviceItem) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *DeviceItem) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *DeviceItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeviceItem) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *DeviceItem) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *DeviceItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DeviceItem) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *DeviceItem) GetSourceSn() string {
	if x != nil {
		return x.SourceSn
	}
	return ""
}

func (x *DeviceItem) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *DeviceItem) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *DeviceItem) GetNetworkType() int32 {
	if x != nil {
		return x.NetworkType
	}
	return 0
}

func (x *DeviceItem) GetCabinStatus() bool {
	if x != nil {
		return x.CabinStatus
	}
	return false
}

func (x *DeviceItem) GetLockStatus() int32 {
	if x != nil {
		return x.LockStatus
	}
	return 0
}

func (x *DeviceItem) GetNetworkStatus() bool {
	if x != nil {
		return x.NetworkStatus
	}
	return false
}

func (x *DeviceItem) GetSignalQuality() string {
	if x != nil {
		return x.SignalQuality
	}
	return ""
}

func (x *DeviceItem) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

func (x *DeviceItem) GetFirmwareVersion() string {
	if x != nil {
		return x.FirmwareVersion
	}
	return ""
}

func (x *DeviceItem) GetUppedTime() float64 {
	if x != nil && x.UppedTime != nil {
		return *x.UppedTime
	}
	return 0
}

func (x *DeviceItem) GetPropData() *structpb.Struct {
	if x != nil {
		return x.PropData
	}
	return nil
}

func (x *DeviceItem) GetExtraData() *structpb.Struct {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

func (x *DeviceItem) GetSubdevices() []*structpb.Struct {
	if x != nil {
		return x.Subdevices
	}
	return nil
}

type DeviceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *DeviceItem `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DeviceReply) Reset() {
	*x = DeviceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReply) ProtoMessage() {}

func (x *DeviceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReply.ProtoReflect.Descriptor instead.
func (*DeviceReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{30}
}

func (x *DeviceReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeviceReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeviceReply) GetData() *DeviceItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page          int32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size          int32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Search        *string `protobuf:"bytes,3,opt,name=search,proto3,oneof" json:"search,omitempty"`
	Type          *string `protobuf:"bytes,4,opt,name=type,proto3,oneof" json:"type,omitempty"`
	Category      *string `protobuf:"bytes,5,opt,name=category,proto3,oneof" json:"category,omitempty"`
	NetworkStatus *bool   `protobuf:"varint,6,opt,name=networkStatus,proto3,oneof" json:"networkStatus,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{31}
}

func (x *ListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListRequest) GetSearch() string {
	if x != nil && x.Search != nil {
		return *x.Search
	}
	return ""
}

func (x *ListRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *ListRequest) GetCategory() string {
	if x != nil && x.Category != nil {
		return *x.Category
	}
	return ""
}

func (x *ListRequest) GetNetworkStatus() bool {
	if x != nil && x.NetworkStatus != nil {
		return *x.NetworkStatus
	}
	return false
}

type ListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ListReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListReply) Reset() {
	*x = ListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReply) ProtoMessage() {}

func (x *ListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReply.ProtoReflect.Descriptor instead.
func (*ListReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{32}
}

func (x *ListReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListReply) GetData() *ListReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type EventsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size      int32  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Id        int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	StartTime int64  `protobuf:"varint,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   int64  `protobuf:"varint,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Types     string `protobuf:"bytes,6,opt,name=types,proto3" json:"types,omitempty"`
	Levels    string `protobuf:"bytes,7,opt,name=levels,proto3" json:"levels,omitempty"`
}

func (x *EventsRequest) Reset() {
	*x = EventsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventsRequest) ProtoMessage() {}

func (x *EventsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventsRequest.ProtoReflect.Descriptor instead.
func (*EventsRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{33}
}

func (x *EventsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *EventsRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *EventsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EventsRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *EventsRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *EventsRequest) GetTypes() string {
	if x != nil {
		return x.Types
	}
	return ""
}

func (x *EventsRequest) GetLevels() string {
	if x != nil {
		return x.Levels
	}
	return ""
}

type EventsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *EventsReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *EventsReply) Reset() {
	*x = EventsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventsReply) ProtoMessage() {}

func (x *EventsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventsReply.ProtoReflect.Descriptor instead.
func (*EventsReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{34}
}

func (x *EventsReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *EventsReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *EventsReply) GetData() *EventsReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DatalogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size      int32 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Id        int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	StartTime int64 `protobuf:"varint,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   int64 `protobuf:"varint,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *DatalogsRequest) Reset() {
	*x = DatalogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DatalogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DatalogsRequest) ProtoMessage() {}

func (x *DatalogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DatalogsRequest.ProtoReflect.Descriptor instead.
func (*DatalogsRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{35}
}

func (x *DatalogsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DatalogsRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DatalogsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DatalogsRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *DatalogsRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type DatalogsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *DatalogsReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DatalogsReply) Reset() {
	*x = DatalogsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DatalogsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DatalogsReply) ProtoMessage() {}

func (x *DatalogsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DatalogsReply.ProtoReflect.Descriptor instead.
func (*DatalogsReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{36}
}

func (x *DatalogsReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DatalogsReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DatalogsReply) GetData() *DatalogsReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeviceDatalog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string      `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Sn        string      `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	RealTs    string      `protobuf:"bytes,3,opt,name=realTs,proto3" json:"realTs,omitempty"`
	Timestamp float64     `protobuf:"fixed64,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Data      []*Property `protobuf:"bytes,5,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *DeviceDatalog) Reset() {
	*x = DeviceDatalog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceDatalog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceDatalog) ProtoMessage() {}

func (x *DeviceDatalog) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceDatalog.ProtoReflect.Descriptor instead.
func (*DeviceDatalog) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{37}
}

func (x *DeviceDatalog) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeviceDatalog) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *DeviceDatalog) GetRealTs() string {
	if x != nil {
		return x.RealTs
	}
	return ""
}

func (x *DeviceDatalog) GetTimestamp() float64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DeviceDatalog) GetData() []*Property {
	if x != nil {
		return x.Data
	}
	return nil
}

type SchedulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type      int32  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	StartTime int64  `protobuf:"varint,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   int64  `protobuf:"varint,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	MissionId *int64 `protobuf:"varint,5,opt,name=missionId,proto3,oneof" json:"missionId,omitempty"`
}

func (x *SchedulesRequest) Reset() {
	*x = SchedulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchedulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchedulesRequest) ProtoMessage() {}

func (x *SchedulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchedulesRequest.ProtoReflect.Descriptor instead.
func (*SchedulesRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{38}
}

func (x *SchedulesRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SchedulesRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SchedulesRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *SchedulesRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *SchedulesRequest) GetMissionId() int64 {
	if x != nil && x.MissionId != nil {
		return *x.MissionId
	}
	return 0
}

type SchedulesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                  `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *SchedulesReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SchedulesReply) Reset() {
	*x = SchedulesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchedulesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchedulesReply) ProtoMessage() {}

func (x *SchedulesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchedulesReply.ProtoReflect.Descriptor instead.
func (*SchedulesReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{39}
}

func (x *SchedulesReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SchedulesReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SchedulesReply) GetData() *SchedulesReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type OperationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size      int32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Id        int64   `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	StartTime int64   `protobuf:"varint,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   int64   `protobuf:"varint,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Type      *string `protobuf:"bytes,6,opt,name=type,proto3,oneof" json:"type,omitempty"`
}

func (x *OperationsRequest) Reset() {
	*x = OperationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationsRequest) ProtoMessage() {}

func (x *OperationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationsRequest.ProtoReflect.Descriptor instead.
func (*OperationsRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{40}
}

func (x *OperationsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *OperationsRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *OperationsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OperationsRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *OperationsRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *OperationsRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

type Operation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type        string           `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Status      string           `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	SourceId    int64            `protobuf:"varint,4,opt,name=sourceId,proto3" json:"sourceId,omitempty"`
	TenantId    int64            `protobuf:"varint,5,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	MerchantId  int64            `protobuf:"varint,6,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	AvatarId    int64            `protobuf:"varint,7,opt,name=avatarId,proto3" json:"avatarId,omitempty"`
	Avatar      *Avatar          `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	From        string           `protobuf:"bytes,9,opt,name=from,proto3" json:"from,omitempty"`
	Content     *structpb.Struct `protobuf:"bytes,10,opt,name=content,proto3" json:"content,omitempty"`
	CreatedTime float64          `protobuf:"fixed64,13,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime float64          `protobuf:"fixed64,14,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
}

func (x *Operation) Reset() {
	*x = Operation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operation) ProtoMessage() {}

func (x *Operation) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operation.ProtoReflect.Descriptor instead.
func (*Operation) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{41}
}

func (x *Operation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Operation) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Operation) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Operation) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *Operation) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *Operation) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *Operation) GetAvatarId() int64 {
	if x != nil {
		return x.AvatarId
	}
	return 0
}

func (x *Operation) GetAvatar() *Avatar {
	if x != nil {
		return x.Avatar
	}
	return nil
}

func (x *Operation) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *Operation) GetContent() *structpb.Struct {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Operation) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *Operation) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

type OperationsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *OperationsReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *OperationsReply) Reset() {
	*x = OperationsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationsReply) ProtoMessage() {}

func (x *OperationsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationsReply.ProtoReflect.Descriptor instead.
func (*OperationsReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{42}
}

func (x *OperationsReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *OperationsReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *OperationsReply) GetData() *OperationsReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type RemoteLogfilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Modules []string `protobuf:"bytes,2,rep,name=modules,proto3" json:"modules,omitempty"`
}

func (x *RemoteLogfilesRequest) Reset() {
	*x = RemoteLogfilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteLogfilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteLogfilesRequest) ProtoMessage() {}

func (x *RemoteLogfilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteLogfilesRequest.ProtoReflect.Descriptor instead.
func (*RemoteLogfilesRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{43}
}

func (x *RemoteLogfilesRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RemoteLogfilesRequest) GetModules() []string {
	if x != nil {
		return x.Modules
	}
	return nil
}

type UploadLogfilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64                        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Files []*UploadLogfilesRequestFile `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *UploadLogfilesRequest) Reset() {
	*x = UploadLogfilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadLogfilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadLogfilesRequest) ProtoMessage() {}

func (x *UploadLogfilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadLogfilesRequest.ProtoReflect.Descriptor instead.
func (*UploadLogfilesRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{44}
}

func (x *UploadLogfilesRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UploadLogfilesRequest) GetFiles() []*UploadLogfilesRequestFile {
	if x != nil {
		return x.Files
	}
	return nil
}

type ListLogfilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size      int32  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Id        int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	StartTime int64  `protobuf:"varint,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   int64  `protobuf:"varint,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Modules   string `protobuf:"bytes,6,opt,name=modules,proto3" json:"modules,omitempty"`
}

func (x *ListLogfilesRequest) Reset() {
	*x = ListLogfilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLogfilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLogfilesRequest) ProtoMessage() {}

func (x *ListLogfilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLogfilesRequest.ProtoReflect.Descriptor instead.
func (*ListLogfilesRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{45}
}

func (x *ListLogfilesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListLogfilesRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListLogfilesRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListLogfilesRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListLogfilesRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListLogfilesRequest) GetModules() string {
	if x != nil {
		return x.Modules
	}
	return ""
}

type Logfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Module      string  `protobuf:"bytes,2,opt,name=module,proto3" json:"module,omitempty"`
	BootIndex   int32   `protobuf:"varint,3,opt,name=bootIndex,proto3" json:"bootIndex,omitempty"`
	Status      int32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	DeviceId    int64   `protobuf:"varint,5,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	Size        int64   `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`
	TenantId    int64   `protobuf:"varint,7,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	MerchantId  int64   `protobuf:"varint,8,opt,name=merchantId,proto3" json:"merchantId,omitempty"`
	EndTime     float64 `protobuf:"fixed64,9,opt,name=endTime,proto3" json:"endTime,omitempty"`
	StartTime   float64 `protobuf:"fixed64,10,opt,name=startTime,proto3" json:"startTime,omitempty"`
	CreatedTime float64 `protobuf:"fixed64,11,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime float64 `protobuf:"fixed64,12,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
}

func (x *Logfile) Reset() {
	*x = Logfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Logfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Logfile) ProtoMessage() {}

func (x *Logfile) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Logfile.ProtoReflect.Descriptor instead.
func (*Logfile) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{46}
}

func (x *Logfile) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Logfile) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *Logfile) GetBootIndex() int32 {
	if x != nil {
		return x.BootIndex
	}
	return 0
}

func (x *Logfile) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Logfile) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *Logfile) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Logfile) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *Logfile) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *Logfile) GetEndTime() float64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Logfile) GetStartTime() float64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Logfile) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *Logfile) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

type LogfilesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *LogfilesReplyListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *LogfilesReply) Reset() {
	*x = LogfilesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogfilesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogfilesReply) ProtoMessage() {}

func (x *LogfilesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogfilesReply.ProtoReflect.Descriptor instead.
func (*LogfilesReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{47}
}

func (x *LogfilesReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LogfilesReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogfilesReply) GetData() *LogfilesReplyListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DownloadLogfilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FileId int64 `protobuf:"varint,2,opt,name=fileId,proto3" json:"fileId,omitempty"`
}

func (x *DownloadLogfilesRequest) Reset() {
	*x = DownloadLogfilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadLogfilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadLogfilesRequest) ProtoMessage() {}

func (x *DownloadLogfilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadLogfilesRequest.ProtoReflect.Descriptor instead.
func (*DownloadLogfilesRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{48}
}

func (x *DownloadLogfilesRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DownloadLogfilesRequest) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

type DownloadReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *DownloadReplyDowndata `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DownloadReply) Reset() {
	*x = DownloadReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadReply) ProtoMessage() {}

func (x *DownloadReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadReply.ProtoReflect.Descriptor instead.
func (*DownloadReply) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{49}
}

func (x *DownloadReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DownloadReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DownloadReply) GetData() *DownloadReplyDowndata {
	if x != nil {
		return x.Data
	}
	return nil
}

type CallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Did         int64                   `protobuf:"varint,1,opt,name=did,proto3" json:"did,omitempty"`
	Id          string                  `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	ClientId    string                  `protobuf:"bytes,3,opt,name=clientId,proto3" json:"clientId,omitempty"`
	ExecuteTime int64                   `protobuf:"varint,4,opt,name=executeTime,proto3" json:"executeTime,omitempty"`
	Body        *CallbackRequestPayload `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *CallbackRequest) Reset() {
	*x = CallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackRequest) ProtoMessage() {}

func (x *CallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackRequest.ProtoReflect.Descriptor instead.
func (*CallbackRequest) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{50}
}

func (x *CallbackRequest) GetDid() int64 {
	if x != nil {
		return x.Did
	}
	return 0
}

func (x *CallbackRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CallbackRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *CallbackRequest) GetExecuteTime() int64 {
	if x != nil {
		return x.ExecuteTime
	}
	return 0
}

func (x *CallbackRequest) GetBody() *CallbackRequestPayload {
	if x != nil {
		return x.Body
	}
	return nil
}

type CommonReplyCommonData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CommonReplyCommonData) Reset() {
	*x = CommonReplyCommonData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReplyCommonData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReplyCommonData) ProtoMessage() {}

func (x *CommonReplyCommonData) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReplyCommonData.ProtoReflect.Descriptor instead.
func (*CommonReplyCommonData) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{2, 0}
}

func (x *CommonReplyCommonData) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type DeploymentImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceImg string  `protobuf:"bytes,1,opt,name=deviceImg,proto3" json:"deviceImg,omitempty"`
	EnvImg    string  `protobuf:"bytes,2,opt,name=envImg,proto3" json:"envImg,omitempty"`
	ShopImg   *string `protobuf:"bytes,3,opt,name=shopImg,proto3,oneof" json:"shopImg,omitempty"`
}

func (x *DeploymentImage) Reset() {
	*x = DeploymentImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentImage) ProtoMessage() {}

func (x *DeploymentImage) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentImage.ProtoReflect.Descriptor instead.
func (*DeploymentImage) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{4, 0}
}

func (x *DeploymentImage) GetDeviceImg() string {
	if x != nil {
		return x.DeviceImg
	}
	return ""
}

func (x *DeploymentImage) GetEnvImg() string {
	if x != nil {
		return x.EnvImg
	}
	return ""
}

func (x *DeploymentImage) GetShopImg() string {
	if x != nil && x.ShopImg != nil {
		return *x.ShopImg
	}
	return ""
}

type DeploymentContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Contact string `protobuf:"bytes,2,opt,name=contact,proto3" json:"contact,omitempty"`
}

func (x *DeploymentContact) Reset() {
	*x = DeploymentContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentContact) ProtoMessage() {}

func (x *DeploymentContact) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentContact.ProtoReflect.Descriptor instead.
func (*DeploymentContact) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{4, 1}
}

func (x *DeploymentContact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeploymentContact) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

type DeploymentRelatedCh struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeploymentRelatedCh) Reset() {
	*x = DeploymentRelatedCh{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentRelatedCh) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentRelatedCh) ProtoMessage() {}

func (x *DeploymentRelatedCh) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentRelatedCh.ProtoReflect.Descriptor instead.
func (*DeploymentRelatedCh) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{4, 2}
}

func (x *DeploymentRelatedCh) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeploymentRelatedCh) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TakeoffRequestSimulator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsEnable bool      `protobuf:"varint,1,opt,name=isEnable,proto3" json:"isEnable,omitempty"`
	Lnglat   []float64 `protobuf:"fixed64,2,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
}

func (x *TakeoffRequestSimulator) Reset() {
	*x = TakeoffRequestSimulator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TakeoffRequestSimulator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TakeoffRequestSimulator) ProtoMessage() {}

func (x *TakeoffRequestSimulator) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TakeoffRequestSimulator.ProtoReflect.Descriptor instead.
func (*TakeoffRequestSimulator) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{8, 0}
}

func (x *TakeoffRequestSimulator) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *TakeoffRequestSimulator) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

type LaunchRequestSimulator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsEnable bool      `protobuf:"varint,1,opt,name=isEnable,proto3" json:"isEnable,omitempty"`
	Lnglat   []float64 `protobuf:"fixed64,2,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
}

func (x *LaunchRequestSimulator) Reset() {
	*x = LaunchRequestSimulator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaunchRequestSimulator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaunchRequestSimulator) ProtoMessage() {}

func (x *LaunchRequestSimulator) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaunchRequestSimulator.ProtoReflect.Descriptor instead.
func (*LaunchRequestSimulator) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{10, 0}
}

func (x *LaunchRequestSimulator) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *LaunchRequestSimulator) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

type ControlRequestOther struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index *string `protobuf:"bytes,1,opt,name=index,proto3,oneof" json:"index,omitempty"`
}

func (x *ControlRequestOther) Reset() {
	*x = ControlRequestOther{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ControlRequestOther) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlRequestOther) ProtoMessage() {}

func (x *ControlRequestOther) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlRequestOther.ProtoReflect.Descriptor instead.
func (*ControlRequestOther) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{11, 0}
}

func (x *ControlRequestOther) GetIndex() string {
	if x != nil && x.Index != nil {
		return *x.Index
	}
	return ""
}

type ListReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32         `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32         `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32         `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*DeviceItem `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListReplyListData) Reset() {
	*x = ListReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReplyListData) ProtoMessage() {}

func (x *ListReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReplyListData.ProtoReflect.Descriptor instead.
func (*ListReplyListData) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{32, 0}
}

func (x *ListReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListReplyListData) GetList() []*DeviceItem {
	if x != nil {
		return x.List
	}
	return nil
}

type EventsReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32                       `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32                       `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32                       `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*EventsReplyListDataEvent `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *EventsReplyListData) Reset() {
	*x = EventsReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventsReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventsReplyListData) ProtoMessage() {}

func (x *EventsReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventsReplyListData.ProtoReflect.Descriptor instead.
func (*EventsReplyListData) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{34, 0}
}

func (x *EventsReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *EventsReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *EventsReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *EventsReplyListData) GetList() []*EventsReplyListDataEvent {
	if x != nil {
		return x.List
	}
	return nil
}

type EventsReplyListDataEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sn           string  `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	Type         string  `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Code         string  `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	Level        int32   `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	Description  string  `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	CreatedTime  float64 `protobuf:"fixed64,7,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime  float64 `protobuf:"fixed64,8,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	OccurredTime float64 `protobuf:"fixed64,9,opt,name=occurredTime,proto3" json:"occurredTime,omitempty"`
}

func (x *EventsReplyListDataEvent) Reset() {
	*x = EventsReplyListDataEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventsReplyListDataEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventsReplyListDataEvent) ProtoMessage() {}

func (x *EventsReplyListDataEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventsReplyListDataEvent.ProtoReflect.Descriptor instead.
func (*EventsReplyListDataEvent) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{34, 0, 0}
}

func (x *EventsReplyListDataEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EventsReplyListDataEvent) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *EventsReplyListDataEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *EventsReplyListDataEvent) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *EventsReplyListDataEvent) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *EventsReplyListDataEvent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EventsReplyListDataEvent) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *EventsReplyListDataEvent) GetUpdatedTime() float64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *EventsReplyListDataEvent) GetOccurredTime() float64 {
	if x != nil {
		return x.OccurredTime
	}
	return 0
}

type DatalogsReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32            `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32            `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32            `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*DeviceDatalog `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *DatalogsReplyListData) Reset() {
	*x = DatalogsReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DatalogsReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DatalogsReplyListData) ProtoMessage() {}

func (x *DatalogsReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DatalogsReplyListData.ProtoReflect.Descriptor instead.
func (*DatalogsReplyListData) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{36, 0}
}

func (x *DatalogsReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DatalogsReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DatalogsReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DatalogsReplyListData) GetList() []*DeviceDatalog {
	if x != nil {
		return x.List
	}
	return nil
}

type SchedulesReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*SchedulesReplyTimer `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SchedulesReplyListData) Reset() {
	*x = SchedulesReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchedulesReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchedulesReplyListData) ProtoMessage() {}

func (x *SchedulesReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchedulesReplyListData.ProtoReflect.Descriptor instead.
func (*SchedulesReplyListData) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{39, 0}
}

func (x *SchedulesReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SchedulesReplyListData) GetList() []*SchedulesReplyTimer {
	if x != nil {
		return x.List
	}
	return nil
}

type SchedulesReplyTimer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Runday  int32   `protobuf:"varint,1,opt,name=runday,proto3" json:"runday,omitempty"`
	Moments []int32 `protobuf:"varint,2,rep,packed,name=moments,proto3" json:"moments,omitempty"`
}

func (x *SchedulesReplyTimer) Reset() {
	*x = SchedulesReplyTimer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchedulesReplyTimer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchedulesReplyTimer) ProtoMessage() {}

func (x *SchedulesReplyTimer) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchedulesReplyTimer.ProtoReflect.Descriptor instead.
func (*SchedulesReplyTimer) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{39, 1}
}

func (x *SchedulesReplyTimer) GetRunday() int32 {
	if x != nil {
		return x.Runday
	}
	return 0
}

func (x *SchedulesReplyTimer) GetMoments() []int32 {
	if x != nil {
		return x.Moments
	}
	return nil
}

type OperationsReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32        `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32        `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32        `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*Operation `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *OperationsReplyListData) Reset() {
	*x = OperationsReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationsReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationsReplyListData) ProtoMessage() {}

func (x *OperationsReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationsReplyListData.ProtoReflect.Descriptor instead.
func (*OperationsReplyListData) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{42, 0}
}

func (x *OperationsReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *OperationsReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *OperationsReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *OperationsReplyListData) GetList() []*Operation {
	if x != nil {
		return x.List
	}
	return nil
}

type UploadLogfilesRequestFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Module    string `protobuf:"bytes,1,opt,name=module,proto3" json:"module,omitempty"`
	BootIndex int32  `protobuf:"varint,2,opt,name=bootIndex,proto3" json:"bootIndex,omitempty"`
	Size      int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	StartTime int64  `protobuf:"varint,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   int64  `protobuf:"varint,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *UploadLogfilesRequestFile) Reset() {
	*x = UploadLogfilesRequestFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadLogfilesRequestFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadLogfilesRequestFile) ProtoMessage() {}

func (x *UploadLogfilesRequestFile) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadLogfilesRequestFile.ProtoReflect.Descriptor instead.
func (*UploadLogfilesRequestFile) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{44, 0}
}

func (x *UploadLogfilesRequestFile) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *UploadLogfilesRequestFile) GetBootIndex() int32 {
	if x != nil {
		return x.BootIndex
	}
	return 0
}

func (x *UploadLogfilesRequestFile) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *UploadLogfilesRequestFile) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *UploadLogfilesRequestFile) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type LogfilesReplyListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32      `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32      `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Total int32      `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List  []*Logfile `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *LogfilesReplyListData) Reset() {
	*x = LogfilesReplyListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogfilesReplyListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogfilesReplyListData) ProtoMessage() {}

func (x *LogfilesReplyListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogfilesReplyListData.ProtoReflect.Descriptor instead.
func (*LogfilesReplyListData) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{47, 0}
}

func (x *LogfilesReplyListData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *LogfilesReplyListData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *LogfilesReplyListData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *LogfilesReplyListData) GetList() []*Logfile {
	if x != nil {
		return x.List
	}
	return nil
}

type DownloadReplyDowndata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url         string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Fingerprint string `protobuf:"bytes,2,opt,name=fingerprint,proto3" json:"fingerprint,omitempty"`
}

func (x *DownloadReplyDowndata) Reset() {
	*x = DownloadReplyDowndata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadReplyDowndata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadReplyDowndata) ProtoMessage() {}

func (x *DownloadReplyDowndata) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadReplyDowndata.ProtoReflect.Descriptor instead.
func (*DownloadReplyDowndata) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{49, 0}
}

func (x *DownloadReplyDowndata) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DownloadReplyDowndata) GetFingerprint() string {
	if x != nil {
		return x.Fingerprint
	}
	return ""
}

type CallbackRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Action   string `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	SourceId string `protobuf:"bytes,3,opt,name=sourceId,proto3" json:"sourceId,omitempty"`
}

func (x *CallbackRequestPayload) Reset() {
	*x = CallbackRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_devices_v1_device_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackRequestPayload) ProtoMessage() {}

func (x *CallbackRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_devices_v1_device_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackRequestPayload.ProtoReflect.Descriptor instead.
func (*CallbackRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_devices_v1_device_proto_rawDescGZIP(), []int{50, 0}
}

func (x *CallbackRequestPayload) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CallbackRequestPayload) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *CallbackRequestPayload) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

var File_api_devices_v1_device_proto protoreflect.FileDescriptor

var file_api_devices_v1_device_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x01, 0x0a, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x6f, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x72, 0x6f, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x2c, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x24, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x76, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x86,
	0x05, 0x0a, 0x0a, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x38, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x61,
	0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x01, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c,
	0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12, 0x3e, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x43, 0x68, 0x49, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x43, 0x68, 0x49, 0x64, 0x73, 0x12, 0x44, 0x0a, 0x0a, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x43, 0x68, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x43, 0x68, 0x52, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x68, 0x73, 0x1a,
	0x68, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6d, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6d, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x76, 0x49, 0x6d, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x76, 0x49, 0x6d, 0x67, 0x12, 0x1d,
	0x0a, 0x07, 0x73, 0x68, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x07, 0x73, 0x68, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x73, 0x68, 0x6f, 0x70, 0x49, 0x6d, 0x67, 0x1a, 0x37, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x1a, 0x2f, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x68, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x3a, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x5b, 0x0a, 0x0d,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3a, 0x0a,
	0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x4b, 0x0a, 0x0f, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8a, 0x02, 0x0a, 0x0e, 0x54, 0x61, 0x6b, 0x65, 0x6f,
	0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x69, 0x72,
	0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x69,
	0x72, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72,
	0x69, 0x74, 0x68, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6c, 0x67, 0x6f,
	0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x57, 0x0a, 0x0f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x61, 0x6b, 0x65, 0x6f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x1a, 0x3f,
	0x0a, 0x09, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x37, 0x0a, 0x0d, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xc6, 0x03, 0x0a,
	0x0d, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x43,
	0x4c, 0x6f, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x72, 0x43, 0x4c, 0x6f, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x6d, 0x64, 0x65, 0x72, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0b, 0x63, 0x6d, 0x64, 0x65, 0x72, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x41, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0e, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x41, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x01,
	0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12, 0x56, 0x0a, 0x0f, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x1a, 0x3f, 0x0a, 0x09, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67,
	0x6c, 0x61, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61,
	0x74, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xa6, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x12, 0x3c, 0x0a, 0x06, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x73,
	0x1a, 0x2c, 0x0a, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x55,
	0x0a, 0x0f, 0x41, 0x65, 0x72, 0x6f, 0x6d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x65, 0x72,
	0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x65, 0x72,
	0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x5b, 0x0a, 0x0b, 0x4c, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x65, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x65, 0x6e, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69,
	0x76, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65,
	0x49, 0x64, 0x22, 0x89, 0x01, 0x0a, 0x0b, 0x5a, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x65, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x65, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x76, 0x65,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x61,
	0x0a, 0x0d, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x76,
	0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49,
	0x64, 0x22, 0x64, 0x0a, 0x0e, 0x43, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0x53, 0x0a, 0x0b, 0x46, 0x72, 0x65, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x22, 0x94, 0x01, 0x0a,
	0x0c, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x72, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c,
	0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67,
	0x6c, 0x61, 0x74, 0x22, 0x35, 0x0a, 0x0d, 0x4f, 0x72, 0x62, 0x65, 0x65, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x22, 0x7d, 0x0a, 0x0d, 0x4c, 0x6f,
	0x6f, 0x6b, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c,
	0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x6f, 0x63,
	0x6b, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x22, 0x4e, 0x0a, 0x0e, 0x50, 0x69, 0x63,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x0c, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x16, 0x0a, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xbe, 0x01, 0x0a, 0x0c, 0x44, 0x72, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1b, 0x0a, 0x06, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x48, 0x00, 0x52, 0x06, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a,
	0x06, 0x79, 0x53, 0x70, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52,
	0x06, 0x79, 0x53, 0x70, 0x65, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x68, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02, 0x52, 0x06, 0x68, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x77, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x03, 0x52, 0x06, 0x77, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x79, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x68,
	0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x77, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x22, 0x8e, 0x01, 0x0a, 0x0c, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x4d, 0x0a, 0x0d, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x22, 0x53, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x79, 0x6d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61,
	0x79, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x79, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x46, 0x0a, 0x0e, 0x41, 0x6c, 0x67, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x6c, 0x67, 0x66, 0x6c,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x61, 0x6c, 0x67, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6d, 0x0a,
	0x0e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xaa, 0x06, 0x0a,
	0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x61, 0x62, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x63, 0x61, 0x62, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x6c, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3a, 0x0a,
	0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x66, 0x69, 0x72,
	0x6d, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x09, 0x75, 0x70, 0x70, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x09, 0x75, 0x70, 0x70, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x09, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x37, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x0a, 0x73, 0x75, 0x62, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x75, 0x70, 0x70, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x6b, 0x0a, 0x0b, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xcb, 0x02, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30,
	0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x4e, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0x9a, 0x7c, 0x2e, 0x9a, 0x43, 0x06, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0xa2, 0x43, 0x22, 0x73, 0x6e, 0x2c, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x48, 0x00, 0x52, 0x06, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x02, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x88, 0x01,
	0x01, 0x12, 0x29, 0x0a, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x0d, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x3a, 0x03, 0x88, 0x43,
	0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xeb, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x78, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0xe8, 0x01, 0x0a, 0x0d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c,
	0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x73, 0x3a, 0x03, 0x88, 0x43, 0x01, 0x22, 0xf2, 0x03,
	0x0a, 0x0b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xfa, 0x02, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x3e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x1a, 0xef, 0x01, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x0f, 0x44, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30,
	0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x3a, 0x03, 0x88, 0x43,
	0x01, 0x22, 0xf6, 0x01, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c,
	0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x7b, 0x0a,
	0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x93, 0x01, 0x0a, 0x0d, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x6c, 0x54, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x6c, 0x54, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xb1, 0x01, 0x0a, 0x10, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x09, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x09, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x3a, 0x03, 0x88, 0x43, 0x00, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0x92, 0x02, 0x0a, 0x0e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x5a, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x38, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x39,
	0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x75, 0x6e, 0x64, 0x61,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x07, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xe0, 0x01, 0x0a, 0x11, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0xfa,
	0x42, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x3a, 0x03,
	0x88, 0x43, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xf6, 0x02, 0x0a,
	0x09, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x31,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xf6, 0x01, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x77, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x41,
	0x0a, 0x15, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x22, 0xf4, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x6f, 0x67, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x40, 0x0a, 0x05, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x4c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x1a, 0x88, 0x01,
	0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xda, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73,
	0x74, 0x4c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11,
	0xfa, 0x42, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d,
	0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xfa, 0x42, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x30, 0x30, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73,
	0x3a, 0x03, 0x88, 0x43, 0x01, 0x22, 0xcf, 0x02, 0x0a, 0x07, 0x4c, 0x6f, 0x67, 0x66, 0x69, 0x6c,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6f, 0x6f,
	0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x62, 0x6f,
	0x6f, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xf0, 0x01, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x75, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2b, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x4e, 0x0a, 0x17, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xb9, 0x01, 0x0a, 0x0d, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3e, 0x0a, 0x08, 0x64, 0x6f, 0x77, 0x6e, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x22, 0x8e, 0x02, 0x0a, 0x0f, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x03, 0x64, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x03, 0x64, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x1a, 0x51, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x32, 0x93, 0x28, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x60, 0x0a, 0x09, 0x41, 0x64, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14,
	0x22, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x3a, 0x01, 0x2a, 0x12, 0x5d, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x11, 0x12, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x12, 0x65, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x7f, 0x0a, 0x0c, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x1a, 0x1f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x3a,
	0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x7b, 0x0a, 0x0e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x1a, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x78, 0x0a, 0x0d, 0x54, 0x61, 0x6b, 0x65,
	0x6f, 0x66, 0x66, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x6b, 0x65, 0x6f,
	0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x21, 0x1a, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x61, 0x6b, 0x65, 0x6f, 0x66, 0x66, 0x3a,
	0x01, 0x2a, 0x12, 0x75, 0x0a, 0x0c, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x1a, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f,
	0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x3a, 0x01, 0x2a, 0x12, 0x73, 0x0a, 0x0b, 0x43, 0x72, 0x61,
	0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x1a,
	0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x72, 0x61, 0x73, 0x68, 0x3a, 0x01, 0x2a, 0x12, 0x75,
	0x0a, 0x0c, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x20, 0x1a, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x79, 0x0a, 0x0e, 0x41, 0x75, 0x74, 0x6f, 0x62, 0x61, 0x63,
	0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x1a, 0x1d,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x62, 0x61, 0x63, 0x6b, 0x3a, 0x01, 0x2a,
	0x12, 0x73, 0x0a, 0x0b, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x1a, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x72, 0x6f, 0x75,
	0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x78, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x1a, 0x1c,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x3a, 0x01, 0x2a, 0x12,
	0x78, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x1a, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x78, 0x0a, 0x0e, 0x41, 0x65, 0x72,
	0x6f, 0x6d, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x65, 0x72,
	0x6f, 0x6d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x22, 0x1a, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x65, 0x72, 0x6f, 0x6d, 0x6f, 0x64, 0x65,
	0x3a, 0x01, 0x2a, 0x12, 0x6f, 0x0a, 0x0a, 0x4c, 0x65, 0x6e, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x24,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x1a, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6c, 0x65, 0x6e,
	0x73, 0x3a, 0x01, 0x2a, 0x12, 0x6f, 0x0a, 0x0a, 0x5a, 0x6f, 0x6f, 0x6d, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x5a, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x1a, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6f, 0x6d, 0x3a, 0x01, 0x2a, 0x12, 0x75, 0x0a, 0x0c, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x1a, 0x1b, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x2f, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x3a, 0x01, 0x2a, 0x12, 0x78, 0x0a, 0x0d,
	0x43, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x21, 0x1a, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x6c, 0x61, 0x72,
	0x69, 0x74, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x6f, 0x0a, 0x0a, 0x46, 0x72, 0x65, 0x65, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x1a, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f,
	0x66, 0x72, 0x65, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x72, 0x0a, 0x0b, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x1a, 0x1a, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x2f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x76, 0x0a, 0x0d, 0x52,
	0x65, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x21, 0x1a, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x3a, 0x01, 0x2a, 0x12, 0x72, 0x0a, 0x0b, 0x4f, 0x72, 0x62, 0x69, 0x74, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x1a, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f,
	0x72, 0x62, 0x69, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x75, 0x0a, 0x0c, 0x4f, 0x72, 0x62, 0x65, 0x65,
	0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x62, 0x65, 0x65, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x1a, 0x1b,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6f, 0x72, 0x62, 0x65, 0x65, 0x64, 0x3a, 0x01, 0x2a, 0x12, 0x75,
	0x0a, 0x0c, 0x4c, 0x6f, 0x6f, 0x6b, 0x61, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x6f, 0x6b, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x20, 0x1a, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6c, 0x6f, 0x6f, 0x6b,
	0x61, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x78, 0x0a, 0x0d, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x1a, 0x1c,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x3a, 0x01, 0x2a, 0x12,
	0x72, 0x0a, 0x0b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1f, 0x1a, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x3a, 0x01, 0x2a, 0x12, 0x72, 0x0a, 0x0b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x1a, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x73, 0x0a, 0x0b, 0x53, 0x68, 0x6f, 0x75, 0x74,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x1a, 0x1a, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b,
	0x69, 0x64, 0x7d, 0x2f, 0x73, 0x68, 0x6f, 0x75, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x72, 0x0a, 0x0b,
	0x53, 0x70, 0x65, 0x61, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65,
	0x61, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1f, 0x1a, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x3a, 0x01, 0x2a,
	0x12, 0x75, 0x0a, 0x0c, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x1a, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x7b, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x79, 0x6d,
	0x6f, 0x64, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x6d,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x22, 0x1a, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x6d, 0x6f, 0x64,
	0x65, 0x3a, 0x01, 0x2a, 0x12, 0x75, 0x0a, 0x0d, 0x41, 0x6c, 0x67, 0x66, 0x6c, 0x6f, 0x77, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x67, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x1a, 0x1c, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d,
	0x2f, 0x61, 0x6c, 0x67, 0x66, 0x6c, 0x6f, 0x77, 0x3a, 0x01, 0x2a, 0x12, 0x68, 0x0a, 0x0c, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x2a,
	0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x6f, 0x0a, 0x0c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x77, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x73, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f,
	0x12, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x73, 0x12,
	0x7b, 0x0a, 0x0f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x7f, 0x0a, 0x10,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x81, 0x01,
	0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x1a,
	0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x3a, 0x01,
	0x2a, 0x12, 0x81, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x6f, 0x67, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x6f, 0x67, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x22, 0x22, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6c, 0x6f, 0x67, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x79, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1f, 0x12, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6c, 0x6f, 0x67, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x89, 0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x6f, 0x67,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x6f,
	0x67, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x28, 0x12, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x6c, 0x6f, 0x67, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x7d, 0x12, 0x7e, 0x0a, 0x0e,
	0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x28, 0x22, 0x23, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x76, 0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x64, 0x69, 0x64, 0x7d,
	0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x3a, 0x01, 0x2a, 0x12, 0x7f, 0x0a, 0x0d,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2a, 0x22, 0x25, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x64, 0x69, 0x64, 0x7d, 0x2f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x42, 0x42, 0x0a,
	0x0e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_devices_v1_device_proto_rawDescOnce sync.Once
	file_api_devices_v1_device_proto_rawDescData = file_api_devices_v1_device_proto_rawDesc
)

func file_api_devices_v1_device_proto_rawDescGZIP() []byte {
	file_api_devices_v1_device_proto_rawDescOnce.Do(func() {
		file_api_devices_v1_device_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_devices_v1_device_proto_rawDescData)
	})
	return file_api_devices_v1_device_proto_rawDescData
}

var file_api_devices_v1_device_proto_msgTypes = make([]protoimpl.MessageInfo, 69)
var file_api_devices_v1_device_proto_goTypes = []interface{}{
	(*Avatar)(nil),                    // 0: api.devices.v1.Avatar
	(*CommonRequest)(nil),             // 1: api.devices.v1.CommonRequest
	(*CommonReply)(nil),               // 2: api.devices.v1.CommonReply
	(*Property)(nil),                  // 3: api.devices.v1.Property
	(*Deployment)(nil),                // 4: api.devices.v1.Deployment
	(*AddRequest)(nil),                // 5: api.devices.v1.AddRequest
	(*UpdateRequest)(nil),             // 6: api.devices.v1.UpdateRequest
	(*PropertyRequest)(nil),           // 7: api.devices.v1.PropertyRequest
	(*TakeoffRequest)(nil),            // 8: api.devices.v1.TakeoffRequest
	(*ReturnRequest)(nil),             // 9: api.devices.v1.ReturnRequest
	(*LaunchRequest)(nil),             // 10: api.devices.v1.LaunchRequest
	(*ControlRequest)(nil),            // 11: api.devices.v1.ControlRequest
	(*AeromodeRequest)(nil),           // 12: api.devices.v1.AeromodeRequest
	(*LensRequest)(nil),               // 13: api.devices.v1.LensRequest
	(*ZoomRequest)(nil),               // 14: api.devices.v1.ZoomRequest
	(*GimbalRequest)(nil),             // 15: api.devices.v1.GimbalRequest
	(*ClarityRequest)(nil),            // 16: api.devices.v1.ClarityRequest
	(*FreeRequest)(nil),               // 17: api.devices.v1.FreeRequest
	(*PointRequest)(nil),              // 18: api.devices.v1.PointRequest
	(*OrbeedRequest)(nil),             // 19: api.devices.v1.OrbeedRequest
	(*LookatRequest)(nil),             // 20: api.devices.v1.LookatRequest
	(*PictureRequest)(nil),            // 21: api.devices.v1.PictureRequest
	(*VideoRequest)(nil),              // 22: api.devices.v1.VideoRequest
	(*DriveRequest)(nil),              // 23: api.devices.v1.DriveRequest
	(*SpeakRequest)(nil),              // 24: api.devices.v1.SpeakRequest
	(*VolumeRequest)(nil),             // 25: api.devices.v1.VolumeRequest
	(*PlaymodeRequest)(nil),           // 26: api.devices.v1.PlaymodeRequest
	(*AlgflowRequest)(nil),            // 27: api.devices.v1.AlgflowRequest
	(*OperationReply)(nil),            // 28: api.devices.v1.OperationReply
	(*DeviceItem)(nil),                // 29: api.devices.v1.DeviceItem
	(*DeviceReply)(nil),               // 30: api.devices.v1.DeviceReply
	(*ListRequest)(nil),               // 31: api.devices.v1.ListRequest
	(*ListReply)(nil),                 // 32: api.devices.v1.ListReply
	(*EventsRequest)(nil),             // 33: api.devices.v1.EventsRequest
	(*EventsReply)(nil),               // 34: api.devices.v1.EventsReply
	(*DatalogsRequest)(nil),           // 35: api.devices.v1.DatalogsRequest
	(*DatalogsReply)(nil),             // 36: api.devices.v1.DatalogsReply
	(*DeviceDatalog)(nil),             // 37: api.devices.v1.DeviceDatalog
	(*SchedulesRequest)(nil),          // 38: api.devices.v1.SchedulesRequest
	(*SchedulesReply)(nil),            // 39: api.devices.v1.SchedulesReply
	(*OperationsRequest)(nil),         // 40: api.devices.v1.OperationsRequest
	(*Operation)(nil),                 // 41: api.devices.v1.Operation
	(*OperationsReply)(nil),           // 42: api.devices.v1.OperationsReply
	(*RemoteLogfilesRequest)(nil),     // 43: api.devices.v1.RemoteLogfilesRequest
	(*UploadLogfilesRequest)(nil),     // 44: api.devices.v1.UploadLogfilesRequest
	(*ListLogfilesRequest)(nil),       // 45: api.devices.v1.ListLogfilesRequest
	(*Logfile)(nil),                   // 46: api.devices.v1.Logfile
	(*LogfilesReply)(nil),             // 47: api.devices.v1.LogfilesReply
	(*DownloadLogfilesRequest)(nil),   // 48: api.devices.v1.DownloadLogfilesRequest
	(*DownloadReply)(nil),             // 49: api.devices.v1.DownloadReply
	(*CallbackRequest)(nil),           // 50: api.devices.v1.CallbackRequest
	(*CommonReplyCommonData)(nil),     // 51: api.devices.v1.CommonReply.commonData
	(*DeploymentImage)(nil),           // 52: api.devices.v1.Deployment.image
	(*DeploymentContact)(nil),         // 53: api.devices.v1.Deployment.contact
	(*DeploymentRelatedCh)(nil),       // 54: api.devices.v1.Deployment.relatedCh
	(*TakeoffRequestSimulator)(nil),   // 55: api.devices.v1.TakeoffRequest.simulator
	(*LaunchRequestSimulator)(nil),    // 56: api.devices.v1.LaunchRequest.simulator
	(*ControlRequestOther)(nil),       // 57: api.devices.v1.ControlRequest.other
	(*ListReplyListData)(nil),         // 58: api.devices.v1.ListReply.listData
	(*EventsReplyListData)(nil),       // 59: api.devices.v1.EventsReply.listData
	(*EventsReplyListDataEvent)(nil),  // 60: api.devices.v1.EventsReply.listData.event
	(*DatalogsReplyListData)(nil),     // 61: api.devices.v1.DatalogsReply.listData
	(*SchedulesReplyListData)(nil),    // 62: api.devices.v1.SchedulesReply.listData
	(*SchedulesReplyTimer)(nil),       // 63: api.devices.v1.SchedulesReply.timer
	(*OperationsReplyListData)(nil),   // 64: api.devices.v1.OperationsReply.listData
	(*UploadLogfilesRequestFile)(nil), // 65: api.devices.v1.UploadLogfilesRequest.file
	(*LogfilesReplyListData)(nil),     // 66: api.devices.v1.LogfilesReply.listData
	(*DownloadReplyDowndata)(nil),     // 67: api.devices.v1.DownloadReply.downdata
	(*CallbackRequestPayload)(nil),    // 68: api.devices.v1.CallbackRequest.payload
	(*structpb.Value)(nil),            // 69: google.protobuf.Value
	(*structpb.Struct)(nil),           // 70: google.protobuf.Struct
}
var file_api_devices_v1_device_proto_depIdxs = []int32{
	51, // 0: api.devices.v1.CommonReply.data:type_name -> api.devices.v1.CommonReply.commonData
	69, // 1: api.devices.v1.Property.value:type_name -> google.protobuf.Value
	52, // 2: api.devices.v1.Deployment.images:type_name -> api.devices.v1.Deployment.image
	53, // 3: api.devices.v1.Deployment.contacts:type_name -> api.devices.v1.Deployment.contact
	54, // 4: api.devices.v1.Deployment.relatedChs:type_name -> api.devices.v1.Deployment.relatedCh
	4,  // 5: api.devices.v1.AddRequest.deployment:type_name -> api.devices.v1.Deployment
	4,  // 6: api.devices.v1.UpdateRequest.deployment:type_name -> api.devices.v1.Deployment
	55, // 7: api.devices.v1.TakeoffRequest.simulateMission:type_name -> api.devices.v1.TakeoffRequest.simulator
	56, // 8: api.devices.v1.LaunchRequest.simulateMission:type_name -> api.devices.v1.LaunchRequest.simulator
	57, // 9: api.devices.v1.ControlRequest.others:type_name -> api.devices.v1.ControlRequest.other
	41, // 10: api.devices.v1.OperationReply.data:type_name -> api.devices.v1.Operation
	4,  // 11: api.devices.v1.DeviceItem.deployment:type_name -> api.devices.v1.Deployment
	70, // 12: api.devices.v1.DeviceItem.propData:type_name -> google.protobuf.Struct
	70, // 13: api.devices.v1.DeviceItem.extraData:type_name -> google.protobuf.Struct
	70, // 14: api.devices.v1.DeviceItem.subdevices:type_name -> google.protobuf.Struct
	29, // 15: api.devices.v1.DeviceReply.data:type_name -> api.devices.v1.DeviceItem
	58, // 16: api.devices.v1.ListReply.data:type_name -> api.devices.v1.ListReply.listData
	59, // 17: api.devices.v1.EventsReply.data:type_name -> api.devices.v1.EventsReply.listData
	61, // 18: api.devices.v1.DatalogsReply.data:type_name -> api.devices.v1.DatalogsReply.listData
	3,  // 19: api.devices.v1.DeviceDatalog.data:type_name -> api.devices.v1.Property
	62, // 20: api.devices.v1.SchedulesReply.data:type_name -> api.devices.v1.SchedulesReply.listData
	0,  // 21: api.devices.v1.Operation.avatar:type_name -> api.devices.v1.Avatar
	70, // 22: api.devices.v1.Operation.content:type_name -> google.protobuf.Struct
	64, // 23: api.devices.v1.OperationsReply.data:type_name -> api.devices.v1.OperationsReply.listData
	65, // 24: api.devices.v1.UploadLogfilesRequest.files:type_name -> api.devices.v1.UploadLogfilesRequest.file
	66, // 25: api.devices.v1.LogfilesReply.data:type_name -> api.devices.v1.LogfilesReply.listData
	67, // 26: api.devices.v1.DownloadReply.data:type_name -> api.devices.v1.DownloadReply.downdata
	68, // 27: api.devices.v1.CallbackRequest.body:type_name -> api.devices.v1.CallbackRequest.payload
	29, // 28: api.devices.v1.ListReply.listData.list:type_name -> api.devices.v1.DeviceItem
	60, // 29: api.devices.v1.EventsReply.listData.list:type_name -> api.devices.v1.EventsReply.listData.event
	37, // 30: api.devices.v1.DatalogsReply.listData.list:type_name -> api.devices.v1.DeviceDatalog
	63, // 31: api.devices.v1.SchedulesReply.listData.list:type_name -> api.devices.v1.SchedulesReply.timer
	41, // 32: api.devices.v1.OperationsReply.listData.list:type_name -> api.devices.v1.Operation
	46, // 33: api.devices.v1.LogfilesReply.listData.list:type_name -> api.devices.v1.Logfile
	5,  // 34: api.devices.v1.Device.AddDevice:input_type -> api.devices.v1.AddRequest
	31, // 35: api.devices.v1.Device.ListDevice:input_type -> api.devices.v1.ListRequest
	1,  // 36: api.devices.v1.Device.GetDevice:input_type -> api.devices.v1.CommonRequest
	6,  // 37: api.devices.v1.Device.UpdateDevice:input_type -> api.devices.v1.UpdateRequest
	7,  // 38: api.devices.v1.Device.PropertyDevice:input_type -> api.devices.v1.PropertyRequest
	8,  // 39: api.devices.v1.Device.TakeoffDevice:input_type -> api.devices.v1.TakeoffRequest
	10, // 40: api.devices.v1.Device.LaunchDevice:input_type -> api.devices.v1.LaunchRequest
	1,  // 41: api.devices.v1.Device.CrashDevice:input_type -> api.devices.v1.CommonRequest
	9,  // 42: api.devices.v1.Device.ReturnDevice:input_type -> api.devices.v1.ReturnRequest
	9,  // 43: api.devices.v1.Device.AutobackDevice:input_type -> api.devices.v1.ReturnRequest
	9,  // 44: api.devices.v1.Device.RouteDevice:input_type -> api.devices.v1.ReturnRequest
	11, // 45: api.devices.v1.Device.ControlDevice:input_type -> api.devices.v1.ControlRequest
	11, // 46: api.devices.v1.Device.ReleaseDevice:input_type -> api.devices.v1.ControlRequest
	12, // 47: api.devices.v1.Device.AeromodeDevice:input_type -> api.devices.v1.AeromodeRequest
	13, // 48: api.devices.v1.Device.LensDevice:input_type -> api.devices.v1.LensRequest
	14, // 49: api.devices.v1.Device.ZoomDevice:input_type -> api.devices.v1.ZoomRequest
	15, // 50: api.devices.v1.Device.GimbalDevice:input_type -> api.devices.v1.GimbalRequest
	16, // 51: api.devices.v1.Device.ClarityDevice:input_type -> api.devices.v1.ClarityRequest
	17, // 52: api.devices.v1.Device.FreeDevice:input_type -> api.devices.v1.FreeRequest
	18, // 53: api.devices.v1.Device.PointDevice:input_type -> api.devices.v1.PointRequest
	18, // 54: api.devices.v1.Device.RepointDevice:input_type -> api.devices.v1.PointRequest
	18, // 55: api.devices.v1.Device.OrbitDevice:input_type -> api.devices.v1.PointRequest
	19, // 56: api.devices.v1.Device.OrbeedDevice:input_type -> api.devices.v1.OrbeedRequest
	20, // 57: api.devices.v1.Device.LookatDevice:input_type -> api.devices.v1.LookatRequest
	21, // 58: api.devices.v1.Device.PictureDevice:input_type -> api.devices.v1.PictureRequest
	22, // 59: api.devices.v1.Device.VideoDevice:input_type -> api.devices.v1.VideoRequest
	23, // 60: api.devices.v1.Device.DriveDevice:input_type -> api.devices.v1.DriveRequest
	9,  // 61: api.devices.v1.Device.ShoutDevice:input_type -> api.devices.v1.ReturnRequest
	24, // 62: api.devices.v1.Device.SpeakDevice:input_type -> api.devices.v1.SpeakRequest
	25, // 63: api.devices.v1.Device.VolumeDevice:input_type -> api.devices.v1.VolumeRequest
	26, // 64: api.devices.v1.Device.PlaymodeDevice:input_type -> api.devices.v1.PlaymodeRequest
	27, // 65: api.devices.v1.Device.AlgflowDevice:input_type -> api.devices.v1.AlgflowRequest
	1,  // 66: api.devices.v1.Device.RemoveDevice:input_type -> api.devices.v1.CommonRequest
	33, // 67: api.devices.v1.Device.DeviceEvents:input_type -> api.devices.v1.EventsRequest
	35, // 68: api.devices.v1.Device.DeviceDatalogs:input_type -> api.devices.v1.DatalogsRequest
	38, // 69: api.devices.v1.Device.DeviceSchedules:input_type -> api.devices.v1.SchedulesRequest
	40, // 70: api.devices.v1.Device.DeviceOperations:input_type -> api.devices.v1.OperationsRequest
	43, // 71: api.devices.v1.Device.RemoteLogfiles:input_type -> api.devices.v1.RemoteLogfilesRequest
	44, // 72: api.devices.v1.Device.UploadLogfiles:input_type -> api.devices.v1.UploadLogfilesRequest
	45, // 73: api.devices.v1.Device.ListLogfiles:input_type -> api.devices.v1.ListLogfilesRequest
	48, // 74: api.devices.v1.Device.DownloadLogfile:input_type -> api.devices.v1.DownloadLogfilesRequest
	50, // 75: api.devices.v1.Device.CallbackDevice:input_type -> api.devices.v1.CallbackRequest
	50, // 76: api.devices.v1.Device.ExecuteDevice:input_type -> api.devices.v1.CallbackRequest
	2,  // 77: api.devices.v1.Device.AddDevice:output_type -> api.devices.v1.CommonReply
	32, // 78: api.devices.v1.Device.ListDevice:output_type -> api.devices.v1.ListReply
	30, // 79: api.devices.v1.Device.GetDevice:output_type -> api.devices.v1.DeviceReply
	2,  // 80: api.devices.v1.Device.UpdateDevice:output_type -> api.devices.v1.CommonReply
	28, // 81: api.devices.v1.Device.PropertyDevice:output_type -> api.devices.v1.OperationReply
	28, // 82: api.devices.v1.Device.TakeoffDevice:output_type -> api.devices.v1.OperationReply
	28, // 83: api.devices.v1.Device.LaunchDevice:output_type -> api.devices.v1.OperationReply
	28, // 84: api.devices.v1.Device.CrashDevice:output_type -> api.devices.v1.OperationReply
	28, // 85: api.devices.v1.Device.ReturnDevice:output_type -> api.devices.v1.OperationReply
	28, // 86: api.devices.v1.Device.AutobackDevice:output_type -> api.devices.v1.OperationReply
	28, // 87: api.devices.v1.Device.RouteDevice:output_type -> api.devices.v1.OperationReply
	28, // 88: api.devices.v1.Device.ControlDevice:output_type -> api.devices.v1.OperationReply
	28, // 89: api.devices.v1.Device.ReleaseDevice:output_type -> api.devices.v1.OperationReply
	2,  // 90: api.devices.v1.Device.AeromodeDevice:output_type -> api.devices.v1.CommonReply
	28, // 91: api.devices.v1.Device.LensDevice:output_type -> api.devices.v1.OperationReply
	28, // 92: api.devices.v1.Device.ZoomDevice:output_type -> api.devices.v1.OperationReply
	28, // 93: api.devices.v1.Device.GimbalDevice:output_type -> api.devices.v1.OperationReply
	28, // 94: api.devices.v1.Device.ClarityDevice:output_type -> api.devices.v1.OperationReply
	28, // 95: api.devices.v1.Device.FreeDevice:output_type -> api.devices.v1.OperationReply
	28, // 96: api.devices.v1.Device.PointDevice:output_type -> api.devices.v1.OperationReply
	28, // 97: api.devices.v1.Device.RepointDevice:output_type -> api.devices.v1.OperationReply
	28, // 98: api.devices.v1.Device.OrbitDevice:output_type -> api.devices.v1.OperationReply
	28, // 99: api.devices.v1.Device.OrbeedDevice:output_type -> api.devices.v1.OperationReply
	28, // 100: api.devices.v1.Device.LookatDevice:output_type -> api.devices.v1.OperationReply
	28, // 101: api.devices.v1.Device.PictureDevice:output_type -> api.devices.v1.OperationReply
	28, // 102: api.devices.v1.Device.VideoDevice:output_type -> api.devices.v1.OperationReply
	28, // 103: api.devices.v1.Device.DriveDevice:output_type -> api.devices.v1.OperationReply
	28, // 104: api.devices.v1.Device.ShoutDevice:output_type -> api.devices.v1.OperationReply
	28, // 105: api.devices.v1.Device.SpeakDevice:output_type -> api.devices.v1.OperationReply
	28, // 106: api.devices.v1.Device.VolumeDevice:output_type -> api.devices.v1.OperationReply
	28, // 107: api.devices.v1.Device.PlaymodeDevice:output_type -> api.devices.v1.OperationReply
	2,  // 108: api.devices.v1.Device.AlgflowDevice:output_type -> api.devices.v1.CommonReply
	2,  // 109: api.devices.v1.Device.RemoveDevice:output_type -> api.devices.v1.CommonReply
	34, // 110: api.devices.v1.Device.DeviceEvents:output_type -> api.devices.v1.EventsReply
	36, // 111: api.devices.v1.Device.DeviceDatalogs:output_type -> api.devices.v1.DatalogsReply
	39, // 112: api.devices.v1.Device.DeviceSchedules:output_type -> api.devices.v1.SchedulesReply
	42, // 113: api.devices.v1.Device.DeviceOperations:output_type -> api.devices.v1.OperationsReply
	28, // 114: api.devices.v1.Device.RemoteLogfiles:output_type -> api.devices.v1.OperationReply
	28, // 115: api.devices.v1.Device.UploadLogfiles:output_type -> api.devices.v1.OperationReply
	47, // 116: api.devices.v1.Device.ListLogfiles:output_type -> api.devices.v1.LogfilesReply
	49, // 117: api.devices.v1.Device.DownloadLogfile:output_type -> api.devices.v1.DownloadReply
	2,  // 118: api.devices.v1.Device.CallbackDevice:output_type -> api.devices.v1.CommonReply
	2,  // 119: api.devices.v1.Device.ExecuteDevice:output_type -> api.devices.v1.CommonReply
	77, // [77:120] is the sub-list for method output_type
	34, // [34:77] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_api_devices_v1_device_proto_init() }
func file_api_devices_v1_device_proto_init() {
	if File_api_devices_v1_device_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_devices_v1_device_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Avatar); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Property); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropertyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TakeoffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReturnRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaunchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ControlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AeromodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZoomRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GimbalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClarityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FreeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PointRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrbeedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LookatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PictureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpeakRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaymodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlgflowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DatalogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DatalogsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceDatalog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SchedulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SchedulesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteLogfilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadLogfilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLogfilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Logfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogfilesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadLogfilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReplyCommonData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentRelatedCh); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TakeoffRequestSimulator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaunchRequestSimulator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ControlRequestOther); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventsReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventsReplyListDataEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DatalogsReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SchedulesReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SchedulesReplyTimer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationsReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadLogfilesRequestFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogfilesReplyListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadReplyDowndata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_devices_v1_device_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_devices_v1_device_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[23].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[31].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[38].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[40].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[52].OneofWrappers = []interface{}{}
	file_api_devices_v1_device_proto_msgTypes[57].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_devices_v1_device_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   69,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_devices_v1_device_proto_goTypes,
		DependencyIndexes: file_api_devices_v1_device_proto_depIdxs,
		MessageInfos:      file_api_devices_v1_device_proto_msgTypes,
	}.Build()
	File_api_devices_v1_device_proto = out.File
	file_api_devices_v1_device_proto_rawDesc = nil
	file_api_devices_v1_device_proto_goTypes = nil
	file_api_devices_v1_device_proto_depIdxs = nil
}
