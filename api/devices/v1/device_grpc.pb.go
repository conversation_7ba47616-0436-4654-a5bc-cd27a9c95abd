// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/devices/v1/device.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DeviceClient is the client API for Device service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DeviceClient interface {
	AddDevice(ctx context.Context, in *AddRequest, opts ...grpc.CallOption) (*CommonReply, error)
	ListDevice(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error)
	GetDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DeviceReply, error)
	UpdateDevice(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (*CommonReply, error)
	PropertyDevice(ctx context.Context, in *PropertyRequest, opts ...grpc.CallOption) (*OperationReply, error)
	TakeoffDevice(ctx context.Context, in *TakeoffRequest, opts ...grpc.CallOption) (*OperationReply, error)
	LaunchDevice(ctx context.Context, in *LaunchRequest, opts ...grpc.CallOption) (*OperationReply, error)
	CrashDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ReturnDevice(ctx context.Context, in *ReturnRequest, opts ...grpc.CallOption) (*OperationReply, error)
	AutobackDevice(ctx context.Context, in *ReturnRequest, opts ...grpc.CallOption) (*OperationReply, error)
	RouteDevice(ctx context.Context, in *ReturnRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ControlDevice(ctx context.Context, in *ControlRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ReleaseDevice(ctx context.Context, in *ControlRequest, opts ...grpc.CallOption) (*OperationReply, error)
	AeromodeDevice(ctx context.Context, in *AeromodeRequest, opts ...grpc.CallOption) (*CommonReply, error)
	LensDevice(ctx context.Context, in *LensRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ZoomDevice(ctx context.Context, in *ZoomRequest, opts ...grpc.CallOption) (*OperationReply, error)
	GimbalDevice(ctx context.Context, in *GimbalRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ClarityDevice(ctx context.Context, in *ClarityRequest, opts ...grpc.CallOption) (*OperationReply, error)
	FreeDevice(ctx context.Context, in *FreeRequest, opts ...grpc.CallOption) (*OperationReply, error)
	PointDevice(ctx context.Context, in *PointRequest, opts ...grpc.CallOption) (*OperationReply, error)
	RepointDevice(ctx context.Context, in *PointRequest, opts ...grpc.CallOption) (*OperationReply, error)
	OrbitDevice(ctx context.Context, in *PointRequest, opts ...grpc.CallOption) (*OperationReply, error)
	OrbeedDevice(ctx context.Context, in *OrbeedRequest, opts ...grpc.CallOption) (*OperationReply, error)
	LookatDevice(ctx context.Context, in *LookatRequest, opts ...grpc.CallOption) (*OperationReply, error)
	PictureDevice(ctx context.Context, in *PictureRequest, opts ...grpc.CallOption) (*OperationReply, error)
	VideoDevice(ctx context.Context, in *VideoRequest, opts ...grpc.CallOption) (*OperationReply, error)
	DriveDevice(ctx context.Context, in *DriveRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ShoutDevice(ctx context.Context, in *ReturnRequest, opts ...grpc.CallOption) (*OperationReply, error)
	SpeakDevice(ctx context.Context, in *SpeakRequest, opts ...grpc.CallOption) (*OperationReply, error)
	VolumeDevice(ctx context.Context, in *VolumeRequest, opts ...grpc.CallOption) (*OperationReply, error)
	PlaymodeDevice(ctx context.Context, in *PlaymodeRequest, opts ...grpc.CallOption) (*OperationReply, error)
	AlgflowDevice(ctx context.Context, in *AlgflowRequest, opts ...grpc.CallOption) (*CommonReply, error)
	RemoveDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error)
	DeviceEvents(ctx context.Context, in *EventsRequest, opts ...grpc.CallOption) (*EventsReply, error)
	DeviceDatalogs(ctx context.Context, in *DatalogsRequest, opts ...grpc.CallOption) (*DatalogsReply, error)
	DeviceSchedules(ctx context.Context, in *SchedulesRequest, opts ...grpc.CallOption) (*SchedulesReply, error)
	DeviceOperations(ctx context.Context, in *OperationsRequest, opts ...grpc.CallOption) (*OperationsReply, error)
	RemoteLogfiles(ctx context.Context, in *RemoteLogfilesRequest, opts ...grpc.CallOption) (*OperationReply, error)
	UploadLogfiles(ctx context.Context, in *UploadLogfilesRequest, opts ...grpc.CallOption) (*OperationReply, error)
	ListLogfiles(ctx context.Context, in *ListLogfilesRequest, opts ...grpc.CallOption) (*LogfilesReply, error)
	DownloadLogfile(ctx context.Context, in *DownloadLogfilesRequest, opts ...grpc.CallOption) (*DownloadReply, error)
	CallbackDevice(ctx context.Context, in *CallbackRequest, opts ...grpc.CallOption) (*CommonReply, error)
	ExecuteDevice(ctx context.Context, in *CallbackRequest, opts ...grpc.CallOption) (*CommonReply, error)
}

type deviceClient struct {
	cc grpc.ClientConnInterface
}

func NewDeviceClient(cc grpc.ClientConnInterface) DeviceClient {
	return &deviceClient{cc}
}

func (c *deviceClient) AddDevice(ctx context.Context, in *AddRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/AddDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ListDevice(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListReply, error) {
	out := new(ListReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ListDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) GetDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DeviceReply, error) {
	out := new(DeviceReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/GetDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) UpdateDevice(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/UpdateDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) PropertyDevice(ctx context.Context, in *PropertyRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/PropertyDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) TakeoffDevice(ctx context.Context, in *TakeoffRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/TakeoffDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) LaunchDevice(ctx context.Context, in *LaunchRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/LaunchDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) CrashDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/CrashDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ReturnDevice(ctx context.Context, in *ReturnRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ReturnDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) AutobackDevice(ctx context.Context, in *ReturnRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/AutobackDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) RouteDevice(ctx context.Context, in *ReturnRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/RouteDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ControlDevice(ctx context.Context, in *ControlRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ControlDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ReleaseDevice(ctx context.Context, in *ControlRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ReleaseDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) AeromodeDevice(ctx context.Context, in *AeromodeRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/AeromodeDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) LensDevice(ctx context.Context, in *LensRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/LensDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ZoomDevice(ctx context.Context, in *ZoomRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ZoomDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) GimbalDevice(ctx context.Context, in *GimbalRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/GimbalDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ClarityDevice(ctx context.Context, in *ClarityRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ClarityDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) FreeDevice(ctx context.Context, in *FreeRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/FreeDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) PointDevice(ctx context.Context, in *PointRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/PointDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) RepointDevice(ctx context.Context, in *PointRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/RepointDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) OrbitDevice(ctx context.Context, in *PointRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/OrbitDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) OrbeedDevice(ctx context.Context, in *OrbeedRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/OrbeedDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) LookatDevice(ctx context.Context, in *LookatRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/LookatDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) PictureDevice(ctx context.Context, in *PictureRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/PictureDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) VideoDevice(ctx context.Context, in *VideoRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/VideoDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) DriveDevice(ctx context.Context, in *DriveRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/DriveDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ShoutDevice(ctx context.Context, in *ReturnRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ShoutDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) SpeakDevice(ctx context.Context, in *SpeakRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/SpeakDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) VolumeDevice(ctx context.Context, in *VolumeRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/VolumeDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) PlaymodeDevice(ctx context.Context, in *PlaymodeRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/PlaymodeDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) AlgflowDevice(ctx context.Context, in *AlgflowRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/AlgflowDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) RemoveDevice(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/RemoveDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) DeviceEvents(ctx context.Context, in *EventsRequest, opts ...grpc.CallOption) (*EventsReply, error) {
	out := new(EventsReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/DeviceEvents", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) DeviceDatalogs(ctx context.Context, in *DatalogsRequest, opts ...grpc.CallOption) (*DatalogsReply, error) {
	out := new(DatalogsReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/DeviceDatalogs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) DeviceSchedules(ctx context.Context, in *SchedulesRequest, opts ...grpc.CallOption) (*SchedulesReply, error) {
	out := new(SchedulesReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/DeviceSchedules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) DeviceOperations(ctx context.Context, in *OperationsRequest, opts ...grpc.CallOption) (*OperationsReply, error) {
	out := new(OperationsReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/DeviceOperations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) RemoteLogfiles(ctx context.Context, in *RemoteLogfilesRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/RemoteLogfiles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) UploadLogfiles(ctx context.Context, in *UploadLogfilesRequest, opts ...grpc.CallOption) (*OperationReply, error) {
	out := new(OperationReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/UploadLogfiles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ListLogfiles(ctx context.Context, in *ListLogfilesRequest, opts ...grpc.CallOption) (*LogfilesReply, error) {
	out := new(LogfilesReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ListLogfiles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) DownloadLogfile(ctx context.Context, in *DownloadLogfilesRequest, opts ...grpc.CallOption) (*DownloadReply, error) {
	out := new(DownloadReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/DownloadLogfile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) CallbackDevice(ctx context.Context, in *CallbackRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/CallbackDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ExecuteDevice(ctx context.Context, in *CallbackRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, "/api.devices.v1.Device/ExecuteDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeviceServer is the server API for Device service.
// All implementations must embed UnimplementedDeviceServer
// for forward compatibility
type DeviceServer interface {
	AddDevice(context.Context, *AddRequest) (*CommonReply, error)
	ListDevice(context.Context, *ListRequest) (*ListReply, error)
	GetDevice(context.Context, *CommonRequest) (*DeviceReply, error)
	UpdateDevice(context.Context, *UpdateRequest) (*CommonReply, error)
	PropertyDevice(context.Context, *PropertyRequest) (*OperationReply, error)
	TakeoffDevice(context.Context, *TakeoffRequest) (*OperationReply, error)
	LaunchDevice(context.Context, *LaunchRequest) (*OperationReply, error)
	CrashDevice(context.Context, *CommonRequest) (*OperationReply, error)
	ReturnDevice(context.Context, *ReturnRequest) (*OperationReply, error)
	AutobackDevice(context.Context, *ReturnRequest) (*OperationReply, error)
	RouteDevice(context.Context, *ReturnRequest) (*OperationReply, error)
	ControlDevice(context.Context, *ControlRequest) (*OperationReply, error)
	ReleaseDevice(context.Context, *ControlRequest) (*OperationReply, error)
	AeromodeDevice(context.Context, *AeromodeRequest) (*CommonReply, error)
	LensDevice(context.Context, *LensRequest) (*OperationReply, error)
	ZoomDevice(context.Context, *ZoomRequest) (*OperationReply, error)
	GimbalDevice(context.Context, *GimbalRequest) (*OperationReply, error)
	ClarityDevice(context.Context, *ClarityRequest) (*OperationReply, error)
	FreeDevice(context.Context, *FreeRequest) (*OperationReply, error)
	PointDevice(context.Context, *PointRequest) (*OperationReply, error)
	RepointDevice(context.Context, *PointRequest) (*OperationReply, error)
	OrbitDevice(context.Context, *PointRequest) (*OperationReply, error)
	OrbeedDevice(context.Context, *OrbeedRequest) (*OperationReply, error)
	LookatDevice(context.Context, *LookatRequest) (*OperationReply, error)
	PictureDevice(context.Context, *PictureRequest) (*OperationReply, error)
	VideoDevice(context.Context, *VideoRequest) (*OperationReply, error)
	DriveDevice(context.Context, *DriveRequest) (*OperationReply, error)
	ShoutDevice(context.Context, *ReturnRequest) (*OperationReply, error)
	SpeakDevice(context.Context, *SpeakRequest) (*OperationReply, error)
	VolumeDevice(context.Context, *VolumeRequest) (*OperationReply, error)
	PlaymodeDevice(context.Context, *PlaymodeRequest) (*OperationReply, error)
	AlgflowDevice(context.Context, *AlgflowRequest) (*CommonReply, error)
	RemoveDevice(context.Context, *CommonRequest) (*CommonReply, error)
	DeviceEvents(context.Context, *EventsRequest) (*EventsReply, error)
	DeviceDatalogs(context.Context, *DatalogsRequest) (*DatalogsReply, error)
	DeviceSchedules(context.Context, *SchedulesRequest) (*SchedulesReply, error)
	DeviceOperations(context.Context, *OperationsRequest) (*OperationsReply, error)
	RemoteLogfiles(context.Context, *RemoteLogfilesRequest) (*OperationReply, error)
	UploadLogfiles(context.Context, *UploadLogfilesRequest) (*OperationReply, error)
	ListLogfiles(context.Context, *ListLogfilesRequest) (*LogfilesReply, error)
	DownloadLogfile(context.Context, *DownloadLogfilesRequest) (*DownloadReply, error)
	CallbackDevice(context.Context, *CallbackRequest) (*CommonReply, error)
	ExecuteDevice(context.Context, *CallbackRequest) (*CommonReply, error)
	mustEmbedUnimplementedDeviceServer()
}

// UnimplementedDeviceServer must be embedded to have forward compatible implementations.
type UnimplementedDeviceServer struct {
}

func (UnimplementedDeviceServer) AddDevice(context.Context, *AddRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddDevice not implemented")
}
func (UnimplementedDeviceServer) ListDevice(context.Context, *ListRequest) (*ListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDevice not implemented")
}
func (UnimplementedDeviceServer) GetDevice(context.Context, *CommonRequest) (*DeviceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDevice not implemented")
}
func (UnimplementedDeviceServer) UpdateDevice(context.Context, *UpdateRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDevice not implemented")
}
func (UnimplementedDeviceServer) PropertyDevice(context.Context, *PropertyRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PropertyDevice not implemented")
}
func (UnimplementedDeviceServer) TakeoffDevice(context.Context, *TakeoffRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TakeoffDevice not implemented")
}
func (UnimplementedDeviceServer) LaunchDevice(context.Context, *LaunchRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LaunchDevice not implemented")
}
func (UnimplementedDeviceServer) CrashDevice(context.Context, *CommonRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CrashDevice not implemented")
}
func (UnimplementedDeviceServer) ReturnDevice(context.Context, *ReturnRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReturnDevice not implemented")
}
func (UnimplementedDeviceServer) AutobackDevice(context.Context, *ReturnRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AutobackDevice not implemented")
}
func (UnimplementedDeviceServer) RouteDevice(context.Context, *ReturnRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RouteDevice not implemented")
}
func (UnimplementedDeviceServer) ControlDevice(context.Context, *ControlRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ControlDevice not implemented")
}
func (UnimplementedDeviceServer) ReleaseDevice(context.Context, *ControlRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseDevice not implemented")
}
func (UnimplementedDeviceServer) AeromodeDevice(context.Context, *AeromodeRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AeromodeDevice not implemented")
}
func (UnimplementedDeviceServer) LensDevice(context.Context, *LensRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LensDevice not implemented")
}
func (UnimplementedDeviceServer) ZoomDevice(context.Context, *ZoomRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ZoomDevice not implemented")
}
func (UnimplementedDeviceServer) GimbalDevice(context.Context, *GimbalRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GimbalDevice not implemented")
}
func (UnimplementedDeviceServer) ClarityDevice(context.Context, *ClarityRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClarityDevice not implemented")
}
func (UnimplementedDeviceServer) FreeDevice(context.Context, *FreeRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FreeDevice not implemented")
}
func (UnimplementedDeviceServer) PointDevice(context.Context, *PointRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PointDevice not implemented")
}
func (UnimplementedDeviceServer) RepointDevice(context.Context, *PointRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RepointDevice not implemented")
}
func (UnimplementedDeviceServer) OrbitDevice(context.Context, *PointRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrbitDevice not implemented")
}
func (UnimplementedDeviceServer) OrbeedDevice(context.Context, *OrbeedRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrbeedDevice not implemented")
}
func (UnimplementedDeviceServer) LookatDevice(context.Context, *LookatRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LookatDevice not implemented")
}
func (UnimplementedDeviceServer) PictureDevice(context.Context, *PictureRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PictureDevice not implemented")
}
func (UnimplementedDeviceServer) VideoDevice(context.Context, *VideoRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoDevice not implemented")
}
func (UnimplementedDeviceServer) DriveDevice(context.Context, *DriveRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DriveDevice not implemented")
}
func (UnimplementedDeviceServer) ShoutDevice(context.Context, *ReturnRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShoutDevice not implemented")
}
func (UnimplementedDeviceServer) SpeakDevice(context.Context, *SpeakRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpeakDevice not implemented")
}
func (UnimplementedDeviceServer) VolumeDevice(context.Context, *VolumeRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VolumeDevice not implemented")
}
func (UnimplementedDeviceServer) PlaymodeDevice(context.Context, *PlaymodeRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaymodeDevice not implemented")
}
func (UnimplementedDeviceServer) AlgflowDevice(context.Context, *AlgflowRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AlgflowDevice not implemented")
}
func (UnimplementedDeviceServer) RemoveDevice(context.Context, *CommonRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveDevice not implemented")
}
func (UnimplementedDeviceServer) DeviceEvents(context.Context, *EventsRequest) (*EventsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceEvents not implemented")
}
func (UnimplementedDeviceServer) DeviceDatalogs(context.Context, *DatalogsRequest) (*DatalogsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceDatalogs not implemented")
}
func (UnimplementedDeviceServer) DeviceSchedules(context.Context, *SchedulesRequest) (*SchedulesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceSchedules not implemented")
}
func (UnimplementedDeviceServer) DeviceOperations(context.Context, *OperationsRequest) (*OperationsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceOperations not implemented")
}
func (UnimplementedDeviceServer) RemoteLogfiles(context.Context, *RemoteLogfilesRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoteLogfiles not implemented")
}
func (UnimplementedDeviceServer) UploadLogfiles(context.Context, *UploadLogfilesRequest) (*OperationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadLogfiles not implemented")
}
func (UnimplementedDeviceServer) ListLogfiles(context.Context, *ListLogfilesRequest) (*LogfilesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLogfiles not implemented")
}
func (UnimplementedDeviceServer) DownloadLogfile(context.Context, *DownloadLogfilesRequest) (*DownloadReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadLogfile not implemented")
}
func (UnimplementedDeviceServer) CallbackDevice(context.Context, *CallbackRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallbackDevice not implemented")
}
func (UnimplementedDeviceServer) ExecuteDevice(context.Context, *CallbackRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteDevice not implemented")
}
func (UnimplementedDeviceServer) mustEmbedUnimplementedDeviceServer() {}

// UnsafeDeviceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeviceServer will
// result in compilation errors.
type UnsafeDeviceServer interface {
	mustEmbedUnimplementedDeviceServer()
}

func RegisterDeviceServer(s grpc.ServiceRegistrar, srv DeviceServer) {
	s.RegisterService(&Device_ServiceDesc, srv)
}

func _Device_AddDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).AddDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/AddDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).AddDevice(ctx, req.(*AddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ListDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ListDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ListDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ListDevice(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_GetDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).GetDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/GetDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).GetDevice(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_UpdateDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).UpdateDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/UpdateDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).UpdateDevice(ctx, req.(*UpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_PropertyDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PropertyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).PropertyDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/PropertyDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).PropertyDevice(ctx, req.(*PropertyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_TakeoffDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TakeoffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).TakeoffDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/TakeoffDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).TakeoffDevice(ctx, req.(*TakeoffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_LaunchDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LaunchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).LaunchDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/LaunchDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).LaunchDevice(ctx, req.(*LaunchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_CrashDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).CrashDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/CrashDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).CrashDevice(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ReturnDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReturnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ReturnDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ReturnDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ReturnDevice(ctx, req.(*ReturnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_AutobackDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReturnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).AutobackDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/AutobackDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).AutobackDevice(ctx, req.(*ReturnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_RouteDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReturnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).RouteDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/RouteDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).RouteDevice(ctx, req.(*ReturnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ControlDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ControlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ControlDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ControlDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ControlDevice(ctx, req.(*ControlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ReleaseDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ControlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ReleaseDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ReleaseDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ReleaseDevice(ctx, req.(*ControlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_AeromodeDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AeromodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).AeromodeDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/AeromodeDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).AeromodeDevice(ctx, req.(*AeromodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_LensDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LensRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).LensDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/LensDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).LensDevice(ctx, req.(*LensRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ZoomDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ZoomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ZoomDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ZoomDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ZoomDevice(ctx, req.(*ZoomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_GimbalDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GimbalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).GimbalDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/GimbalDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).GimbalDevice(ctx, req.(*GimbalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ClarityDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClarityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ClarityDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ClarityDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ClarityDevice(ctx, req.(*ClarityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_FreeDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).FreeDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/FreeDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).FreeDevice(ctx, req.(*FreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_PointDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).PointDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/PointDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).PointDevice(ctx, req.(*PointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_RepointDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).RepointDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/RepointDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).RepointDevice(ctx, req.(*PointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_OrbitDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).OrbitDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/OrbitDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).OrbitDevice(ctx, req.(*PointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_OrbeedDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrbeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).OrbeedDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/OrbeedDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).OrbeedDevice(ctx, req.(*OrbeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_LookatDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LookatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).LookatDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/LookatDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).LookatDevice(ctx, req.(*LookatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_PictureDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PictureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).PictureDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/PictureDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).PictureDevice(ctx, req.(*PictureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_VideoDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).VideoDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/VideoDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).VideoDevice(ctx, req.(*VideoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_DriveDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DriveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).DriveDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/DriveDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).DriveDevice(ctx, req.(*DriveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ShoutDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReturnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ShoutDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ShoutDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ShoutDevice(ctx, req.(*ReturnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_SpeakDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SpeakRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).SpeakDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/SpeakDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).SpeakDevice(ctx, req.(*SpeakRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_VolumeDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VolumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).VolumeDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/VolumeDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).VolumeDevice(ctx, req.(*VolumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_PlaymodeDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaymodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).PlaymodeDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/PlaymodeDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).PlaymodeDevice(ctx, req.(*PlaymodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_AlgflowDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AlgflowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).AlgflowDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/AlgflowDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).AlgflowDevice(ctx, req.(*AlgflowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_RemoveDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).RemoveDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/RemoveDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).RemoveDevice(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_DeviceEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).DeviceEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/DeviceEvents",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).DeviceEvents(ctx, req.(*EventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_DeviceDatalogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DatalogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).DeviceDatalogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/DeviceDatalogs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).DeviceDatalogs(ctx, req.(*DatalogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_DeviceSchedules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchedulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).DeviceSchedules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/DeviceSchedules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).DeviceSchedules(ctx, req.(*SchedulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_DeviceOperations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OperationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).DeviceOperations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/DeviceOperations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).DeviceOperations(ctx, req.(*OperationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_RemoteLogfiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoteLogfilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).RemoteLogfiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/RemoteLogfiles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).RemoteLogfiles(ctx, req.(*RemoteLogfilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_UploadLogfiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadLogfilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).UploadLogfiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/UploadLogfiles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).UploadLogfiles(ctx, req.(*UploadLogfilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ListLogfiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLogfilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ListLogfiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ListLogfiles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ListLogfiles(ctx, req.(*ListLogfilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_DownloadLogfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadLogfilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).DownloadLogfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/DownloadLogfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).DownloadLogfile(ctx, req.(*DownloadLogfilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_CallbackDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).CallbackDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/CallbackDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).CallbackDevice(ctx, req.(*CallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ExecuteDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ExecuteDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devices.v1.Device/ExecuteDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ExecuteDevice(ctx, req.(*CallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Device_ServiceDesc is the grpc.ServiceDesc for Device service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Device_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devices.v1.Device",
	HandlerType: (*DeviceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddDevice",
			Handler:    _Device_AddDevice_Handler,
		},
		{
			MethodName: "ListDevice",
			Handler:    _Device_ListDevice_Handler,
		},
		{
			MethodName: "GetDevice",
			Handler:    _Device_GetDevice_Handler,
		},
		{
			MethodName: "UpdateDevice",
			Handler:    _Device_UpdateDevice_Handler,
		},
		{
			MethodName: "PropertyDevice",
			Handler:    _Device_PropertyDevice_Handler,
		},
		{
			MethodName: "TakeoffDevice",
			Handler:    _Device_TakeoffDevice_Handler,
		},
		{
			MethodName: "LaunchDevice",
			Handler:    _Device_LaunchDevice_Handler,
		},
		{
			MethodName: "CrashDevice",
			Handler:    _Device_CrashDevice_Handler,
		},
		{
			MethodName: "ReturnDevice",
			Handler:    _Device_ReturnDevice_Handler,
		},
		{
			MethodName: "AutobackDevice",
			Handler:    _Device_AutobackDevice_Handler,
		},
		{
			MethodName: "RouteDevice",
			Handler:    _Device_RouteDevice_Handler,
		},
		{
			MethodName: "ControlDevice",
			Handler:    _Device_ControlDevice_Handler,
		},
		{
			MethodName: "ReleaseDevice",
			Handler:    _Device_ReleaseDevice_Handler,
		},
		{
			MethodName: "AeromodeDevice",
			Handler:    _Device_AeromodeDevice_Handler,
		},
		{
			MethodName: "LensDevice",
			Handler:    _Device_LensDevice_Handler,
		},
		{
			MethodName: "ZoomDevice",
			Handler:    _Device_ZoomDevice_Handler,
		},
		{
			MethodName: "GimbalDevice",
			Handler:    _Device_GimbalDevice_Handler,
		},
		{
			MethodName: "ClarityDevice",
			Handler:    _Device_ClarityDevice_Handler,
		},
		{
			MethodName: "FreeDevice",
			Handler:    _Device_FreeDevice_Handler,
		},
		{
			MethodName: "PointDevice",
			Handler:    _Device_PointDevice_Handler,
		},
		{
			MethodName: "RepointDevice",
			Handler:    _Device_RepointDevice_Handler,
		},
		{
			MethodName: "OrbitDevice",
			Handler:    _Device_OrbitDevice_Handler,
		},
		{
			MethodName: "OrbeedDevice",
			Handler:    _Device_OrbeedDevice_Handler,
		},
		{
			MethodName: "LookatDevice",
			Handler:    _Device_LookatDevice_Handler,
		},
		{
			MethodName: "PictureDevice",
			Handler:    _Device_PictureDevice_Handler,
		},
		{
			MethodName: "VideoDevice",
			Handler:    _Device_VideoDevice_Handler,
		},
		{
			MethodName: "DriveDevice",
			Handler:    _Device_DriveDevice_Handler,
		},
		{
			MethodName: "ShoutDevice",
			Handler:    _Device_ShoutDevice_Handler,
		},
		{
			MethodName: "SpeakDevice",
			Handler:    _Device_SpeakDevice_Handler,
		},
		{
			MethodName: "VolumeDevice",
			Handler:    _Device_VolumeDevice_Handler,
		},
		{
			MethodName: "PlaymodeDevice",
			Handler:    _Device_PlaymodeDevice_Handler,
		},
		{
			MethodName: "AlgflowDevice",
			Handler:    _Device_AlgflowDevice_Handler,
		},
		{
			MethodName: "RemoveDevice",
			Handler:    _Device_RemoveDevice_Handler,
		},
		{
			MethodName: "DeviceEvents",
			Handler:    _Device_DeviceEvents_Handler,
		},
		{
			MethodName: "DeviceDatalogs",
			Handler:    _Device_DeviceDatalogs_Handler,
		},
		{
			MethodName: "DeviceSchedules",
			Handler:    _Device_DeviceSchedules_Handler,
		},
		{
			MethodName: "DeviceOperations",
			Handler:    _Device_DeviceOperations_Handler,
		},
		{
			MethodName: "RemoteLogfiles",
			Handler:    _Device_RemoteLogfiles_Handler,
		},
		{
			MethodName: "UploadLogfiles",
			Handler:    _Device_UploadLogfiles_Handler,
		},
		{
			MethodName: "ListLogfiles",
			Handler:    _Device_ListLogfiles_Handler,
		},
		{
			MethodName: "DownloadLogfile",
			Handler:    _Device_DownloadLogfile_Handler,
		},
		{
			MethodName: "CallbackDevice",
			Handler:    _Device_CallbackDevice_Handler,
		},
		{
			MethodName: "ExecuteDevice",
			Handler:    _Device_ExecuteDevice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/devices/v1/device.proto",
}
