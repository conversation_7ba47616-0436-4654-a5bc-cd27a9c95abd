// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// protoc-gen-go-http v2.2.1

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

type AnnotationHTTPServer interface {
	CreateAnnotation(context.Context, *CreateAnnotationRequest) (*CreateAnnotationReply, error)
	GetAnnotation(context.Context, *GetAnnotationRequest) (*GetAnnotationReply, error)
	ListAnnotation(context.Context, *ListAnnotationRequest) (*ListAnnotationReply, error)
}

func RegisterAnnotationHTTPServer(s *http.Server, srv AnnotationHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/annotations", _Annotation_CreateAnnotation0_HTTP_Handler(srv))
	r.GET("/api/v1/annotations/{id}", _Annotation_GetAnnotation0_HTTP_Handler(srv))
	r.POST("/api/v1/annotations/listQuery", _Annotation_ListAnnotation0_HTTP_Handler(srv))
}

func _Annotation_CreateAnnotation0_HTTP_Handler(srv AnnotationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAnnotationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.annotation.v1.Annotation/CreateAnnotation")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAnnotation(ctx, req.(*CreateAnnotationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateAnnotationReply)
		return ctx.Result(200, reply)
	}
}

func _Annotation_GetAnnotation0_HTTP_Handler(srv AnnotationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAnnotationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.annotation.v1.Annotation/GetAnnotation")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAnnotation(ctx, req.(*GetAnnotationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAnnotationReply)
		return ctx.Result(200, reply)
	}
}

func _Annotation_ListAnnotation0_HTTP_Handler(srv AnnotationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListAnnotationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, "/api.annotation.v1.Annotation/ListAnnotation")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAnnotation(ctx, req.(*ListAnnotationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAnnotationReply)
		return ctx.Result(200, reply)
	}
}

type AnnotationHTTPClient interface {
	CreateAnnotation(ctx context.Context, req *CreateAnnotationRequest, opts ...http.CallOption) (rsp *CreateAnnotationReply, err error)
	GetAnnotation(ctx context.Context, req *GetAnnotationRequest, opts ...http.CallOption) (rsp *GetAnnotationReply, err error)
	ListAnnotation(ctx context.Context, req *ListAnnotationRequest, opts ...http.CallOption) (rsp *ListAnnotationReply, err error)
}

type AnnotationHTTPClientImpl struct {
	cc *http.Client
}

func NewAnnotationHTTPClient(client *http.Client) AnnotationHTTPClient {
	return &AnnotationHTTPClientImpl{client}
}

func (c *AnnotationHTTPClientImpl) CreateAnnotation(ctx context.Context, in *CreateAnnotationRequest, opts ...http.CallOption) (*CreateAnnotationReply, error) {
	var out CreateAnnotationReply
	pattern := "/api/v1/annotations"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.annotation.v1.Annotation/CreateAnnotation"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AnnotationHTTPClientImpl) GetAnnotation(ctx context.Context, in *GetAnnotationRequest, opts ...http.CallOption) (*GetAnnotationReply, error) {
	var out GetAnnotationReply
	pattern := "/api/v1/annotations/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation("/api.annotation.v1.Annotation/GetAnnotation"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AnnotationHTTPClientImpl) ListAnnotation(ctx context.Context, in *ListAnnotationRequest, opts ...http.CallOption) (*ListAnnotationReply, error) {
	var out ListAnnotationReply
	pattern := "/api/v1/annotations/listQuery"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation("/api.annotation.v1.Annotation/ListAnnotation"))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
