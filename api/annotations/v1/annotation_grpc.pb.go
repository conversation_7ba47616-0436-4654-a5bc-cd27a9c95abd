// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.3
// source: api/annotations/v1/annotation.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AnnotationClient is the client API for Annotation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AnnotationClient interface {
	CreateAnnotation(ctx context.Context, in *CreateAnnotationRequest, opts ...grpc.CallOption) (*CreateAnnotationReply, error)
	GetAnnotation(ctx context.Context, in *GetAnnotationRequest, opts ...grpc.CallOption) (*GetAnnotationReply, error)
	ListAnnotation(ctx context.Context, in *ListAnnotationRequest, opts ...grpc.CallOption) (*ListAnnotationReply, error)
}

type annotationClient struct {
	cc grpc.ClientConnInterface
}

func NewAnnotationClient(cc grpc.ClientConnInterface) AnnotationClient {
	return &annotationClient{cc}
}

func (c *annotationClient) CreateAnnotation(ctx context.Context, in *CreateAnnotationRequest, opts ...grpc.CallOption) (*CreateAnnotationReply, error) {
	out := new(CreateAnnotationReply)
	err := c.cc.Invoke(ctx, "/api.annotation.v1.Annotation/CreateAnnotation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *annotationClient) GetAnnotation(ctx context.Context, in *GetAnnotationRequest, opts ...grpc.CallOption) (*GetAnnotationReply, error) {
	out := new(GetAnnotationReply)
	err := c.cc.Invoke(ctx, "/api.annotation.v1.Annotation/GetAnnotation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *annotationClient) ListAnnotation(ctx context.Context, in *ListAnnotationRequest, opts ...grpc.CallOption) (*ListAnnotationReply, error) {
	out := new(ListAnnotationReply)
	err := c.cc.Invoke(ctx, "/api.annotation.v1.Annotation/ListAnnotation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnnotationServer is the server API for Annotation service.
// All implementations must embed UnimplementedAnnotationServer
// for forward compatibility
type AnnotationServer interface {
	CreateAnnotation(context.Context, *CreateAnnotationRequest) (*CreateAnnotationReply, error)
	GetAnnotation(context.Context, *GetAnnotationRequest) (*GetAnnotationReply, error)
	ListAnnotation(context.Context, *ListAnnotationRequest) (*ListAnnotationReply, error)
	mustEmbedUnimplementedAnnotationServer()
}

// UnimplementedAnnotationServer must be embedded to have forward compatible implementations.
type UnimplementedAnnotationServer struct {
}

func (UnimplementedAnnotationServer) CreateAnnotation(context.Context, *CreateAnnotationRequest) (*CreateAnnotationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAnnotation not implemented")
}
func (UnimplementedAnnotationServer) GetAnnotation(context.Context, *GetAnnotationRequest) (*GetAnnotationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAnnotation not implemented")
}
func (UnimplementedAnnotationServer) ListAnnotation(context.Context, *ListAnnotationRequest) (*ListAnnotationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAnnotation not implemented")
}
func (UnimplementedAnnotationServer) mustEmbedUnimplementedAnnotationServer() {}

// UnsafeAnnotationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnnotationServer will
// result in compilation errors.
type UnsafeAnnotationServer interface {
	mustEmbedUnimplementedAnnotationServer()
}

func RegisterAnnotationServer(s grpc.ServiceRegistrar, srv AnnotationServer) {
	s.RegisterService(&Annotation_ServiceDesc, srv)
}

func _Annotation_CreateAnnotation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAnnotationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnotationServer).CreateAnnotation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.annotation.v1.Annotation/CreateAnnotation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnotationServer).CreateAnnotation(ctx, req.(*CreateAnnotationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Annotation_GetAnnotation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnnotationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnotationServer).GetAnnotation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.annotation.v1.Annotation/GetAnnotation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnotationServer).GetAnnotation(ctx, req.(*GetAnnotationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Annotation_ListAnnotation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAnnotationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnotationServer).ListAnnotation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.annotation.v1.Annotation/ListAnnotation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnotationServer).ListAnnotation(ctx, req.(*ListAnnotationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Annotation_ServiceDesc is the grpc.ServiceDesc for Annotation service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Annotation_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.annotation.v1.Annotation",
	HandlerType: (*AnnotationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAnnotation",
			Handler:    _Annotation_CreateAnnotation_Handler,
		},
		{
			MethodName: "GetAnnotation",
			Handler:    _Annotation_GetAnnotation_Handler,
		},
		{
			MethodName: "ListAnnotation",
			Handler:    _Annotation_ListAnnotation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/annotations/v1/annotation.proto",
}
