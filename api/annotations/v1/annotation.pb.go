// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: api/annotations/v1/annotation.proto

package v1

import (
	_ "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X int32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
	Y int32 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{0}
}

func (x *Point) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Point) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type Area struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Points []*Point `protobuf:"bytes,1,rep,name=points,proto3" json:"points,omitempty"`
}

func (x *Area) Reset() {
	*x = Area{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Area) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Area) ProtoMessage() {}

func (x *Area) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Area.ProtoReflect.Descriptor instead.
func (*Area) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{1}
}

func (x *Area) GetPoints() []*Point {
	if x != nil {
		return x.Points
	}
	return nil
}

type BreifMedia struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Url       string  `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Thumbnail string  `protobuf:"bytes,3,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	Areas     []*Area `protobuf:"bytes,4,rep,name=areas,proto3" json:"areas,omitempty"`
}

func (x *BreifMedia) Reset() {
	*x = BreifMedia{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BreifMedia) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BreifMedia) ProtoMessage() {}

func (x *BreifMedia) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BreifMedia.ProtoReflect.Descriptor instead.
func (*BreifMedia) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{2}
}

func (x *BreifMedia) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BreifMedia) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BreifMedia) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

func (x *BreifMedia) GetAreas() []*Area {
	if x != nil {
		return x.Areas
	}
	return nil
}

type MediaWithAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Url         string                          `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	CreatedTime float64                         `protobuf:"fixed64,3,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	ShootTime   float64                         `protobuf:"fixed64,4,opt,name=shootTime,proto3" json:"shootTime,omitempty"`
	Lnglat      []float64                       `protobuf:"fixed64,5,rep,packed,name=lnglat,proto3" json:"lnglat,omitempty"`
	PInfo       *MediaWithAnnotation_PhotoInfo  `protobuf:"bytes,6,opt,name=pInfo,proto3" json:"pInfo,omitempty"`
	SubType     string                          `protobuf:"bytes,7,opt,name=subType,proto3" json:"subType,omitempty"`
	Voyage      *MediaWithAnnotation_Voyage     `protobuf:"bytes,8,opt,name=voyage,proto3" json:"voyage,omitempty"`
	Annotation  *MediaWithAnnotation_Annotation `protobuf:"bytes,9,opt,name=annotation,proto3" json:"annotation,omitempty"`
	DeviceId    int64                           `protobuf:"varint,10,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
}

func (x *MediaWithAnnotation) Reset() {
	*x = MediaWithAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaWithAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaWithAnnotation) ProtoMessage() {}

func (x *MediaWithAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaWithAnnotation.ProtoReflect.Descriptor instead.
func (*MediaWithAnnotation) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{3}
}

func (x *MediaWithAnnotation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MediaWithAnnotation) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *MediaWithAnnotation) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *MediaWithAnnotation) GetShootTime() float64 {
	if x != nil {
		return x.ShootTime
	}
	return 0
}

func (x *MediaWithAnnotation) GetLnglat() []float64 {
	if x != nil {
		return x.Lnglat
	}
	return nil
}

func (x *MediaWithAnnotation) GetPInfo() *MediaWithAnnotation_PhotoInfo {
	if x != nil {
		return x.PInfo
	}
	return nil
}

func (x *MediaWithAnnotation) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *MediaWithAnnotation) GetVoyage() *MediaWithAnnotation_Voyage {
	if x != nil {
		return x.Voyage
	}
	return nil
}

func (x *MediaWithAnnotation) GetAnnotation() *MediaWithAnnotation_Annotation {
	if x != nil {
		return x.Annotation
	}
	return nil
}

func (x *MediaWithAnnotation) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

type Subject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	EventType   string `protobuf:"bytes,3,opt,name=eventType,proto3" json:"eventType,omitempty"`
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Subject) Reset() {
	*x = Subject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Subject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subject) ProtoMessage() {}

func (x *Subject) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subject.ProtoReflect.Descriptor instead.
func (*Subject) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{4}
}

func (x *Subject) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Subject) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Subject) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *Subject) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateAnnotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubjectId   int64         `protobuf:"varint,1,opt,name=subjectId,proto3" json:"subjectId,omitempty"`
	Media       []*BreifMedia `protobuf:"bytes,2,rep,name=media,proto3" json:"media,omitempty"`
	Description string        `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *CreateAnnotationRequest) Reset() {
	*x = CreateAnnotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnotationRequest) ProtoMessage() {}

func (x *CreateAnnotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnotationRequest.ProtoReflect.Descriptor instead.
func (*CreateAnnotationRequest) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{5}
}

func (x *CreateAnnotationRequest) GetSubjectId() int64 {
	if x != nil {
		return x.SubjectId
	}
	return 0
}

func (x *CreateAnnotationRequest) GetMedia() []*BreifMedia {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *CreateAnnotationRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateAnnotationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *CreateAnnotationReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CreateAnnotationReply) Reset() {
	*x = CreateAnnotationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnotationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnotationReply) ProtoMessage() {}

func (x *CreateAnnotationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnotationReply.ProtoReflect.Descriptor instead.
func (*CreateAnnotationReply) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{6}
}

func (x *CreateAnnotationReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateAnnotationReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateAnnotationReply) GetData() *CreateAnnotationReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAnnotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetAnnotationRequest) Reset() {
	*x = GetAnnotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnnotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnnotationRequest) ProtoMessage() {}

func (x *GetAnnotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnnotationRequest.ProtoReflect.Descriptor instead.
func (*GetAnnotationRequest) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{7}
}

func (x *GetAnnotationRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAnnotationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *GetAnnotationReply_AnnotationItem `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetAnnotationReply) Reset() {
	*x = GetAnnotationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnnotationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnnotationReply) ProtoMessage() {}

func (x *GetAnnotationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnnotationReply.ProtoReflect.Descriptor instead.
func (*GetAnnotationReply) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{8}
}

func (x *GetAnnotationReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAnnotationReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetAnnotationReply) GetData() *GetAnnotationReply_AnnotationItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListAnnotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime      int64    `protobuf:"varint,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime        int64    `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Page           int32    `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Size           int32    `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	AnnotateTypes  []string `protobuf:"bytes,5,rep,name=annotateTypes,proto3" json:"annotateTypes,omitempty"`
	AnnotateStates []int32  `protobuf:"varint,6,rep,packed,name=annotateStates,proto3" json:"annotateStates,omitempty"`
}

func (x *ListAnnotationRequest) Reset() {
	*x = ListAnnotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAnnotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnotationRequest) ProtoMessage() {}

func (x *ListAnnotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnotationRequest.ProtoReflect.Descriptor instead.
func (*ListAnnotationRequest) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{9}
}

func (x *ListAnnotationRequest) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListAnnotationRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListAnnotationRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAnnotationRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListAnnotationRequest) GetAnnotateTypes() []string {
	if x != nil {
		return x.AnnotateTypes
	}
	return nil
}

func (x *ListAnnotationRequest) GetAnnotateStates() []int32 {
	if x != nil {
		return x.AnnotateStates
	}
	return nil
}

type ListAnnotationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *ListAnnotationReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListAnnotationReply) Reset() {
	*x = ListAnnotationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAnnotationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnotationReply) ProtoMessage() {}

func (x *ListAnnotationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnotationReply.ProtoReflect.Descriptor instead.
func (*ListAnnotationReply) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{10}
}

func (x *ListAnnotationReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListAnnotationReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListAnnotationReply) GetData() *ListAnnotationReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type MediaWithAnnotation_PhotoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Thumbnail string `protobuf:"bytes,1,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
}

func (x *MediaWithAnnotation_PhotoInfo) Reset() {
	*x = MediaWithAnnotation_PhotoInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaWithAnnotation_PhotoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaWithAnnotation_PhotoInfo) ProtoMessage() {}

func (x *MediaWithAnnotation_PhotoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaWithAnnotation_PhotoInfo.ProtoReflect.Descriptor instead.
func (*MediaWithAnnotation_PhotoInfo) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{3, 0}
}

func (x *MediaWithAnnotation_PhotoInfo) GetThumbnail() string {
	if x != nil {
		return x.Thumbnail
	}
	return ""
}

type MediaWithAnnotation_Airline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *MediaWithAnnotation_Airline) Reset() {
	*x = MediaWithAnnotation_Airline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaWithAnnotation_Airline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaWithAnnotation_Airline) ProtoMessage() {}

func (x *MediaWithAnnotation_Airline) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaWithAnnotation_Airline.ProtoReflect.Descriptor instead.
func (*MediaWithAnnotation_Airline) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{3, 1}
}

func (x *MediaWithAnnotation_Airline) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MediaWithAnnotation_Airline) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type MediaWithAnnotation_Voyage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64                        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name      string                       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Airline   *MediaWithAnnotation_Airline `protobuf:"bytes,3,opt,name=airline,proto3" json:"airline,omitempty"`
	StartTime float64                      `protobuf:"fixed64,4,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   float64                      `protobuf:"fixed64,5,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *MediaWithAnnotation_Voyage) Reset() {
	*x = MediaWithAnnotation_Voyage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaWithAnnotation_Voyage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaWithAnnotation_Voyage) ProtoMessage() {}

func (x *MediaWithAnnotation_Voyage) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaWithAnnotation_Voyage.ProtoReflect.Descriptor instead.
func (*MediaWithAnnotation_Voyage) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{3, 2}
}

func (x *MediaWithAnnotation_Voyage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MediaWithAnnotation_Voyage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MediaWithAnnotation_Voyage) GetAirline() *MediaWithAnnotation_Airline {
	if x != nil {
		return x.Airline
	}
	return nil
}

func (x *MediaWithAnnotation_Voyage) GetStartTime() float64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *MediaWithAnnotation_Voyage) GetEndTime() float64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type MediaWithAnnotation_Annotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type        string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *MediaWithAnnotation_Annotation) Reset() {
	*x = MediaWithAnnotation_Annotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaWithAnnotation_Annotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaWithAnnotation_Annotation) ProtoMessage() {}

func (x *MediaWithAnnotation_Annotation) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaWithAnnotation_Annotation.ProtoReflect.Descriptor instead.
func (*MediaWithAnnotation_Annotation) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{3, 3}
}

func (x *MediaWithAnnotation_Annotation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MediaWithAnnotation_Annotation) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *MediaWithAnnotation_Annotation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateAnnotationReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type        string                            `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	CreatedTime float64                           `protobuf:"fixed64,3,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	Media       []*CreateAnnotationReply_DataItem `protobuf:"bytes,4,rep,name=media,proto3" json:"media,omitempty"`
	Description string                            `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *CreateAnnotationReply_Data) Reset() {
	*x = CreateAnnotationReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnotationReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnotationReply_Data) ProtoMessage() {}

func (x *CreateAnnotationReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnotationReply_Data.ProtoReflect.Descriptor instead.
func (*CreateAnnotationReply_Data) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{6, 0}
}

func (x *CreateAnnotationReply_Data) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateAnnotationReply_Data) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateAnnotationReply_Data) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *CreateAnnotationReply_Data) GetMedia() []*CreateAnnotationReply_DataItem {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *CreateAnnotationReply_Data) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateAnnotationReply_DataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Areas []*Area              `protobuf:"bytes,2,rep,name=areas,proto3" json:"areas,omitempty"`
	Item  *MediaWithAnnotation `protobuf:"bytes,3,opt,name=item,proto3" json:"item,omitempty"`
}

func (x *CreateAnnotationReply_DataItem) Reset() {
	*x = CreateAnnotationReply_DataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnotationReply_DataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnotationReply_DataItem) ProtoMessage() {}

func (x *CreateAnnotationReply_DataItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnotationReply_DataItem.ProtoReflect.Descriptor instead.
func (*CreateAnnotationReply_DataItem) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{6, 1}
}

func (x *CreateAnnotationReply_DataItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateAnnotationReply_DataItem) GetAreas() []*Area {
	if x != nil {
		return x.Areas
	}
	return nil
}

func (x *CreateAnnotationReply_DataItem) GetItem() *MediaWithAnnotation {
	if x != nil {
		return x.Item
	}
	return nil
}

type GetAnnotationReply_AnnotationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedTime float64       `protobuf:"fixed64,2,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	Type        string        `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Media       []*BreifMedia `protobuf:"bytes,4,rep,name=media,proto3" json:"media,omitempty"`
	Subject     *Subject      `protobuf:"bytes,5,opt,name=subject,proto3" json:"subject,omitempty"`
	Description string        `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *GetAnnotationReply_AnnotationItem) Reset() {
	*x = GetAnnotationReply_AnnotationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnnotationReply_AnnotationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnnotationReply_AnnotationItem) ProtoMessage() {}

func (x *GetAnnotationReply_AnnotationItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnnotationReply_AnnotationItem.ProtoReflect.Descriptor instead.
func (*GetAnnotationReply_AnnotationItem) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{8, 0}
}

func (x *GetAnnotationReply_AnnotationItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAnnotationReply_AnnotationItem) GetCreatedTime() float64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *GetAnnotationReply_AnnotationItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetAnnotationReply_AnnotationItem) GetMedia() []*BreifMedia {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *GetAnnotationReply_AnnotationItem) GetSubject() *Subject {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *GetAnnotationReply_AnnotationItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ListAnnotationReply_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Page  int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32                  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	List  []*MediaWithAnnotation `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListAnnotationReply_Data) Reset() {
	*x = ListAnnotationReply_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_annotations_v1_annotation_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAnnotationReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnotationReply_Data) ProtoMessage() {}

func (x *ListAnnotationReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_annotations_v1_annotation_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnotationReply_Data.ProtoReflect.Descriptor instead.
func (*ListAnnotationReply_Data) Descriptor() ([]byte, []int) {
	return file_api_annotations_v1_annotation_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListAnnotationReply_Data) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListAnnotationReply_Data) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAnnotationReply_Data) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListAnnotationReply_Data) GetList() []*MediaWithAnnotation {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_annotations_v1_annotation_proto protoreflect.FileDescriptor

var file_api_annotations_v1_annotation_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f,
	0x72, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x23, 0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x22, 0x38, 0x0a, 0x04, 0x41, 0x72, 0x65, 0x61, 0x12, 0x30,
	0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x22, 0x7b, 0x0a, 0x0a, 0x42, 0x72, 0x65, 0x69, 0x66, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x12, 0x2d,
	0x0a, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x52, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x22, 0x86, 0x06,
	0x0a, 0x13, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x57, 0x69, 0x74, 0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x68, 0x6f,
	0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x68,
	0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61,
	0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6e, 0x67, 0x6c, 0x61, 0x74, 0x12,
	0x46, 0x0a, 0x05, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x57, 0x69, 0x74, 0x68, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x45, 0x0a, 0x06, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x57, 0x69, 0x74, 0x68, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65,
	0x52, 0x06, 0x76, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x57, 0x69, 0x74, 0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x1a, 0x29, 0x0a, 0x09, 0x50, 0x68, 0x6f, 0x74, 0x6f,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61,
	0x69, 0x6c, 0x1a, 0x2d, 0x0a, 0x07, 0x41, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x1a, 0xae, 0x01, 0x0a, 0x06, 0x56, 0x6f, 0x79, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x48, 0x0a, 0x07, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x57, 0x69, 0x74, 0x68, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x69, 0x72, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x07, 0x61, 0x69, 0x72, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x1a, 0x52, 0x0a, 0x0a, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6d, 0x0a, 0x07, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8e, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x72, 0x65, 0x69, 0x66, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x05, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xca, 0x03, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x41,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0xb7, 0x01, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x47, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x85, 0x01, 0x0a, 0x08,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x05, 0x61, 0x72, 0x65, 0x61,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x65, 0x61,
	0x52, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x12, 0x3a, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x57,
	0x69, 0x74, 0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x69,
	0x74, 0x65, 0x6d, 0x22, 0x26, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xf2, 0x02, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x48, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xe3, 0x01, 0x0a, 0x0e, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x72, 0x65, 0x69, 0x66, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xc9, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x6d, 0x61, 0x78, 0x3d, 0x31, 0x30, 0x30,
	0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x0d, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x5b, 0xfa, 0x42, 0x58, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x3d, 0x27, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x3a, 0x63, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x27, 0x20, 0x27, 0x64, 0x72, 0x6f,
	0x6e, 0x65, 0x3a, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x66, 0x6c, 0x6f, 0x61, 0x74,
	0x65, 0x72, 0x27, 0x20, 0x27, 0x64, 0x72, 0x6f, 0x6e, 0x65, 0x3a, 0x63, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x27, 0x52, 0x0d, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0e, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x05, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x6d, 0x61, 0x78, 0x3d, 0x32, 0x2c, 0x64, 0x69,
	0x76, 0x65, 0x2c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x3d, 0x30, 0x20, 0x31, 0x52, 0x0e, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x22, 0x87, 0x02, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x3f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x80, 0x01, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x57, 0x69, 0x74, 0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0xaa, 0x03, 0x0a, 0x0a, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x88, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x22, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a,
	0x12, 0x81, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x12, 0x8c, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x22, 0x22, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x3a, 0x01, 0x2a, 0x42, 0x48, 0x0a, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73,
	0x6b, 0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_annotations_v1_annotation_proto_rawDescOnce sync.Once
	file_api_annotations_v1_annotation_proto_rawDescData = file_api_annotations_v1_annotation_proto_rawDesc
)

func file_api_annotations_v1_annotation_proto_rawDescGZIP() []byte {
	file_api_annotations_v1_annotation_proto_rawDescOnce.Do(func() {
		file_api_annotations_v1_annotation_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_annotations_v1_annotation_proto_rawDescData)
	})
	return file_api_annotations_v1_annotation_proto_rawDescData
}

var file_api_annotations_v1_annotation_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_annotations_v1_annotation_proto_goTypes = []interface{}{
	(*Point)(nil),                             // 0: api.annotation.v1.Point
	(*Area)(nil),                              // 1: api.annotation.v1.Area
	(*BreifMedia)(nil),                        // 2: api.annotation.v1.BreifMedia
	(*MediaWithAnnotation)(nil),               // 3: api.annotation.v1.MediaWithAnnotation
	(*Subject)(nil),                           // 4: api.annotation.v1.Subject
	(*CreateAnnotationRequest)(nil),           // 5: api.annotation.v1.CreateAnnotationRequest
	(*CreateAnnotationReply)(nil),             // 6: api.annotation.v1.CreateAnnotationReply
	(*GetAnnotationRequest)(nil),              // 7: api.annotation.v1.GetAnnotationRequest
	(*GetAnnotationReply)(nil),                // 8: api.annotation.v1.GetAnnotationReply
	(*ListAnnotationRequest)(nil),             // 9: api.annotation.v1.ListAnnotationRequest
	(*ListAnnotationReply)(nil),               // 10: api.annotation.v1.ListAnnotationReply
	(*MediaWithAnnotation_PhotoInfo)(nil),     // 11: api.annotation.v1.MediaWithAnnotation.PhotoInfo
	(*MediaWithAnnotation_Airline)(nil),       // 12: api.annotation.v1.MediaWithAnnotation.Airline
	(*MediaWithAnnotation_Voyage)(nil),        // 13: api.annotation.v1.MediaWithAnnotation.Voyage
	(*MediaWithAnnotation_Annotation)(nil),    // 14: api.annotation.v1.MediaWithAnnotation.Annotation
	(*CreateAnnotationReply_Data)(nil),        // 15: api.annotation.v1.CreateAnnotationReply.Data
	(*CreateAnnotationReply_DataItem)(nil),    // 16: api.annotation.v1.CreateAnnotationReply.DataItem
	(*GetAnnotationReply_AnnotationItem)(nil), // 17: api.annotation.v1.GetAnnotationReply.AnnotationItem
	(*ListAnnotationReply_Data)(nil),          // 18: api.annotation.v1.ListAnnotationReply.Data
}
var file_api_annotations_v1_annotation_proto_depIdxs = []int32{
	0,  // 0: api.annotation.v1.Area.points:type_name -> api.annotation.v1.Point
	1,  // 1: api.annotation.v1.BreifMedia.areas:type_name -> api.annotation.v1.Area
	11, // 2: api.annotation.v1.MediaWithAnnotation.pInfo:type_name -> api.annotation.v1.MediaWithAnnotation.PhotoInfo
	13, // 3: api.annotation.v1.MediaWithAnnotation.voyage:type_name -> api.annotation.v1.MediaWithAnnotation.Voyage
	14, // 4: api.annotation.v1.MediaWithAnnotation.annotation:type_name -> api.annotation.v1.MediaWithAnnotation.Annotation
	2,  // 5: api.annotation.v1.CreateAnnotationRequest.media:type_name -> api.annotation.v1.BreifMedia
	15, // 6: api.annotation.v1.CreateAnnotationReply.data:type_name -> api.annotation.v1.CreateAnnotationReply.Data
	17, // 7: api.annotation.v1.GetAnnotationReply.data:type_name -> api.annotation.v1.GetAnnotationReply.AnnotationItem
	18, // 8: api.annotation.v1.ListAnnotationReply.data:type_name -> api.annotation.v1.ListAnnotationReply.Data
	12, // 9: api.annotation.v1.MediaWithAnnotation.Voyage.airline:type_name -> api.annotation.v1.MediaWithAnnotation.Airline
	16, // 10: api.annotation.v1.CreateAnnotationReply.Data.media:type_name -> api.annotation.v1.CreateAnnotationReply.DataItem
	1,  // 11: api.annotation.v1.CreateAnnotationReply.DataItem.areas:type_name -> api.annotation.v1.Area
	3,  // 12: api.annotation.v1.CreateAnnotationReply.DataItem.item:type_name -> api.annotation.v1.MediaWithAnnotation
	2,  // 13: api.annotation.v1.GetAnnotationReply.AnnotationItem.media:type_name -> api.annotation.v1.BreifMedia
	4,  // 14: api.annotation.v1.GetAnnotationReply.AnnotationItem.subject:type_name -> api.annotation.v1.Subject
	3,  // 15: api.annotation.v1.ListAnnotationReply.Data.list:type_name -> api.annotation.v1.MediaWithAnnotation
	5,  // 16: api.annotation.v1.Annotation.CreateAnnotation:input_type -> api.annotation.v1.CreateAnnotationRequest
	7,  // 17: api.annotation.v1.Annotation.GetAnnotation:input_type -> api.annotation.v1.GetAnnotationRequest
	9,  // 18: api.annotation.v1.Annotation.ListAnnotation:input_type -> api.annotation.v1.ListAnnotationRequest
	6,  // 19: api.annotation.v1.Annotation.CreateAnnotation:output_type -> api.annotation.v1.CreateAnnotationReply
	8,  // 20: api.annotation.v1.Annotation.GetAnnotation:output_type -> api.annotation.v1.GetAnnotationReply
	10, // 21: api.annotation.v1.Annotation.ListAnnotation:output_type -> api.annotation.v1.ListAnnotationReply
	19, // [19:22] is the sub-list for method output_type
	16, // [16:19] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_api_annotations_v1_annotation_proto_init() }
func file_api_annotations_v1_annotation_proto_init() {
	if File_api_annotations_v1_annotation_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_annotations_v1_annotation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Area); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BreifMedia); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaWithAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Subject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnotationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnnotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnnotationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAnnotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAnnotationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaWithAnnotation_PhotoInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaWithAnnotation_Airline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaWithAnnotation_Voyage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaWithAnnotation_Annotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnotationReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnotationReply_DataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnnotationReply_AnnotationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_annotations_v1_annotation_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAnnotationReply_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_annotations_v1_annotation_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_annotations_v1_annotation_proto_goTypes,
		DependencyIndexes: file_api_annotations_v1_annotation_proto_depIdxs,
		MessageInfos:      file_api_annotations_v1_annotation_proto_msgTypes,
	}.Build()
	File_api_annotations_v1_annotation_proto = out.File
	file_api_annotations_v1_annotation_proto_rawDesc = nil
	file_api_annotations_v1_annotation_proto_goTypes = nil
	file_api_annotations_v1_annotation_proto_depIdxs = nil
}
