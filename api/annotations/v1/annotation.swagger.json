{"swagger": "2.0", "info": {"title": "api/annotations/v1/annotation.proto", "version": "version not set"}, "tags": [{"name": "Annotation"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/annotations": {"post": {"operationId": "Annotation_CreateAnnotation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CreateAnnotationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1CreateAnnotationRequest"}}], "tags": ["Annotation"]}}, "/api/v1/annotations/listQuery": {"post": {"operationId": "Annotation_ListAnnotation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListAnnotationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1ListAnnotationRequest"}}], "tags": ["Annotation"]}}, "/api/v1/annotations/{id}": {"get": {"operationId": "Annotation_GetAnnotation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetAnnotationReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Annotation"]}}}, "definitions": {"CreateAnnotationReplyDataItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "areas": {"type": "array", "items": {"$ref": "#/definitions/v1Area"}}, "item": {"$ref": "#/definitions/v1MediaWithAnnotation"}}}, "GetAnnotationReplyAnnotationItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "createdTime": {"type": "number", "format": "double"}, "type": {"type": "string"}, "media": {"type": "array", "items": {"$ref": "#/definitions/v1BreifMedia"}}, "subject": {"$ref": "#/definitions/v1Subject"}, "description": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "v1Area": {"type": "object", "properties": {"points": {"type": "array", "items": {"$ref": "#/definitions/v1Point"}}}}, "v1BreifMedia": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "url": {"type": "string"}, "thumbnail": {"type": "string"}, "areas": {"type": "array", "items": {"$ref": "#/definitions/v1Area"}}}}, "v1CreateAnnotationReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1CreateAnnotationReplyData"}}}, "v1CreateAnnotationReplyData": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "type": {"type": "string"}, "createdTime": {"type": "number", "format": "double"}, "media": {"type": "array", "items": {"$ref": "#/definitions/CreateAnnotationReplyDataItem"}}, "description": {"type": "string"}}}, "v1CreateAnnotationRequest": {"type": "object", "properties": {"subjectId": {"type": "string", "format": "int64"}, "media": {"type": "array", "items": {"$ref": "#/definitions/v1BreifMedia"}}, "description": {"type": "string"}}}, "v1GetAnnotationReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/GetAnnotationReplyAnnotationItem"}}}, "v1ListAnnotationReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1ListAnnotationReplyData"}}}, "v1ListAnnotationReplyData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/definitions/v1MediaWithAnnotation"}}}}, "v1ListAnnotationRequest": {"type": "object", "properties": {"startTime": {"type": "string", "format": "int64"}, "endTime": {"type": "string", "format": "int64"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "annotateTypes": {"type": "array", "items": {"type": "string"}}, "annotateStates": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "v1MediaWithAnnotation": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "url": {"type": "string"}, "createdTime": {"type": "number", "format": "double"}, "shootTime": {"type": "number", "format": "double"}, "lnglat": {"type": "array", "items": {"type": "number", "format": "double"}}, "pInfo": {"$ref": "#/definitions/v1MediaWithAnnotationPhotoInfo"}, "subType": {"type": "string"}, "voyage": {"$ref": "#/definitions/v1MediaWithAnnotationVoyage"}, "annotation": {"$ref": "#/definitions/v1MediaWithAnnotationAnnotation"}, "deviceId": {"type": "string", "format": "int64"}}}, "v1MediaWithAnnotationAirline": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}}}, "v1MediaWithAnnotationAnnotation": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "type": {"type": "string"}, "description": {"type": "string"}}}, "v1MediaWithAnnotationPhotoInfo": {"type": "object", "properties": {"thumbnail": {"type": "string"}}}, "v1MediaWithAnnotationVoyage": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "airline": {"$ref": "#/definitions/v1MediaWithAnnotationAirline"}, "startTime": {"type": "number", "format": "double"}, "endTime": {"type": "number", "format": "double"}}}, "v1Point": {"type": "object", "properties": {"x": {"type": "integer", "format": "int32"}, "y": {"type": "integer", "format": "int32"}}}, "v1Subject": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "eventType": {"type": "string"}, "description": {"type": "string"}}}}}