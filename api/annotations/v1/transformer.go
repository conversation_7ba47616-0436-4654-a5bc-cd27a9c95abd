package v1

import (
	"github.com/samber/lo"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func NewMediaWithAnnotation(bma *biz.MediaWithAnnotation) *MediaWithAnnotation {
	if bma == nil {
		return nil
	}
	mwa := &MediaWithAnnotation{
		Id:          bma.Media.Id,
		DeviceId:    bma.Media.DeviceId,
		Url:         bma.Media.URL,
		CreatedTime: float64(bma.Media.CreatedTime.UnixMilli()),
		PInfo: &MediaWithAnnotation_PhotoInfo{
			Thumbnail: bma.Media.GetThumbnailUrl(),
		},
		SubType: biz.MediaSubTypeKeyMapper[bma.SubType],
		Voyage: &MediaWithAnnotation_Voyage{
			Id: bma.VoyageId,
		},
	}
	if bma.Voyage != nil {
		mwa.Voyage.Name = bma.Voyage.Name
		mwa.Voyage.StartTime = float64(bma.Voyage.StartTime.UnixMilli())
		mwa.Voyage.EndTime = float64(bma.Voyage.EndTime.UnixMilli())
	}
	if bma.Airline != nil {
		mwa.Voyage.Airline = &MediaWithAnnotation_Airline{
			Id:   bma.Airline.Id,
			Name: bma.Airline.Name,
		}
	}
	if bma.Media.Meta != nil {
		mwa.ShootTime = float64(bma.Media.Meta.ShootTime.UnixMilli())
		mwa.Lnglat = bma.Media.Meta.ShootLnglat
	}
	if bma.Annotation != nil {
		mwa.Annotation = &MediaWithAnnotation_Annotation{
			Id:          bma.Annotation.Id,
			Type:        bma.Annotation.Type.String(),
			Description: bma.Annotation.Description,
		}
	}
	return mwa
}

func ToBizAnnotationArea(a *Area) biz.ObjectArea {
	return biz.ObjectArea{
		Points: lo.Map(a.Points, func(it *Point, _ int) biz.Point {
			return biz.Point{X: it.X, Y: it.Y}
		}),
	}
}

func NewArea(oa biz.ObjectArea) *Area {
	return &Area{
		Points: lo.Map(oa.Points, func(it biz.Point, _ int) *Point {
			return &Point{X: it.X, Y: it.Y}
		}),
	}
}

func NewSubject(bs *biz.Subject) *Subject {
	if bs == nil {
		return nil
	}
	return &Subject{
		Id:          bs.Id,
		Name:        bs.Name,
		EventType:   bs.EventType.String(),
		Description: bs.Description,
	}
}
