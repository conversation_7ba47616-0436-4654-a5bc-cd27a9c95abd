// Code generated by protoc-gen-go-http. DO NOT EDIT.

package v1

import (
	context "context"
	v10 "github.com/go-playground/validator/v10"
	validator "gitlab.sensoro.com/go-sensoro/protoc-gen-validator/validator"
)

var _ = new(context.Context)
var _ = new(v10.Validate)
var _ = new(validator.ValidateError)

func (m *ListAnnotationRequest) Validate() error {
	ctx := context.TODO()
	v := v10.New()

	for _, mi := range m.AnnotateStates {
		if err := validator.DoValidate(mi, "AnnotateStates"); err != nil {
			return err
		}
	}

	for _, mi := range m.AnnotateTypes {
		if err := validator.DoValidate(mi, "AnnotateTypes"); err != nil {
			return err
		}
	}
	if err := v.VarCtx(ctx, m.AnnotateStates, "max=2,dive,oneof=0 1"); err != nil {
		return validator.WrapValidatorError("AnnotateStates", err)
	}
	if err := v.VarCtx(ctx, m.Size, "max=1000"); err != nil {
		return validator.WrapValidatorError("Size", err)
	}
	if err := v.VarCtx(ctx, m.AnnotateTypes, "max=5,dive,oneof='drone:capture.person' 'drone:capture.floater' 'drone:capture.firework'"); err != nil {
		return validator.WrapValidatorError("AnnotateTypes", err)
	}
	return nil
}
