syntax = "proto3";

package api.annotation.v1;

import "google/api/annotations.proto";
import "validator/validator.proto";

option go_package = "gitlab.sensoro.com/skai/skai/api/annotation/v1;v1";
option java_multiple_files = true;
option java_package = "api.annotation.v1";

service Annotation {
	rpc CreateAnnotation (CreateAnnotationRequest) returns (CreateAnnotationReply) {
		option (google.api.http) = {
			post: "/api/v1/annotations"
			body: "*"
		};
	};
	rpc GetAnnotation (GetAnnotationRequest) returns (GetAnnotationReply) {
		option (google.api.http) = {
			get: "/api/v1/annotations/{id}"
		};
	};
	rpc ListAnnotation (ListAnnotationRequest) returns (ListAnnotationReply) {
		option (google.api.http) = {
			post: "/api/v1/annotations/listQuery"
			body: "*"
		};
	};
}

message Point {
	int32 x = 1;
	int32 y = 2;
}

message Area {
	repeated Point points = 1;
}

message BreifMedia {
	int64 id = 1;
	string url = 2;
	string thumbnail = 3;
	repeated Area areas = 4;
}



message MediaWithAnnotation  {
	message PhotoInfo {
		string thumbnail = 1;
	}
	message Airline {
		int64 id = 1;
		string name = 2;
	}
	message Voyage {
		int64 id = 1;
		string name = 2;
		Airline airline = 3;
		double startTime = 4;
		double endTime = 5;
	}
	message Annotation {
		int64 id = 1;
		string type = 2;
		string description = 3;
	}
	int64 id = 1;
	string url = 2;
  double createdTime = 3;
	double shootTime = 4;
	repeated double lnglat = 5;
	PhotoInfo pInfo = 6;
	string subType = 7;
	Voyage voyage = 8;
	Annotation annotation = 9;
	int64 deviceId = 10;
}



message Subject {
	int64 id = 1;
	string name = 2;
	string eventType = 3;
	string description = 4;
}


message CreateAnnotationRequest {
	int64 subjectId = 1;
	repeated BreifMedia media = 2;
	string description = 3;
}
message CreateAnnotationReply {
	int32 code = 1;
	string message = 2;
	message Data {
		int64 id = 1;
		string type = 2;
		double createdTime = 3;
		repeated DataItem media = 4;
		string description = 5;
	}
	message DataItem {
		int64 id = 1;
		repeated Area areas = 2;
		MediaWithAnnotation item = 3;
	}
	Data data = 3;
}


message GetAnnotationRequest {
	int64 id = 1;
}
message GetAnnotationReply {
	message AnnotationItem {
		int64 id = 1;
		double createdTime = 2;
		string type = 3;
		repeated BreifMedia media = 4;
		Subject subject = 5;
		string description = 6;
	}
	int32 code = 1;
	string message = 2;
	AnnotationItem data = 3;
}

message ListAnnotationRequest {
	int64 startTime = 1;
	int64 endTime = 2;
	int32 page = 3;
	int32 size = 4 [(validator.rules) = "max=1000"];
	repeated string annotateTypes = 5  [(validator.rules) = "max=5,dive,oneof='drone:capture.person' 'drone:capture.floater' 'drone:capture.firework'"];
	repeated int32 annotateStates = 6 [(validator.rules) = "max=2,dive,oneof=0 1"];
}
message ListAnnotationReply {
	int32 code = 1;
	string message = 2;
	message Data {
		int32 total = 1;
		int32 page = 2;
		int32 size = 3;
		repeated MediaWithAnnotation list = 4;
	}
	Data data = 3;
}