# Kratos Project 样板

## 环境准备
```sh
export GOPRIVATE=gitlab.sensoro.com
git config --global url."**********************:".insteadOf "https://gitlab.sensoro.com/" 
#由于我们用的是ssh的git鉴权，需要在~/.gitconfig里添加以上规则，go get 自动把https转换为ssh
```
## 样板功能
- [x] 集成logger
- [x] 集成Prometheus metrics middleware
- [x] 集成jeager tracer
- [x] 集成gorm
- [x] 集成go-redis
- [x] crud examples
- [x] test examples
- [x] protoc-gen-validate
- [x] 分页、筛选业务抽象
- [x] 配置读取lins env
- [x] 公共库迁移到gitlab一级分组

## 安装Kratos
```
go get -u github.com/go-kratos/kratos/cmd/kratos/v2@latest
```

## 安装代码生成器等依赖
```
go get gitlab.sensoro.com/go-sensoro/protoc-gen-validator
go get gitlab.sensoro.com/go-sensoro/protoc-gen-list
go get github.com/google/wire/cmd/wire

```

## 目录结构说明

```
.
├── go.mod
├── go.sum
├── README.md
├── api        // 下面维护了微服务使用的proto文件以及根据它们所生成的go文件
│   └── helloworld
│       ├── errors
│       │   ├── helloworld.pb.go
│       │   ├── helloworld.proto
│       │   └── helloworld_errors.pb.go
│       └── v1
│           ├── greeter.pb.go
│           ├── greeter.proto
│           ├── greeter_grpc.pb.go
│           └── greeter_http.pb.go
├── cmd    // 整个项目启动的入口文件
│   └── server
│       ├── main.go
│       ├── wire.go  // 我们使用wire来维护依赖注入
│       └── wire_gen.go
├── configs     // 这里通常维护一些本地调试用的样例配置文件
│   └── config.yaml
└── internal    // 该服务所有不对外暴露的代码，通常的业务逻辑都在这下面，使用internal避免错误引用
    ├── conf    // 内部使用的config的结构定义，使用proto格式生成
    │   ├── conf.pb.go
    │   └── conf.proto
    ├── data    // 业务数据访问，包含 cache、db、外部服务等封装，实现了 biz 的 repo 接口。我们可能会把 data 与 dao 混淆在一起，data 偏重业务的含义，它所要做的是将领域对象重新拿出来，我们去掉了 DDD 的 infra层。
    │   ├── README.md
    │   ├── data.go
    │   └── greeter.go
    ├── biz     // 业务逻辑的组装层，类似 DDD 的 domain 层，data 类似 DDD 的 repo，repo 接口在这里定义，使用依赖倒置的原则。
    │   ├── README.md
    │   ├── biz.go
    │   └── greeter.go
    ├──service  // 实现了 api 定义的服务层，类似 DDD 的 application 层，处理 DTO 到 biz 领域实体的转换(DTO -> DO)，同时协同各类 biz 交互，但是不应处理复杂逻辑
    │   ├── README.md
    │   ├── greeter.go
    │   └── service.go
    └── server  // http和grpc实例的创建和配置
        ├── grpc.go
        ├── http.go
        └── server.go
```


## 创建一个项目
```
# create a template project
kratos new -r **********************:lins/golang/skai.git helloworld

cd helloworld
# Add a proto template
kratos proto add api/helloworld/helloworld.proto
# Generate the source code of service by proto file
kratos proto server api/helloworld/helloworld.proto -t internal/service

vim ./internel/service/service.go # 添加service构造方法到provider set中

go generate ./...
make build
./bin/helloworld -conf ./configs
```

## 注册服务

注册service到http或者grpc：
```go
//vi internel/server/http.go
func NewHTTPServer(c *conf.Server, tracer trace.TracerProvider, greeter *service.GreeterService, hellloworld *service.HelloworldService, logger log.Logger) *http.Server {
    .....
    helleworldPkg.RegisterHelloworldHTTPServer(srv, helloworld)
    .....
}
```

## 验证代码生成

由于envoy的proto-gen-validate还不够成熟，无法用于生产，自研了proto-gen-validator生成器，通过proto文件注解生成(https://https://github.com/go-playground/validator)验证规则,demo如下:规则请参考validator官方文档.
```protobuf

message CreateArticleRequest {
  string email = 4 [(validator.rules) = "required,min=8,max=32"];
  int32 category_id = 5 [(validator.rules) = "required,gt=0"];;
}

```
运行以下命令，生成*_validate.pb.go
```bash
make validate #生成validate方法用于中间件校验
```
## 分页代码生成
```protobuf
message ListArticleRequest {
  option (list.page) = true; //设置该注解启用page info
  int32 page = 1;
  int32 page_size = 2;

  optional string category_id = 3 [(list.filter_options)={filter_name: "category_id"}];
  optional int64 startAt = 5; [(list.filter_options)={filter_name: "created_at", operation:">="}];
  optional int64 endAt = 6;
}
```
运行以下命令,生成*_list.pb.go
```bash
make list-api #生成列表过滤和分页方法文件
```
自动生成的代码实现了DataListQuery接口，开发者可以在数据层直接调用Condition方法返回的condition对象applyToGorm，自动生成gorm where条件语句。

```go
type DataListQuery interface {
	PageInfo() (int, int)
	Condition() *list.Condition
}
```

## 配置文件

配置文件支持动态解析环境变量
```yaml
dataApi:
  addr: ${DATA_API_URL}
  secret: ${DATA_API_SECRET}
```

## Automated Initialization (wire)
```
# install wire
go get github.com/google/wire/cmd/wire

# generate wire
cd cmd/server
wire
```

## Docker
```bash
# build
docker build -t <your-docker-image-name> .

# run
docker run --rm -p 8000:8000 -p 9000:9000 -v </path/to/your/configs>:/data/conf <your-docker-image-name>
```
## 目录规范

## Q&A

#### go generate ./... 生成代码的时候报有optional字段需要使用`--experimental_allow_proto3_optional`flag

kratos默认使用电脑上的protoc进行proto文件的编译代码生成, 默认kratos使用protoc的时候是不传该flag的, 所以需要使用一个protoc默认支持该flag的版本, 官方从[3.15.0](https://github.com/protocolbuffers/protobuf/releases/tag/v3.15.0)开始支持默认开启此flag