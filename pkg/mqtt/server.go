package mqtt

import (
	"context"
	"net/url"
	"time"

	"github.com/eclipse/paho.golang/autopaho"
	"github.com/eclipse/paho.golang/paho"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/google/uuid"
)

var _ transport.Server = (*Subscriber)(nil)

type Message = paho.Publish

type Reply struct {
	Topic string
	Qos   byte
	Data  []byte
}

type MessageHandler interface {
	Sub() (topic string, qos int)
	Handle(*Message) (reply *Reply, err error)
}

type Subscriber struct {
	conf     subcriberConfig
	conn     *autopaho.ConnectionManager
	log      *log.Helper
	context  func() context.Context
	handlers []MessageHandler
}

type SubscriberOption func(*Subscriber)

type subcriberConfig struct {
	broker           string
	username         string
	password         string
	clientId         string
	keepalive        time.Duration
	retryDelay       time.Duration
	workerConcurrent int
}

func WithBroker(url string) SubscriberOption {
	return func(s *Subscriber) {
		s.conf.broker = url
	}
}

func WithHandlers(handlers []MessageHandler) SubscriberOption {
	return func(s *Subscriber) {
		s.handlers = append(s.handlers, handlers...)
	}
}

func WithUsernamePassword(username, password string) SubscriberOption {
	return func(s *Subscriber) {
		s.conf.username = username
		s.conf.password = password
	}
}

func WithClientId(id string) SubscriberOption {
	return func(s *Subscriber) {
		s.conf.clientId = id
	}
}

func NewMQTTSubscriber(logger log.Logger, opts ...SubscriberOption) (*Subscriber, error) {
	s := &Subscriber{
		conf: subcriberConfig{
			broker:           "tcp://localhost:1883",
			keepalive:        15 * time.Second,
			retryDelay:       5 * time.Second,
			workerConcurrent: 4,
			clientId:         newRandomClientId(),
		},
		log: log.NewHelper(logger),
	}
	for _, opt := range opts {
		opt(s)
	}
	return s, nil
}

func (s *Subscriber) Start(ctx context.Context) error {
	s.context = func() context.Context { return ctx }
	brokerUrl, err := url.Parse(s.conf.broker)
	if err != nil {
		s.log.Error("parse broker url failed", s.conf.broker, "err", err)
		panic(err)
	}
	go func() {
		conf := autopaho.ClientConfig{
			BrokerUrls:        []*url.URL{brokerUrl},
			KeepAlive:         uint16(s.conf.keepalive.Seconds()),
			ConnectRetryDelay: s.conf.retryDelay,
			OnConnectionUp: func(cm *autopaho.ConnectionManager, connAck *paho.Connack) {
				s.log.Info("mqtt connect up")
				if ack, err := s.doSub(ctx); err != nil {
					s.log.Error("failed to subscribe err", err, "ack", ack)
				} else {
					s.log.Info("successfully subscribe finished ")
				}
			},
			OnConnectError: func(err error) {
				s.log.Error("mqttError: ", err)
			},
			ClientConfig: paho.ClientConfig{
				ClientID: s.conf.clientId,
				Router:   s.newRouter(),
			},
			ConnectTimeout: 5 * time.Second,
		}
		if s.conf.username != "" {
			conf.SetUsernamePassword(s.conf.username, []byte(s.conf.password))
		}
		connCtx, cancel := context.WithCancel(ctx)
		defer cancel()
		s.conn, err = autopaho.NewConnection(connCtx, conf)
		if err != nil {
			s.log.Error("connect to mqtt broker failed", s.conf.broker, "username", s.conf.username, "err", err)
			panic(err)
		}
		if err = s.conn.AwaitConnection(connCtx); err != nil {
			s.log.Error("connect to mqtt broker failed: ", err)
			panic(err)
		}
		s.log.Info("connect to mqtt broker success")
		<-s.conn.Done()
	}()

	return nil
}

func (s *Subscriber) Stop(ctx context.Context) error {
	if s.conn == nil {
		return nil
	}
	return s.conn.Disconnect(ctx)
}

func (s *Subscriber) doSub(ctx context.Context) (*paho.Suback, error) {
	subs := make(map[string]paho.SubscribeOptions)
	for _, h := range s.handlers {
		topic, qos := h.Sub()
		s.log.Infof("do sub for topic %s qos %d", topic, qos)
		subs[topic] = paho.SubscribeOptions{
			QoS:     byte(qos),
			NoLocal: true,
		}
	}
	return s.conn.Subscribe(ctx, &paho.Subscribe{
		Subscriptions: subs,
	})
}

func (s *Subscriber) newRouter() paho.Router {
	r := paho.NewStandardRouter()
	for _, h := range s.handlers {
		topic, _ := h.Sub()
		mh := h.Handle
		s.log.Infof("register topic %s MessageHandler", topic)
		r.RegisterHandler(topic, func(p *paho.Publish) {
			go func(m *Message) {
				s.log.Debugf("handle topic %s msgId %d payload: %s", m.Topic, m.PacketID, m.Payload)
				reply, err := mh(m)
				if err != nil {
					s.log.Errorf("handle msgId %d for topic %s raw %s err %v", m.PacketID, m.Topic, m.Payload, err)
					return
				}
				if reply != nil {
					ctx, cancel := context.WithTimeout(s.context(), 5*time.Second)
					defer cancel()
					s.log.Debugf("send reply for msgId %d to topic %s data %s", m.PacketID, reply.Topic, reply.Data)
					res, err := s.conn.Publish(ctx, &paho.Publish{
						Topic:   reply.Topic,
						QoS:     reply.Qos,
						Payload: reply.Data,
					})
					if err != nil {
						s.log.Errorf("send reply  for msgId %d for topic %s to topic %s err %v", m.PacketID, m.Topic, reply.Topic, err)
					}
					if res != nil {
						s.log.Debugf("server resonse %d", res.ReasonCode)
						if res.ReasonCode == 16 {
							s.log.Warnf("send msg %s to topic %s but no subscrber!", reply.Data, reply.Topic)
						}
					}
				}
			}(p)
		})
	}

	return r
}

func newRandomClientId() string {
	return uuid.NewString()
}
