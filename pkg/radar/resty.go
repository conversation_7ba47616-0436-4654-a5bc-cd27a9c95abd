package radar

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/go-resty/resty/v2"
)

var reqFactory = resty.New().SetDisableWarn(true).SetTimeout(60 * time.Second)

func init() {
	reqFactory.JSONMarshal = json.Marshal
	reqFactory.JSONUnmarshal = json.Unmarshal
}

type AuthOption func(http.Header)

func WithJSONContentType() AuthOption {
	return func(h http.Header) {
		h.Set("Content-Type", "application/json")
	}
}

func NewRequest(opts ...AuthOption) *resty.Request {
	req := reqFactory.R()
	for _, opt := range opts {
		opt(req.Header)
	}
	return req
}

type RadarResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    *struct {
		Status bool `json:"status"`
	} `json:"data"`
}
