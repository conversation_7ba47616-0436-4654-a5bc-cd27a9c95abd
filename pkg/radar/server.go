package radar

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-redis/redis/v8"
	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

var _ transport.Server = (*Subscriber)(nil)

type SubscriberOption func(*Subscriber)

type Subscriber struct {
	conf     *redis.UniversalOptions
	client   redis.UniversalClient
	pubsub   *redis.PubSub
	log      *log.Helper
	callback *resty.Request
	context  func() context.Context
}

func NewRadarSubscriber(logger log.Logger, conf *redis.UniversalOptions) (*Subscriber, error) {
	s := &Subscriber{
		conf: conf,
		log:  log.NewHelper(logger),
	}
	return s, nil
}

func (s *Subscriber) Start(ctx context.Context) error {
	s.context = func() context.Context { return ctx }
	connCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	s.client = redis.NewUniversalClient(s.conf)
	channel := fmt.Sprintf("__keyevent@%d__:expired", s.conf.DB)
	s.pubsub = s.client.Subscribe(connCtx, []string{channel}...)
	s.callback = NewRequest(WithJSONContentType())
	ch := s.pubsub.Channel()
	go func() {
		for {
			select {
			case message, ok := <-ch:
				if !ok {
					s.log.Infof("Rardar subscriber channel has no message")
					return
				}
				s.log.Infof("Rardar subscriber channel: %s with message: %s", message.Channel, message.Payload)
				payload, err := s.client.Get(ctx, fmt.Sprintf("%s:DATA", message.Payload)).Result()
				if err != nil {
					s.log.Warnf("Rardar get expired key's data error: %+v", err.Error())
					continue
				}
				cburl := gjson.Get(payload, "callback")
				if !cburl.Exists() {
					s.log.Warnf("Rardar callback url for source caller is required")
					continue
				}
				url := "http://127.0.0.1:8000" + cburl.String()
				r, err := s.callback.SetResult(&RadarResponse{}).SetBody(payload).Post(url)
				if err != nil {
					s.log.Errorf("Rardar call source url %s with error: %s", url, err)
					continue
				}
				if r.IsError() {
					s.log.Errorf("Rardar call source url %s code %d with error: %s", url, r.StatusCode(), r.Body())
					continue
				}
				res := r.Result().(*RadarResponse)
				if res.Code != 0 || res.Data == nil {
					s.log.Infof("Rardar call return error code %d with message: %s", res.Code, res.Message)
					continue
				}
			case <-ctx.Done():
				return
			}
		}
	}()
	s.log.Info("[RADAR] server starting up")
	return nil
}

func (s *Subscriber) Stop(ctx context.Context) error {
	if s.pubsub == nil {
		return nil
	}
	if s.client == nil {
		return nil
	}
	channel := fmt.Sprintf("__keyevent@%d__:expired", s.conf.DB)
	if err := s.pubsub.Unsubscribe(ctx, channel); err != nil {
		s.log.Errorf("Rardar unsubscribe pubsub error: %+v", err)
		return err
	}
	if err := s.client.Close(); err != nil {
		s.log.Errorf("Rardar close client error: %+v", err)
		return err
	}
	s.log.Info("[RADAR] server stopping")
	return nil
}
