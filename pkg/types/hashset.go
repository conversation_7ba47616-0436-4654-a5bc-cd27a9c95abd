package types

// HashSet is a set of comparable elements.
// It is backed by a hash table.
// And it is not thread-safe.
type HashSet[T comparable] struct {
	m map[T]struct{}
}

func NewHashSet[T comparable]() HashSet[T] {
	return HashSet[T]{m: make(map[T]struct{})}
}
func NewHashSetFromSlice[T comparable](s []T) HashSet[T] {
	set := NewHashSet[T]()
	for _, item := range s {
		set.Add(item)
	}
	return set
}

func (s *HashSet[T]) Add(item T) {
	s.m[item] = struct{}{}
}

func (s *HashSet[T]) Remove(item T) {
	delete(s.m, item)
}

func (s *HashSet[T]) Contains(item T) bool {
	_, ok := s.m[item]
	return ok
}

func (s *HashSet[T]) Clear() {
	s.m = make(map[T]struct{})
}

func (s *HashSet[T]) Size() int {
	return len(s.m)
}

func (s *HashSet[T]) IsEmpty() bool {
	return s.Size() == 0
}

func (s *HashSet[T]) ToSlice() []T {
	slice := make([]T, s.Size())
	i := 0
	for item := range s.m {
		slice[i] = item
		i++
	}
	return slice
}
