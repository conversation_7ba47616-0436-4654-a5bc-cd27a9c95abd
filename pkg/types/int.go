package types

func Int32ToInt64(i32 int32) int64 {
	return int64(i32)
}

func Int64ToInt32(i64 int64) int32 {
	return int32(i64)
}

func IntToInt32(i int) int32 {
	return int32(i)
}

func IntToInt64(i int) int64 {
	return int64(i)
}

func IntToBool(i int) bool {
	return i == 1
}

func Int32ToBool(i32 int32) bool {
	return i32 == 1
}

func Int8ToBool(i int8) bool {
	return i == 1
}

func BoolToInt32(b bool) int32 {
	if b {
		return 1
	}
	return 0
}

func BoolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

func BoolToInt8(b bool) int8 {
	if b {
		return 1
	}
	return 0
}
