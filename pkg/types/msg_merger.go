package types

import (
	"sync"
	"time"
)

type Merger[V any] interface {
	Merge(v []V) V
	Sink(v V)
}

type messageList[T any] struct {
	list   []T
	active time.Time
	//size   int
}

type MessageMergeExecutor[K comparable, V any] struct {
	data     map[K]messageList[V]
	mtx      *sync.Mutex
	merger   Merger[V]
	interval time.Duration
	maxSize  int
	ticker   *time.Ticker
}

func NewMessageMergeExecutor[K comparable, V any](merger Merger[V], interval time.Duration, maxSize int) *MessageMergeExecutor[K, V] {
	return &MessageMergeExecutor[K, V]{
		data:     make(map[K]messageList[V]),
		mtx:      &sync.Mutex{},
		merger:   merger,
		interval: interval,
		maxSize:  maxSize,
	}
}

func (e *MessageMergeExecutor[K, V]) Add(key K, value V) {
	e.mtx.Lock()
	defer e.mtx.Unlock()
	ml, ok := e.data[key]
	if !ok {
		ml = messageList[V]{
			list:   make([]V, 0, e.maxSize),
			active: time.Now(),
		}
	}
	ml.list = append(ml.list, value)
	e.data[key] = ml
	if len(ml.list) >= e.maxSize {
		e.merger.Sink(e.merger.Merge(ml.list))
		delete(e.data, key)
	}
}

func (e *MessageMergeExecutor[K, V]) Run() {
	ticker := time.NewTicker(e.interval / 2)
	for {
		<-ticker.C
		e.mtx.Lock()
		for k, v := range e.data {
			if time.Since(v.active) >= e.interval {
				e.merger.Sink(e.merger.Merge(v.list))
				delete(e.data, k)
			}
		}
		e.mtx.Unlock()
	}
}

func (e *MessageMergeExecutor[K, V]) Close() {
	e.ticker.Stop()
}
