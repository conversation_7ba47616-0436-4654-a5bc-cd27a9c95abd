package types

import "reflect"

func IsPtr(v interface{}) bool {
	valueOf := reflect.ValueOf(v)
	return valueOf.Kind() == reflect.Ptr
}

func GetElem(v interface{}) any {
	valueOf := reflect.ValueOf(v)
	return valueOf.Elem().Interface()
}

func IsNil(v interface{}) bool {
	valueOf := reflect.ValueOf(v)
	k := valueOf.Kind()
	switch k {
	case reflect.Chan, reflect.Func, reflect.Map, reflect.Ptr, reflect.UnsafePointer, reflect.Interface, reflect.Slice:
		return valueOf.IsNil()
	default:
		return v == nil
	}
}
