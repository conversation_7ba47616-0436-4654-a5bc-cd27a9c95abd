package kmz

import (
	"encoding/xml"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
)

type Lnglat []float64
type PolygonCoord [][]float64

type PolygonGeometry struct {
	Type        string         `json:"type"`
	Coordinates []PolygonCoord `json:"coordinates"`
}

type Airline struct {
	Id             int32
	Type           string // 线状航线:waypoint 平面航线:mapping2d 倾斜航线:mapping3d 带状航线:mappingStrip
	Speed          float32
	TranSpeed      float32
	Height         float32
	ReturnHeight   float32
	SecurityHeight float32
	FlytoMode      string
	FinishAction   string
	ExitOnRCLost   string
	RCLostAction   string
	FenceArea      *PolygonGeometry
}

type Waypoint struct {
	Lnglat      Lnglat  `json:"lnglat"`
	Speed       float32 `json:"speed"`
	Serial      int     `json:"serial"`
	Height      float32 `json:"height"`
	TurnMode    string  `json:"turnMode"`
	TurnDamping float32 `json:"turnDamping"`
}

type Mission struct {
	FlytoMode      string  `xml:"flyToWaylineMode" validate:"required,oneof=safely pointToPoint"`
	TranSpeed      float32 `xml:"globalTransitionalSpeed" validate:"required,numeric,min=1,max=15"`
	ReturnHeight   float32 `xml:"globalRTHHeight" validate:"omitempty,numeric,gte=0"`
	SecurityHeight float32 `xml:"takeOffSecurityHeight" validate:"required,numeric,min=1.5,max=1500"`
	FinishAction   string  `xml:"finishAction" validate:"required,oneof=goHome noAction autoLand gotoFirstWaypoint"`
	ExitOnRCLost   string  `xml:"exitOnRCLost" validate:"required,oneof=goContinue executeLostAction"`
	RCLostAction   string  `xml:"executeRCLostAction" validate:"required_if=ExitOnRCLost executeLostAction,oneof=goBack landing hover"`
}

type KMLFolder struct {
	Height     float32 `xml:"globalHeight" validate:"omitempty,numeric,gte=0"`
	Speed      float32 `xml:"autoFlightSpeed" validate:"required,numeric,min=1,max=15"`
	Type       string  `xml:"templateType" validate:"required,oneof=waypoint mapping2d mapping3d mappingStrip"`
	Placemarks []*struct {
		Point      *Coord   `xml:"Point" validate:"omitempty"`
		Polygon    *Polygon `xml:"Polygon" validate:"omitempty"`
		LineString *Coord   `xml:"LineString" validate:"omitempty"`
		Height     float32  `xml:"height" validate:"numeric"`
	} `xml:"Placemark" validate:"required,dive,required"`
}

// type KMLDocument struct {
// 	Author  string     `xml:"author"`
// 	Folder  *KMLFolder `xml:"Folder" validate:"required"`
// 	Mission *Mission   `xml:"missionConfig" validate:"required"`
// }

type KMLRoot struct {
	XMLName  xml.Name `xml:"kml" validate:"required"`
	Document struct {
		Author  string     `xml:"author"`
		Folder  *KMLFolder `xml:"Folder" validate:"required"`
		Mission *Mission   `xml:"missionConfig" validate:"required"`
	} `xml:"Document" validate:"required"`
}

// type WPMLPlacemark struct {
// 	Point       *Coord      `xml:"Point" validate:"required"`
// 	Index       int         `xml:"index" validate:"number,min=0,max=999"`
// 	Speed       float32     `xml:"waypointSpeed" validate:"numeric,min=1,max=15"`
// 	Height      float32     `xml:"executeHeight" validate:"numeric,gte=0"`
// 	ActionGroup ActionGroup `xml:"actionGroup" validate:"required"`
// 	TurnParam   TurnParam   `xml:"waypointTurnParam" validate:"required"`
// }

type WPMLFolder struct {
	Placemarks []struct {
		Point        *Coord        `xml:"Point" validate:"required"`
		Index        int           `xml:"index" validate:"number,min=0,max=999"`
		Speed        float32       `xml:"waypointSpeed" validate:"numeric,min=1,max=15"`
		Height       float32       `xml:"executeHeight" validate:"numeric"`
		ActionGroups []ActionGroup `xml:"actionGroup" validate:"omitempty,unique=Id,dive,required"`
		TurnParam    TurnParam     `xml:"waypointTurnParam" validate:"required"`
	} `xml:"Placemark" validate:"required,unique=Index,dive,required"`
}

// type WPMLDocument struct {
// 	Folder  *WPMLFolder `xml:"Folder" validate:"required"`
// 	Mission *Mission    `xml:"missionConfig" validate:"required"`
// }

type WPMLRoot struct {
	XMLName  xml.Name `xml:"kml" validate:"required"`
	Document struct {
		Folder  *WPMLFolder `xml:"Folder" validate:"required"`
		Mission *Mission    `xml:"missionConfig" validate:"required"`
	} `xml:"Document" validate:"required"`
}

type Coord struct {
	Coordinates string `xml:"coordinates" validate:"required,iscoords"`
}

type Polygon struct {
	Boundary struct {
		LinearRing Coord `xml:"LinearRing"`
	} `xml:"outerBoundaryIs"`
}

func (p Coord) FormatPoints() PolygonCoord {
	linear := strings.Split(p.Coordinates, ",0")
	points := make([][]float64, 0)
	for _, point := range linear {
		point := strings.TrimSpace(point)
		if point == "" {
			continue
		}
		lnglat := strings.Split(point, ",")
		points = append(points, lo.Map(lnglat, func(coord string, _ int) float64 { return conv.Atof(coord) }))
	}
	return points
}

func (p Coord) AsPoint() Lnglat {
	return p.FormatPoints()[0]
}

func GeneratePolygon(coords PolygonCoord) *PolygonGeometry {
	return &PolygonGeometry{
		Type:        "Polygon",
		Coordinates: []PolygonCoord{coords},
	}
}

type TurnMode string

const (
	Coordinate   TurnMode = "coordinateTurn"                           // 变焦
	PointStopWCC TurnMode = "toPointAndStopWithContinuityCurvature"    // 悬停等待
	PointPassWCC TurnMode = "toPointAndPassWithContinuityCurvature"    // 单拍
	PointStopWDC TurnMode = "toPointAndStopWithDiscontinuityCurvature" // 对焦
)

type TurnParam struct {
	Mode    TurnMode `xml:"waypointTurnMode"`
	Damping float32  `xml:"waypointTurnDampingDist" validate:"numeric,gte=0"`
}

type ActionGroup struct {
	Id         int32  `xml:"actionGroupId" validate:"numeric,gte=0"`
	Mode       string `xml:"actionGroupMode" validate:"required,oneof=sequence"`
	StartIndex int32  `xml:"actionGroupStartIndex" validate:"numeric,gte=0"`
	EndIndex   int32  `xml:"actionGroupEndIndex" validate:"numeric,gte=0,gtefield=StartIndex"`
	Trigger    struct {
		Type  string  `xml:"actionTriggerType" validate:"required,oneof=reachPoint multipleTiming multipleDistance betweenAdjacentPoints"`
		Param float32 `xml:"actionTriggerParam" validate:"numeric,gte=0"`
	} `xml:"actionTrigger"`
	Actions []Action `xml:"action" validate:"required,unique=Id,dive,required"`
}

type FuncName string

const (
	Zoom               FuncName = "zoom"               // 变焦
	Focus              FuncName = "focus"              // 对焦
	Hover              FuncName = "hover"              // 悬停等待
	PanoShot           FuncName = "panoShot"           // 全景拍
	TakePhoto          FuncName = "takePhoto"          // 单拍
	RotateYaw          FuncName = "rotateYaw"          // 飞行器偏航
	StopRecord         FuncName = "stopRecord"         // 结束录像
	StartRecord        FuncName = "startRecord"        // 开始录像
	SetFocusType       FuncName = "setFocusType"       // 设置焦距类型
	GimbalRotate       FuncName = "gimbalRotate"       // 旋转云台
	CustomDirName      FuncName = "customDirName"      // 创建新文件夹
	OrientedShoot      FuncName = "orientedShoot"      // 精准复拍动作
	StopTimeLapse      FuncName = "stopTimeLapse"      // 停止时间滞后
	StartTimeLapse     FuncName = "startTimeLapse"     // 启动时间滞后
	GimbalEvenlyRotate FuncName = "gimbalEvenlyRotate" // 航段间均匀转动云台pitch角
)

type Action struct {
	Id        int32     `xml:"actionId" validate:"numeric,gte=0"`
	Func      FuncName  `xml:"actionActuatorFunc" validate:"required,oneof=zoom focus hover panoShot takePhoto rotateYaw stopRecord startRecord setFocusType gimbalRotate orientedShoot stopTimeLapse startTimeLapse gimbalEvenlyRotate"`
	FuncParam FuncParam `xml:"actionActuatorFuncParam" validate:"required"`
}

type FuncParam struct {
	// 悬停等待
	HoverTime float32 `xml:"hoverTime" validate:"omitempty,numeric,gt=0"` // 飞行器悬停等待时间,s

	// 变焦
	FocalLength float32 `xml:"focalLength" validate:"omitempty,numeric,gt=0"` // 变焦焦距

	// 设置焦距类型
	SetFocalType string `xml:"cameraFocusType" validate:"omitempty,alphanum"` // 焦距类型

	// 飞行器偏航
	Heading  float32 `xml:"aircraftHeading" validate:"omitempty,numeric,min=-180,max=180"`          // 飞行器目标偏航角
	PathMode string  `xml:"aircraftPathMode" validate:"omitempty,oneof=clockwise counterClockwise"` // 飞行器偏航角转动模式

	// 单拍, 开始录像
	FileSuffix string `xml:"fileSuffix" validate:"omitempty"` // 照片/视频文件后缀
	// 单拍, 全景拍, 开始录像
	GlobalLensIndex int `xml:"useGlobalPayloadLensIndex" validate:"omitempty,number,oneof=0 1"` // 是否使用全局存储类型
	// 结束录像, 全景拍, 停止时间滞后
	LensIndex string `xml:"payloadLensIndex" validate:"omitempty"` // 照片/视频存储类型: zoom wide ir narrow_band
	// 全景拍
	PanoShotSubMode string `xml:"panoShotSubMode" validate:"omitempty"` // 照片/视频存储类型: zoom wide ir narrow_band
	// 启动时间滞后
	MinShootInterval float32 `xml:"minShootInterval" validate:"omitempty,number,min=0"` // 最小拍照间隔

	// 对焦
	IsPoint      int     `xml:"isPointFocus" validate:"omitempty,number,oneof=0 1"`         // 是否点对焦
	XAxis        float32 `xml:"focusX" validate:"omitempty,numeric,min=0,max=1"`            // 对焦点位置X
	YAxis        float32 `xml:"focusY" validate:"omitempty,numeric,min=0,max=1"`            // 对焦点位置Y
	IsInfinite   int     `xml:"isInfiniteFocus" validate:"omitempty,number,oneof=0 1"`      // 是否无穷远对焦
	RegionWidth  float32 `xml:"focusRegionWidth" validate:"omitempty,numeric,min=0,max=1"`  // 对焦区域宽度比
	RegionHeight float32 `xml:"focusRegionHeight" validate:"omitempty,numeric,min=0,max=1"` // 对焦区域高度比

	// 旋转云台
	Mode        string  `xml:"gimbalRotateMode" validate:"omitempty,oneof=relativeAngle absoluteAngle"` // 转动模式
	YawBase     string  `xml:"gimbalHeadingYawBase" validate:"omitempty,oneof=north aircraft"`          // 偏航角转动坐标系
	YawAngle    float32 `xml:"gimbalYawRotateAngle" validate:"omitempty,numeric"`                       // Yaw转动角度
	YawEnable   int32   `xml:"gimbalYawRotateEnable" validate:"omitempty,number,oneof=0 1"`             // 是否使能Yaw转动
	RollAngle   float32 `xml:"gimbalRollRotateAngle" validate:"omitempty,numeric"`                      // Roll转动角度
	RollEnable  int32   `xml:"gimbalRollRotateEnable" validate:"omitempty,number,oneof=0 1"`            // 是否使能Roll转动
	PitchEnable int32   `xml:"gimbalPitchRotateEnable" validate:"omitempty,number,oneof=0 1"`           // 是否使能Pitch转动
	TimeSecond  int32   `xml:"gimbalRotateTime" validate:"omitempty,numeric"`                           // 完成转动用时
	TimeEnable  int32   `xml:"gimbalRotateTimeEnable" validate:"omitempty,number,oneof=0 1"`            // 是否使能转动时间
	// 航段间均匀转动云台pitch角
	PitchAngle float32 `xml:"gimbalPitchRotateAngle" validate:"omitempty,numeric"` // Pitch转动角度

	// 公共字段
	PositionIndex int32 `xml:"payloadPositionIndex" validate:"omitempty,number,oneof=0 1 2"` // 负载挂载位置
}

var (
	uni      *ut.UniversalTranslator
	validate *validator.Validate
)

// IsCoords implements validator.Func
func IsCoords(fl validator.FieldLevel) bool {
	linear := strings.Split(fl.Field().String(), ",0")
	for _, point := range linear {
		point := strings.TrimSpace(point)
		if point != "" {
			lnglat := strings.Split(point, ",")
			if err := validate.Var(lnglat[0], "required,longitude"); err != nil {
				return false
			}
			if err := validate.Var(lnglat[1], "required,latitude"); err != nil {
				return false
			}
		}
	}
	return true
}

func ParseTemplate(dirPath string) (*Airline, error) {
	filename := fmt.Sprintf("%s/wpmz/template.kml", dirPath)
	xmlFile, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer os.Remove(filename)
	defer xmlFile.Close()
	byteValue, _ := io.ReadAll(xmlFile)
	var root KMLRoot
	if err := xml.Unmarshal(byteValue, &root); err != nil {
		return nil, err
	}

	zh := zh.New()
	uni = ut.New(zh, zh)
	validate = validator.New()
	trans, _ := uni.GetTranslator("zh")
	zh_translations.RegisterDefaultTranslations(validate, trans)
	validate.RegisterValidation("iscoords", IsCoords)

	folder := root.Document.Folder
	if err := validate.Struct(root); err != nil {
		errs := err.(validator.ValidationErrors)
		for _, e := range errs {
			fmt.Println(e.Translate(trans))
		}
		return nil, err
	}
	airline := &Airline{}
	if folder != nil {
		copier.Copy(airline, folder)
	}
	mission := root.Document.Mission
	if mission != nil {
		copier.Copy(airline, mission)
	}
	placemark := folder.Placemarks[0]
	if placemark == nil {
		return airline, nil
	}
	switch airline.Type {
	case "mapping2d", "mapping3d":
		list := placemark.Polygon.Boundary.LinearRing.FormatPoints()
		airline.FenceArea = GeneratePolygon(list)
	case "mappingStrip":
		list := placemark.LineString.FormatPoints()
		airline.FenceArea = GeneratePolygon(list)
	}
	return airline, nil
}

func ParseWaypoints(dirPath string) ([]*Waypoint, error) {
	filename := fmt.Sprintf("%s/wpmz/waylines.wpml", dirPath)
	xmlFile, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer os.Remove(filename)
	defer xmlFile.Close()
	waypoints := make([]*Waypoint, 0)
	byteValue, _ := io.ReadAll(xmlFile)
	var root WPMLRoot
	if err := xml.Unmarshal(byteValue, &root); err != nil {
		return waypoints, err
	}
	folder := root.Document.Folder
	if err := validate.Struct(root); err != nil {
		return nil, err
	}
	for _, pm := range folder.Placemarks {
		waypoint := &Waypoint{
			Lnglat: pm.Point.AsPoint(),
			Speed:  pm.Speed, Serial: pm.Index, Height: pm.Height,
			TurnMode: string(pm.TurnParam.Mode), TurnDamping: pm.TurnParam.Damping,
		}
		waypoints = append(waypoints, waypoint)
	}
	return waypoints, nil
}
