package kmz

import (
	"archive/zip"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"strings"
)

const tempFile = "dji-wayline.kmz"

func Sign(dirpath string, reader io.ReadCloser) (string, error) {
	defer reader.Close()
	// 创建资源目录
	if err := os.MkdirAll(fmt.Sprintf("%s/wpmz", dirpath), os.ModePerm); err != nil {
		fmt.Println("Error make directory:", err)
		return "", err
	}

	// 创建本地文件
	filename := fmt.Sprintf("%s/%s", dirpath, tempFile)
	localFile, err := os.Create(filename)
	if err != nil {
		fmt.Println("Error creating local file:", err)
		return "", err
	}
	defer localFile.Close()
	if _, err = io.Copy(localFile, reader); err != nil {
		fmt.Println("Error copy S3 file:", err)
		return "", err
	}

	// 读取本地文件流，计算MD5签名
	hasher := md5.New()
	localReader, err := os.Open(filename)
	if err != nil {
		fmt.Println("Error creating local file:", err)
		return "", err
	}
	defer localReader.Close()
	_, err = io.Copy(hasher, localReader)
	if err != nil {
		return "", err
	}
	signature := strings.ToUpper(hex.EncodeToString(hasher.Sum(nil)))
	return signature, nil
}

func Unzip(dirpath string) error {
	filename := fmt.Sprintf("%s/%s", dirpath, tempFile)
	zipReader, err := zip.OpenReader(filename)
	if err != nil {
		fmt.Println("Error creating zip reader:", err)
		return err
	}
	defer os.Remove(filename)
	defer zipReader.Close()

	for _, f := range zipReader.File {
		zipFile, err := f.Open()
		if err != nil {
			fmt.Println("Error openning zip file:", err)
			continue
		}
		defer zipFile.Close()

		outFile, err := os.Create(fmt.Sprintf("%s/%s", dirpath, f.Name))
		if err != nil {
			fmt.Println("Error creating output file:", err)
			continue
		}
		defer outFile.Close()

		_, err = io.Copy(outFile, zipFile)
		if err != nil {
			fmt.Println("Error copying file:", err)
			continue
		}

		fmt.Println("Extracted:", f.Name)
	}
	return nil
}
