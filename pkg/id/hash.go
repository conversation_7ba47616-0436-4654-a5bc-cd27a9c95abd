package id

import (
	"encoding/binary"
	"encoding/hex"
	"strings"

	"github.com/cespare/xxhash/v2"
	"golang.org/x/crypto/blake2b"
)

func NewHashSN(length int, inputs ...[]byte) string {
	hasher, _ := blake2b.New(length, nil)
	for _, input := range inputs {
		hasher.Write(input)
	}
	digest := hasher.Sum(nil)
	return strings.ToUpper(hex.EncodeToString(digest))
}

func NewXXHashId(data []byte) string {
	ret := make([]byte, 8)
	binary.BigEndian.PutUint64(ret, xxhash.Sum64(data))
	return hex.EncodeToString(ret)
}
