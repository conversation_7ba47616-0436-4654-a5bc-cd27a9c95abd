package main

import (
	"fmt"
	"reflect"

	"github.com/stoewer/go-strcase"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/td"
)

var db = td.DB

func main() {
	fmt.Println(genDBsql())
	fmt.Println(useDB())
	fmt.Println(genDockPropertyStable())
	fmt.Println(genControllerPropertyStable())
	fmt.Println(genDockDronPropertyStable())
	fmt.Println(genDockEventStable())
	fmt.Println(genCameraPropertyStable())
}

func genDBsql() string {
	return "create database if not exists " + db + " KEEP 365 vgroups 3 REPLICA 1;"
}

func useDB() string {
	return "use " + db + ";"
}
func genCameraPropertyStable() string {
	sql := "CREATE STABLE IF NOT EXISTS " + td.DroneCameraSTableName
	sql += `(rx_time timestamp, ts timestamp, `
	sql = appendColumnName(sql, "camera", reflect.TypeOf(biz.DroneCameraState{}))
	sql += " extra binary(4096))"
	sql += " TAGS (sn binary(30), device_id binary(50), drone_sn binary(50), payload binary(50));"
	return sql
}

func genDockPropertyStable() string {
	sql := "CREATE STABLE IF NOT EXISTS " + td.DockPropertySTableName
	sql += `(rx_time timestamp, ts timestamp, state_bitmap BIGINT UNSIGNED, id VARCHAR(60), `
	sql = appendColumnName(sql, "state", reflect.TypeOf(biz.DockState{}))
	sql = appendColumnName(sql, "position", reflect.TypeOf(biz.PositionState{}))
	sql = appendColumnName(sql, "network", reflect.TypeOf(biz.NetworkState{}))
	sql = appendColumnName(sql, "flight", reflect.TypeOf(biz.FlightTaskState{}))
	sql = appendColumnName(sql, "env", reflect.TypeOf(biz.EnvironmentState{}))
	sql = appendColumnName(sql, "wireless", reflect.TypeOf(biz.WirelessLinkState{}))
	sql = appendColumnName(sql, "elec", reflect.TypeOf(biz.ElecPowerState{}))
	sql = appendColumnName(sql, "battery", reflect.TypeOf(biz.BatteryChargeState{}))
	sql += " extra binary(4096))"
	sql += " TAGS (sn binary(30), device_id binary(50));"
	return sql
}

func genControllerPropertyStable() string {
	sql := "CREATE STABLE IF NOT EXISTS " + td.RemoteControllerPropertyStableName
	sql += `(rx_time timestamp, ts timestamp, state_bitmap BIGINT UNSIGNED, id VARCHAR(60), `
	sql = appendColumnName(sql, "rc_state", reflect.TypeOf(biz.RemoteControllerState{}))
	sql = appendColumnName(sql, "wireless", reflect.TypeOf(biz.WirelessLinkState{}))
	sql += " extra binary(4096))"
	sql += " TAGS (sn binary(30), device_id binary(50));"
	return sql
}

func genDockDronPropertyStable() string {
	sql := "CREATE STABLE IF NOT EXISTS " + td.DronePropertySTableName
	sql += "(rx_time timestamp, ts timestamp, state_bitmap BIGINT UNSIGNED, id VARCHAR(60), mode TINYINT, "
	sql = appendColumnName(sql, "drone_flight", reflect.TypeOf(biz.DroneFlightState{}))
	sql = appendColumnName(sql, "drone_position", reflect.TypeOf(biz.PositionState{}))
	sql = appendColumnName(sql, "drone_battery", reflect.TypeOf(biz.DroneBatteryState{}))
	sql = appendColumnName(sql, "drone_storage", reflect.TypeOf(biz.Storage{}))
	sql += " extra binary(4096))"
	sql += " TAGS (sn binary(30), device_id binary(50), drone_sn binary(50), drone_type binary(20));"
	return sql
}

func genDockEventStable() string {
	return `CREATE STABLE IF NOT EXISTS ` + td.DockEventSTableName +
		`(rx_time timestamp, occurred_time timestamp, type int, id varchar(60), data varchar(4096) ) TAGS (sn VARCHAR(30),device_id binary(50));`
}

func appendColumnName(sql, prefix string, t reflect.Type) string {
	for i := 0; i < t.NumField(); i++ {
		f := t.Field(i)
		if f.IsExported() {
			sql += fmt.Sprintf("%s_%s %s, ", prefix, strcase.SnakeCase(f.Name), getColumnType(f.Type))
		}
	}
	return sql
}

func getColumnType(t reflect.Type) string {
	ct := "binary(1024)"
	switch t.Kind() {
	case reflect.Bool:
		ct = "BOOL"
	case reflect.Float32, reflect.Float64:
		ct = "DOUBLE"
	case reflect.Int8, reflect.Int16, reflect.Int32:
		ct = "INT"
	case reflect.Uint8, reflect.Uint16, reflect.Uint32:
		ct = "INT UNSIGNED"
	case reflect.Int, reflect.Int64:
		ct = "BIGINT"
	case reflect.Uint, reflect.Uint64:
		ct = "BIGINT UNSIGNED"
	case reflect.String:
		ct = "VARCHAR(1024)"
	case reflect.Array, reflect.Slice, reflect.Struct:
		ct = "VARCHAR(4096)"
	default:
		panic("unknown type: " + t.Kind().String())
	}
	return ct
}
