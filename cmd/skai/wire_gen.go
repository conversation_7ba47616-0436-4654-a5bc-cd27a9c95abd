// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/data"
	"gitlab.sensoro.com/skai/skai/internal/server"
	"gitlab.sensoro.com/skai/skai/internal/service"
	"go.opentelemetry.io/otel/trace"
)

// Injectors from wire.go:

// initApp init kratos application.
func initApp(confServer *conf.Server, confData *conf.Data, logger log.Logger, traceTracerProvider trace.TracerProvider) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(confServer, confData, logger)
	if err != nil {
		return nil, nil, err
	}
	airlineRepo := data.NewAirlineRepo(logger, dataData)
	delayRepo := data.NewDelayRepo(logger, dataData)
	eventRepo := data.NewEventRepo(logger, dataData)
	deviceRepo := data.NewDeviceRepo(logger, dataData)
	connectRepo, err := data.NewConnectRepo(logger, dataData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	mqttRepo, err := data.NewMqttRepo(logger, dataData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	operationRepo := data.NewOperationRepo(logger, dataData)
	config, err := data.NewS3Config(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	client, err := data.NewS3Client(config)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	presignClient := data.NewS3PresignClient(confData, client)
	simpleStorageRepo, err := data.NewSimpleStorageRepo(logger, dataData, config, client, presignClient)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	voyageRepo := data.NewVoyageRepo(logger, dataData)
	signer, err := data.NewSigner(confData, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	mediaRepo := data.NewMediaRepo(dataData, logger, signer)
	tsMetaStore, err := data.NewTSMetaStore(dataData, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	liveRepo, err := data.NewLiveRepo(logger, dataData, confData, config, signer, tsMetaStore)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	missionRepo := data.NewMissionRepo(logger, dataData)
	executionRepo := data.NewExecutionRepo(logger, dataData)
	snapshotRepo := data.NewSnapshotRepo(logger, dataData)
	analyserRepo := data.NewAnalyserRepo(logger, confServer)
	logfileRepo := data.NewLogfileRepo(logger, dataData)
	connectUsecase := biz.NewConnectUsecase(logger, airlineRepo, delayRepo, eventRepo, deviceRepo, connectRepo, mqttRepo, operationRepo, simpleStorageRepo, voyageRepo, mediaRepo, liveRepo, missionRepo, executionRepo, snapshotRepo, analyserRepo, logfileRepo)
	authRepo := data.NewAuthRepo(logger, dataData)
	propertyRepo := data.NewPropertyRepo(logger, dataData)
	deviceUsecase := biz.NewDeviceUsecase(confData, logger, authRepo, eventRepo, delayRepo, deviceRepo, mqttRepo, operationRepo, voyageRepo, propertyRepo, logfileRepo, simpleStorageRepo)
	adminService := service.NewAdminService(logger, connectUsecase, deviceUsecase)
	authUsecase := biz.NewAuthUsecase(logger, authRepo)
	authService := service.NewAuthService(logger, authUsecase)
	waypointRepo := data.NewWaypointRepo(logger, dataData)
	airlineUsecase := biz.NewAirlineUsecase(confData, logger, authRepo, deviceRepo, airlineRepo, waypointRepo, simpleStorageRepo)
	aiEventRepo := data.NewAiEventRepo(logger, dataData, confData)
	missionUsecase := biz.NewMissionUsecase(logger, authRepo, delayRepo, executionRepo, aiEventRepo, missionRepo)
	airlineService := service.NewAirlineService(logger, airlineUsecase, missionUsecase)
	dockEventRepo := data.NewDockEventRepo(logger, dataData)
	thingUsecase := biz.NewThingUsecase(logger, authRepo, deviceRepo, propertyRepo, connectRepo, mqttRepo, dockEventRepo, voyageRepo, simpleStorageRepo, airlineRepo, waypointRepo)
	liveUsecase := biz.NewLiveUsecase(logger, mediaRepo, liveRepo, mqttRepo, connectRepo, deviceRepo, delayRepo)
	deviceService := service.NewDeviceService(logger, airlineUsecase, connectUsecase, deviceUsecase, missionUsecase, thingUsecase, liveUsecase)
	voyageUsecase := biz.NewVoyageUsecase(logger, authRepo, airlineRepo, voyageRepo, propertyRepo)
	voyageService := service.NewVoyageService(logger, deviceUsecase, voyageUsecase)
	onemapUsecase := biz.NewOnemapUsecase(logger, authRepo, deviceRepo, liveRepo, mediaRepo, voyageRepo)
	onemapService := service.NewOnemapService(logger, onemapUsecase, liveUsecase)
	sessionRepo := data.NewSessionRepo(logger, dataData)
	sessionUsecase := biz.NewSessionUsecase(logger, sessionRepo)
	sessionService := service.NewSessionService(logger, sessionUsecase)
	waypointUsecase := biz.NewWaypointUsecase(logger, authRepo, airlineRepo, waypointRepo)
	waypointService := service.NewWaypointService(logger, waypointUsecase)
	lockRepo := data.NewLockRepo(dataData, logger)
	mediaUsecase := biz.NewMediaUsecase(logger, mediaRepo, authRepo, simpleStorageRepo, voyageRepo, lockRepo, delayRepo, mqttRepo, confData)
	mediaService := service.NewMediaService(logger, authUsecase, mediaUsecase, liveUsecase, deviceUsecase)
	storageUsecase := biz.NewStorageUsecase(logger, confData, simpleStorageRepo, authRepo)
	storageService := service.NewStorageService(logger, storageUsecase)
	cloudUsecase := biz.NewCloudUsecase(logger, authRepo, airlineRepo, mediaRepo, simpleStorageRepo, deviceRepo, voyageRepo, mqttRepo)
	cloudService := service.NewCloudService(logger, cloudUsecase)
	configRepo := data.NewConfigRepo(logger, dataData)
	configUsecase := biz.NewConfigUsecase(logger, authRepo, configRepo)
	configService := service.NewConfigService(logger, configUsecase)
	galleryService := service.NewGalleryService(authUsecase, mediaUsecase)
	missionService := service.NewMissionService(logger, airlineUsecase, deviceUsecase, missionUsecase, thingUsecase)
	operationUsecase := biz.NewOperationUsecase(logger, operationRepo, deviceRepo, connectRepo, mqttRepo, delayRepo)
	deviceOperationService := service.NewDeviceOperationService(logger, authUsecase, deviceUsecase, operationUsecase, liveUsecase)
	internalLiveService := service.NewInternalLiveService(logger, liveUsecase, deviceUsecase)
	subjectRepo := data.NewSubjectRepo(logger, dataData)
	subjectUsecase := biz.NewSubjectUsecase(logger, authRepo, subjectRepo)
	subjectService := service.NewSubjectService(logger, subjectUsecase)
	annotationRepo := data.NewAnnotationRepo(logger, dataData)
	annotationUsecase := biz.NewAnnotationUsecase(logger, annotationRepo, subjectRepo, aiEventRepo, mediaRepo, deviceRepo, voyageRepo, missionRepo, simpleStorageRepo, propertyRepo)
	annotationService := service.NewAnnotationService(logger, authUsecase, annotationUsecase)
	httpServer := server.NewHTTPServer(logger, traceTracerProvider, confServer, adminService, authService, airlineService, deviceService, voyageService, onemapService, sessionService, waypointService, mediaService, storageService, cloudService, configService, galleryService, missionService, deviceOperationService, internalLiveService, subjectService, annotationService)
	connectService := service.NewConnectService(logger, airlineUsecase, connectUsecase)
	grpcServer := server.NewGRPCServer(logger, confServer, connectService)
	aiEventUsecase := biz.NewAiEventUsecase(logger, aiEventRepo, voyageRepo, airlineRepo, simpleStorageRepo)
	linsConsumer := service.NewLinsConsumer(logger, confData, aiEventUsecase, deviceUsecase, missionUsecase, subjectUsecase, voyageUsecase)
	kafkaServer := server.NewSkaiKafkaServer(logger, confData, linsConsumer)
	mqttUsecase := biz.NewMQTTUsecase(logger, mqttRepo)
	delayUsecase := biz.NewDelayUsecase(logger, delayRepo)
	djiConnectService := service.NewDJIConnectService(logger, aiEventUsecase, thingUsecase, mediaUsecase, mqttUsecase, delayUsecase, missionUsecase)
	subscriber := server.NewMQTTSubServer(logger, confData, djiConnectService)
	radarSubscriber := server.NewRadarSubServer(logger, confData)
	archiveTaskRepo := data.NewArchiveTaskRepo(dataData, logger, tsMetaStore)
	archiveTaskUsecase := biz.NewArchiveTaskUsecase(logger, mediaRepo, simpleStorageRepo, archiveTaskRepo, lockRepo)
	archiveTaskService := service.NewArchiveTaskService(logger, archiveTaskUsecase)
	taskServer := server.NewTaskServer(confServer, logger, archiveTaskService)
	app := newApp(logger, httpServer, grpcServer, kafkaServer, subscriber, radarSubscriber, taskServer)
	return app, func() {
		cleanup()
	}, nil
}
