package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/server"
	"gitlab.sensoro.com/skai/skai/pkg/mqtt"
	"gitlab.sensoro.com/skai/skai/pkg/radar"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/trace"

	tracesdk "go.opentelemetry.io/otel/sdk/trace"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/env"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"

	myLog "gitlab.sensoro.com/go-sensoro/kratos-utils/log"
	"gitlab.sensoro.com/go-sensoro/lins-common/kafka"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string
	// Mode 服务运行模式，支持两种 server (默认) 或 connect
	Mode string
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
	flag.StringVar(&Mode, "mode", "server", "if the process should connect to mqtt broker")
}

func newApp(logger log.Logger, hs *http.Server, gs *grpc.Server, ks *kafka.Server, ms *mqtt.Subscriber, rs *radar.Subscriber, ts *server.TaskServer) *kratos.App {
	startServers := []transport.Server{
		hs,
		gs,
	}
	if Mode == "connect" {
		startServers = append(startServers, ms)
	} else {
		startServers = append(startServers, rs, ks, ts)
	}
	return kratos.New(
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(startServers...),
	)
}

func tracerProvider(url string) (*tracesdk.TracerProvider, error) {
	if url == "" {
		return nil, nil
	}
	// Create the Jaeger exporter
	exp, err := jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(url)))
	if err != nil {
		return nil, err
	}
	tp := tracesdk.NewTracerProvider(
		tracesdk.WithSampler(tracesdk.AlwaysSample()),
		// Always be sure to batch in production.
		tracesdk.WithBatcher(exp),
		// Record information about this application in an Resource.
		tracesdk.WithResource(resource.NewSchemaless(
			semconv.ServiceNameKey.String(Name),
			attribute.String("environment", os.Getenv("RUN_ENV")),
		)),
	)
	return tp, nil
}

func NewTraceIdValuer() log.Valuer {
	return func(ctx context.Context) interface{} {
		spanCtx := trace.SpanContextFromContext(ctx)
		if spanCtx.HasTraceID() {
			return spanCtx.TraceID().String()
		}
		return ""
	}
}

func main() {
	flag.Parse()

	c := config.New(
		config.WithSource(
			env.NewSource(""),
			file.NewSource(flagconf),
		),
	)
	if err := c.Load(); err != nil {
		fmt.Println("load config failed", err)
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		fmt.Println("parse config failed", err)
		panic(err)
	}
	logger := log.With(myLog.NewLogger(myLog.WithLogLevel(bc.Server.LogLevel)),
		"service.name", Name,
		"service.version", Version,
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
	)
	tp, err := tracerProvider(bc.Server.Tracer)
	if err != nil {
		panic(err)
	}
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	defer func(ctx context.Context) {
		// Do not make the application hang when it is shutdown.
		ctx, cancel = context.WithTimeout(ctx, time.Second*5)
		defer cancel()
		if tp != nil {
			if err := tp.Shutdown(ctx); err != nil {
				panic(err)
			}
		}
	}(ctx)
	app, cleanup, err := initApp(bc.Server, bc.Data, logger, nil)
	if err != nil {
		fmt.Println("initApp failed", err)
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
