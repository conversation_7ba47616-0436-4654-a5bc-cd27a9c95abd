FROM harbor.sensoro.com/public/golang:1.21-vips AS builder

WORKDIR /src

RUN mkdir -p -m 0600 ~/.ssh && ssh-keyscan gitlab.sensoro.com >> ~/.ssh/known_hosts; \
    git config --add --global url."**********************:".insteadOf https://gitlab.sensoro.com; \
    git config --global  user.name "cicd"; \
    git config --global user.email "<EMAIL>";
ADD go.mod go.sum ./


RUN --mount=type=ssh GOPROXY=https://goproxy.cn GOPRIVATE=gitlab.sensoro.com go mod download
COPY . /src
RUN git config --global --add safe.directory /src
RUN GOOS=linux make build

# FROM harbor.sensoro.com/public/debian:libvips

# RUN apt-get update && apt-get install -y curl
# RUN apt-get clean && rm -rf /var/lib/apt/lists /var/cache/apt/archives

FROM harbor.sensoro.com/public/debian:bullseye
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && \
    apt-get install -y --no-install-recommends curl libvips-tools && \
    apt-get clean && rm -rf /var/lib/apt/lists /var/cache/apt/archives

WORKDIR /app
COPY --from=builder /src/bin /app

EXPOSE 8000
EXPOSE 9000
COPY  ./configs/config.yaml /data/conf.yaml

CMD ["/app/skai", "-conf", "/data/conf.yaml"]