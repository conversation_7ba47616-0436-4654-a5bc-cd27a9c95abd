map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

server {
  listen 0.0.0.0:8080;
  server_name skai-api;
  proxy_http_version 1.1;

  location /api/auth/ {
    proxy_pass http://auth_server:8080/api/;
  }

  location /api/skai/ {
    proxy_pass http://skai_server:8000/api/;
  }

  location /api/mqtt {
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header  Connection $connection_upgrade;
    proxy_set_header Host  $http_host;
    proxy_read_timeout 600s;
    proxy_pass http://emqx:8083/mqtt;
  }

  location /skai {
    proxy_set_header Host  $http_host;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_connect_timeout 300;
    proxy_pass http://minio:9000;
  }
}

server {
  listen 0.0.0.0:8888;
  server_name cloud-api;
  proxy_http_version 1.1;

  location /media/api/v1/ {
    proxy_pass http://skai_server:8000/api/v1/cloud/media/;
  }

  location /storage/api/v1/ {
    proxy_pass http://skai_server:8000/api/v1/cloud/storage/;
  }

  location /wayline/api/v1/ {
    proxy_pass http://skai_server:8000/api/v1/cloud/wayline/;
  }
}
