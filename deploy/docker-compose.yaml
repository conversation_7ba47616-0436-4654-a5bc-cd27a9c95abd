version: '3'

x-min-logging: &default-logging
  options:
    max-size: "10m"
    max-file: 3
  driver: json-file
x-appLogging: &app-logging
  options:
    max-size: "100m"
    max-file: 5
  driver: json-file

services:
  emqx:
    image: emqx/emqx:5.1.0
    container_name: emqx
    restart: always
    logging: *default-logging
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx_ctl", "status"]
      interval: 5s
      timeout: 25s
      retries: 5
    ports:
      - 1883:1883
      - 8083:8083
      - 18083:18083
      # - 8883:8883  ssl
      # - 8084:8084 wss
    environment:
      - EMQX_DASHBOARD__DEFAULT_PASSWORD=${MQTT_REST_ADMIN_PASSWORD}
      - EMQX_API_KEY__BOOTSTRAP_FILE=/opt/skai/apikeys
    volumes:
      - ./config/emqx_apikeys:/opt/skai/apikeys:ro
  postgres:
    image: postgis/postgis:14-3.3-alpine
    restart: always
    container_name: postgres
    logging: *default-logging
    command: ["postgres", "-c", "shared_buffers=256M", "-c", "max_connections=500"]
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PGSQL_SKAI_USERNAME}"]
      interval: 5s
      timeout: 5s
      retries: 5
    environment:
      - POSTGRES_PASSWORD=${PGSQL_SKAI_PASSWORD}
      - POSTGRES_USER=${PGSQL_SKAI_USERNAME}
      - POSTGRES_DB=skai
    expose:
      - 5432
    # volumes:
    #   - ./data/db:/var/lib/postgresql/data
  redis:
    image: bitnami/redis:6.2
    container_name: redis
    logging: *default-logging
    restart: always
    command: ["/opt/bitnami/scripts/redis/run.sh", "--maxmemory 64mb", "--maxmemory-policy volatile-lru", "--notify-keyspace-events Ex"]
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_DISABLE_COMMANDS=FLUSHDB,FLUSHALL
    expose:
      - 6379
    deploy:
      mode: global
      resources:
        limits:
          cpus: '1'
          memory: 128M
    # volumes:
    #   - ./data/redis:/bitnami/redis/data
  minio:
    image: bitnami/minio:2023.5.18
    container_name: minio
    restart: always
    logging: *default-logging
    environment:
      - MINIO_ROOT_USER=admin
      - MINIO_ROOT_PASSWORD=sensoro2023
      - MINIO_DEFAULT_BUCKETS=${S3_BUCKET}
    ports:
      - 9000:9000
      - 9001:9001
    # volumes:
    #   - ./data/minio:/data
  tdengine:
    image: tdengine/tdengine:*******
    container_name: tdengine
    restart: always
    logging: *default-logging
    environment:
      - TAOS_FQDN=tdengine
    # volumes:
    #   - ./data/taos/data:/var/lib/taos
    #   - ./data/taos/log:/var/log/taos
    expose:
      - 6041
  mediaserver:
    image: harbor.sensoro.com/skai/zlmediakit:latest
    container_name: mediaserver
    restart: always
    logging: *default-logging
    ports:
      - 8080:80
      # rtmp
      - 1935:1935
      - 8443:443
    volumes:
      - ./config/zlmedia.ini:/opt/media/conf/config.ini:ro
      - ./data/media:/opt/media/bin/www
  stream_sidecar:
    image: harbor.sensoro.com/skai/stream-sidecar:latest
    container_name: stream-sidecar
    restart: always
    logging: *default-logging
    ports:
      - 3000:3000
    volumes:
      - ./data/media:/opt/media/bin/www
  nginx:
    image: bitnami/nginx:1.24
    container_name: nginx
    restart: always
    logger: *appLogging
    ports:
      - 80:8080
      - 8888:8888
    volumes:
      - ./config/nginx.conf:/opt/bitnami/nginx/conf/server_blocks/my_server_block.conf:ro
  skai_server:
    image: harbor.sensoro.com/skai/skai:latest
    command: ["/app/skai", "-conf", "/data/conf.yaml", "-mode", "server"]
    logging: *app-logging
    restart: always
    depends_on:
      postgres:
        condition: service_started
      redis:
        condition: service_started
      emqx:
        condition: service_started
    ports:
      - 8000:8000
    expose:
      - 9000
    env_file:
      - skai.env
      - .env
  skai_connect:
    image: harbor.sensoro.com/skai/skai:latest
    command: ["/app/skai", "-conf", "/data/conf.yaml", "-mode", "connect"]
    logging: *app-logging
    restart: always
    depends_on:
      postgres:
        condition: service_started
      redis:
        condition: service_started
      emqx:
        condition: service_started
    expose:
      - 9000
    env_file:
      - skai.env
      - .env






