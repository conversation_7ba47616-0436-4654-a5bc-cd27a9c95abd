syntax = "proto3";

package list;
option go_package = "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list;list";
import "google/protobuf/descriptor.proto";

//protoc --proto_path=/Users/<USER>/Documents/workspace/protoc-gen-list/list --go_out=paths=source_relative:. list.proto
extend google.protobuf.MessageOptions {
    optional bool page = 1073;
}


message FilterOptions {
  optional bool filterable = 1074;
  optional string operator = 1075;
  optional string filter_name = 1076;
}

extend google.protobuf.FieldOptions {
  FilterOptions filter_options = 1987;
}
