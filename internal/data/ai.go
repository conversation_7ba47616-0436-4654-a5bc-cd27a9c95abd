package data

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	image "image/jpeg"
	"io"
	"regexp"
	"strings"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/snownd/cake"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/pkg/radar"
)

type chatRequestBody struct {
	ChatID    string     `json:"chatId"`
	Detail    bool       `json:"detail"`
	Stream    bool       `json:"stream"`
	Messages  []message  `json:"messages"`
	Variables biz.AnyMap `json:"variables"`
}

type message struct {
	Role    string    `json:"role"`
	Content []content `json:"content"`
}

type content struct {
	Type  string     `json:"type"`
	Text  string     `json:"text,omitempty"`
	Image biz.AnyMap `json:"image_url,omitempty"`
}

type aiEventRepo struct {
	c        *conf.Data
	log      *log.Helper
	emclient *client.EventManagerClient
}

func NewAiEventRepo(logger log.Logger, data *Data, c *conf.Data) biz.AiEventRepo {
	return &aiEventRepo{
		c:        c,
		log:      log.NewHelper(logger),
		emclient: data.emCleint,
	}
}

func (r *aiEventRepo) PushAIEvent(ctx context.Context, event *biz.LinsMetaEvent) error {
	err := r.emclient.Sync(ctx, &client.EventSyncData{
		Body: event,
	})
	if err != nil {
		var re cake.RequestError
		if errors.As(err, &re) {
			r.log.Errorf("PushAIEvent %+v error status %d res %s", event, re.StatusCode(), re.Body())
		} else {
			r.log.Error("PushAIEvent %+v error", event, err)

		}
	}
	r.log.WithContext(ctx).Debug("push event: ", event)
	return nil
}

func (r *aiEventRepo) AggAIEvent(ctx context.Context, query *client.AlarmAggQuery) ([]*client.AggTaskItem, error) {
	data, err := r.emclient.AlarmAggByTask(ctx, query)
	if err != nil {
		return nil, err
	}
	if data.Code != 0 {
		r.log.Warnf("call server with aggAlarm byTask failed, code: %d, message: %s", data.Code, data.Message)
		return nil, errors.New(500, "call alarm service failed", data.Message)
	}
	return data.Data.List, nil
}

func (r *aiEventRepo) AnalyseSelfDetect(ctx context.Context, algname biz.AlgName, reader io.ReadCloser) (*biz.AlgoDetectData, error) {
	defer reader.Close()
	if r.c.Algorithm.SelfUrl == "" {
		return nil, errors.BadRequest("40003005", "自研算法地址为空，请联系升哲管理人员")
	}
	if algname != biz.AlgNamePerson {
		return nil, errors.BadRequest("40003005", "当前算法不支持，请重试")
	}
	imageBytes, err := io.ReadAll(reader)
	if err != nil {
		return nil, err
	}
	imageBase64 := base64.StdEncoding.EncodeToString(imageBytes)
	body, _ := json.Marshal(map[string]interface{}{"imageBase64": imageBase64})
	res, err := radar.NewRequest().SetContext(ctx).SetBody(body).
		SetBasicAuth("user", "hW6YsqMMx6xW").
		SetHeader("Content-Type", "application/json").
		Post(fmt.Sprintf("%s/seSkai/image/objectDetect", r.c.Algorithm.SelfUrl))
	if err != nil {
		return nil, err
	}
	if res.IsError() {
		r.log.Warnf("call self algo with error: %d, %s", res.StatusCode(), res.String())
		return nil, errors.BadRequest("40003005", "算法服务调用状态码出错")
	}
	var response struct {
		Code    int    `json:"code"`
		Message string `json:"msg"`
		Data    *struct {
			People [][]int32 `json:"people,omitempty"`
		} `json:"result"`
	}
	json.Unmarshal(res.Body(), &response)
	if response.Code != 1000 || response.Data == nil {
		r.log.Warnf("call self algo failed with code %d, message: %s", response.Code, response.Message)
		return nil, errors.BadRequest("40003005", "算法服务请求响应码错误，请联系升哲管理人员")
	}
	etype := biz.NewAiEventType(algname)
	list := lo.Map(response.Data.People, func(pe []int32, _ int) *biz.DectectTarget {
		return &biz.DectectTarget{Score: 1, Cls: "", Tag: algname.Translate(), X: pe[0], Y: pe[1], Width: pe[2] - pe[0], Height: pe[3] - pe[1]}
	})
	return &biz.AlgoDetectData{
		Name:        etype.Name(),
		Type:        etype.String(),
		ObjectInfo:  list,
		ObjectCount: int32(len(list)),
		LLMEngine:   &biz.LLMEngine{Analysis: ""},
	}, nil
}

func (r *aiEventRepo) AnalyseGddiDetect(ctx context.Context, algname biz.AlgName, reader io.ReadCloser) (*biz.AlgoDetectData, error) {
	defer reader.Close()
	return nil, errors.BadRequest("40003005", "三方算法暂不支持，请联系升哲管理人员")
}

func (r *aiEventRepo) AnalyseChatDetect(ctx context.Context, algname biz.AlgName, reader io.ReadCloser) (*biz.AlgoDetectData, error) {
	defer reader.Close()
	if r.c.Algorithm.ChatUrl == "" {
		return nil, errors.BadRequest("40003005", "大模型地址为空，请联系升哲管理人员")
	}
	token := ""
	switch algname {
	case biz.AlgNameSmokeye:
		token = r.c.Algorithm.ChatSmokeye
	case biz.AlgNameFloater:
		token = r.c.Algorithm.ChatFloater
	default:
		return nil, errors.BadRequest("40003005", "当前算法不支持，请重试")
	}
	imageBytes, err := io.ReadAll(reader)
	if err != nil {
		r.log.Warnf("AnalyzeGptDetect readAll image failed with error: %s", err.Error())
		return nil, errors.BadRequest("40003005", "读取图片数据失败，请重试")
	}
	// 解码图片，获取尺寸即宽度和高度
	size := []int{}
	img, err := image.Decode(bytes.NewReader(imageBytes))
	if err != nil {
		r.log.Warnf("AnalyzeGptDetect decode image failed with error: %s", err.Error())
	}
	bounds := img.Bounds()
	size = append(size, bounds.Dx(), bounds.Dy())
	r.log.Infof("AnalyzeGptDetect decode image size: %+v", size)
	// base64编码字节流
	imageBase64 := base64.StdEncoding.EncodeToString(imageBytes)
	body, _ := json.Marshal(&chatRequestBody{
		Detail: false, Stream: false,
		Variables: biz.AnyMap{"event": algname.Translate(), "size": size},
		Messages:  []message{{Role: "user", Content: []content{{Type: "image_url", Image: biz.AnyMap{"url": fmt.Sprintf(`data:image/jpeg;base64,%s`, imageBase64)}}}}},
	})
	res, err := radar.NewRequest().SetContext(ctx).SetBody(body).
		SetHeader("Content-Type", "application/json").
		SetHeader("Authorization", "Bearer "+token).
		Post(r.c.Algorithm.ChatUrl + "/chat/completions")
	if err != nil {
		return nil, err
	}
	if res.IsError() {
		r.log.Warnf("AnalyzeGptDetect call chat algo with error: %d, %s", res.StatusCode(), res.String())
		return nil, errors.BadRequest("40003005", "算法服务调用状态码出错")
	}
	var response struct {
		Id      string `json:"id"`
		Model   string `json:"model"`
		Choices []struct {
			Message struct {
				Role    string `json:"role"`
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	}
	json.Unmarshal(res.Body(), &response)
	if len(response.Choices) == 0 {
		return nil, errors.BadRequest("40003005", "算法服务调用状态码出错")
	}
	content := response.Choices[0].Message.Content
	r.log.Infof("AnalyzeGptDetect response content %s", content)
	regex := regexp.MustCompile(`\{[\s\S]*\}`)
	jsonStr := regex.FindString(content)
	var ret struct {
		Reason      string               `json:"reason"`
		IsAlarm     bool                 `json:"isAlarm"`
		CaptureTime string               `json:"captureTime"`
		Objects     []*biz.DectectTarget `json:"objects"`
	}
	if err = json.Unmarshal([]byte(jsonStr), &ret); err != nil {
		return nil, errors.BadRequest("40003005", "算法服务响应数据解析失败")
	}
	if !ret.IsAlarm {
		return &biz.AlgoDetectData{}, nil
	}
	etype := biz.NewAiEventType(algname)
	analysis := strings.TrimSpace(regex.ReplaceAllString(content, ""))
	return &biz.AlgoDetectData{
		Name:        etype.Name(),
		Type:        etype.String(),
		ObjectInfo:  ret.Objects,
		ObjectCount: int32(len(ret.Objects)),
		LLMEngine:   &biz.LLMEngine{Analysis: analysis},
	}, nil
}
