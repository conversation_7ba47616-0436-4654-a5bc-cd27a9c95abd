package data

import (
	"context"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
)

type missionRepo struct {
	data *Data
	log  *log.Helper
}

func NewMissionRepo(logger log.Logger, data *Data) biz.MissionRepo {
	return &missionRepo{
		data: data,
		log:  log.<PERSON><PERSON>per(logger),
	}
}

func (r *missionRepo) CreateMission(ctx context.Context, body *biz.Mission) (*biz.Mission, error) {
	mission := &gorm.Mission{}
	mission.FromBizMission(body)
	if err := r.data.db.WithContext(ctx).Create(mission).Error; err != nil {
		return nil, errors.New(500, "insert mission to db failed", err.Error())
	}
	return mission.ToBizMission(), nil
}

func (r *missionRepo) UpdateMission(ctx context.Context, id int64, body biz.AnyMap) error {
	mission := &gorm.Mission{}
	mission.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(mission).Updates(body).Error
}

func (r *missionRepo) DeleteMission(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&gorm.Mission{}, "id=?", id).Error
}

func (r *missionRepo) GetMission(ctx context.Context, query *biz.DetailQuery) (*biz.Mission, error) {
	mission := &gorm.Mission{}
	r.log.Debugf("get mission %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Mission{})
	if query.Unscoped {
		tx = tx.Unscoped()
	}
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if query.Id > 0 {
		tx = tx.Where("id = ?", query.Id)
	}
	if err := tx.First(mission).Error; err != nil {
		return nil, err
	}
	return mission.ToBizMission(), nil
}

func (r *missionRepo) ListMissions(ctx context.Context, query *biz.MissionListQuery) (int32, []*biz.Mission, error) {
	var count int64
	r.log.Warnf("list missions query: %+v", query)
	missions := []*gorm.Mission{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Mission{}).Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	if len(query.Ids) > 0 {
		tx = tx.Where("id in ?", query.Ids)
	}
	if query.Status != nil {
		tx = tx.Where("status = ?", *query.Status)
	}
	if query.Search != nil {
		tx = tx.Where("name LIKE ?", "%"+*query.Search+"%")
	}
	if query.AirlineId != nil {
		tx = tx.Where("airline_id = ?", *query.AirlineId)
	}
	if len(query.DeviceIds) > 0 {
		tx = tx.Where("device_id in ?", query.DeviceIds)
	}
	if query.StartTime != nil && query.EndTime != nil {
		tx.Where("created_time between ? and ?", *query.StartTime, *query.EndTime)
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get mission count from db failed", err.Error())
	}
	if count == 0 {
		return 0, make([]*biz.Mission, 0), nil
	}
	if err := tx.Order("created_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&missions).Error; err != nil {
		r.log.Errorf("get missions from db failed: %v", err)
		return 0, nil, errors.New(500, "get missions from db failed", err.Error())
	}
	list := lo.Map(missions, func(c *gorm.Mission, _ int) *biz.Mission {
		return c.ToBizMission()
	})
	return int32(count), list, nil
}

func (r *missionRepo) ScheduleMissions(ctx context.Context, query *biz.MissionScheduleQuery) ([]*biz.Mission, error) {
	missions := []*gorm.Mission{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Mission{})
	tx.Where("end_time >= ?", query.StartTime)
	tx.Where("start_time <= ?", query.EndTime)
	tx.Where("status IN ?", []int{0, 1, 2})
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if query.ExcludeId > 0 {
		tx = tx.Where("id != ?", query.ExcludeId)
	}
	if query.DeviceId > 0 {
		tx = tx.Where("device_id = ?", query.DeviceId)
	}
	if err := tx.Order("start_time ASC").Find(&missions).Error; err != nil {
		r.log.Errorf("get missions from db failed: %v", err)
		return nil, errors.New(500, "get missions from db failed", err.Error())
	}
	list := lo.Map(missions, func(c *gorm.Mission, _ int) *biz.Mission {
		return c.ToBizMission()
	})
	return list, nil
}

func (r *missionRepo) CountMission(ctx context.Context, query *biz.CountQuery) ([]*biz.CountRet, error) {
	counts := make([]*biz.CountRet, 0)
	r.log.Warnf("count mission query: %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Mission{}).Select(query.CountBy + " as radix, count(*) as count")
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if err := tx.Group("radix").Scan(&counts).Error; err != nil {
		r.log.Errorf("count mission from db failed: %v", err)
		return nil, errors.New(500, "count mission from db failed", err.Error())
	}
	return counts, nil
}

func (r *missionRepo) GetMissionByVoyageId(ctx context.Context, voyageId int64) (*biz.Mission, error) {
	mission := &gorm.Mission{}
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Mission{}).
		Where("id = (?)", r.data.db.Model(&gorm.Execution{}).Select("mission_id").Where("voyage_id =?", voyageId).Limit(1)).First(mission).Error; err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return nil, biz.NewNotFoundError("Mission", "voyageId", conv.Itoa(voyageId))
		}
		r.log.Errorf("GetMissionByVoyageId %d failed: %v", voyageId, err)
		return nil, biz.NewInternalError("GetMissionByVoyageId.db", map[string]string{"voyageId": conv.Itoa(voyageId)})
	}
	return mission.ToBizMission(), nil
}
