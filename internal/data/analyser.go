package data

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/pkg/radar"
)

var baseResponse struct {
	Code    int           `json:"code"`
	Message string        `json:"message"`
	Data    *biz.Analyser `json:"data"`
}

type analyserRepo struct {
	s   *conf.Server
	log *log.Helper
}

func NewAnalyserRepo(logger log.Logger, s *conf.Server) biz.AnalyserRepo {
	return &analyserRepo{
		s:   s,
		log: log.NewHelper(logger),
	}
}

func (r *analyserRepo) CreateAnalyser(ctx context.Context, data *biz.Analyser) (*biz.Analyser, error) {
	data.Options = biz.AnyMap{"tritonUrl": r.s.Analyser.Triton}
	body, _ := json.Marshal(data)
	res, err := radar.NewRequest().SetContext(ctx).SetBody(body).
		SetHeader("Content-Type", "application/json").
		Post(fmt.Sprintf("%s/internal/v1/analysers", r.s.Analyser.Url))
	if err != nil {
		return nil, err
	}
	if res.IsError() {
		r.log.Warnf("call analyser with error: %d, %s", res.StatusCode(), res.String())
		return nil, errors.BadRequest("40003005", "视频解析服务调用状态码出错")
	}
	response := baseResponse
	json.Unmarshal(res.Body(), &response)
	if response.Code != 0 || response.Data == nil {
		r.log.Warnf("call analyser failed with code %d, message: %s", response.Code, response.Message)
		return nil, errors.BadRequest("40003005", "视频解析服务请求响应码错误")
	}
	return response.Data, nil
}

func (r *analyserRepo) GetAnalyser(ctx context.Context, id int64) (*biz.Analyser, error) {
	res, err := radar.NewRequest().SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		Get(fmt.Sprintf("%s/internal/v1/analysers/%d", r.s.Analyser.Url, id))
	if err != nil {
		return nil, err
	}
	if res.IsError() {
		r.log.Warnf("call analyser with error: %d, %s", res.StatusCode(), res.String())
		return nil, errors.BadRequest("40003005", "视频解析服务调用状态码出错")
	}
	response := baseResponse
	json.Unmarshal(res.Body(), &response)
	if response.Code != 0 || response.Data == nil {
		r.log.Warnf("call analyser failed with code %d, message: %s", response.Code, response.Message)
		return nil, errors.BadRequest("40003005", "视频解析服务请求响应码错误")
	}
	return response.Data, nil
}

func (r *analyserRepo) DeleteAnalyser(ctx context.Context, id int64) error {
	res, err := radar.NewRequest().SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		Delete(fmt.Sprintf("%s/internal/v1/analysers/%d", r.s.Analyser.Url, id))
	if err != nil {
		return err
	}
	if res.IsError() {
		r.log.Warnf("call analyser with error: %d, %s", res.StatusCode(), res.String())
		return errors.BadRequest("40003005", "视频解析服务调用状态码出错")
	}
	response := baseResponse
	json.Unmarshal(res.Body(), &response)
	if response.Code != 0 || response.Data == nil {
		r.log.Warnf("call analyser failed with code %d, message: %s", response.Code, response.Message)
		return errors.BadRequest("40003005", "视频解析服务请求响应码错误")
	}
	return nil
}
