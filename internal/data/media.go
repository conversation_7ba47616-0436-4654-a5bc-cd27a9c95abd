package data

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"strings"
	"time"

	stdJson "encoding/json"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"
	"gitlab.sensoro.com/skai/skai/internal/data/mediaserver"
	"gitlab.sensoro.com/skai/skai/pkg/types"
	orm "gorm.io/gorm"
)

type mediaRepo struct {
	data   *Data
	log    *log.Helper
	mc     mediaserver.Client
	signer Signer
}

func NewMediaRepo(data *Data, logger log.Logger, signer Signer) biz.MediaRepo {
	return &mediaRepo{
		data:   data,
		log:    log.<PERSON>per(logger),
		mc:     mediaserver.NewMediaClient(data.conf, logger),
		signer: signer,
	}
}

func (r *mediaRepo) getOne(ctx context.Context, id int64) (*gorm.Media, error) {
	m := &gorm.Media{}
	if err := r.data.NewDBQuery(ctx).Take(m, id).Error; err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return nil, biz.NewNotFoundError("Media", "id", conv.Itoa(id))
		}
		return nil, biz.NewInternalError("getOne.db", map[string]string{"id": conv.Itoa(id)})
	}
	return m, nil
}
func (r *mediaRepo) GetOne(ctx context.Context, id int64) (*biz.Media, error) {
	m, err := r.getOne(ctx, id)
	if err != nil {
		return nil, err
	}
	return m.ToBiz(), nil
}

func (r *mediaRepo) Update(ctx context.Context, m *biz.Media) error {
	gm := &gorm.Media{}
	gm.FromBiz(m)
	if err := r.data.NewDBQuery(ctx).Save(gm).Error; err != nil {
		return biz.NewInternalError("Media.Update.db", nil)
	}
	return nil
}

func (r *mediaRepo) Create(ctx context.Context, m []*biz.Media) error {
	var lives []*gorm.Media
	gm := lo.Map(m, func(it *biz.Media, _ int) *gorm.Media {
		g := &gorm.Media{}
		g.FromBiz(it)
		if it.Type == biz.MediaTypeLive {
			lives = append(lives, g)
		}
		return g
	})
	if err := r.data.NewDBQuery(ctx).Transaction(func(tx *orm.DB) error {
		if e := tx.Create(gm).Error; e != nil {
			return biz.NewInternalError("Media.Create.media", nil)
		}
		if len(lives) > 0 {
			if e := tx.Create(lo.Map(gm, func(it *gorm.Media, _ int) *gorm.Stream {
				return &gorm.Stream{
					BaseEntity: it.BaseEntity,
					SourceId:   it.Key,
					MediaApp:   "live",
				}
			})).Error; e != nil {
				return biz.NewInternalError("Media.Create.stream", nil)
			}
		}
		return nil
	}); err != nil {
		return err
	}
	lo.ForEach(gm, func(it *gorm.Media, _ int) {
		for _, bm := range m {
			if bm.SubDeviceIndex == it.SubDeviceIndex {
				bm.Id = it.Id
			}
		}
	})
	return nil
}

func (r *mediaRepo) applyListQuery(tx *orm.DB, query *biz.MediaListQuery) *orm.DB {
	where := strings.Builder{}
	args := make([]any, 0, 2)
	if !query.DisableProjectFilter {
		where.WriteString("merchant_id = ANY(?)")
		args = append(args, pq.Int64Array(query.ProjectInfo.MerchantIds))
	} else {
		where.WriteString("1=1")
	}
	if len(query.Ids) > 0 {
		where.WriteString(" AND id = ANY(?)")
		args = append(args, pq.Int64Array(query.Ids))
	}
	if query.TimeScope != nil {
		where.WriteString(" AND created_time BETWEEN ? AND ?")
		args = append(args, query.TimeScope.StartTime, query.TimeScope.EndTime)
	}
	if l := len(query.DeviceIds); l > 0 {
		if l == 1 {
			where.WriteString(" AND device_id = ?")
			args = append(args, query.DeviceIds[0])
		} else {
			where.WriteString(" AND device_id IN (?)")
			args = append(args, query.DeviceIds)
		}
	}
	if l := len(query.VoyageIds); l > 0 {
		if l == 1 {
			where.WriteString(" AND voyage_id = ?")
			args = append(args, query.VoyageIds[0])
		} else {
			where.WriteString(" AND voyage_id IN (?)")
			args = append(args, query.VoyageIds)
		}
	}
	if len(query.AirlineIds) > 0 {
		where.WriteString(" AND airline_id IN (?)")
		args = append(args, query.AirlineIds)
	}
	if len(query.WaypointIds) > 0 {
		where.WriteString(" AND waypoint_id IN (?)")
		args = append(args, query.WaypointIds)
	}
	if len(query.Type) > 0 {
		where.WriteString(" AND type in (?)")
		args = append(args, lo.Map(query.Type, func(it biz.MediaType, _ int) int { return int(it) }))
	}
	if len(query.LenTypes) > 0 {
		sts := lo.Map(query.LenTypes, func(it string, _ int) int32 { return biz.MediaSubTypeValueMapper[it] })
		if query.WithVoyageRecord {
			sts = append(sts, 0)
		}
		where.WriteString(" AND sub_type in (?)")
		args = append(args, sts)
	}
	if query.FullVoyageRecord {
		where.WriteString(" AND sk_media.type = 1 AND (sk_media.extra#>'{other,fullRecord}')::integer =1")
	}
	return tx.Where(where.String(), args...)
}

func (r *mediaRepo) List(ctx context.Context, query *biz.MediaListQuery) (int64, []*biz.Media, error) {
	qb := r.applyListQuery(r.data.NewDBQuery(ctx).Model(&gorm.Media{}), query)
	var total int64
	if err := qb.Count(&total).Error; err != nil {
		return 0, nil, biz.NewInternalError("Media.List.count", nil)
	}
	if query.Limit() == 0 || query.Offset() >= int(total) {
		return total, nil, nil
	}
	var media []*gorm.Media
	if query.Sort.Field == nil {
		qb = qb.Order("created_time desc")
	} else {
		qb = qb.Order(query.Sort.SortField() + " " + query.Sort.Order.String())
	}
	if err := qb.Limit(query.Limit()).Offset(query.Offset()).Find(&media).Error; err != nil {
		return 0, nil, biz.NewInternalError("Media.List.find", nil)
	}
	return total, lo.Map(media, func(it *gorm.Media, _ int) *biz.Media {
		return it.ToBiz()
	}), nil
}

func (r *mediaRepo) GetDeviceLives(ctx context.Context, query *biz.DeviceLiveQuery) ([]*biz.Media, error) {
	var media []*gorm.Media
	conditions := map[string]any{
		"device_id": query.DeviceId,
		"voyage_id": 0,
		"type":      int(biz.MediaTypeLive),
	}
	if query.SubDeviceIndex != "" {
		conditions["sub_device_index"] = query.SubDeviceIndex
	}
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).Where(conditions).Find(&media).Limit(100).Order("id desc").Error; err != nil {
		r.log.Errorf("GetDeviceLive.dbErr with Query %+v, detail %v", query, err)
		return nil, biz.NewInternalError("GetDeviceLive.db", nil)
	}
	if len(media) == 0 {
		return nil, nil
	}
	streams := make([]*gorm.Stream, 0, len(media))
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Stream{}).
		Where("id=ANY(?)", pq.Int64Array(lo.Map(media, func(it *gorm.Media, _ int) int64 { return it.Id }))).
		Find(&streams).Error; err != nil {
		return nil, biz.NewInternalError("GetDeviceLive.status.db", nil)
	}
	// playSchema := r.getPlaySchema()
	return lo.Map(media, func(it *gorm.Media, _ int) *biz.Media {
		m := it.ToBiz()
		if v, ok := lo.Find(streams, func(s *gorm.Stream) bool {
			return it.Id == s.Id
		}); ok {
			m.Status = int32(v.Status)
		}
		m.URL = r.getLiveUrl(it)
		return m
	}), nil
}

// func (r *mediaRepo) getPlaySchema() string {
// 	playSchema := "https"
// 	if os.Getenv("RUN_ENV") == "edge" {
// 		playSchema = "http"
// 	}
// 	return playSchema
// }

func (r *mediaRepo) getLiveUrl(m *gorm.Media) string {
	u, e := r.signer.Sign(fmt.Sprintf("%s/live/%s.live.flv",
		r.data.conf.Media.PublicUrl,
		m.Key,
	), 1*time.Hour)
	if e != nil {
		r.log.Warnf("sign url for media %s faile %+v", m.Key, e)
	}
	return u
}

func (r *mediaRepo) GetLivePushConfig(ctx context.Context, dev *biz.Device, live *biz.Media) (*biz.LivePushConfig, error) {
	if dev.Model == biz.ModelDJIDock || dev.Model == biz.ModelDJILite || dev.Model == biz.ModelDJIPilot {
		pu, err := url.Parse(r.data.conf.Media.PublicUrl)
		if err != nil {
			return nil, biz.NewBadRequestError("GetLivePushConfig.invalidMeidaPublicUrl", map[string]string{"url": r.data.conf.Media.PublicUrl})
		}
		u, err := r.signer.Sign(fmt.Sprintf("rtmp://%s:%d/live/%s", pu.Hostname(), r.data.conf.Media.RtmpPort, live.Key), 24*time.Hour)
		if err != nil {
			return nil, biz.NewInternalError("GetLivePushConfig.signUrl", nil)
		}
		r.log.Infof("GetLivePushConfig %s %s", pu.Hostname(), u)
		return &biz.LivePushConfig{
			StreamId: live.Key,
			Url:      u,
		}, nil
	}
	return nil, errors.InternalServer("GetLivePushConfig.unknownModel", "不支持的设备类型")
}

func (r *mediaRepo) UpdateLiveStatus(ctx context.Context, id int64, s biz.LiveStatus) error {
	m, err := r.getOne(ctx, id)
	if err != nil {
		return err
	}
	name := s.Type.String()
	// if name == m.Name && s.Status == m.Status {
	// 	bm := m.ToBiz()
	// 	if bm.GetLiveClarity() == s.Clarity {
	// 		return nil
	// 	}
	// }
	extra := make(map[string]any)
	stdJson.Unmarshal(m.Extra, &extra)
	other := extra["other"].(map[string]any)
	other["clarity"] = s.Clarity
	other["index"] = s.Type.Index()
	ev, _ := stdJson.Marshal(extra)
	update := &gorm.Media{Status: s.Status, Extra: ev, Name: name}
	r.log.Infof("UpdateLiveStatus %d status %+v", id, s)
	return r.data.NewDBQuery(ctx).Model(m).
		Select("Status", "Extra", "Name", "UpdatedTime").
		Updates(update).Error
}

func (r *mediaRepo) UpdateMediaExtra(ctx context.Context, id int64, extra map[string]any) error {
	v, _ := stdJson.Marshal(map[string]any{
		"other": extra,
	})
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).Where("id=?", id).Update("extra", orm.Expr("extra||?", v)).Error; err != nil {
		return biz.NewInternalError("UpdateMediaExtra.db", nil)
	}
	return nil
}

func (r *mediaRepo) StartRecordLive(ctx context.Context, live *biz.Media) error {
	if live.Type != biz.MediaTypeLive {
		return biz.NewBadRequestError("StartRecordLive.invalidLive", nil)
	}
	recroding, err := r.mc.IfLiveRecording(ctx, live)
	if err != nil {
		return err
	}
	if recroding {
		r.log.Warnf("try StartRecordLive %d failed because live is alreay recording", live.Id)
		return biz.NewBadRequestError("StartRecordLive.alreayRecording", map[string]string{
			"live": conv.Itoa(live.Id),
		})
	}
	r.log.Infof("StartRecordLive %d of device %d ", live.Id, live.DeviceId)
	return r.mc.StartRecordLive(ctx, &mediaserver.LiveRecordRequest{
		Live: live,
	})
}

func (r *mediaRepo) IfLiveRecording(ctx context.Context, live *biz.Media) (bool, error) {
	return r.mc.IfLiveRecording(ctx, live)
}

func (r *mediaRepo) ListRecordFile(ctx context.Context, live *biz.Media, date time.Time) ([]string, error) {
	if live.Type != biz.MediaTypeLive {
		return nil, biz.NewBadRequestError("ListRecordFile.invalidLive", nil)
	}
	return r.mc.ListRecordFile(ctx, &mediaserver.RecordFileQuery{Live: live, Date: date})
}

func (r *mediaRepo) GetRecordFileStream(ctx context.Context, query *biz.RecordFileQuery) (io.ReadCloser, *biz.VideoRecordMeta, error) {
	if query.Live == nil || query.Live.Type != biz.MediaTypeLive {
		return nil, nil, biz.NewBadRequestError("GetRecordFileStream.invalidLive", nil)
	}
	return r.mc.GetRecordFileStream(ctx, &mediaserver.LiveRecordRequest{
		Live: query.Live,
		Path: query.Path,
	})
}

func (r *mediaRepo) DelRecordFile(ctx context.Context, live *biz.Media, date time.Time) error {
	return r.mc.DelRecordFile(ctx, &mediaserver.RecordFileQuery{Live: live, Date: date})
}

func (r *mediaRepo) GetDeviceMedia(ctx context.Context, query *biz.DeviceMediaListQuery) ([]*biz.Media, error) {
	qb := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).Where("device_id = ? ", query.Dev.Id)
	if ts := query.BaseListQuery.TimeScope; ts != nil {
		qb.Where("created_time between ? and ?", ts.StartTime, ts.EndTime)
	}
	if l := len(query.VoyageIds); l > 0 {
		if l == 1 {
			qb.Where(" voyage_id = ?", query.VoyageIds[0])
		} else {
			qb.Where(" voyage_id =ANY(?)", pq.Int64Array(query.VoyageIds))
		}
	}
	if len(query.Type) > 0 {
		qb.Where("type = ANY(?)", pq.Int64Array(lo.Map(query.Type, func(it biz.MediaType, _ int) int64 { return int64(it) })))
	}
	if len(query.ShortSignatures) > 0 {
		qb.Where("extra#>>'{other,sSign}' = ANY(?)", pq.StringArray(query.ShortSignatures))
	}
	var list []*gorm.Media
	if err := qb.Limit(query.Limit()).Offset(query.Offset()).Order("created_time desc").Find(&list).Error; err != nil {
		return nil, biz.NewInternalError("GetDeviceMedia.db", nil)
	}
	return lo.Map(list, func(it *gorm.Media, _ int) *biz.Media {
		return it.ToBiz()
	}), nil
}

type liveCount struct {
	Online  int32
	Offline int32
}

func (r *mediaRepo) ListLive(ctx context.Context, query *biz.LiveListQuery) (biz.LiveList, error) {
	deviceQuery := r.data.db.Model(&gorm.Device{}).Select("id").Where("sk_device.merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	if len(query.DeviceIds) > 0 {
		deviceQuery.Where("sk_device.id = ANY(?)", pq.Int64Array(query.DeviceIds))
	}
	qb := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).
		Joins("left join sk_stream on sk_media.id=sk_stream.id").
		Where("sk_media.device_id = ANY(ARRAY(?)) and sk_media.voyage_id = 0 AND type = 2", deviceQuery)
	if len(query.SubDeviceIndexes) > 0 {
		qb.Where("sk_media.sub_device_index IN (?)", query.SubDeviceIndexes)
	}
	count := liveCount{}
	if err := qb.Select("count(*) filter (where sk_stream.status=1) as online, count(*) filter (where sk_stream.status=0) as offline").
		Take(&count).Error; err != nil {
		return biz.LiveList{}, biz.NewInternalError("ListLive.count", nil)
	}
	if total := count.Offline + count.Online; total == 0 {
		return biz.LiveList{}, nil
	}
	if l := len(query.Statuses); l > 0 {
		if l == 1 {
			qb.Where("sk_stream.status = ?", query.Statuses[0])
		} else {
			qb.Where("sk_stream.status IN (?)", query.Statuses)
		}
	}
	qb.Select("*")
	qb.Statement.RaiseErrorOnNotFound = false
	var lives []*gorm.Media
	if err := qb.Limit(query.Limit()).Offset(query.Offset()).Find(&lives).Error; err != nil {
		return biz.LiveList{}, biz.NewInternalError("ListLive.find", nil)
	}
	deviceIds := types.NewHashSet[int64]()
	for _, l := range lives {
		deviceIds.Add(l.DeviceId)
	}
	var devices []*gorm.Device
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Device{}).Where("id = ANY(?)", pq.Int64Array(deviceIds.ToSlice())).Find(&devices).Error; err != nil {
		return biz.LiveList{}, biz.NewInternalError("ListLive.dev", nil)
	}
	devMap := lo.SliceToMap(devices, func(it *gorm.Device) (int64, *biz.Device) {
		return it.Id, it.ToBizDevice()
	})

	return biz.LiveList{
		Online:  count.Online,
		Offline: count.Offline,
		List: lo.Map(lives, func(it *gorm.Media, _ int) *biz.Live {
			l := it.ToLive(devMap[it.DeviceId])
			l.URL = r.getLiveUrl(it)
			return l
		}),
	}, nil
}

type meidaWithDeviceVoyage struct {
	gorm.Media
	Device *gorm.Device
	Voyage *gorm.Voyage
}

func (r *mediaRepo) ListGalleryMedia(ctx context.Context, query *biz.GalleryMediaListQuery) (int32, []biz.MediaWithRelatedDevice, error) {
	qb := r.data.NewDBQuery(ctx).Model(&meidaWithDeviceVoyage{}).
		Joins("Device", r.data.db.Where(`"Device".merchant_id = ANY(?)`, pq.Int64Array(query.MerchantIds))).
		Joins("Voyage", r.data.db.Where(`"Voyage".merchant_id = ANY(?)`, pq.Int64Array(query.MerchantIds))).
		Where("sk_media.merchant_id = ANY(?) AND sk_media.created_time between ? AND ?",
			pq.Int64Array(query.MerchantIds),
			query.BaseListQuery.TimeScope.StartTime, query.BaseListQuery.TimeScope.EndTime,
		)
	galleryMediaTypes := []int32{int32(biz.MediaTypePhoto), int32(biz.MediaTypeVideo)}
	ts := lo.Filter(query.Types, func(item int32, index int) bool { return lo.Contains(galleryMediaTypes, item) })
	if len(ts) == 0 {
		ts = galleryMediaTypes
	}
	tf := "sk_media.type = ANY(?)"
	or := false
	if query.IsVideo && lo.Contains(ts, int32(biz.MediaTypeVideo)) {
		or = true
		tf += " AND ( (sk_media.type = 1 AND (sk_media.extra#>'{other,fullRecord}') is null)"
	}
	if query.IsFullRecord && lo.Contains(ts, int32(biz.MediaTypeVideo)) {
		if or {
			tf += " OR (sk_media.extra#>'{other,fullRecord}')::integer =1"
		} else {
			or = true
			tf += " AND (sk_media.type = 1 AND (sk_media.extra#>'{other,fullRecord}')::integer =1"
		}
	}
	if query.IsPhoto && lo.Contains(ts, int32(biz.MediaTypePhoto)) {
		if or {
			tf += " OR sk_media.type = 3"
		} else {
			or = true
			tf += " AND (sk_media.type = 3"
		}
	}
	if or {
		tf += ")"
	}
	qb.Where(tf, pq.Int32Array(ts))
	if len(query.DeviceCategories) > 0 {
		qb.Where(`"Device".category IN (?)`, query.DeviceCategories)
	}
	if len(query.DeviceTypes) > 0 {
		qb.Where(`"Device".type IN (?)`, query.DeviceTypes)
	}
	if len(query.DeviceIds) > 0 {
		qb.Where("sk_media.device_id = ANY(?)", pq.Int64Array(query.DeviceIds))
	}
	if len(query.AirlineIds) > 0 {
		qb.Where("sk_media.airline_id = ANY(?)", pq.Int64Array(query.AirlineIds))
	}
	if len(query.LenTypes) > 0 {
		sts := lo.Map(query.LenTypes, func(it string, _ int) int32 { return biz.MediaSubTypeValueMapper[it] })
		if query.IsFullRecord {
			sts = append(sts, 0)
		}
		qb.Where("sk_media.sub_type = ANY(?)", pq.Int32Array(sts))
	}
	if query.Search != "" {
		if query.Grouped == biz.GalleryMediaListQueryGroupByVoyage {
			sv := "%" + query.Search + "%"
			qb.Where(`(sk_media.name ilike ? or "Voyage".name ilike ?)`, sv, sv)
		} else {
			qb.Where("sk_media.name ilike ?", "%"+query.Search+"%")
		}
	}
	var count int64 = 10
	if err := qb.Count(&count).Error; err != nil {
		return 0, nil, biz.NewInternalError("ListGalleryMedia.count", nil)
	}
	if count == 0 || query.Limit() == 0 {
		return int32(count), nil, nil
	}
	var list []meidaWithDeviceVoyage
	order := "sk_media.created_time desc"
	if query.Grouped == biz.GalleryMediaListQueryGroupByVoyage {
		order = `"Voyage".start_time,sk_media.created_time desc`
	}
	findQB := r.data.NewDBQuery(ctx).Model(&meidaWithDeviceVoyage{}).
		Joins("Device", r.data.db.Omit("prop_data", "extra_data").Where(`"Device".merchant_id = ANY(?)`, pq.Int64Array(query.MerchantIds))).
		Joins("Voyage", r.data.db.Where(`"Voyage".merchant_id = ANY(?)`, pq.Int64Array(query.MerchantIds)))
	findQB.Statement.Clauses["WHERE"] = qb.Statement.Clauses["WHERE"]
	if err := findQB.Limit(query.Limit()).Offset(query.Offset()).Order(order).Find(&list).Error; err != nil {
		return 0, nil, biz.NewInternalError("ListGalleryMedia.find", nil)
	}
	arilineIds := types.NewHashSet[int64]()
	for _, m := range list {
		arilineIds.Add(m.AirlineId)
	}
	var airlines []*gorm.Airline
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Airline{}).Where("id=ANY(?)", pq.Int64Array(arilineIds.ToSlice())).Find(&airlines).Error; err != nil {
		return 0, nil, biz.NewInternalError("ListGalleryMedia.airline", nil)
	}
	airlineMap := lo.SliceToMap(airlines, func(it *gorm.Airline) (int64, *biz.Airline) {
		return it.Id, it.ToBizAirline()
	})

	return int32(count), lo.Map(list, func(it meidaWithDeviceVoyage, _ int) biz.MediaWithRelatedDevice {
		var d *biz.Device
		var v *biz.Voyage
		if it.Device != nil {
			d = it.Device.ToBizDevice()
		}
		if it.Voyage != nil {
			v = it.Voyage.ToBizVoyage()
		}
		return biz.MediaWithRelatedDevice{
			Media:   it.ToBiz(),
			Device:  d,
			Voyage:  v,
			Airline: airlineMap[it.AirlineId],
		}
	}), nil
}

func (r *mediaRepo) RemoveMedia(ctx context.Context, mr biz.MediaRemovement) ([]*biz.Media, error) {
	if ids := mr.Ids; len(ids) > 0 {
		var media []*gorm.Media
		if err := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).Where("id = ANY(?) and merchant_id = ANY(?)", pq.Int64Array(ids), pq.Int64Array(mr.MerchantIds)).Find(&media).Error; err != nil {
			r.log.WithContext(ctx).Errorf("RemoveMedia.findByIds failed %v", err)
			return nil, biz.NewInternalError("RemoveMedia.findByIds", nil)
		}
		if err := r.data.NewDBQuery(ctx).Where("id = ANY(?) and merchant_id = ANY(?)", pq.Int64Array(ids), pq.Int64Array(mr.MerchantIds)).Delete(&gorm.Media{}).Error; err != nil {
			r.log.WithContext(ctx).Errorf("RemoveMedia.deleteByIds failed %v", err)
			return nil, biz.NewInternalError("RemoveMedia.deleteByIds", nil)
		}
		return lo.Map(media, func(it *gorm.Media, _ int) *biz.Media {
			return it.ToBiz()
		}), nil
	}
	if vIds := mr.VoyageIds; len(vIds) > 0 {
		var media []*gorm.Media
		if err := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).Where("voyage_id = ANY(?) and merchant_id = ANY(?)", pq.Int64Array(vIds), pq.Int64Array(mr.MerchantIds)).Limit(1000).Find(&media).Error; err != nil {
			r.log.WithContext(ctx).Errorf("RemoveMedia.findByVoyageIds failed %v", err)
			return nil, biz.NewInternalError("RemoveMedia.findByVoyageIds", nil)
		}
		if err := r.data.NewDBQuery(ctx).Where("voyage_id = ANY(?) and merchant_id = ANY(?)", pq.Int64Array(vIds), pq.Int64Array(mr.MerchantIds)).Delete(&gorm.Media{}).Error; err != nil {
			r.log.WithContext(ctx).Errorf("RemoveMedia.deleteByVoyageIds failed %v", err)
			return nil, biz.NewInternalError("RemoveMedia.deleteByVoyageIds", nil)
		}
		return lo.Map(media, func(it *gorm.Media, _ int) *biz.Media {
			return it.ToBiz()
		}), nil
	}
	return nil, nil
}

func (r *mediaRepo) MakeVideoSnap(ctx context.Context, videoURL string) (io.ReadCloser, error) {
	return r.mc.TakeASnap(ctx, videoURL)
}
