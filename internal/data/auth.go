package data

import (
	"context"
	"encoding/json"

	"gitlab.sensoro.com/skai/skai/internal/biz"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/go-sensoro/lins-common/middleware"
)

type authRepo struct {
	client *client.AuthClient
	log    *log.Helper
}

type projectMerchants struct {
	ProjectId int64           `json:"projectId,string"`
	Merchants []*biz.Merchant `json:"merchants"`
}

func (m *projectMerchants) MarshalBinary() ([]byte, error) {
	return json.Marshal(m)
}

func (m *projectMerchants) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, m)
}

func NewAuthRepo(logger log.Logger, data *Data) biz.AuthRepo {
	return &authRepo{
		client: data.auth,
		log:    log.<PERSON><PERSON>per(logger),
	}
}

func (r *authRepo) GetCurrentAuthValue(ctx context.Context) (*biz.SkaiAuthValue, error) {
	v, ok := middleware.GetAuthValue(ctx)
	if !ok {
		return nil, errors.Unauthorized("GetCurrentAuthValueFailed", "you have no permission to access this project")
	}
	return v.(*biz.SkaiAuthValue), nil
}

func (r *authRepo) GetAvatar(ctx context.Context, id int64) (*biz.Avatar, error) {
	data, err := r.client.GetAvatar(ctx, client.AvatarIdDTO{AvatarId: conv.Vtoa(id)})
	if err != nil {
		return nil, err
	}
	if data.Code != 0 {
		r.log.Warnf("call auth service with GetAvatar failed, code: %d, message: %s", data.Code, data.Message)
		return nil, errors.New(500, "call auth service failed", data.Message)
	}
	return &data.Data, nil
}

func (r *authRepo) GetProjectMerchants(ctx context.Context, id int64) ([]*biz.Merchant, error) {
	pms := &projectMerchants{ProjectId: id}
	data, err := r.client.GetProjectMerchants(ctx, client.ProjectIdDTO{ProjectId: conv.Vtoa(id)})
	if err != nil {
		return nil, err
	}
	if data.Code != 0 {
		r.log.Warnf("call auth service with GetProjectMerchants failed, code: %d, message: %s", data.Code, data.Message)
		return nil, errors.New(500, "call auth service failed", data.Message)
	}
	r.log.WithContext(ctx).Info("get project merchants from auth ", id)
	pms.ProjectId = id
	pms.Merchants = data.Data.List
	return data.Data.List, nil
}

func (r *authRepo) GetTenantAvatars(ctx context.Context, tenantId int64, avatarIds []int64) ([]*biz.Avatar, error) {
	if vo, err := r.client.GetTenantAvatars(ctx, client.TenantAvatarsDTO{
		Body: client.TenantAvatars{
			TenantId:  tenantId,
			AvatarIds: lo.Map(avatarIds, func(i int64, _ int) string { return conv.Vtoa(i) }),
		},
	}); err == nil {
		if vo.Code != 0 {
			r.log.Warnf("call auth service with GetTenantAvatars failed, code: %d, message: %s", vo.Code, vo.Message)
			return nil, errors.InternalServer("GetTenantAvatarsFailed", vo.Message)
		}
		return vo.Data.List, nil
	} else {
		r.log.Errorf("call auth service with GetTenantAvatars for tenant:%v failed: %v", tenantId, err)
		return nil, errors.InternalServer("GetTenantAvatarsFailed", err.Error())
	}
}

func (r *authRepo) GetTenantMerchants(ctx context.Context, tenantId int64) ([]*biz.Merchant, error) {
	if vo, err := r.client.GetTenantMerchants(ctx, client.TenantMerchantsDTO{
		TenantId: conv.Itoa(tenantId),
		Page:     1,
		Size:     500,
	}); err == nil {
		if vo.Code != 0 {
			r.log.Warnf("call auth service with GetTenantMerchants failed, code: %d, message: %s", vo.Code, vo.Message)
			return nil, errors.InternalServer("GetTenantMerchantsFailed", vo.Message)
		} else {
			return vo.Data.List, nil
		}
	} else {
		r.log.Errorf("call auth service with GetTenantMerchants for tenant:%v failed: %v", tenantId, err)
		return nil, errors.InternalServer("GetTenantAvatarsFailed", err.Error())
	}
}
