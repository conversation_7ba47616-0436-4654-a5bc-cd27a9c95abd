package td

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

const DockPropertySTableName = "dock_properties"
const DronePropertySTableName = "drone_properties"
const DockEventSTableName = "dock_events"
const DroneCameraSTableName = "camera_property"

var dockStateType = reflect.TypeOf(biz.DockState{})
var positionStateType = reflect.TypeOf(biz.PositionState{})
var networkStateType = reflect.TypeOf(biz.NetworkState{})
var flightTaskStateType = reflect.TypeOf(biz.FlightTaskState{})
var envStateType = reflect.TypeOf(biz.EnvironmentState{})
var wirelessLinkStateType = reflect.TypeOf(biz.WirelessLinkState{})
var elecStateType = reflect.TypeOf(biz.ElecPowerState{})
var batteryChargeStateType = reflect.TypeOf(biz.BatteryChargeState{})

var droneFlightStateType = reflect.TypeOf(biz.DroneFlightState{})
var droneBatteryStateType = reflect.TypeOf(biz.DroneBatteryState{})
var storageType = reflect.TypeOf(biz.Storage{})
var cameraStateType = reflect.TypeOf(biz.DroneCameraState{})

func writeState(value map[string]any, t reflect.Type, v reflect.Value, prefix string) {
	if v.Type().Kind() == reflect.Pointer {
		v = v.Elem()
	}
	for i := 0; i < t.NumField(); i++ {
		f := t.Field(i)
		if f.IsExported() && !f.Anonymous {
			value[newColumnName(prefix, f.Name)] = v.Field(i).Interface()
		}
	}
}

func newValueForState(values map[string]any, t reflect.Type, prefix string) any {
	if t.Kind() != reflect.Struct {
		return nil
	}
	r := reflect.New(t)
	re := r.Elem()
	for i := 0; i < t.NumField(); i++ {
		if re.Field(i).CanSet() {
			f := t.Field(i)
			v, ok := values[newColumnName(prefix, f.Name)]
			if ok {
				// trans type
				re.Field(i).Set(getFieldValue(f.Type, v))
			}
		}
	}
	return r.Interface()
}

func listColumns(value map[string]any) []string {
	cns := make([]string, 0, len(value))
	for k := range value {
		cns = append(cns, k)
	}
	return cns
}

type DockProperties struct {
	DeviceId int64
	Sn       string

	RxTime      time.Time
	stateBitmap uint32
	TS          time.Time
	Id          string

	Value map[string]any
}

func (p *DockProperties) TableName() string {
	return fmt.Sprintf("dp_%s", strings.ReplaceAll(p.Sn, "-", "_"))
}

func (p *DockProperties) IsEmpty() bool {
	return p.stateBitmap == 0
}

func (p *DockProperties) FromDockProperties(bp *biz.DockProperties) {
	p.DeviceId = bp.DeviceId
	p.Sn = bp.Sn
	p.RxTime = bp.RxTime
	p.TS = bp.Timestamp
	p.Id = bp.Id
	p.Value = make(map[string]interface{})
	if bp.State != nil {
		p.stateBitmap = 1 << 31
		v := reflect.ValueOf(bp.State)
		writeState(p.Value, dockStateType, v, "state")
	}
	if bp.PositionState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 30)
		v := reflect.ValueOf(bp.PositionState)
		writeState(p.Value, positionStateType, v, "position")
	}

	if bp.NetworkState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 29)
		v := reflect.ValueOf(bp.NetworkState)
		writeState(p.Value, networkStateType, v, "network")
	}

	if bp.FlightTaskState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 28)
		writeState(p.Value, flightTaskStateType, reflect.ValueOf(bp.FlightTaskState), "flight")
	}

	if bp.EnvironmentState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 27)
		writeState(p.Value, envStateType, reflect.ValueOf(bp.EnvironmentState), "env")
	}

	if bp.WirelessLinkState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 26)
		writeState(p.Value, wirelessLinkStateType, reflect.ValueOf(bp.WirelessLinkState), "wireless")
	}
	if bp.ElecPowerState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 25)
		writeState(p.Value, elecStateType, reflect.ValueOf(bp.ElecPowerState), "elec")
	}
	if bp.BatteryChargeState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 24)
		writeState(p.Value, batteryChargeStateType, reflect.ValueOf(bp.BatteryChargeState), "battery")
	}
	if len(bp.Other) > 0 {
		v, _ := json.Marshal(bp.Other)
		if len(v) < 4*1024 {
			p.Value["extra"] = string(v)
		}
	}

}

func (p *DockProperties) ToBiz() *biz.DockProperties {
	bp := &biz.DockProperties{
		Id:        p.Id,
		RxTime:    p.RxTime,
		Timestamp: p.TS,
		DeviceId:  p.DeviceId,
		Sn:        p.Sn,
	}
	if (p.stateBitmap>>31)&1 == 1 {
		bp.State = newValueForState(p.Value, dockStateType, "state").(*biz.DockState)
	}
	if (p.stateBitmap>>30)&1 == 1 {
		bp.PositionState = newValueForState(p.Value, positionStateType, "position").(*biz.PositionState)
	}
	if (p.stateBitmap>>29)&1 == 1 {
		bp.NetworkState = newValueForState(p.Value, networkStateType, "network").(*biz.NetworkState)
	}
	if (p.stateBitmap>>28)&1 == 1 {
		bp.FlightTaskState = newValueForState(p.Value, flightTaskStateType, "flight").(*biz.FlightTaskState)
	}
	if (p.stateBitmap>>27)&1 == 1 {
		bp.EnvironmentState = newValueForState(p.Value, envStateType, "env").(*biz.EnvironmentState)
	}
	if (p.stateBitmap>>26)&1 == 1 {
		bp.WirelessLinkState = newValueForState(p.Value, wirelessLinkStateType, "wireless").(*biz.WirelessLinkState)
	}
	if (p.stateBitmap>>25)&1 == 1 {
		bp.ElecPowerState = newValueForState(p.Value, elecStateType, "elec").(*biz.ElecPowerState)
	}
	if (p.stateBitmap>>24)&1 == 1 {
		bp.BatteryChargeState = newValueForState(p.Value, batteryChargeStateType, "battery").(*biz.BatteryChargeState)
	}
	if v, ok := p.Value["extra"].(string); ok {
		json.Unmarshal([]byte(v), &bp.Other)
	}
	return bp

}

// raw 会是json反序列化的结果，因而数字都会死float64类型
func (p *DockProperties) FromTDResult(raw map[string]any) {
	if v, ok := raw["device_id"].(string); ok {
		p.DeviceId = conv.Atoi(v)
		delete(raw, "device_id")
	}
	if v, ok := raw["sn"].(string); ok {
		p.Sn = v
		delete(raw, "sn")
	}
	if v, ok := raw["id"].(string); ok {
		p.Id = v
		delete(raw, "id")
	}
	if v, ok := raw["rx_time"].(time.Time); ok {
		p.RxTime = v
		delete(raw, "rx_time")
	}
	if v, ok := raw["ts"].(time.Time); ok {
		p.TS = v
		delete(raw, "ts")
	}
	if v, ok := raw["state_bitmap"].(float64); ok {
		p.stateBitmap = uint32(v)
		delete(raw, "state_bitmap")
	}
	p.Value = raw
}

func (p *DockProperties) GenInsert() string {
	sb := &strings.Builder{}
	sb.Grow(2048)
	sb.WriteString("INSERT INTO ")
	sb.WriteString(p.TableName())
	sb.WriteString(" USING ")
	sb.WriteString(DockPropertySTableName)
	sb.WriteString(" (sn, device_id) TAGS (")
	sb.WriteString(stringValuer(p.Sn))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(conv.Vtoa(p.DeviceId)))
	sb.WriteString(") (rx_time, ts, state_bitmap, id")
	cs := listColumns(p.Value)
	for _, cn := range cs {
		sb.WriteRune(',')
		sb.WriteString(cn)
	}
	sb.WriteString(") VALUES (")
	sb.WriteString(conv.Vtoa(p.RxTime.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Vtoa(p.TS.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Utoa(uint64(p.stateBitmap)))
	sb.WriteString(",'")
	sb.WriteString(p.Id)
	sb.WriteByte('\'')
	for _, cn := range cs {
		sb.WriteRune(',')
		if valuer, ok := columnSQLValuers[cn]; ok {
			sb.WriteString(valuer(p.Value[cn]))
		} else {
			sb.WriteString("NULL")
		}
	}
	sb.WriteString(")")
	return sb.String()
}

type DroneProperties struct {
	// TAG
	DeviceId  int64
	Sn        string
	DroneSN   string
	DroneType string
	// Columns
	RxTime      time.Time
	stateBitmap uint32
	TS          time.Time
	Id          string
	Mode        int32
	Value       map[string]any
}

func (p *DroneProperties) TableName() string {
	return fmt.Sprintf("drone_%s", p.Sn)
}

func (p *DroneProperties) IsEmpty() bool {
	return p.stateBitmap == 0
}

func (p *DroneProperties) FromDockDroneProperties(bp *biz.DroneProperties) {
	p.DeviceId = bp.DeviceId
	p.Sn = bp.Sn
	p.DroneSN = bp.DroneSn
	p.DroneType = bp.DroneType

	p.RxTime = bp.RxTime
	p.TS = bp.Timestamp
	p.Id = bp.Id
	p.Mode = bp.Mode
	p.Value = make(map[string]interface{})

	if bp.FlightState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 31)
		writeState(p.Value, droneFlightStateType, reflect.ValueOf(bp.FlightState), "drone_flight")
	}

	if bp.PositionState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 30)
		writeState(p.Value, positionStateType, reflect.ValueOf(bp.PositionState), "drone_position")
	}
	if bp.Battery != nil {
		p.stateBitmap = p.stateBitmap | (1 << 29)
		writeState(p.Value, droneBatteryStateType, reflect.ValueOf(bp.Battery), "drone_battery")
	}
	if bp.Storage != nil {
		p.stateBitmap = p.stateBitmap | (1 << 28)
		writeState(p.Value, storageType, reflect.ValueOf(bp.Storage), "drone_storage")
	}
	if len(bp.Other) > 0 {
		v, _ := json.Marshal(bp.Other)
		if len(v) < 4*1024 {
			p.Value["extra"] = string(v)
		}
	}
}

func (p *DroneProperties) ToBiz() *biz.DroneProperties {
	bp := &biz.DroneProperties{
		Id:        p.Id,
		RxTime:    p.RxTime,
		Timestamp: p.TS,
		DeviceId:  p.DeviceId,
		Sn:        p.Sn,
		DroneSn:   p.DroneSN,
		DroneType: p.DroneType,
		Mode:      p.Mode,
	}
	if (p.stateBitmap>>31)&1 == 1 {
		bp.FlightState = newValueForState(p.Value, droneFlightStateType, "drone_flight").(*biz.DroneFlightState)
	}
	if (p.stateBitmap>>30)&1 == 1 {
		bp.PositionState = newValueForState(p.Value, positionStateType, "drone_position").(*biz.PositionState)
	}
	if (p.stateBitmap>>29)&1 == 1 {
		bp.Battery = newValueForState(p.Value, droneBatteryStateType, "drone_battery").(*biz.DroneBatteryState)
	}
	if (p.stateBitmap>>28)&1 == 1 {
		bp.Storage = newValueForState(p.Value, storageType, "drone_storage").(*biz.Storage)
	}
	if v, ok := p.Value["extra"].(string); ok {
		json.Unmarshal([]byte(v), &bp.Other)
	}
	return bp
}

func (p *DroneProperties) FromTDResult(raw map[string]any) {
	if v, ok := raw["device_id"].(string); ok {
		p.DeviceId = conv.Atoi(v)
		delete(raw, "device_id")
	}
	if v, ok := raw["sn"].(string); ok {
		p.Sn = v
		delete(raw, "sn")
	}
	if v, ok := raw["drone_sn"].(string); ok {
		p.DroneSN = v
		delete(raw, "drone_sn")
	}
	if v, ok := raw["drone_type"].(string); ok {
		p.DroneType = v
		delete(raw, "drone_type")
	}
	if v, ok := raw["id"].(string); ok {
		p.Id = v
		delete(raw, "id")
	}
	if v, ok := raw["rx_time"].(time.Time); ok {
		p.RxTime = v
		delete(raw, "rx_time")
	}
	if v, ok := raw["ts"].(time.Time); ok {
		p.TS = v
		delete(raw, "ts")
	}
	if v, ok := raw["state_bitmap"].(float64); ok {
		p.stateBitmap = uint32(v)
		delete(raw, "state_bitmap")
	}
	if v, ok := raw["mode"].(float64); ok {
		p.Mode = int32(v)
		delete(raw, "mode")
	}
	p.Value = raw
}

func (p *DroneProperties) GenInsert() string {
	sb := &strings.Builder{}
	sb.Grow(2048)
	sb.WriteString("INSERT INTO ")
	sb.WriteString(p.TableName())
	sb.WriteString(" USING ")
	sb.WriteString(DronePropertySTableName)
	sb.WriteString(" (sn,device_id,drone_sn, drone_type)")
	sb.WriteString(" TAGS (")
	sb.WriteString(stringValuer(p.Sn))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(conv.Vtoa(p.DeviceId)))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(p.DroneSN))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(p.DroneType))
	sb.WriteString(") (rx_time, ts, state_bitmap, id, mode")
	cs := listColumns(p.Value)
	for _, cn := range cs {
		sb.WriteRune(',')
		sb.WriteString(cn)
	}
	sb.WriteString(") VALUES (")
	sb.WriteString(conv.Vtoa(p.RxTime.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Vtoa(p.TS.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Utoa(uint64(p.stateBitmap)))
	sb.WriteString(",'")
	sb.WriteString(p.Id)
	sb.WriteString("',")
	sb.WriteString(conv.Itoa(int64(p.Mode)))
	for _, cn := range cs {
		sb.WriteRune(',')
		if valuer, ok := columnSQLValuers[cn]; ok {
			sb.WriteString(valuer(p.Value[cn]))
		} else {
			sb.WriteString("NULL")
		}
	}
	sb.WriteString(")")
	return sb.String()
}

type DockEvent struct {
	// Tag
	DeviceId int64
	Sn       string
	// Columns
	Id           string
	RxTime       time.Time
	OccurredTime time.Time
	Type         int32

	Values map[string]any
}

func (e *DockEvent) TableName() string {
	return fmt.Sprintf("de_%s", strings.ReplaceAll(e.Sn, "-", "_"))
}

func (e *DockEvent) FromEvent(te biz.ThingEvent, data any) {
	if data == nil {
		return
	}
	e.DeviceId = te.DeviceId
	e.Sn = te.Sn
	e.Id = te.Id
	e.RxTime = te.RxTime
	e.OccurredTime = te.OccurredTime
	e.Type = int32(te.Type)
	v := reflect.ValueOf(data)
	if !v.IsValid() {
		return
	}
	if v.Kind() == reflect.Pointer {
		v = v.Elem()
	}
	t := v.Type()
	for i := 0; i < t.NumField(); i++ {
		ft := t.Field(i)
		if ft.IsExported() && !ft.Anonymous {
			e.Values[ft.Name] = v.Field(i).Interface()
		}
	}
}

func (e *DockEvent) GenInsert() string {
	sb := strings.Builder{}
	sb.Grow(2048)
	sb.WriteString("INSERT INTO ")
	sb.WriteString(e.TableName())
	sb.WriteString(" USING ")
	sb.WriteString(DockPropertySTableName)
	sb.WriteString(" (sn, device_id) TAGS (")
	sb.WriteString(stringValuer(e.Sn))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(conv.Vtoa(e.DeviceId)))
	sb.WriteString(") (rx_time, occurred_time, type, id, data) VALUES (")
	sb.WriteString(conv.Vtoa(e.RxTime.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Vtoa(e.OccurredTime.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Vtoa(e.Type))
	sb.WriteString(",'")
	sb.WriteString(e.Id)
	sb.WriteString(",")
	if len(e.Values) > 0 {
		sb.WriteString("'")
		data, _ := json.Marshal(e.Values)
		sb.WriteString(string(data))
		sb.WriteString("'")
	} else {
		sb.WriteString("NULL")
	}
	sb.WriteString(")")
	return sb.String()
}
