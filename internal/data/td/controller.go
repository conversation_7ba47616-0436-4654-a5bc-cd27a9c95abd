package td

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

const RemoteControllerPropertyStableName = "controller_properties"

var controllerStateType = reflect.TypeOf(biz.RemoteControllerState{})

// 遥控器属性
type RemoteControllerProperties struct {
	DeviceId int64
	Sn       string

	RxTime      time.Time
	stateBitmap uint32
	TS          time.Time
	Id          string

	Value map[string]any
}

func (p *RemoteControllerProperties) TableName() string {
	return fmt.Sprintf("rc_%s", strings.ReplaceAll(p.Sn, "-", "_"))
}

func (p *RemoteControllerProperties) IsEmpty() bool {
	return p.stateBitmap == 0
}

func (p *RemoteControllerProperties) FromControllerProperties(bp *biz.RemoteControllerProperties) {
	p.DeviceId = bp.DeviceId
	p.Sn = bp.Sn
	p.RxTime = bp.RxTime
	p.TS = bp.Timestamp
	p.Id = bp.Id
	p.Value = make(map[string]interface{})
	if bp.State != nil {
		p.stateBitmap = 1 << 31
		v := reflect.ValueOf(bp.State)
		writeState(p.Value, controllerStateType, v, "rc_state")
	}
	if bp.WirelessLinkState != nil {
		p.stateBitmap = p.stateBitmap | (1 << 30)
		v := reflect.ValueOf(bp.WirelessLinkState)
		writeState(p.Value, wirelessLinkStateType, v, "wireless")
	}

	if len(bp.Other) > 0 {
		v, _ := json.Marshal(bp.Other)
		if len(v) < 4*1024 {
			p.Value["extra"] = string(v)
		}
	}

}

func (p *RemoteControllerProperties) ToBiz() *biz.RemoteControllerProperties {
	bp := &biz.RemoteControllerProperties{
		Id:        p.Id,
		RxTime:    p.RxTime,
		Timestamp: p.TS,
		DeviceId:  p.DeviceId,
		Sn:        p.Sn,
	}
	if (p.stateBitmap>>31)&1 == 1 {
		bp.State = newValueForState(p.Value, controllerStateType, "rc_state").(*biz.RemoteControllerState)
	}
	if (p.stateBitmap>>30)&1 == 1 {
		bp.WirelessLinkState = newValueForState(p.Value, wirelessLinkStateType, "wireless").(*biz.WirelessLinkState)
	}

	if v, ok := p.Value["extra"].(string); ok {
		json.Unmarshal([]byte(v), &bp.Other)
	}
	return bp
}

func (p *RemoteControllerProperties) FromTDResult(raw map[string]any) {
	if v, ok := raw["device_id"].(string); ok {
		p.DeviceId = conv.Atoi(v)
		delete(raw, "device_id")
	}
	if v, ok := raw["sn"].(string); ok {
		p.Sn = v
		delete(raw, "sn")
	}
	if v, ok := raw["id"].(string); ok {
		p.Id = v
		delete(raw, "id")
	}
	if v, ok := raw["rx_time"].(time.Time); ok {
		p.RxTime = v
		delete(raw, "rx_time")
	}
	if v, ok := raw["ts"].(time.Time); ok {
		p.TS = v
		delete(raw, "ts")
	}
	if v, ok := raw["state_bitmap"].(float64); ok {
		p.stateBitmap = uint32(v)
		delete(raw, "state_bitmap")
	}
	p.Value = raw
}

func (p *RemoteControllerProperties) GenInsert() string {
	sb := &strings.Builder{}
	sb.Grow(2048)
	sb.WriteString("INSERT INTO ")
	sb.WriteString(p.TableName())
	sb.WriteString(" USING ")
	sb.WriteString(RemoteControllerPropertyStableName)
	sb.WriteString(" (sn, device_id) TAGS (")
	sb.WriteString(stringValuer(p.Sn))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(conv.Vtoa(p.DeviceId)))
	sb.WriteString(") (rx_time, ts, state_bitmap, id")
	cs := listColumns(p.Value)
	for _, cn := range cs {
		sb.WriteRune(',')
		sb.WriteString(cn)
	}
	sb.WriteString(") VALUES (")
	sb.WriteString(conv.Vtoa(p.RxTime.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Vtoa(p.TS.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Utoa(uint64(p.stateBitmap)))
	sb.WriteString(",'")
	sb.WriteString(p.Id)
	sb.WriteByte('\'')
	for _, cn := range cs {
		sb.WriteRune(',')
		if valuer, ok := columnSQLValuers[cn]; ok {
			sb.WriteString(valuer(p.Value[cn]))
		} else {
			sb.WriteString("NULL")
		}
	}
	sb.WriteString(")")
	return sb.String()
}
