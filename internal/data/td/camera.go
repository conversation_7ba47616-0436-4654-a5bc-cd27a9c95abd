package td

import (
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type DroneCameraProperty struct {
	biz.DroneCameraState
	RxTime    time.Time
	Timestamp time.Time
	// 所属网关id
	DeviceId int64
	// 所属网关sn
	Sn string
	// 所属无人机sn
	DroneSn string
	// 负载编号
	PayloadIndex string
}

func (p *DroneCameraProperty) TableName() string {
	return fmt.Sprintf("camera_%s_%s", p.DroneSn, strings.ReplaceAll(p.Index, "-", "_"))
}

func (p *DroneCameraProperty) FromBiz(bdp *biz.DroneProperties, bcp *biz.DroneCameraState) *DroneCameraProperty {
	p.DroneSn = bdp.DroneSn
	p.DeviceId = bdp.DeviceId
	p.Sn = bdp.Sn
	p.Timestamp = bdp.Timestamp
	p.RxTime = bdp.RxTime
	p.Index = bcp.Index
	p.DroneCameraState = *bcp
	return p
}

func (p *DroneCameraProperty) ToBiz(bdp *biz.DroneProperties) *biz.DrondCameraProperty {
	if bdp != nil {
		bdp.Cameras = append(bdp.Cameras, &p.DroneCameraState)
	}
	return &biz.DrondCameraProperty{
		DockSn:    p.Sn,
		DroneSn:   p.DroneSn,
		RxTime:    p.RxTime,
		Timestamp: p.Timestamp,
		State:     &p.DroneCameraState,
	}
}

// CREATE STABLE IF NOT EXISTS camera_property(rx_time timestamp, ts timestamp, camera_index VARCHAR(1024), camera_mode INT,
// camera_photo_state INT, camera_recording_state INT, camera_zoom_factor DOUBLE, camera_ir_zoom_factor DOUBLE, camera_zoom_focus_mode INT, camera_zoom_focus_value INT, camera_zoom_focus_state INT, camera_ir_metering_mode INT, camera_gimbal_pitch DOUBLE, camera_gimbal_roll DOUBLE, camera_gimbal_yaw DOUBLE, camera_measure_target_longitude DOUBLE, camera_measure_target_latitude DOUBLE, camera_measure_target_altitude DOUBLE, camera_measure_target_distance DOUBLE, camera_measure_err_state INT, camera_live_view_world_region VARCHAR(4096),  extra binary(4096))
// TAGS (sn binary(30), device_id binary(50), drone_sn binary(50));
func (p *DroneCameraProperty) GenInsert() string {
	sb := &strings.Builder{}
	sb.Grow(1024)
	sb.WriteString("INSERT INTO ")
	sb.WriteString(p.TableName())
	sb.WriteString(" USING ")
	sb.WriteString(DroneCameraSTableName)
	sb.WriteString(" (sn,device_id,drone_sn,payload)")
	sb.WriteString(" TAGS (")
	sb.WriteString(stringValuer(p.Sn))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(conv.Vtoa(p.DeviceId)))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(p.DroneSn))
	sb.WriteRune(',')
	sb.WriteString(stringValuer(p.PayloadIndex))
	sb.WriteString(") (rx_time, ts")
	cn := cameraStateType.NumField()
	cns := make([]string, 0, cn)
	for i := 0; i < cn; i++ {
		sb.WriteRune(',')
		n := newColumnName("camera", cameraStateType.Field(i).Name)
		cns = append(cns, n)
		sb.WriteString(n)
	}
	sb.WriteString(") VALUES (")
	sb.WriteString(conv.Vtoa(p.RxTime.UnixMilli()))
	sb.WriteRune(',')
	sb.WriteString(conv.Vtoa(p.Timestamp.UnixMilli()))
	v := reflect.ValueOf(p.DroneCameraState)
	for i := 0; i < cn; i++ {
		sb.WriteRune(',')
		if valuer, ok := columnSQLValuers[cns[i]]; ok {
			sb.WriteString(valuer(v.Field(i).Interface()))
		} else {
			sb.WriteString("NULL")
		}
	}
	sb.WriteString(")")
	return sb.String()
}

func (p *DroneCameraProperty) FromTDResult(raw map[string]any) {
	if v, ok := raw["device_id"].(string); ok {
		p.DeviceId = conv.Atoi(v)
		delete(raw, "device_id")
	}
	if v, ok := raw["sn"].(string); ok {
		p.Sn = v
		delete(raw, "sn")
	}
	if v, ok := raw["drone_sn"].(string); ok {
		p.DroneSn = v
		delete(raw, "drone_sn")
	}
	if v, ok := raw["payload"].(string); ok {
		p.PayloadIndex = v
		delete(raw, "payload")
	}
	if v, ok := raw["rx_time"].(time.Time); ok {
		p.RxTime = v
		delete(raw, "rx_time")
	}
	if v, ok := raw["ts"].(time.Time); ok {
		p.Timestamp = v
		delete(raw, "ts")
	}
	s := newValueForState(raw, cameraStateType, "camera").(*biz.DroneCameraState)
	p.DroneCameraState = *s
}
