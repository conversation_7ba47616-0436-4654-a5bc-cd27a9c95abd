package td

import (
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strings"
	"time"

	"github.com/stoewer/go-strcase"
	"github.com/tidwall/conv"
)

var typeTime = reflect.TypeOf(time.Time{})

var DB = "skai_data"
var columnSQLValuers map[string]func(v any) string = make(map[string]func(v any) string)

func init() {
	runENV := os.Getenv("RUN_ENV")
	if strings.Contains(runENV, "dev") || strings.Contains(runENV, "test") {
		DB = fmt.Sprintf("%s_%s", DB, runENV)
	}
	makeValuerForType(dockStateType, "state")
	makeValuerForType(positionStateType, "position")
	makeValuerForType(networkStateType, "network")
	makeValuerForType(flightTaskStateType, "flight")
	makeValuerForType(envStateType, "env")
	makeValuerForType(wirelessLinkStateType, "wireless")
	makeValuerForType(elecStateType, "elec")
	makeValuerForType(batteryChargeStateType, "battery")

	makeValuerForType(droneFlightStateType, "drone_flight")
	makeValuerForType(positionStateType, "drone_position")
	makeValuerForType(droneBatteryStateType, "drone_battery")
	makeValuerForType(storageType, "drone_storage")
	makeValuerForType(cameraStateType, "camera")
	makeValuerForType(controllerStateType, "rc_state")
}

func newColumnName(prefix, fieldName string) string {
	return fmt.Sprintf("%s_%s", prefix, strcase.SnakeCase(fieldName))
}

func makeValuerForType(t reflect.Type, prefix string) {
	if t.Kind() != reflect.Struct {
		return
	}
	for i := 0; i < t.NumField(); i++ {
		f := t.Field(i)
		columnSQLValuers[newColumnName(prefix, f.Name)] = newColumnSQLValuer(f.Type)
	}
}

func newColumnSQLValuer(t reflect.Type) func(v any) string {
	switch t.Kind() {
	case reflect.Bool:
		return func(v any) string {
			if v == nil {
				return "FALSE"
			}
			if r, ok := v.(bool); ok {
				if r {
					return "TRUE"
				}
			}
			return "FALSE"
		}
	case reflect.Float32:
		return func(v any) string {
			if v != nil {
				if r, ok := v.(float32); ok {
					return conv.Ftoa(float64(r))
				}
			}
			return "0"
		}
	case reflect.Float64:
		return func(v any) string {
			if v != nil {
				if r, ok := v.(float64); ok {
					return conv.Ftoa(r)
				}
			}
			return "0"
		}
	case reflect.Int32:
		return func(v any) string {
			if v != nil {
				if r, ok := v.(int32); ok {
					return conv.Vtoa(r)
				}
			}
			return "0"
		}
	case reflect.Int:
		return func(v any) string {
			if v != nil {
				if r, ok := v.(int); ok {
					return conv.Vtoa(r)
				}
			}
			return "0"
		}
	case reflect.Int64:
		return func(v any) string {
			if v != nil {
				if r, ok := v.(int64); ok {
					return conv.Vtoa(r)
				}
			}
			return "0"
		}
	case reflect.Uint64:
		return func(v any) string {
			if v != nil {
				if r, ok := v.(uint64); ok {
					return conv.Utoa(r)
				}
			}
			return "0"
		}
	case reflect.Int8, reflect.Int16, reflect.Uint8, reflect.Uint16, reflect.Uint32:
		return func(v any) string {
			if v != nil {
				var fv int64 = 0
				switch r := v.(type) {
				case int8:
					fv = int64(r)
				case int16:
					fv = int64(r)
				case uint8:
					fv = int64(r)
				case uint16:
					fv = int64(r)
				case uint32:
					fv = int64(r)
				}
				return conv.Itoa(fv)
			}
			return "0"
		}
	case reflect.String:
		return func(v any) string {
			if v != nil {
				return stringValuer(v.(string))
			}
			return "''"
		}
	case reflect.Array, reflect.Slice, reflect.Struct, reflect.Pointer:
		return func(v any) string {
			if v != nil {
				if jv, err := json.Marshal(v); err == nil {
					return fmt.Sprintf("'%s'", jv)
				}
			}
			return "''"
		}
	default:
		return func(v any) string {
			return "NULL"
		}
	}
}

var stringValuer = func(v string) string {
	return fmt.Sprintf("'%s'", strings.Replace(v, `'`, `''`, -1))
}

func getFieldValue(t reflect.Type, v any) reflect.Value {
	if t == typeTime {
		if ts, err := time.Parse(time.RFC3339Nano, v.(string)); err == nil {
			return reflect.ValueOf(ts)
		} else {
			return reflect.ValueOf(time.Time{})
		}
	}
	k := t.Kind()
	switch k {
	case reflect.Float32:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(float32(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Array, reflect.Slice, reflect.Struct:
		rv := reflect.New(t)
		if err := json.Unmarshal([]byte(v.(string)), rv.Interface()); err == nil {
			return rv.Elem()
		} else {
			return rv.Elem()
		}
	case reflect.Int:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(int(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Int8:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(int8(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Int16:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(int16(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Int32:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(int32(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Int64:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(int64(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Uint:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(uint(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Uint8:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(uint8(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Uint16:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(uint16(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Uint32:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(uint32(rv))
		} else {
			return reflect.ValueOf(0)
		}
	case reflect.Uint64:
		if rv, ok := v.(float64); ok {
			return reflect.ValueOf(uint64(rv))
		} else {
			return reflect.ValueOf(0)
		}
	default:
		return reflect.ValueOf(v)
	}
}
