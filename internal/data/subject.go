package data

import (
	"context"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
)

type subjectRepo struct {
	data *Data
	log  *log.Helper
}

func NewSubjectRepo(logger log.Logger, data *Data) biz.SubjectRepo {
	return &subjectRepo{
		data: data,
		log:  log.<PERSON>elper(logger),
	}
}

func (r *subjectRepo) CreateSubject(ctx context.Context, body *biz.Subject) (*biz.Subject, error) {
	subject := &gorm.Subject{}
	subject.FromBizSubject(body)
	if err := r.data.db.WithContext(ctx).Create(subject).Error; err != nil {
		return nil, err
	}
	return subject.ToBizSubject(), nil
}

func (r *subjectRepo) UpdateSubject(ctx context.Context, id int64, body map[string]interface{}) error {
	subject := &gorm.Subject{}
	subject.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(subject).Updates(body).Error
}

func (r *subjectRepo) DeleteSubject(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&gorm.Subject{}, "id=?", id).Error
}

func (r *subjectRepo) ListSubjects(ctx context.Context, query *biz.SubjectListQuery) (int32, []*biz.Subject, error) {
	var count int64
	subjects := []*gorm.Subject{}
	r.log.Warnf("list subjects query: %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Subject{}).Where("project_id = ?", query.ProjectId)
	if query.Unscoped {
		tx = tx.Unscoped()
	}
	if query.EventType != nil {
		tx.Where("event_type = ?", *query.EventType)
	}
	if query.Search != nil {
		value := *query.Search
		tx = tx.Where("name LIKE ?", "%"+value+"%")
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get subjects from db failed", err.Error())
	}
	if count == 0 {
		return 0, make([]*biz.Subject, 0), nil
	}
	if err := tx.Order("updated_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&subjects).Error; err != nil {
		r.log.Errorf("get subjects from db failed: %v", err)
		return 0, nil, errors.New(500, "get subjects from db failed", err.Error())
	}
	list := lo.Map(subjects, func(o *gorm.Subject, _ int) *biz.Subject {
		return o.ToBizSubject()
	})
	return int32(count), list, nil
}

func (r *subjectRepo) GetSubject(ctx context.Context, query *biz.DetailQuery) (*biz.Subject, error) {
	subject := &gorm.Subject{}
	r.log.Warnf("get subject %d", query.Id)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Subject{}).Where("id = ?", query.Id)
	// 如果查询范围为true，表示查询软删除的主题
	if query.Unscoped {
		tx = tx.Unscoped()
	}
	if err := tx.First(subject).Error; err != nil {
		return nil, err
	}
	return subject.ToBizSubject(), nil
}
