package data

import (
	"context"
	"encoding/json"
	"reflect"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/tidwall/conv"
	connV1 "gitlab.sensoro.com/skai/skai/api/connects/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"google.golang.org/protobuf/types/known/structpb"
)

type connectRepo struct {
	log    *log.Helper
	client connV1.ConnectClient
	data   *Data
}

func NewConnectRepo(
	logger log.Logger,
	data *Data,
) (biz.ConnectRepo, error) {
	client, err := grpc.DialInsecure(context.Background(), grpc.WithEndpoint(data.conf.Skai.ServerEp))
	if err != nil {
		return nil, err
	}

	return &connectRepo{
		log:    log.NewHelper(logger),
		data:   data,
		client: connV1.NewConnectClient(client),
	}, nil
}

func (r *connectRepo) PushDockProperties(ctx context.Context, dp *biz.DockProperties) error {
	data := connV1.BuildDockPropertyUpRequest(dp)
	res, err := r.client.PropertyUp(ctx, data)
	r.log.Debugf("PushDockProperties %s res %+v", data.Id, res)
	return err
}

func (r *connectRepo) PushDroneProperties(ctx context.Context, dp *biz.DroneProperties) error {
	data := connV1.BuildDockDronePropertyUpRequest(dp)
	res, err := r.client.PropertyUp(ctx, data)
	r.log.Debugf("PushDroneProperties %s res %+v", data.Id, res)
	return err
}

func (r *connectRepo) PushControllerProperties(ctx context.Context, cp *biz.RemoteControllerProperties) error {
	data := connV1.BuildControllerPropertyUpRequest(cp)
	res, err := r.client.PropertyUp(ctx, data)
	r.log.Debugf("PushControllerProperties %s res %+v", data.Id, res)
	return err
}

func (r *connectRepo) PushEvent(ctx context.Context, eventType biz.ThingModelEventType, event any) (*biz.EventUpReply, error) {
	var data *connV1.EventUpRequest
	switch eventType {
	case biz.ThingModelEventTypeFlightTaskProgress:
		if be, ok := event.(*biz.AutoFlightTaskProgressEvent); ok {
			data = connV1.BuildDockFlightTaskProgressEvent(be)
		}
	case biz.ThingModelEventTypeHMS:
		if be, ok := event.(*biz.DockHealMonitorEvent); ok {
			data = connV1.BuildDockHealMoniterEvent(be)
		}
	case biz.ThingModelEventTypeUpdateTopo:
		if be, ok := event.(*biz.DockTopoUpdateEvent); ok {
			data = connV1.BuildDockTopoUpdateEvent(be)
		}
	case biz.ThingModelEventTypeFlightTaskResourceGet:
		if be, ok := event.(*biz.DockFlightTaskResourceRequestEvent); ok {
			data = connV1.BuildDockFlightTaskResourceRequestEvent(be)
		}
	case biz.ThingModelEventTypeFlytoPointProgress:
		if be, ok := event.(*biz.FlytoPointProgressEvent); ok {
			data = connV1.BuildSimlepleEvent(be.ThingEvent, &biz.FlytoPointProgressEvent{Result: be.Result, FlytoId: be.FlytoId, Status: be.Status, CurrentWaypointIndex: be.CurrentWaypointIndex})
		}
	case biz.ThingModelEventTypeDRCState:
		if be, ok := event.(*biz.DockDRCStatusEvent); ok {
			data = connV1.BuildSimlepleEvent(be.ThingEvent, &biz.DockDRCStatusEvent{State: be.State, ErrCode: be.ErrCode})
		}
	case biz.ThingModelEventTypeSpeakerStatus:
		if be, ok := event.(*biz.SpeakerPlayStatusEvent); ok {
			data = connV1.BuildSimlepleEvent(be.ThingEvent, &biz.SpeakerPlayStatusEvent{Code: be.Code, Mode: be.Mode, Progress: be.Progress, Step: be.Step, PlayFileSignature: be.PlayFileSignature, Index: be.Index})
		}
	case biz.ThingModelEventTypeLaunchPointProgress:
		if be, ok := event.(*biz.LaunchPointProgressEvent); ok {
			data = connV1.BuildSimlepleEvent(be.ThingEvent, &biz.LaunchPointProgressEvent{Result: be.Result, Status: be.Status, CurrentWaypointIndex: be.CurrentWaypointIndex, FlightId: be.FlightId})
		}
	case biz.ThingModelEventTypeOrbitPointNotify:
		if be, ok := event.(*biz.OrbitPointNotifyEvent); ok {
			data = connV1.BuildSimlepleEvent(be.ThingEvent, &biz.OrbitPointNotifyEvent{Status: be.Status, Code: be.Code, Speed: be.Speed, Radius: be.Radius, MaxSpeed: be.MaxSpeed})
		}
	case biz.ThingModelEventTypeOperationProgress:
		if be, ok := event.(*biz.OperationProgressEvent); ok {
			data = connV1.BuildSimlepleEvent(be.ThingEvent, &biz.OperationProgressEvent{Status: be.Status, Code: be.Code, Progress: be.Progress, OperationId: be.OperationId, ServiceType: be.ServiceType})
		}
	case biz.ThingModelEventTypeFileuploadProgress:
		if be, ok := event.(*biz.FileuploadProgressEvent); ok {
			data = connV1.BuildSimlepleEvent(be.ThingEvent, &biz.FileuploadProgressEvent{Files: be.Files})
		}
	default:
		return nil, biz.NewBadRequestError("PushEvent.unknownEvent", map[string]string{"eventType": conv.Vtoa(eventType)})
	}
	if data != nil {
		res, err := r.client.EventUp(ctx, data)
		if err != nil {
			return nil, err
		}
		var resData biz.AnyMap
		if res.Data != nil {
			resData = res.Data.AsMap()
			r.log.Infof("PushEvent response %+v", resData)
		}
		return &biz.EventUpReply{
			Id:       data.Id,
			Sn:       data.Sn,
			DeviceId: data.DeviceId,
			Data:     resData,
		}, nil
	}
	eventData, _ := json.Marshal(event)
	return nil, biz.NewBadRequestError("PushEvent.invalidEvent", map[string]string{
		"event": string(eventData),
	})
}

func (s *connectRepo) PushDockServiceReply(ctx context.Context, r *biz.DockServiceReply) error {
	var data *structpb.Struct
	if len(r.Data) > 0 {
		data, _ = structpb.NewStruct(r.Data)
	}
	_, err := s.client.ServiceReply(ctx, &connV1.ServiceReplyRequest{
		Sn:         r.Sn,
		Id:         r.Id,
		RxTime:     r.RxTime.UnixMilli(),
		Timestamp:  r.Timestamp.UnixMilli(),
		DeviceId:   r.DeviceId,
		ServiceId:  r.ServiceId,
		Identifier: r.Identifier,
		Status:     r.Status.String(),
		Message:    r.Message,
		Data:       data,
		Code:       r.Code,
	})
	return err
}

func (s *connectRepo) RunDownlinkNextStep(ctx context.Context, dev *biz.Device, puber biz.PacketPublisher, reply any) (bool, error) {
	sender, ok := s.data.senderFactory.NewSender(dev, puber)
	if !ok {
		return false, biz.NewInternalError("RunDownlinkNextStep.senderNotFound", nil)
	}
	return sender.HasMoreOperation(ctx, reply)
}

func (s *connectRepo) SendDownlink(ctx context.Context, dev *biz.Device, puber biz.PacketPublisher, serviceId string, serviceData any) error {
	sender, ok := s.data.senderFactory.NewSender(dev, puber)
	if !ok {
		return biz.NewInternalError("SendDownlink.senderNotFound", nil)
	}
	return sender.RunService(ctx, serviceId, serviceData)
}

func (s *connectRepo) GetDownlinkState(ctx context.Context, model biz.DeviceModel, downId string) (any, error) {
	sender, ok := s.data.senderFactory.NewSender(&biz.Device{Model: model}, nil)
	if !ok {
		return nil, biz.NewInternalError("GetDownlinkState.senderNotFound", nil)
	}
	return sender.GetState(ctx, downId)
}

func (s *connectRepo) newSimlepleEventValue(event any) []byte {
	v := reflect.ValueOf(event)
	if v.Kind() == reflect.Pointer {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		return nil
	}
	vt := v.Type()
	m := make(map[string]any)
	for i := 0; i < vt.NumField(); i++ {
		f := v.Field(i)
		ft := vt.Field(i)
		if ft.Anonymous {
			continue
		}
		if f.IsNil() || f.IsZero() {
			continue
		}
		if ft.IsExported() && f.CanInterface() {
			name := ft.Name
			if jt, ok := ft.Tag.Lookup("json"); ok {
				if tvs := strings.Split(jt, ","); len(tvs) > 0 {
					name = tvs[0]
				}
			}
			m[name] = f.Interface()
		}
	}
	out, _ := json.Marshal(m)
	return out
}
