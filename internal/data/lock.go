package data

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type lockRepo struct {
	data *Data
	log  *log.Helper
}

func NewLockRepo(data *Data, logger log.Logger) biz.LockRepo {
	return &lockRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *lockRepo) newLockKey(l *biz.Lock) string {
	return fmt.Sprintf("%s:%s", biz.SkaiLockKeyPrefix, l.Name)
}

func (r *lockRepo) TryLock(ctx context.Context, l *biz.Lock) error {
	if l.TTL == 0 {
		l.TTL = 10 * time.Second
	}
	ret := r.data.rdb.SetNX(ctx, r.new<PERSON><PERSON><PERSON>(l), l.GetLockId(), l.TTL)
	if ret.Err() != nil || !ret.Val() {
		return biz.ErrLockFailed
	}
	return nil
}

func (r *lockRepo) Release(ctx context.Context, l *biz.Lock) error {
	k := r.newLockKey(l)
	id, err := r.data.rdb.Get(ctx, k).Result()
	if err != nil {
		if err == redis.Nil {
			return nil
		}
		r.log.WithContext(ctx).Error("release lock get for %s failed %e", k, err)
		return biz.NewInternalError("lock.Release.get", map[string]string{"lk": k})
	}
	if l.GetLockId() == id {
		if err = r.data.rdb.Del(ctx, k).Err(); err != nil {
			r.log.WithContext(ctx).Error("release lock del for %s failed %e", k, err)
			return biz.NewInternalError("lock.Release.del", map[string]string{"lk": k})
		}
	}
	return errors.BadRequest("lock.Release.notYourLock", "无法释放不属于你的锁")
}
