package data

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/snownd/cake"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"
)

type emqxClientSubListQuery struct {
	cake.RequestConfig
	ClientId string `param:"clientId"`
}

type emqxSubscription struct {
	Node     string `json:"node"`
	Topic    string `json:"topic"`
	ClientId string `json:"clientid"`
	Qos      byte   `json:"qos"`
	Nl       byte   `json:"nl"`
	Rap      byte   `json:"rap"`
	Rh       byte   `json:"rh"`
}

// type emqxPublishMessageProperties struct {
// 	// 载荷格式指示标识符，0 表示载荷是未指定格式的数据，相当于没有发送载荷格式指示；1 表示载荷是 UTF-8 编码的字符数据，载荷中的 UTF-8 数据必须是按照 Unicode 的规范和 RFC 3629 的标准要求进行编码的。
// 	PayloadFormatIndicator byte `json:"payload_format_indicator"`
// 	// 消息过期间隔标识符，以秒为单位。当消失已经过期时，如果服务端还没有开始向匹配的订阅者投递该消息，则服务端会删除该订阅者的消息副本。如果不设置，则消息永远不会过期
// 	MessageExpiryInterval int               `json:"message_expiry_interval"`
// 	ResponseTopic         string            `json:"response_topic,omitempty"`
// 	UserProperties        map[string]string `json:"user_properties,omitempty"`
// 	ContentType           string            `json:"content_type,omitempty"`
// }

// var defaultProperties emqxPublishMessageProperties = emqxPublishMessageProperties{
// 	PayloadFormatIndicator: 0,
// 	MessageExpiryInterval:  5,
// }

type emqxPublishMessage struct {
	Qos      byte   `json:"qos"`
	Encoding string `json:"payload_encoding"`
	Topic    string `json:"topic"`
	Payload  string `json:"payload"`
	// Propertiest *emqxPublishMessageProperties `json:"properties,"`
	Retain bool `json:"retain"`
}

type emqxPublishDto struct {
	cake.RequestConfig
	Message *emqxPublishMessage `body:"json"`
}

type emqxPublishVo struct {
	ReasonCode int    `json:"reason_code"`
	Id         string `json:"id"`
	Message    string `json:"message"`
}

type emqxManagerClient struct {
	ListClientSubscriptions func(ctx context.Context, query *emqxClientSubListQuery) ([]*emqxSubscription, error) `method:"GET" url:"/api/v5/clients/:clientId/subscriptions"`
	Publish                 func(ctx context.Context, data *emqxPublishDto) (*emqxPublishVo, error)               `method:"POST" url:"/api/v5/publish"`
}

type mqttRepo struct {
	log  *log.Helper
	data *Data
	mc   *emqxManagerClient
}

func NewMqttRepo(
	logger log.Logger,
	data *Data,
) (biz.MQTTRepo, error) {
	mc, err := data.clientFactory.Build(&emqxManagerClient{},
		cake.WithBaseURL(data.conf.Mqtt.RestfulUrl),
		cake.WithRequestMiddleware(newEMQXAuthTokenMW(data.conf)),
	)
	if err != nil {
		return nil, err
	}
	return &mqttRepo{
		log.NewHelper(logger),
		data,
		mc.(*emqxManagerClient),
	}, nil
}

func (r *mqttRepo) getDeviceBySourceSn(ctx context.Context, sourceSn string) (*biz.Device, error) {
	device := &gorm.Device{}
	if err := r.data.db.WithContext(ctx).First(device, "source_sn=?", sourceSn).Error; err == nil {
		return device.ToBizDevice(), nil
	} else {
		return nil, err
	}
}

func (r *mqttRepo) Publish(ctx context.Context, topic string, qos byte, data []byte) (biz.PublishResult, error) {
	r.log.Debugf("publish to topic %s with qos %d data %s", topic, qos, data)
	start := time.Now()
	ret, err := r.mc.Publish(ctx, &emqxPublishDto{
		Message: &emqxPublishMessage{
			Qos:      qos,
			Encoding: "plain",
			Topic:    topic,
			Payload:  string(data),
			// Propertiest: defaultProperties,
		},
	})
	if err != nil {
		var rErr cake.RequestError
		if errors.As(err, &rErr) {
			r.log.Warnf("Publish.requestEMQXErr status %d %s", rErr.StatusCode(), rErr.Error())
			return biz.PublishResult{}, errors.New(rErr.StatusCode(), "Publish.requestEMQXErr", "网络错误")
		}
		r.log.Errorf("publish to topic %s data %s failed %v", topic, data, err)
		return biz.PublishResult{}, errors.InternalServer("Publish.unknownErr", "网络错误").WithCause(err)
	}
	r.log.Infof("publish to topic %s cost %v", topic, time.Since(start))
	return biz.PublishResult{
		Id:      ret.Id,
		Message: ret.Message,
		Code:    ret.ReasonCode,
	}, nil
}

func (r *mqttRepo) GetClientSubscriptions(ctx context.Context, clientId string) ([]*biz.MQTTSubscription, error) {
	subs, err := r.mc.ListClientSubscriptions(ctx, &emqxClientSubListQuery{ClientId: clientId})
	if err != nil {
		var rErr cake.RequestError
		if errors.As(err, &rErr) {
			r.log.Warnf("GetClientSubscriptions.requestEMQXErr status %d", rErr.StatusCode())
			return nil, errors.New(rErr.StatusCode(), "GetClientSubscriptions.requestEMQXErr", "网络错误")
		}
		return nil, errors.InternalServer("GetClientSubscriptions.unknownErr", "网络错误").WithCause(err)
	}
	return lo.Map(subs, func(s *emqxSubscription, _ int) *biz.MQTTSubscription {
		return &biz.MQTTSubscription{
			Node:            s.Node,
			ClientId:        s.ClientId,
			Topic:           s.Topic,
			Qos:             s.Qos,
			NotLocal:        s.Nl,
			RetainPublished: s.Rap,
			RetainHanding:   s.Rh,
		}
	}), nil
}

func (r *mqttRepo) CanClientConnect(ctx context.Context, info *biz.MQTTClientInfo) (biz.MQTTConnectResult, error) {
	if info.IsSuper() {
		ok := compareStringSafe(info.Password, r.data.conf.Mqtt.SuperToken)
		return biz.MQTTConnectResult{
			Ok:      ok,
			IsSuper: true,
		}, nil
	}
	if info.IsAppClient() {
		// TODO:
	}

	if _, err := r.getDeviceBySourceSn(ctx, info.Username); err == nil {
		return biz.MQTTConnectResult{
			Ok: true,
		}, nil
	} else {
		r.log.Errorf("get info for device %s failed %v", info.Username, err)
	}
	return biz.MQTTConnectResult{}, nil
}

func (r *mqttRepo) HasAccessToTopic(ctx context.Context, info *biz.MQTTClientTopicAccessInfo) (biz.MQTTClientAccessResult, error) {
	client := &biz.MQTTClientInfo{
		Username: info.Username,
		ClientId: info.ClientId,
	}
	if client.IsSuper() {
		return biz.MQTTClientAccessResult{Ok: true}, nil
	}
	if client.IsAppClient() {
		// TODO:
	}
	device, err := r.getDeviceBySourceSn(ctx, info.Username)
	if err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return biz.MQTTClientAccessResult{}, nil
		}
		r.log.Errorf("HasAccessToTopic.getDeviceFromDB sourceSn %s clientId %s for %d topic %s", info.Username, info.ClientId, info.Action, info.Topic)
		return biz.MQTTClientAccessResult{}, biz.NewInternalError("HasAccessToTopic.getDeviceFromDB", nil)
	}
	// TODO check by model
	if strings.Contains(info.Topic, device.SourceSn) {
		return biz.MQTTClientAccessResult{Ok: true}, nil
	}
	return biz.MQTTClientAccessResult{}, nil
}

func compareStringSafe(a, b string) bool {
	if len(a) != len(b) {
		return false
	}
	same := true
	for i := 0; i < len(a); i++ {
		if a[i] != b[i] {
			same = false
		}
	}

	return same
}

func newEMQXAuthTokenMW(conf *conf.Data) cake.RequestMiddleware {
	token := "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", conf.Mqtt.RestfulUser, conf.Mqtt.RestfulPass)))
	return func(c *cake.RequestContext) error {
		c.Request.Header.Set("Authorization", token)
		return c.Next()
	}
}
