package data

import (
	"context"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
)

type logfileRepo struct {
	data *Data
	log  *log.Helper
}

func NewLogfileRepo(logger log.Logger, data *Data) biz.LogfileRepo {
	return &logfileRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *logfileRepo) CreateLogfiles(ctx context.Context, body []*biz.Logfile) error {
	logfiles := make([]*gorm.Logfile, 0)
	for _, wp := range body {
		logfile := &gorm.Logfile{}
		logfile.FromBizLogfile(wp)
		logfiles = append(logfiles, logfile)
	}
	return r.data.db.WithContext(ctx).CreateInBatches(logfiles, 100).Error
}

func (r *logfileRepo) UpdateLogfile(ctx context.Context, id int64, body biz.AnyMap) error {
	logfile := &gorm.Logfile{}
	logfile.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(logfile).Updates(body).Error
}

func (r *logfileRepo) DeleteLogfile(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&gorm.Logfile{}, "id=?", id).Error
}

func (r *logfileRepo) GetLogfile(ctx context.Context, query *biz.DetailQuery) (*biz.Logfile, error) {
	logfile := &gorm.Logfile{}
	r.log.Warnf("get logfile %d", query.Id)
	if err := r.data.db.WithContext(ctx).First(logfile, "id = ?", query.Id).Error; err != nil {
		return nil, errors.New(500, "get logfile from db failed", err.Error())
	}
	return logfile.ToBizLogfile(), nil
}

func (r *logfileRepo) SearchLogfile(ctx context.Context, query *biz.LogfileSearchQuery) (*biz.Logfile, error) {
	logfile := &gorm.Logfile{}
	r.log.Debugf("search logfile %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Logfile{}).Where("device_id = ?", query.DeviceId)
	tx.Where("url = ? and created_time >= ?", query.Url, time.Now().Add(-7*24*time.Hour))
	if err := tx.First(logfile).Error; err != nil {
		return nil, errors.New(500, "get logfile from db failed", err.Error())
	}
	return logfile.ToBizLogfile(), nil
}

func (r *logfileRepo) ListLogfiles(ctx context.Context, query *biz.LogfileListQuery) (int32, []*biz.Logfile, error) {
	var count int64
	r.log.Warnf("list logfiles query: %+v", query)
	logfiles := []*gorm.Logfile{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Logfile{}).Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	tx.Where("device_id = ?", query.DeviceId)
	tx.Where("created_time between ? and ?", query.StartTime, query.EndTime)
	if len(query.Modules) > 0 {
		tx.Where("module = ANY(?)", pq.StringArray(query.Modules))
	}
	if len(query.Status) > 0 {
		tx.Where("status = ANY(?)", pq.Int32Array(query.Status))
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get logfile count from db failed", err.Error())
	}
	if count == 0 {
		return 0, make([]*biz.Logfile, 0), nil
	}
	if query.Size == 0 {
		return int32(count), nil, nil
	}
	if err := tx.Order("created_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&logfiles).Error; err != nil {
		r.log.Errorf("get logfiles from db failed: %v", err)
		return 0, nil, errors.New(500, "get logfiles from db failed", err.Error())
	}
	list := lo.Map(logfiles, func(c *gorm.Logfile, _ int) *biz.Logfile {
		return c.ToBizLogfile()
	})
	return int32(count), list, nil
}
