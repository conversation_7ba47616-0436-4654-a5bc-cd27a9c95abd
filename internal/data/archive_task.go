package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"
	"gitlab.sensoro.com/skai/stream-sidecar/pkg/stream"
)

type archiveTaskRepo struct {
	log  *log.Helper
	tsMS stream.TSMetaStore
	data *Data
}

func NewArchiveTaskRepo(data *Data, logger log.Logger, ms stream.TSMetaStore) biz.ArchiveTaskRepo {
	return &archiveTaskRepo{
		log:  log.NewHelper(logger),
		data: data,
		tsMS: ms,
	}
}

func (r *archiveTaskRepo) Create(ctx context.Context, t *biz.ArchiveTask) error {
	gt := &gorm.ArchiveTask{}
	if err := gt.FromBizArchiveTask(t); err != nil {
		r.log.Errorf("create archive task %+v error: %v", t, err)
		return biz.NewBadRequestError("archiveTask.create.trans", nil)
	}
	if err := r.data.db.WithContext(ctx).Create(gt).Error; err != nil {
		r.log.Errorf("create archive task %+v db error: %v", t, err)
		return biz.NewInternalError("archiveTask.create.db", nil)
	}
	t.Id = gt.BaseEntity.Id
	t.CreatedTime = gt.CreatedTime
	t.UpdatedTime = gt.UpdatedTime
	return nil
}

func (r *archiveTaskRepo) FindLast(ctx context.Context) (*biz.ArchiveTask, error) {
	gt := &gorm.ArchiveTask{}
	if err := r.data.db.WithContext(ctx).Last(gt).Error; err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return nil, biz.NewNotFoundError("ArchiveTask", "id", "last")
		}
		r.log.Errorf("find last archive task db error: %v", err)
		return nil, biz.NewInternalError("archiveTask.findLast.db", nil)
	}
	return gt.ToBiz(), nil
}

func (r *archiveTaskRepo) NeedToRunNext(ctx context.Context, last *biz.ArchiveTask) (bool, error) {
	ac := r.data.sconf.ArchiveTask
	if ac == nil {
		return false, nil
	}
	if last == nil {
		return true, nil
	}
	if last.TimeScope.EndTime.Add(time.Duration(ac.MinFileLife*24)*time.Hour + 24*time.Hour).Before(time.Now()) {
		return true, nil
	}
	return false, nil
}

func (r *archiveTaskRepo) DownsizeVoyageVideoRecord(ctx context.Context, m *biz.Media, or biz.ObjectRemover) (int64, error) {
	relatedLive := &gorm.Media{}
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).Where("device_id=? and voyage_id=0 and type = ? and sub_device_index=?",
		m.DeviceId, biz.MediaTypeLive, m.SubDeviceIndex,
	).Take(relatedLive).Error; err != nil {
		r.log.Errorf("DownsizeVoyageVideoRecord find related live %d db error: %v", m.Id, err)
		return 0, biz.NewInternalError("media.findRelatedLive.db", nil)
	}
	v := &gorm.Voyage{}
	if err := r.data.db.WithContext(ctx).Where("id=?", m.VoyageId).Take(v).Error; err != nil {
		r.log.Errorf("DownsizeVoyageVideoRecord find voyage %d db error: %v", m.VoyageId, err)
		return 0, biz.NewInternalError("voyage.find.db", nil)
	}
	nextVoyage := &gorm.Voyage{}
	endTime := v.EndTime
	if endTime.IsZero() {
		endTime = m.CreatedTime
	}
	if err := r.data.db.WithContext(ctx).Where("device_id = ? and start_time > ? and start_time < ?",
		m.DeviceId, endTime, m.CreatedTime.Add(1*time.Hour),
	).Order("created_time asc").Take(nextVoyage).Error; err != nil && !gorm.IfRecordNotFoundErr(err) {
		r.log.Errorf("find next voyage %d db error: %v", v.Id, err)
		return 0, biz.NewInternalError("voyage.findNext.db", nil)
	}
	if nextVoyage.Id == 0 {
		nextVoyage.StartTime = m.CreatedTime.Add(1 * time.Hour)
	}
	tss, err := r.tsMS.ListTsMeta(ctx, &stream.ListTsQuery{
		Stream: stream.StreamDescriptor{
			StreamId: relatedLive.Key,
			App:      liveDefaultApp,
		},
		StartTime: endTime.Add(1 * time.Minute),
		EndTime:   nextVoyage.StartTime.Add(-5 * time.Minute),
	})
	if err != nil {
		r.log.Errorf("list ts meta %s error: %v", relatedLive.Key, err)
		return 0, biz.NewInternalError("ts.list", nil)
	}
	var ds int64
	if len(tss) > 0 {
		for _, ts := range tss {
			r.log.Debugf("downsizeTask rm ts %s for voyage %d", ts.Path, v.BaseEntity.Id)
			ds += ts.FileSize
			or.DeleteObject(ctx, &biz.StorageObject{Key: ts.Path})
		}
	}
	return ds, nil
}

func (r *archiveTaskRepo) UpdateTask(ctx context.Context, t *biz.ArchiveTask) error {
	gt := &gorm.ArchiveTask{}
	if err := gt.FromBizArchiveTask(t); err != nil {
		r.log.Errorf("update archive task %+v error: %v", t, err)
		return biz.NewBadRequestError("archiveTask.update.trans", nil)
	}
	if err := r.data.db.WithContext(ctx).Save(gt).Error; err != nil {
		r.log.Errorf("update archive task %+v db error: %v", t, err)
		return biz.NewInternalError("archiveTask.update.db", nil)
	}
	return nil
}
