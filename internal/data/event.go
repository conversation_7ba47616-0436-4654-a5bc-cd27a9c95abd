package data

import (
	"context"
	"fmt"
	"time"

	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
)

type eventRepo struct {
	data *Data
	log  *log.Helper
}

func NewEventRepo(logger log.Logger, data *Data) biz.EventRepo {
	return &eventRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *eventRepo) CreateEvent(ctx context.Context, body *biz.Event) (*biz.Event, error) {
	eventedKey := fmt.Sprintf("%s:EVENT:%s:%s", biz.SkaiCacheKeyPrefix, body.Sn, body.Code)
	// 设置X事件生成锁，默认3分钟内不可连续创建，离线和指令异常事件除外
	flag := true
	if !lo.Contains([]biz.EventType{biz.OfflineEvent, biz.ServiceReplyError}, body.Type) {
		flag = r.data.rdb.SetNX(ctx, eventedKey, eventedKey, 3*time.Minute).Val()
	}
	if !flag {
		return nil, errors.New(409, "event conflict during moments", "event already created")
	}
	event := &gorm.Event{
		BaseEntity:   *gorm.NewBase(),
		Sn:           body.Sn,
		Type:         body.Type,
		Code:         body.Code,
		Level:        body.Level,
		Place:        body.Place,
		ExtraData:    body.ExtraData,
		OccurredTime: body.OccurredTime,
	}
	if err := r.data.db.WithContext(ctx).Create(event).Error; err != nil {
		return nil, err
	}
	return event.ToBizEvent(), nil
}

func (r *eventRepo) ListEvents(ctx context.Context, query *biz.EventListQuery) (count int64, list []*biz.Event, err error) {
	events := []*gorm.Event{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Event{}).Where("sn = ?", query.Sn)
	tx.Where("occurred_time between ? and ?", query.StartTime, query.EndTime)
	if len(query.Types) > 0 {
		tx.Where("type = ANY(?)", pq.StringArray(query.Types))
	}
	if len(query.Levels) > 0 {
		tx.Where("level = ANY(?)", pq.Int32Array(query.Levels))
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get events from db failed", err.Error())
	}
	if err := tx.Order("occurred_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&events).Error; err != nil {
		r.log.Errorf("get events from db failed: %v", err)
		return 0, nil, errors.New(500, "get events from db failed", err.Error())
	}
	list = lo.Map(events, func(o *gorm.Event, _ int) *biz.Event {
		return o.ToBizEvent()
	})
	return
}

func (r *eventRepo) ExportEvents(ctx context.Context, query *biz.EventListQuery) (<-chan *biz.Event, error) {
	tx := r.data.db.WithContext(ctx).Model(&gorm.Event{}).Where("sn = ?", query.Sn)
	tx.Where("occurred_time between ? and ?", query.StartTime, query.EndTime)
	rows, err := tx.Order("occurred_time DESC").Rows()
	if err != nil {
		return nil, err
	}
	ch := make(chan *biz.Event, 10)
	go func() {
		defer close(ch)
		for rows.Next() {
			var event gorm.Event
			r.data.db.ScanRows(rows, &event)
			ch <- event.ToBizEvent()
		}
	}()
	return ch, nil
}

func (r *eventRepo) UpdateEvent(ctx context.Context, id int64, body map[string]interface{}) error {
	event := &gorm.Event{}
	event.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(event).Updates(body).Error
}

func (r *eventRepo) LockAutoback(ctx context.Context, deviceId int64) bool {
	// 设置自动返航生成锁，默认11s内不可连续创建
	eventedKey := fmt.Sprintf("%s:DEVICE:AUTOBACK:%d", biz.SkaiLockKeyPrefix, deviceId)
	ret := r.data.rdb.SetNX(ctx, eventedKey, eventedKey, 11*time.Second)
	if ret.Err() != nil {
		r.log.Errorf("Lock autoback eventKey failed: %+v", ret.Err())
		return false
	}
	r.log.Infof("Lock autoback eventKey within setnx success")
	return ret.Val()
}

func (r *eventRepo) UnlockAutoback(ctx context.Context, deviceId int64) error {
	eventedKey := fmt.Sprintf("%s:DEVICE:AUTOBACK:%d", biz.SkaiLockKeyPrefix, deviceId)
	if r.data.rdb.Exists(ctx, eventedKey).Val() == 0 {
		r.log.Errorf("Nonexist autoback eventKey: %s", eventedKey)
		return errors.New(404, "autoback eventKey not found", "autoback eventKey not found")
	}
	// 清理自动返航生成锁
	if err := r.data.rdb.Del(ctx, eventedKey).Err(); err != nil {
		r.log.Errorf("Unlock autoback eventKey failed: %+v", err)
		return err
	}
	r.log.Infof("Unlock autoback eventKey within delete success")
	return nil
}

func (r *eventRepo) SendManualSms(ctx context.Context, event *biz.Event) error {
	if r.data.pusher == nil {
		r.log.Errorf("Pusher client not init")
		return nil
	}
	// 短信推送规则: 1.同一维保通知重复间隔1小时；2.首次收到通知24小时之内最多3次
	smsKey := fmt.Sprintf("%s:DEVICE:%s:%s:%s", biz.SkaiLockKeyPrefix, event.Type.String(), event.Sn, event.Code)
	list := r.data.rdb.LRange(ctx, smsKey, 0, -1).Val()
	if len(list) == 0 {
		// 首次收到通知，添加时间并设置24小时过期
		r.data.rdb.LPush(ctx, smsKey, time.Now().Unix())
		r.data.rdb.Expire(ctx, smsKey, 24*time.Hour)
	} else if len(list) < 3 {
		// 1小时内重复收到通知，跳过
		if time.Now().Unix()-conv.Atoi(list[0]) < 60*60 {
			r.log.Infof(event.Type.String() + " smsKey repeat 1 hour, skip")
			return nil
		}
		// 添加时间至列表头部
		r.data.rdb.LPushX(ctx, smsKey, time.Now().Unix())
	} else {
		// 24小时内超过3次，跳过
		r.log.Infof(event.Type.String() + " smsKey over 3 times, skip")
		return nil
	}
	// 发送短信
	body := &client.PushOption{
		Mode:       client.SmsMode,
		Source:     "SKAI",
		Sign:       "SENSORO",
		Name:       "无人机飞手",
		To:         r.data.conf.Maintenance.Mobile,
		TemplateId: r.data.conf.Maintenance.Template,
		TemplateValue: biz.AnyMap{
			"description": event.Trans(),
			"name":        event.ExtraData["name"],
			"category":    event.ExtraData["category"],
			"enevt":       fmt.Sprintf("“%s”", event.Describe()),
			"time":        event.OccurredTime.In(biz.TimeLocation).Format("2006-01-02 15:04:05"),
		},
	}
	if _, err := r.data.pusher.Send(ctx, &client.PushDTO{Body: body}); err != nil {
		r.log.Errorf("Push send sms failed: %+v", err)
		return err
	}
	r.log.Infof("Send manual SMS within pusher success")
	return nil
}
