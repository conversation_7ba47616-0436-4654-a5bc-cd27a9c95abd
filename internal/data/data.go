package data

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	tdclient "gitlab.sensoro.com/go-sensoro/td-client"
	snaptaskV1 "gitlab.sensoro.com/lins/lins-snapshoter/api/snaptasks/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/data/downlink"
	gormModel "gitlab.sensoro.com/skai/skai/internal/data/gorm"
	"gitlab.sensoro.com/skai/skai/internal/data/thumbnail"

	"github.com/davidbyttow/govips/v2/vips"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/go-redis/redis/v8"
	"github.com/google/wire"
	"github.com/snownd/cake"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewS3Config,
	NewS3Client,
	NewS3PresignClient,
	NewSigner,
	NewAirlineRepo,
	NewAuthRepo,
	NewData,
	NewEventRepo,
	NewDeviceRepo,
	NewMqttRepo,
	NewOperationRepo,
	NewDelayRepo,
	NewVoyageRepo,
	NewWaypointRepo,
	NewPropertyRepo,
	NewSimpleStorageRepo,
	NewConnectRepo,
	NewDockEventRepo,
	NewMediaRepo,
	NewSessionRepo,
	NewConfigRepo,
	NewLockRepo,
	NewLiveRepo,
	NewAiEventRepo,
	NewMissionRepo,
	NewExecutionRepo,
	NewSnapshotRepo,
	NewAnalyserRepo,
	NewArchiveTaskRepo,
	NewAnnotationRepo,
	NewSubjectRepo,
	NewTSMetaStore,
	NewLogfileRepo,
)

type SnaptaskClient = snaptaskV1.TaskHTTPClient

// Data .
type Data struct {
	conf    *conf.Data
	sconf   *conf.Server
	db      *gorm.DB
	rdb     redis.UniversalClient
	auth    *client.AuthClient
	session *client.SessionClient
	// 通知服务, 边缘环境通常禁用
	pusher *client.PushClient
	// 设备服务, 边缘环境通常禁用
	deviceClient *client.DeviceManagerClient
	// 事件服务，边缘环境通常禁用
	emCleint *client.EventManagerClient
	// 延时服务，边缘环境通常禁用
	dmClient *client.DelayTaskManagerClient
	// 抽帧服务, 边缘环境通常禁用
	stClient         SnaptaskClient
	clientFactory    *cake.Factory
	tdClient         *tdclient.Client
	senderFactory    *downlink.SenderFactory
	thumbnailJobChan chan *thumbnail.Job
	chClient         *client.ChannelManagerClient
}

// NewData .
func NewData(s *conf.Server, c *conf.Data, logger log.Logger) (*Data, func(), error) {
	vips.Startup(&vips.Config{ConcurrencyLevel: 4})
	clientFactory := client.NewClientFactoryWithMiddleware()
	auth, err := clientFactory.BuildAuthClient(&client.BuildOption{Server: s.Auth.Url + "/internal"})
	if err != nil {
		panic(err)
	}
	session, err := clientFactory.BuildSessionClient(&client.BuildOption{Server: s.Auth.Url + "/api"})
	if err != nil {
		panic(err)
	}
	// 边缘环境, "Push.Url"为空, 不实例化client
	var pusher *client.PushClient
	if s.Push != nil && s.Push.Url != "" {
		if pusher, err = clientFactory.BuildPushClient(&client.BuildOption{Server: s.Push.Url}); err != nil {
			panic(err)
		}
	}
	// 边缘环境, "Device.Url"为空, 不实例化client
	var deviceClient *client.DeviceManagerClient
	if s.Device != nil && s.Device.Url != "" {
		if deviceClient, err = clientFactory.BuildDeviceManager(&client.BuildOption{Server: s.Device.Url}); err != nil {
			panic(err)
		}
	}
	// 边缘环境, "Device.Url"为空, 不实例化client
	var emClient *client.EventManagerClient
	if s.Device != nil && s.Device.Url != "" {
		if emClient, err = clientFactory.BuildEventManagerClient(&client.BuildOption{Server: s.Device.Url}); err != nil {
			panic(err)
		}
	}
	// 边缘环境, "Delay.Url"为空, 不实例化client
	var dmClient *client.DelayTaskManagerClient
	if s.Delay != nil && s.Delay.Url != "" {
		if dmClient, err = clientFactory.BuildTaskManager(&client.BuildOption{Server: s.Delay.Url}); err != nil {
			panic(err)
		}
	}
	// 边缘环境, "Snapshot.Endpoint"为空, 不实例化client
	var stClient SnaptaskClient
	if c.Snapshot.Endpoint != "" {
		client, err := http.NewClient(context.Background(), http.WithEndpoint(c.Snapshot.Endpoint), http.WithTimeout(15*time.Second))
		if err != nil {
			panic(err)
		}
		stClient = snaptaskV1.NewTaskHTTPClient(client)
	}
	// 边缘环境, "Visual.Url"为空, 不实例化client
	var chClient *client.ChannelManagerClient
	if s.Visual != nil && s.Visual.Url != "" {
		if chClient, err = clientFactory.BuildChannelManager(&client.BuildOption{Server: s.Visual.Url}); err != nil {
			panic(err)
		}
	}
	tdclient := tdclient.NewClient(tdclient.WithBasicAuth(c.Tdengine.Auth.Username, c.Tdengine.Auth.Password),
		tdclient.WithTimestampResponse(false),
		tdclient.WithBrokers(strings.Split(c.Tdengine.Brokers, ",")),
		tdclient.WithMaxSockets(int(c.Tdengine.MaxSockets)),
	)
	tdclient.Connect(context.TODO())
	factory := cake.New()
	db, err := gorm.Open(postgres.Open(c.Database.Uri), &gorm.Config{
		SkipDefaultTransaction: true,
		PrepareStmt:            true,
	})
	if err != nil {
		panic(err)
	}
	sqlDB, err := db.DB()
	if err != nil {
		panic(err)
	}
	sqlDB.SetMaxIdleConns(3)
	sqlDB.SetConnMaxLifetime(30 * time.Minute)
	sqlDB.SetMaxOpenConns(16)
	sqlDB.SetConnMaxIdleTime(2 * time.Minute)

	db = db.Debug()
	if os.Getenv("RUN_ENV") != "local" {
		runMigrate(db)
	}
	rdb := redis.NewUniversalClient(&redis.UniversalOptions{
		Addrs:        []string{c.Redis.Addr},
		Password:     c.Redis.Password,
		DB:           int(c.Redis.Db),
		ReadTimeout:  c.Redis.ReadTimeout.AsDuration(),
		DialTimeout:  c.Redis.DialTimeout.AsDuration(),
		WriteTimeout: c.Redis.WriteTimeout.AsDuration(),
	})
	sf := downlink.NewSenderFactory(
		rdb, c, logger,
	)
	tc := make(chan *thumbnail.Job)
	ctx, cancel := context.WithCancel(context.Background())
	thumbnail.NewWorkerPool(ctx, tc, 4)
	cleanup := func() {
		cancel()
		tdclient.Close(context.TODO())
		sqlDB.Close()
		rdb.Close()
		vips.Shutdown()
		logger.Log(log.LevelInfo, "closing the data resources")
	}
	return &Data{c, s, db, rdb, auth, session, pusher, deviceClient, emClient, dmClient, stClient, factory, tdclient, sf, tc, chClient}, cleanup, nil
}

func (data *Data) WithQuery(query biz.DataListQuery) *gorm.DB {
	page, pageSize := query.PageInfo()

	if page > 0 {
		page = page - 1
	}

	db := data.db.Limit(pageSize).Offset(page * pageSize)

	query.Conditions().ApplyToGorm(db)

	return db
}

func (data *Data) Count(model interface{}, query biz.DataListQuery) int64 {
	var total int64

	db := data.db.Model(model)

	query.Conditions().ApplyToGorm(db)

	db.Count(&total)

	return total
}

func (data *Data) NewDBQuery(ctx context.Context) *gorm.DB {
	return data.db.WithContext(ctx)
}

func runMigrate(db *gorm.DB) {
	db.AutoMigrate(
		&gormModel.Airline{},
		&gormModel.Device{},
		&gormModel.Event{},
		&gormModel.Operation{},
		&gormModel.Voyage{},
		&gormModel.Waypoint{},
		&gormModel.Media{},
		&gormModel.Stream{},
		&gormModel.Mission{},
		&gormModel.Execution{},
		&gormModel.ArchiveTask{},
		&gormModel.Annotation{},
		&gormModel.Subject{},
		&gormModel.Logfile{},
	)
	db.Exec(fmt.Sprintf("CREATE index if not exists idx_sk_media_created_time on %s (created_time)", gormModel.Media{}.TableName()))
}
