package data

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
)

type delayRepo struct {
	log    *log.Helper
	conf   *conf.Server
	redis  redis.UniversalClient
	client *client.DelayTaskManagerClient
}

func NewDelayRepo(logger log.Logger, data *Data) biz.DelayRepo {
	return &delayRepo{
		redis:  data.rdb,
		conf:   data.sconf,
		client: data.dmClient,
		log:    log.<PERSON>Helper(logger),
	}
}

func (r *delayRepo) CreateTask(ctx context.Context, task *biz.DelayTask) error {
	r.log.Infof("call delay with CreateTask task: %+v", task)
	if r.conf.Delay.Strategy == "builtin" {
		return r.createBuiltinTask(ctx, task)
	} else if r.conf.Delay.Strategy == "clouded" {
		return r.createCloudedTask(ctx, task)
	} else {
		r.log.Errorf("delay strategy not support")
		return errors.New(500, "delay.strategy not support", "unsupport strategy")
	}
}

func (r *delayRepo) DeleteTask(ctx context.Context, key string) error {
	r.log.Infof("call delay with DeleteTask task: %s", key)
	if r.conf.Delay.Strategy == "builtin" {
		return r.deleteBuiltinTask(ctx, key)
	} else if r.conf.Delay.Strategy == "clouded" {
		return r.deleteCloudedTask(ctx, key)
	} else {
		r.log.Errorf("delay strategy not support")
		return nil
	}
}

func (r *delayRepo) createBuiltinTask(ctx context.Context, task *biz.DelayTask) error {
	/** 由两步存储实现[dataKey + eventKey]
	1. 优先存储dataKey数据，作为有效负载回调用，其中TTL比Delay略长，默认+30秒
	2. 然后存储eventKey键，便于Radar服务监听，其中TTL严格等于Delay时长，值随机即可
	*/
	dataKey := fmt.Sprintf("%s:DATA", task.Key)
	dataTTL := task.Delay + 30*time.Second
	dataValue, _ := json.Marshal(map[string]interface{}{"callback": task.Callback, "source": task.Source, "body": task.Payload})
	// 每次dataKey用最新dataValue数据覆盖
	if err := r.redis.SetEX(ctx, dataKey, dataValue, dataTTL).Err(); err != nil {
		r.log.Errorf("call redis-delay service with store dataKey failed: %+v", err)
		return err
	}
	// 如果key已存在则直接续期，否则设置键-值-时
	if ok := r.redis.ExpireXX(ctx, task.Key, task.Delay).Val(); !ok {
		if err := r.redis.SetEX(ctx, task.Key, task.Delay, task.Delay).Err(); err != nil {
			r.log.Errorf("call redis-delay service with store eventKey failed: %+v", err)
			return err
		}
	}
	r.log.Infof("call redis-delay service with CreateTask success")
	return nil
}

func (r *delayRepo) deleteBuiltinTask(ctx context.Context, key string) error {
	// 仅删除eventKey即可，dataKey等待自动失效(回调不会被触发)
	if err := r.redis.Del(ctx, key).Err(); err != nil {
		r.log.Errorf("call redis-delay service with delete eventKey failed: %+v", err)
		return err
	}
	r.log.Infof("call redis-delay service with DeleteTask success")
	return nil
}

func (r *delayRepo) createCloudedTask(ctx context.Context, task *biz.DelayTask) error {
	data, err := r.client.CreateDelayTask(ctx, &client.CreateDelayTaskDTO{Body: &client.DelayTask{
		ClientId: task.Key,
		Source:   task.Source,
		Body:     task.Payload,
		Callback: r.conf.Skai.Url + task.Callback,
		Times:    task.Times,
		Delay:    int(task.Delay / time.Second),
	}})
	if err != nil {
		r.log.Errorf("call delay service with CreateTask failed: %+v", err)
		return err
	}
	if data.Code != 0 {
		r.log.Warnf("call delay service with CreateTask failed, code: %d, message: %s", data.Code, data.Message)
		return errors.New(500, "call delay service failed", data.Message)
	}
	r.log.Infof("call delay service with CreateTask success, taskId: %s", data.Data.Id)
	return nil
}

func (r *delayRepo) deleteCloudedTask(ctx context.Context, clientId string) error {
	data, err := r.client.DeleteDelayTaskByClientId(ctx, &client.DeleteDelayTaskByClientIdDTO{ClientId: &client.TaskClientIdObject{ClientId: clientId}})
	if err != nil {
		return err
	}
	if data.Code != 0 {
		r.log.Warnf("call delay service with DeleteTask failed, code: %d, message: %s", data.Code, data.Message)
		return errors.New(500, "call delay service failed", data.Message)
	}
	r.log.Infof("call delay service with DeleteTask success, data: %+v", data)
	return nil
}
