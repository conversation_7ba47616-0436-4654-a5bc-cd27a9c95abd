package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	tdclient "gitlab.sensoro.com/go-sensoro/td-client"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/td"
)

type propertyRepo struct {
	log  *log.Helper
	data *Data
}

func NewPropertyRepo(logger log.Logger, data *Data) biz.PropertyRepo {
	return &propertyRepo{
		log:  log.NewHelper(logger),
		data: data,
	}
}

func (r *propertyRepo) RecordDroneCameraProperties(ctx context.Context, data *biz.DroneProperties) error {
	if data == nil {
		return biz.NewBadRequestError("RecordDroneCameraProperties.nilData", nil)
	}
	if data.DeviceId == 0 || data.Sn == "" {
		return biz.NewBadRequestError("RecordDroneCameraProperties.noDock", nil)
	}
	r.log.Debugf("RecordDroneCameraProperties for %s  %s id= %s cps %d", data.Sn, data.DroneSn, data.Id, len(data.Cameras))
	if len(data.Cameras) == 0 {
		return nil
	}
	cps := lo.Map(data.Cameras, func(it *biz.DroneCameraState, _ int) *td.DroneCameraProperty {
		return &td.DroneCameraProperty{
			RxTime:           data.RxTime,
			Timestamp:        data.Timestamp,
			DroneCameraState: *it,
			DeviceId:         data.DeviceId,
			Sn:               data.Sn,
			DroneSn:          data.DroneSn,
			PayloadIndex:     it.Index,
		}
	})
	var errs map[string]string
	lo.ForEach(cps, func(it *td.DroneCameraProperty, _ int) {
		sql := it.GenInsert()
		r.log.Debugf("RecordDroneCameraProperties for %s  %s withSQL: %s", data.Sn, data.DroneSn, sql)
		if ret, err := r.data.tdClient.Exec(ctx, td.DB, sql); err != nil {
			r.log.Errorf("RecordDroneCameraProperties withSQL: %s err: %v", sql, err)
			errs[it.Sn+it.PayloadIndex] = err.Error()
		} else if ret.Code != 0 {
			r.log.Errorf("RecordDroneCameraProperties withSQL: %s res: %v", sql, ret)
			errs[it.Sn+it.PayloadIndex] = ret.Message
		}
	})
	if len(errs) > 0 {
		return biz.NewInternalError("RecordDroneCameraProperties.insert", errs)
	}
	return nil
}

func (r *propertyRepo) ListCameraProperties(ctx context.Context, query *biz.CameraPropListQuery) (int64, []*biz.DrondCameraProperty, error) {
	if query == nil {
		return 0, nil, biz.NewBadRequestError("ListCameraProperties.nilQuery", nil)
	}
	if query.Sn == "" {
		return 0, nil, biz.NewBadRequestError("ListCameraProperties.noSn", nil)
	}
	if query.CameraIndex == "" {
		return 0, nil, biz.NewBadRequestError("ListCameraProperties.noCameraIndex", nil)
	}
	var total int64
	if !query.DisableCount() {
		if countRet, err := r.data.tdClient.NewSelect().Select(tdclient.Select{
			ColumnName: "COUNT(*)",
			Alias:      "total",
		}).
			WithTimescope(query.Start, query.End).
			Where(tdclient.Equals("sn", query.Sn), tdclient.Equals("payload", query.CameraIndex)).
			Limit(1, 0).
			UseDatabase(td.DB).
			FromTables(td.DroneCameraSTableName).Exec(ctx); err == nil {
			r.log.Infof("ListCameraProperties count for sn %s  with sql %s cost %d", query.Sn, countRet.SQL, countRet.Cost)
			if len(countRet.Data) == 1 {
				v, ok := countRet.Data[0]["total"].(float64)
				if ok {
					total = int64(v)
				}
			}
		} else {
			r.log.Errorf("ListCameraProperties count for sn %s times %s to %s failed %v", query.Sn, query.Start, query.End, err)
			return 0, nil, biz.NewInternalError("ListCameraProperties.count", nil)
		}
	}
	order := tdclient.DESC
	orderColumn := "_c0"
	limit := int(query.Size)
	offset := int((query.Page - 1) * query.Size)
	if limit == 0 {
		return total, nil, nil
	}
	if ret, err := r.data.tdClient.NewSelect().SelectAll().SelectColumn("sn", "").SelectColumn("payload", "").
		WithTimescope(query.Start, query.End).
		Where(tdclient.Equals("sn", query.Sn), tdclient.Equals("payload", query.CameraIndex)).
		Limit(limit, offset).
		OrderBy(order, orderColumn).
		UseDatabase(td.DB).
		FromTables(td.DroneCameraSTableName).
		Exec(ctx); err == nil {
		r.log.Infof("ListCameraProperties query for sn %s  with sql %s cost %d", query.Sn, ret.SQL, ret.Cost)
		return total, lo.Map(ret.Data, func(raw tdclient.Row, _ int) *biz.DrondCameraProperty {
			tp := &td.DroneCameraProperty{}
			tp.FromTDResult(raw)
			return tp.ToBiz(nil)
		}), nil
	} else {
		r.log.Errorf("ListCameraProperties query for sn %s times %s to %s failed %v", query.Sn, query.Start, query.End, err)
		return 0, nil, biz.NewInternalError("ListCameraProperties.query", map[string]string{
			"sn":    query.Sn,
			"index": query.CameraIndex,
			"start": query.Start.String(),
			"end":   query.End.String(),
		})
	}
}

func (r *propertyRepo) RecordDockProperties(ctx context.Context, data *biz.DockProperties) error {
	if data == nil {
		return biz.NewBadRequestError("RecordDockProperties.nilData", nil)
	}
	if data.DeviceId == 0 || data.Sn == "" {
		return biz.NewBadRequestError("RecordDockProperties.noDock", nil)
	}
	p := &td.DockProperties{}
	p.FromDockProperties(data)
	if p.IsEmpty() {
		r.log.Infof("RecordDockProperties.skipEmpty %+v", data)
		return nil
	}
	sql := p.GenInsert()
	if ret, err := r.data.tdClient.Exec(ctx, td.DB, sql); err != nil {
		r.log.Errorf("RecordDockProperties withSQL: %s err: %v", sql, err)
		return biz.NewInternalError("RecordDockProperties.insert", map[string]string{"detail": err.Error(), "device": data.Sn, "id": data.Id})
	} else if ret.Code != 0 {
		r.log.Errorf("RecordDockProperties withSQL: %s res: %v", sql, ret)
		return biz.NewInternalError("RecordDockProperties.insert", map[string]string{"detail": ret.Message, "device": data.Sn, "id": data.Id})
	}
	r.log.Debug("RecordDockProperties: ", sql)
	return nil
}

func (r *propertyRepo) RecordControllerProperties(ctx context.Context, data *biz.RemoteControllerProperties) error {
	if data == nil {
		return biz.NewBadRequestError("RecordControllerProperties.nilData", nil)
	}
	if data.DeviceId == 0 || data.Sn == "" {
		return biz.NewBadRequestError("RecordControllerProperties.noDock", nil)
	}
	p := &td.RemoteControllerProperties{}
	p.FromControllerProperties(data)
	if p.IsEmpty() {
		r.log.Infof("RecordControllerProperties.skipEmpty %+v", data)
		return nil
	}
	sql := p.GenInsert()
	if ret, err := r.data.tdClient.Exec(ctx, td.DB, sql); err != nil {
		r.log.Errorf("RecordControllerProperties withSQL: %s err: %v", sql, err)
		return biz.NewInternalError("RecordControllerProperties.insert", map[string]string{"detail": err.Error(), "device": data.Sn, "id": data.Id})
	} else if ret.Code != 0 {
		r.log.Errorf("RecordControllerProperties withSQL: %s res: %v", sql, ret)
		return biz.NewInternalError("RecordControllerProperties.insert", map[string]string{"detail": ret.Message, "device": data.Sn, "id": data.Id})
	}
	r.log.Debug("RecordControllerProperties: ", sql)
	return nil
}

func (r *propertyRepo) RecordDockDroneProperties(ctx context.Context, data *biz.DroneProperties) error {
	if data == nil {
		return biz.NewBadRequestError("RecordDockDroneProperties.nilData", nil)
	}
	if data.DeviceId == 0 || data.Sn == "" {
		return biz.NewBadRequestError("RecordDockDroneProperties.noDock", nil)
	}
	if data.DroneSn == "" {
		return biz.NewBadRequestError("RecordDockDroneProperties.noDrone", nil)
	}
	p := &td.DroneProperties{}
	p.FromDockDroneProperties(data)
	if p.IsEmpty() {
		r.log.Infof("RecordDockDroneProperties.skipEmpty %+v", data)
		return nil
	}
	sql := p.GenInsert()
	if ret, err := r.data.tdClient.Exec(ctx, td.DB, sql); err != nil {
		r.log.Errorf("RecordDockDroneProperties withSQL: %s err: %v", sql, err)
		return biz.NewInternalError("RecordDockDroneProperties.insert", map[string]string{"detail": err.Error(), "device": data.Sn, "id": data.Id})
	} else if ret.Code != 0 {
		r.log.Errorf("RecordDockDroneProperties withSQL: %s res: %v", sql, ret)
		return biz.NewInternalError("RecordDockDroneProperties.insert", map[string]string{"detail": ret.Message, "device": data.Sn, "id": data.Id})
	}
	r.log.Debug("RecordDockDroneProperties: ", sql)
	return nil
}

func (r *propertyRepo) ListDockProperties(ctx context.Context, query *biz.PropListQuery) (int64, []*biz.DockProperties, error) {
	if query == nil {
		return 0, nil, biz.NewBadRequestError("ListDockProperties.nilQuery", nil)
	}
	if query.Sn == "" {
		return 0, nil, biz.NewBadRequestError("ListDockProperties.noSn", nil)
	}
	p := &td.DockProperties{
		Sn: query.Sn,
	}
	order := tdclient.DESC
	orderColumn := "_c0"
	// if query.Sort.Field != nil {
	// 	orderColumn = *query.Sort.Field
	// 	if query.Sort.Order == biz.ListSortOrderAsc {
	// 		order = tdclient.ASC
	// 	}
	// }

	var total int64
	if !query.DisableCount() {
		if countRet, err := r.data.tdClient.NewSelect().Select(tdclient.Select{
			ColumnName: "COUNT(*)",
			Alias:      "total",
		}).
			WithTimescope(query.Start, query.End).
			Limit(1, 0).
			UseDatabase(td.DB).
			FromTables(p.TableName()).Exec(ctx); err == nil {
			r.log.Infof("ListDockProperties count for sn %s  with sql %s cost %d", query.Sn, countRet.SQL, countRet.Cost)
			if len(countRet.Data) == 1 {
				v, ok := countRet.Data[0]["total"].(float64)
				if ok {
					total = int64(v)
				}
			}
		} else {
			r.log.Errorf("ListDockProperties count for sn %s times %s to %s failed %v", query.Sn, query.Start, query.End, err)
			return 0, nil, biz.NewInternalError("ListDockProperties.count", nil)
		}
	}

	limit := int(query.Size)
	offset := int((query.Page - 1) * query.Size)
	if limit == 0 {
		return total, nil, nil
	}
	if ret, err := r.data.tdClient.NewSelect().SelectAll().SelectColumn("sn", "").
		WithTimescope(query.Start, query.End).
		Limit(limit, offset).
		OrderBy(order, orderColumn).
		UseDatabase(td.DB).
		FromTables(p.TableName()).
		Exec(ctx); err == nil {
		r.log.Infof("ListDockProperties query for sn %s  with sql %s cost %d", query.Sn, ret.SQL, ret.Cost)
		return total, lo.Map(ret.Data, func(raw tdclient.Row, _ int) *biz.DockProperties {
			tp := &td.DockProperties{}
			tp.FromTDResult(raw)
			return tp.ToBiz()
		}), nil
	} else {
		r.log.Errorf("ListDockProperties query for sn %s times %s to %s failed %v", query.Sn, query.Start, query.End, err)
		return 0, nil, biz.NewInternalError("ListDockProperties.query", map[string]string{
			"sn":    query.Sn,
			"start": query.Start.String(),
			"end":   query.End.String(),
		})
	}
}

func (r *propertyRepo) ListDockDroneProperties(ctx context.Context, query *biz.PropListQuery) (int64, []*biz.DroneProperties, error) {
	if query == nil {
		return 0, nil, biz.NewBadRequestError("ListDockDroneProperties.nilQuery", nil)
	}
	if query.Sn == "" {
		return 0, nil, biz.NewBadRequestError("ListDockDroneProperties.noSn", nil)
	}
	order := tdclient.DESC
	orderColumn := "_c0"
	// if query.Sort.Field != nil {
	// 	orderColumn = *query.Sort.Field
	// 	if query.Sort.Order == biz.ListSortOrderAsc {
	// 		order = tdclient.ASC
	// 	}
	// }

	var total int64
	if !query.DisableCount() {
		if countRet, err := r.data.tdClient.NewSelect().Select(tdclient.Select{
			ColumnName: "COUNT(*)",
			Alias:      "total",
		}).
			WithTimescope(query.Start, query.End).
			Where(tdclient.Equals("sn", query.Sn)).
			Limit(1, 0).
			UseDatabase(td.DB).
			FromSTable(td.DronePropertySTableName).Exec(ctx); err == nil {
			r.log.Infof("ListDockDroneProperties count for sn %s  with sql %s cost %d", query.Sn, countRet.SQL, countRet.Cost)
			if len(countRet.Data) == 1 {
				v, ok := countRet.Data[0]["total"].(float64)
				if ok {
					total = int64(v)
				}
			}
		} else {
			r.log.Errorf("ListDockDroneProperties count for sn %s times %s to %s failed %v", query.Sn, query.Start, query.End, err)
			return 0, nil, biz.NewInternalError("ListDockDroneProperties.count", nil)
		}
	}
	limit := int(query.Size)
	offset := int((query.Page - 1) * query.Size)
	if limit == 0 {
		return total, nil, nil
	}
	if ret, err := r.data.tdClient.NewSelect().SelectAll().SelectColumn("sn", "").
		WithTimescope(query.Start, query.End).
		Where(tdclient.Equals("sn", query.Sn)).
		Limit(limit, offset).
		OrderBy(order, orderColumn).
		UseDatabase(td.DB).
		// 使用超级表查询以处理无人机更换的问题
		FromSTable(td.DronePropertySTableName).
		Exec(ctx); err == nil {
		r.log.Infof("ListDockDroneProperties query for sn %s  with sql %s cost %d", query.Sn, ret.SQL, ret.Cost)
		return total, lo.Map(ret.Data, func(raw tdclient.Row, _ int) *biz.DroneProperties {
			tp := &td.DroneProperties{}
			tp.FromTDResult(raw)
			return tp.ToBiz()
		}), nil
	} else {
		r.log.Errorf("ListDockDroneProperties query for sn %s times %s to %s failed %v", query.Sn, query.Start, query.End, err)
		return 0, nil, biz.NewInternalError("ListDockDroneProperties.query", map[string]string{
			"sn":    query.Sn,
			"start": query.Start.String(),
			"end":   query.End.String(),
		})
	}
}

func (r *propertyRepo) ListControllerProperties(ctx context.Context, query *biz.PropListQuery) (int64, []*biz.RemoteControllerProperties, error) {
	if query == nil {
		return 0, nil, biz.NewBadRequestError("ListControllerProperties.nilQuery", nil)
	}
	if query.Sn == "" {
		return 0, nil, biz.NewBadRequestError("ListControllerProperties.noSn", nil)
	}
	p := &td.RemoteControllerProperties{
		Sn: query.Sn,
	}
	order := tdclient.DESC
	orderColumn := "_c0"
	// if query.Sort.Field != nil {
	// 	orderColumn = *query.Sort.Field
	// 	if query.Sort.Order == biz.ListSortOrderAsc {
	// 		order = tdclient.ASC
	// 	}
	// }

	var total int64
	if !query.DisableCount() {
		if !query.DisableCount() {
			if countRet, err := r.data.tdClient.NewSelect().Select(tdclient.Select{
				ColumnName: "COUNT(*)",
				Alias:      "total",
			}).
				WithTimescope(query.Start, query.End).
				Limit(1, 0).
				UseDatabase(td.DB).
				FromTables(p.TableName()).Exec(ctx); err == nil {
				r.log.Infof("ListControllerProperties count for sn %s  with sql %s cost %d", query.Sn, countRet.SQL, countRet.Cost)
				if len(countRet.Data) == 1 {
					v, ok := countRet.Data[0]["total"].(float64)
					if ok {
						total = int64(v)
					}
				}
			} else {
				r.log.Errorf("ListControllerProperties count for sn %s times %s to %s failed %v", query.Sn, query.Start, query.End, err)
				return 0, nil, biz.NewInternalError("ListControllerProperties.count", nil)
			}
		}
	}
	limit := int(query.Size)
	offset := int((query.Page - 1) * query.Size)
	if limit == 0 {
		return total, nil, nil
	}
	if ret, err := r.data.tdClient.NewSelect().SelectAll().SelectColumn("sn", "").
		WithTimescope(query.Start, query.End).
		Limit(limit, offset).
		OrderBy(order, orderColumn).
		UseDatabase(td.DB).
		FromTables(p.TableName()).
		Exec(ctx); err == nil {
		r.log.Infof("ListControllerProperties query for sn %s  with sql %s cost %d", query.Sn, ret.SQL, ret.Cost)
		return total, lo.Map(ret.Data, func(raw tdclient.Row, _ int) *biz.RemoteControllerProperties {
			tp := &td.RemoteControllerProperties{}
			tp.FromTDResult(raw)
			return tp.ToBiz()
		}), nil
	} else {
		r.log.Errorf("ListControllerProperties query for sn %s times %s to %s failed %v", query.Sn, query.Start, query.End, err)
		return 0, nil, biz.NewInternalError("ListControllerProperties.query", map[string]string{
			"sn":    query.Sn,
			"start": query.Start.String(),
			"end":   query.End.String(),
		})
	}
}
