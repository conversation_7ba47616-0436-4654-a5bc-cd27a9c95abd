package data

import (
	"context"
	stdJson "encoding/json"

	"fmt"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/snownd/cake"
	"github.com/tidwall/conv"
	"github.com/tidwall/gjson"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"
	"gitlab.sensoro.com/skai/skai/internal/data/td"
	"gitlab.sensoro.com/skai/stream-sidecar/pkg/acl"
	"gitlab.sensoro.com/skai/stream-sidecar/pkg/stream"
	orm "gorm.io/gorm"
)

const liveDefaultApp = "live"

type liveRepo struct {
	log                *log.Helper
	signer             Signer
	tsMS               stream.TSMetaStore
	tsFS               stream.TSFileStore
	data               *Data
	c                  *conf.Data
	pubMediaHost       string
	pubMediaScheme     string
	privateMediaHost   string
	privateMediaScheme string

	chClient *client.ChannelManagerClient
}

type Signer interface {
	Sign(rawURL string, ttl time.Duration) (string, error)
}

type signer struct {
	s *acl.UrlSigner
}

func (s *signer) Sign(rawURL string, ttl time.Duration) (string, error) {
	u, err := url.Parse(rawURL)
	if err != nil {
		return "", err
	}
	schema := u.Scheme
	path := u.Path
	// 特殊处理 flv播放回调的schema是rtmp
	if strings.HasSuffix(path, ".live.flv") {
		schema = "rtmp"
		path = strings.TrimSuffix(path, ".live.flv")
	}
	value := fmt.Sprintf("%s://%s%s", schema, u.Host, path)
	signedURL, err := s.s.Sign(value, ttl)
	if err != nil {
		return "", err
	}
	if schema == "rtmp" && strings.HasPrefix(rawURL, "http") {
		u, err = url.Parse(signedURL)
		if err != nil {
			return "", err
		}
		return rawURL + "?" + u.RawQuery, nil
	}
	return signedURL, nil
}

func NewSigner(c *conf.Data, logger log.Logger) (Signer, error) {
	s, err := acl.NewUrlSigner(acl.TokenSignatureAlgoBlake2b, 1*time.Hour, []byte(c.Media.AdminToken))
	if err != nil {
		logger.Log(log.LevelFatal, "module", "urlSigner", "err", err)
		return nil, err
	}
	return &signer{s}, nil
}

func NewTSMetaStore(data *Data, logger log.Logger) (stream.TSMetaStore, error) {
	ms, err := stream.NewTDMetaStorage(td.DB, "sk_ts", data.tdClient)
	if err != nil {
		logger.Log(log.LevelFatal, "module", "TDMetaStorage", "err", err)
		return nil, err
	}
	return ms, nil
}

func NewLiveRepo(logger log.Logger, data *Data, c *conf.Data, sc *aws.Config, s Signer, ms stream.TSMetaStore) (biz.LiveRepo, error) {
	opts := []stream.S3FileStorageOption{
		stream.WithS3Bucket(c.S3.BucketName),
		stream.WithPublicEndpoint(c.S3.PublicEndpoint),
		stream.WithS3Config(sc),
	}
	if !useVirtualHost(c.S3.EndpointUrl) {
		opts = append(opts, stream.WithPathStyle())
	}
	logger.Log(log.LevelInfo, "module", "S3FileStorage", "endpoint", c.S3.PublicEndpoint)
	fs, err := stream.NewS3FileStorage(opts...)
	if err != nil {
		logger.Log(log.LevelFatal, "module", "S3FileStorage", "err", err)
		return nil, err
	}
	var pubScheme, pubHost, privateHost, privateScheme string
	if v, err := url.Parse(c.Media.PublicUrl); err == nil {
		pubScheme = v.Scheme
		if v.Scheme != "" {
			pubHost = v.Host
		} else {
			pubHost = c.Media.PublicUrl
		}
	} else {
		return nil, errors.New(500, "", "config.media.PublicUrl is invalid")
	}
	if v, err := url.Parse(c.Media.Url); err == nil {
		privateScheme = v.Scheme
		if v.Scheme != "" {
			privateHost = v.Host
		} else {
			privateHost = c.Media.Url
		}
	} else {
		return nil, errors.New(500, "", "config.media.PrivateUrl is invalid")
	}
	if strings.ToLower(os.Getenv("RUN_ENV")) != "edge" {
		ns := os.Getenv("ANTMAN_DEPLOY_K8S_NS")
		if ns != "" {
			if !strings.Contains(privateHost, ns) {
				privateHost = fmt.Sprintf("%s.%s", privateHost, ns)
			}
		}
	}
	return &liveRepo{
		log:                log.NewHelper(logger),
		signer:             s,
		tsMS:               ms,
		tsFS:               fs,
		data:               data,
		c:                  c,
		pubMediaHost:       pubHost,
		pubMediaScheme:     pubScheme,
		privateMediaHost:   privateHost,
		privateMediaScheme: privateScheme,
		chClient:           data.chClient,
	}, nil
}

func (r *liveRepo) GetLiveHistoryVideos(ctx context.Context, req *biz.LiveHistoryQuery) (*biz.PlayList, error) {
	now := time.Now()
	tss, err := r.tsMS.ListTsMeta(ctx, &stream.ListTsQuery{
		Stream: stream.StreamDescriptor{
			StreamId: req.Live.Key,
			App:      liveDefaultApp,
		},
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	})
	if err != nil {
		r.log.WithContext(ctx).Errorf("GetLiveHistoryVideo queryMeta %d failed %+v", req.Live.Id, err)
		return nil, biz.NewInternalError("GetLiveHistoryVideo.meta", map[string]string{
			"stream":    req.Live.Key,
			"id":        conv.Itoa(req.Live.Id),
			"timescope": fmt.Sprintf("%s-%s", req.StartTime, req.EndTime),
		})
	}
	if err = r.tsFS.ListTsFiles(ctx, tss); err != nil {
		return nil, biz.NewInternalError("GetLiveHistoryVideo.file", nil)
	}
	m3u8Data, err := stream.NewM3U8Play(tss)
	if err != nil {
		return nil, biz.NewInternalError("GetLiveHistoryVideo.playlist", nil)
	}

	l := &biz.PlayList{
		Name: fmt.Sprintf("%d_%d-%d.m3u8", req.Live.Id, req.StartTime.Unix(), req.EndTime.Unix()),
		Duration: lo.Reduce(tss, func(agg time.Duration, it *biz.TransportStream, _ int) time.Duration {
			return agg + time.Duration(it.Duration)*time.Second
		}, 0),
		TSs:         tss,
		Data:        m3u8Data,
		Createdtime: now,
	}
	return l, nil
}

func (r *liveRepo) MakeValidPlayURL(ctx context.Context, urlValue string, ttl time.Duration) (string, error) {
	return r.signer.Sign(urlValue, ttl)
}

func (r *liveRepo) getLive(ctx context.Context, id int64) (*gorm.Media, error) {
	l := &gorm.Media{}
	if err := r.data.NewDBQuery(ctx).Where("id =? and type = ?", id, int(biz.MediaTypeLive)).Take(l).Error; err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return nil, biz.NewNotFoundError("live", "id", conv.Itoa(id))
		}
		return nil, biz.NewInternalError("GetLive", map[string]string{
			"id": conv.Itoa(id),
		})
	}
	return l, nil
}

func (r *liveRepo) GetLive(ctx context.Context, id int64) (*biz.Media, error) {
	l, err := r.getLive(ctx, id)
	if err != nil {
		return nil, err
	}
	return l.ToBiz(), nil
}

func (r *liveRepo) GetLiveProtocolPlayUrl(ctx context.Context, live *biz.Media, protocol biz.MediaProtocol, private bool) (string, error) {
	scheme, suffix := "", ""
	var port int32
	switch protocol {
	case biz.MediaProtocolHTTP:
		suffix = ".live.flv"
		scheme = lo.Ternary(private, r.privateMediaScheme, r.pubMediaScheme)
		port = lo.Ternary(private, 80, r.data.conf.Media.HttpPort)
	case biz.MediaProtocolRTMP:
		scheme = "rtmp"
		port = lo.Ternary(private, 1935, r.data.conf.Media.RtmpPort)
	case biz.MediaProtocolRTSP:
		scheme = "rtsp"
		port = lo.Ternary(private, 554, r.data.conf.Media.RtspPort)
	default:
		return "", biz.NewBadRequestError("Unsupport streaming media protocol", nil)
	}
	host := lo.Ternary(private, r.privateMediaHost, r.pubMediaHost)
	u, err := r.signer.Sign(fmt.Sprintf("%s://%s:%d/live/%s%s", scheme, host, port, live.Key, suffix), 24*time.Hour)
	if err != nil {
		return "", biz.NewInternalError("Get live protocol playUrl signUrl error", nil)
	}
	return u, nil
}

func (r *liveRepo) ListChannels(ctx context.Context, query *biz.CameraChannelListQuery) (int64, []*biz.CameraChannel, error) {
	if r.chClient == nil {
		return 0, nil, nil
	}
	var search *string
	if query.Search != "" {
		search = &query.Search
	}
	ret, err := r.chClient.GetProjectGrantedChannels(ctx, &client.ProjectGrantedChannelsDTO{
		ProjectId: conv.Itoa(query.ProjectId),
		Body: &client.ProjectGrantedChannels{
			Page:      int(query.Page),
			Size:      int(query.Size),
			GrantType: lo.ToPtr(client.GrantTypeMerchant),
			Search:    search,
			TenantId:  query.TenantId,
			Ids:       query.Ids,
		},
	})
	if err != nil {
		var rpcErr cake.RequestError
		if errors.As(err, &rpcErr) {
			r.log.WithContext(ctx).Errorf("ListChannels rpc error code %d res %s", rpcErr.StatusCode(), rpcErr.Body())
			return 0, nil, errors.New(rpcErr.StatusCode(), "ListChannels.rpc", gjson.GetBytes(rpcErr.Body(), "message").String())
		}
		return 0, nil, biz.NewInternalError("ListChannels.rpc", nil)
	}
	return int64(ret.Data.Total), ret.Data.List, nil
}

func (r *liveRepo) GetChannelDetail(ctx context.Context, query *biz.CameraChannelDetailQuery) (*biz.CameraChannel, error) {
	if r.chClient == nil {
		return nil, biz.NewNotFoundError("Channel", "id", conv.Itoa(query.ChannelId))
	}
	ret, err := r.chClient.GetChannelDetail(ctx, &client.ChannelDetailDTO{
		ChannelId:  conv.Itoa(query.ChannelId),
		TenantId:   query.TenantId,
		PreferH265: true,
	})
	if err != nil {
		var rpcErr cake.RequestError
		if errors.As(err, &rpcErr) {
			r.log.WithContext(ctx).Errorf("GetChannelDetail rpc error code %d res %s", rpcErr.StatusCode(), rpcErr.Body())
			return nil, errors.New(rpcErr.StatusCode(), "GetChannelDetail.rpc", gjson.GetBytes(rpcErr.Body(), "message").String())
		}
		return nil, biz.NewInternalError("GetChannelDetail.rpc", nil)
	}
	if ret.Data.IsDeleted {
		return nil, biz.NewNotFoundError("Channel", "id", conv.Itoa(query.ChannelId))
	}
	return ret.Data, nil
}

func (r *liveRepo) UpdateLiveCamera(ctx context.Context, id int64, position int32) error {
	l, err := r.GetLive(ctx, id)
	if err != nil {
		return err
	}
	l.SetLivePosition(position)
	v, _ := stdJson.Marshal(map[string]any{
		"other": l.Extra,
	})
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).Where("id=?", id).Update("extra", orm.Expr("extra||?", v)).Error; err != nil {
		return biz.NewInternalError("UpdateLiveCamera.db", nil)
	}
	return nil
}
