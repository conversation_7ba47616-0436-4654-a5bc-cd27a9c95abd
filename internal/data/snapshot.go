package data

import (
	"context"

	"gitlab.sensoro.com/skai/skai/internal/biz"

	taskV1 "gitlab.sensoro.com/lins/lins-snapshoter/api/snaptasks/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tidwall/conv"
)

type SnapshotRepo struct {
	log    *log.Helper
	client SnaptaskClient
}

func NewSnapshotRepo(logger log.Logger, data *Data) biz.SnapshotRepo {
	return &SnapshotRepo{
		client: data.stClient,
		log:    log.NewHelper(logger),
	}
}

func (s *SnapshotRepo) Start(ctx context.Context, task *biz.SnapshotTask) error {
	if s.client == nil {
		return nil
	}
	res, err := s.client.CreateTask(ctx, &taskV1.SnapshotTask{
		Id:       conv.Itoa(task.Id),
		Interval: task.Interval,
		Stream: &taskV1.Stream{
			Id:     conv.Itoa(task.Stream.Id),
			Url:    task.Stream.Url,
			Source: task.Stream.Source,
		},
	})
	if err != nil {
		s.log.Warnf("call snapshoter CreateTask with error message: %d, %s", err.Error())
		return err
	}
	s.log.Debugf("create snapshot task %s detail %+v", task.Id, res.Data)
	return nil
}

func (s *SnapshotRepo) Stop(ctx context.Context, taskId int64) error {
	_, err := s.client.DeleteTask(ctx, &taskV1.DeleteTaskRequest{Id: conv.Itoa(taskId)})
	if err != nil {
		s.log.Warnf("call snapshoter DeleteTask with error message: %d, %s", err.Error())
		return err
	}
	s.log.Debugf("delete snapshot task success: %d", taskId)
	return nil
}
