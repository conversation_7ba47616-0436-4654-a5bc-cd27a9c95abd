package thumbnail

import (
	"context"
	"runtime"

	"github.com/davidbyttow/govips/v2/vips"
	"github.com/samber/lo"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type Job struct {
	Content []byte
	Result  chan<- *Result
}

type Result struct {
	Content []byte
	Format  string
	Err     error
}

type Worker struct {
	ctx   context.Context
	input chan *Job
}

func NewWorkerPool(ctx context.Context, input chan *Job, workers int) []*Worker {
	p := make([]*Worker, workers)
	for i := 0; i < workers; i++ {
		p[i] = NewWorker(ctx, input)
	}
	return p
}

func NewWorker(ctx context.Context, input chan *Job) *Worker {
	w := &Worker{
		ctx:   ctx,
		input: input,
	}
	go w.run()
	return w
}

func (w *Worker) run() {
	runtime.LockOSThread()
	defer runtime.UnlockOSThread()
	for {
		select {
		case job := <-w.input:
			job.Result <- w.process(job.Content)
		case <-w.ctx.Done():
			return
		}
	}
}

func (w *Worker) process(content []byte) *Result {
	// defer vips.ClearCache()
	img, err := vips.NewImageFromBuffer(content)
	if err != nil {
		return &Result{
			Err: biz.NewInternalError("GenImageThumbnail.read", map[string]string{"detail": err.Error()}),
		}
	}
	defer img.Close()
	m := img.Metadata()
	if err = img.Thumbnail(lo.Max([]int{m.Width / 10, 384}), lo.Max([]int{m.Height / 10, 204}), vips.InterestingCentre); err != nil {
		return &Result{
			Err: biz.NewInternalError("GenImageThumbnail.Thumbnail", map[string]string{"detail": err.Error()}),
		}
	}
	e, tm, err := img.ExportWebp(&vips.WebpExportParams{
		StripMetadata:   true,
		Quality:         60,
		Lossless:        false,
		NearLossless:    false,
		ReductionEffort: 4,
	})
	if err != nil {
		return &Result{
			Err: biz.NewInternalError("GenImageThumbnail.ExportWebp", map[string]string{"detail": err.Error()}),
		}
	}
	o := make([]byte, len(e))
	copy(o, e)
	return &Result{
		Content: o,
		Format:  tm.Format.FileExt(),
	}
}
