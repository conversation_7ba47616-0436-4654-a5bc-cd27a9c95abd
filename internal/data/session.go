package data

import (
	"context"

	"gitlab.sensoro.com/skai/skai/internal/biz"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
)

type sessionRepo struct {
	client *client.SessionClient
	log    *log.Helper
}

func NewSessionRepo(logger log.Logger, data *Data) biz.SessionRepo {
	return &sessionRepo{
		client: data.session,
		log:    log.NewHelper(logger),
	}
}

func (r *sessionRepo) Logout(ctx context.Context) error {
	data, err := r.client.Logout(ctx, client.LogoutDTO{})
	if err != nil {
		return err
	}
	if data.Code != 0 {
		r.log.Warnf("call session service with logout failed, code: %d, message: %s", data.Code, data.Message)
		return errors.New(400, "call session service with logout failed", data.Message)
	}
	return nil
}

func (r *sessionRepo) LoginByMobile(ctx context.Context, dto biz.MobileLoginDto) (*biz.Session, error) {
	data, err := r.client.LoginByMobile(ctx, dto)
	if err != nil {
		return nil, err
	}
	if data.Code != 0 {
		r.log.Warnf("call session service with login by mobile failed, code: %d, message: %s", data.Code, data.Message)
		return nil, errors.New(400, "call session service with login by mobile failed", data.Message)
	}
	return &data.Data, nil
}

func (r *sessionRepo) LoginByUsername(ctx context.Context, dto biz.UsernameLoginDto) (*biz.Session, error) {
	data, err := r.client.LoginByUsername(ctx, dto)
	if err != nil {
		return nil, err
	}
	if data.Code != 0 {
		r.log.Warnf("call session service with login by username failed, code: %d, message: %s", data.Code, data.Message)
		return nil, errors.New(400, "call session service with login by username failed", data.Message)
	}
	return &data.Data, nil
}
