package data

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	urlUtil "net/url"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/samber/lo"

	awshttp "github.com/aws/aws-sdk-go-v2/aws/transport/http"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/data/thumbnail"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	sts20150401 "github.com/alibabacloud-go/sts-20150401/v2/client"
)

type stsClient interface {
	NewStorageCredential(ctx context.Context, opt biz.SessionTokenOption) (biz.StorageCredential, error)
}

type awsSTSClient struct {
	s3Client  *s3.Client
	stsClient *sts.Client
	dc        *conf.Data
}

func (c *awsSTSClient) NewStorageCredential(ctx context.Context, opt biz.SessionTokenOption) (biz.StorageCredential, error) {
	ret, err := c.stsClient.AssumeRole(ctx, &sts.AssumeRoleInput{
		DurationSeconds: lo.ToPtr(int32(opt.TTL.Seconds())),
		RoleArn:         lo.ToPtr("arn:aws:s3:::*"),
		RoleSessionName: lo.ToPtr(utilities.MustNewUUIDV1()),
	})
	bucket := opt.Bucket
	if bucket == "" {
		bucket = c.dc.S3.BucketName
	}
	if err == nil {
		return biz.StorageCredential{
			AccessKey:    *ret.Credentials.AccessKeyId,
			AccessSecret: *ret.Credentials.SecretAccessKey,
			SessionToken: *ret.Credentials.SessionToken,
			Expire:       ret.Credentials.Expiration.Unix() - time.Now().Unix(),
			Bucket:       bucket,
			Endpoint:     c.dc.S3.PublicEndpoint,
			Provider:     "minio",
		}, nil
	}
	return biz.StorageCredential{}, err
}

type aliSTSClient struct {
	dc     *conf.Data
	region string
	client *sts20150401.Client
}

func (c *aliSTSClient) NewStorageCredential(ctx context.Context, opt biz.SessionTokenOption) (biz.StorageCredential, error) {
	exprie := int64(opt.TTL.Seconds())
	ts, err := c.client.AssumeRole(&sts20150401.AssumeRoleRequest{
		DurationSeconds: lo.ToPtr(lo.Min([]int64{exprie, 3599})),
		RoleArn:         lo.ToPtr(c.dc.S3.Arn),
		//RoleArn:         lo.ToPtr("acs:ram:*:1312983132946738:role/skai-devtest"),
		RoleSessionName: lo.ToPtr(utilities.MustNewUUIDV1()),
	})
	if err != nil {
		return biz.StorageCredential{}, biz.NewInternalError("NewStorageCredential.ali.err", map[string]string{"detail": err.Error()})
	}
	bucket := opt.Bucket
	if bucket == "" {
		bucket = c.dc.S3.BucketName
	}
	ret := ts.Body

	et, err := time.Parse(time.RFC3339, *ret.Credentials.Expiration)
	if err == nil {
		exprie = int64(time.Until(et).Seconds())
	}
	return biz.StorageCredential{
		AccessKey:    *ret.Credentials.AccessKeyId,
		AccessSecret: *ret.Credentials.AccessKeySecret,
		SessionToken: *ret.Credentials.SecurityToken,
		Expire:       exprie,
		Bucket:       bucket,
		Endpoint:     c.dc.S3.PublicEndpoint,
		Provider:     "ali",
		Region:       c.region,
	}, nil
}

func newSTSClient(c *conf.Data, sc *s3.Client, s3Conf aws.Config) (stsClient, error) {
	if useVirtualHost(c.S3.EndpointUrl) {
		aliConf := &openapi.Config{
			AccessKeyId:     &c.S3.AccessKey,
			AccessKeySecret: &c.S3.SecretKey,
		}
		ep, _ := urlUtil.Parse(c.S3.PublicEndpoint)
		ds := strings.Split(ep.Hostname(), ".")
		if len(ds) == 0 {
			return nil, biz.NewBadRequestError("newSTSClient.invalidEndpointUrl", map[string]string{"ep": c.S3.EndpointUrl})
		}
		ossRegion, ok := strings.CutPrefix(ds[0], "oss-")
		if !ok {
			return nil, biz.NewBadRequestError("newSTSClient.invalidAliEndpointUrl", map[string]string{"ep": c.S3.EndpointUrl})
		}
		// Endpoint 请参考 https://api.aliyun.com/product/Sts
		aliConf.Endpoint = lo.ToPtr(fmt.Sprintf("sts.%s.%s.com", ossRegion, ds[1]))
		stsClient, err := sts20150401.NewClient(aliConf)
		if err != nil {
			return nil, err
		}
		return &aliSTSClient{
			dc:     c,
			region: ossRegion,
			client: stsClient,
		}, nil
	}

	return &awsSTSClient{
		s3Client:  sc,
		stsClient: sts.NewFromConfig(s3Conf, sts.WithEndpointResolver(newSTSEndpointResolver(c.S3.EndpointUrl))),
		dc:        c,
	}, nil
}

var s3HttpClient = newHttpClient()

func NewS3Config(c *conf.Data) (*aws.Config, error) {
	cfg, err := config.LoadDefaultConfig(
		context.Background(),
		config.WithEndpointResolverWithOptions(newEndpointResolver(c.S3.EndpointUrl, "")),
		config.WithCredentialsProvider(newCredentialsProvider(c.S3)),
		config.WithHTTPClient(s3HttpClient),
		// config.WithSharedConfigProfile("skai"),
		config.WithRetryMaxAttempts(1),
	)
	if err != nil {
		return nil, err
	}
	return &cfg, nil
}

func NewS3Client(c *aws.Config) (*s3.Client, error) {
	client := s3.NewFromConfig(*c)
	return client, nil
}

func NewS3PresignClient(c *conf.Data, client *s3.Client) *s3.PresignClient {
	return s3.NewPresignClient(client,
		s3.WithPresignClientFromClientOptions(s3.WithEndpointResolver(newPresignEndpointResolver(c.S3.PublicEndpoint, ""))),
	)
}

type simpleStorageRepo struct {
	log                    *log.Helper
	data                   *Data
	s3Client               *s3.Client
	stsClient              stsClient
	presignClient          *s3.PresignClient
	internalPresignClient  *s3.PresignClient
	secondaryPresignClient *s3.PresignClient
	secondaryS3Client      *s3.Client
	hostImmutable          bool
}

func NewSimpleStorageRepo(
	logger log.Logger,
	data *Data,
	s3Conf *aws.Config,
	s3Client *s3.Client,
	presignClient *s3.PresignClient,
) (biz.SimpleStorageRepo, error) {
	stsClient, err := newSTSClient(data.conf, s3Client, *s3Conf)
	if err != nil {
		return nil, err
	}
	var secondaryPresignClient *s3.PresignClient
	var secondaryS3Client *s3.Client
	if data.conf.SecondaryS3 != nil {
		sc, err := config.LoadDefaultConfig(
			context.Background(),
			config.WithEndpointResolverWithOptions(newEndpointResolver(data.conf.SecondaryS3.EndpointUrl, data.conf.SecondaryS3.Region)),
			config.WithCredentialsProvider(newCredentialsProvider(data.conf.SecondaryS3)),
			config.WithHTTPClient(s3HttpClient),
			// config.WithSharedConfigProfile("skai"),
			config.WithRetryMaxAttempts(1),
		)
		if err != nil {
			return nil, err
		}
		secondaryS3Client = s3.NewFromConfig(sc)
		secondaryPresignClient = s3.NewPresignClient(secondaryS3Client,
			s3.WithPresignClientFromClientOptions(s3.WithEndpointResolver(newPresignEndpointResolver(data.conf.SecondaryS3.PublicEndpoint, data.conf.SecondaryS3.Region))),
		)
	}
	return &simpleStorageRepo{
		log:                    log.NewHelper(logger),
		data:                   data,
		s3Client:               s3Client,
		stsClient:              stsClient,
		presignClient:          presignClient,
		internalPresignClient:  s3.NewPresignClient(s3Client),
		secondaryPresignClient: secondaryPresignClient,
		secondaryS3Client:      secondaryS3Client,
		hostImmutable:          !useVirtualHost(data.conf.S3.EndpointUrl),
	}, nil
}

func (r *simpleStorageRepo) getBucket(obj *biz.StorageObject) *string {
	bucket := obj.Bucket
	if bucket == "" {
		bucket = r.data.conf.S3.BucketName
	}
	return &bucket
}

func (r *simpleStorageRepo) pickClient(obj *biz.StorageObject) *s3.Client {
	if r.data.conf.SecondaryS3 != nil && r.data.conf.SecondaryS3.BucketName != "" && obj.Bucket == r.data.conf.SecondaryS3.BucketName {
		return r.secondaryS3Client
	}
	return r.s3Client
}
func (r *simpleStorageRepo) pickPresignClient(obj *biz.StorageObject) *s3.PresignClient {
	if r.data.conf.SecondaryS3 != nil && r.data.conf.SecondaryS3.BucketName != "" && obj.Bucket == r.data.conf.SecondaryS3.BucketName {
		return r.secondaryPresignClient
	}
	return r.presignClient
}

func (r *simpleStorageRepo) GetSessionToken(ctx context.Context, opt biz.SessionTokenOption) (biz.StorageCredential, error) {
	// c, err := r.stsClient.GetSessionToken(ctx, &sts.GetSessionTokenInput{
	// 	DurationSeconds: lo.ToPtr(int32(opt.TTL.Seconds())),
	// })
	return r.stsClient.NewStorageCredential(ctx, opt)
}
func (r *simpleStorageRepo) PutObject(ctx context.Context, obj *biz.StorageObject) (*biz.StorageObjectPutResult, error) {
	input := &s3.PutObjectInput{
		Bucket: r.getBucket(obj),
		Key:    lo.ToPtr(obj.Key),
		Body:   obj.Data,
	}
	if obj.Meta != nil {
		input.ContentLength = obj.Meta.ContentLength
		input.ContentType = &obj.Meta.ContentType
		if input.ContentLength >= 1024*1024*32 {
			return r.putObjectMultiPart(ctx, obj)
		}
	}
	_, err := r.pickClient(obj).PutObject(ctx, input, s3.WithAPIOptions(
		v4.SwapComputePayloadSHA256ForUnsignedPayloadMiddleware,
	))
	if err != nil {
		r.log.Errorf("put object %s error %v, call stack: %s", obj.Key, err, utilities.CollectStack())
		return nil, biz.NewInternalError("PutObject.s3Err", nil)
	}
	r.log.Info("put a object: ", obj.Key, "meta", obj.Meta)
	return &biz.StorageObjectPutResult{
		Bucket: obj.Bucket,
		Key:    obj.Key,
	}, nil
}

func (r *simpleStorageRepo) putObjectMultiPart(ctx context.Context, obj *biz.StorageObject) (*biz.StorageObjectPutResult, error) {
	bucket := r.getBucket(obj)
	key := lo.ToPtr(obj.Key)
	mut, err := r.pickClient(obj).CreateMultipartUpload(ctx, &s3.CreateMultipartUploadInput{
		Bucket:      bucket,
		Key:         key,
		ContentType: &obj.Meta.ContentType,
	})
	if err != nil {
		r.log.Errorf("create multipart upload %s error %v, call stack: %s", obj.Key, err, utilities.CollectStack())
		return nil, biz.NewInternalError("putObjectMultiPart.s3Err", nil)
	}
	buf := make([]byte, 1024*1024*16)
	var partNum int32 = 1
	parts := make([]types.CompletedPart, 0, obj.Meta.ContentLength/int64(len(buf))+1)
	for {
		n, err := io.ReadAtLeast(obj.Data, buf, 1024*1024*8)
		if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
			r.log.Errorf("read data error %v, call stack: %s", err, utilities.CollectStack())
			r.pickClient(obj).AbortMultipartUpload(ctx, &s3.AbortMultipartUploadInput{
				Bucket:   bucket,
				Key:      key,
				UploadId: mut.UploadId,
			})
			return nil, biz.NewInternalError("putObjectMultiPart.readData", nil)
		}
		if n == 0 {
			break
		}
		upr, err := r.pickClient(obj).UploadPart(ctx, &s3.UploadPartInput{
			Bucket:        bucket,
			Key:           key,
			UploadId:      mut.UploadId,
			PartNumber:    int32(partNum),
			Body:          bytes.NewReader(buf[:n]),
			ContentLength: int64(n),
		})
		if err != nil {
			r.pickClient(obj).AbortMultipartUpload(ctx, &s3.AbortMultipartUploadInput{
				Bucket:   bucket,
				Key:      key,
				UploadId: mut.UploadId,
			})
			r.log.Errorf("upload part %d error %v, call stack: %s", partNum, err, utilities.CollectStack())
			return nil, biz.NewInternalError("putObjectMultiPart.s3Err", nil)
		}
		r.log.Debug("upload %s part %d size %d KB success", obj.Key, partNum, n>>10)
		parts = append(parts, types.CompletedPart{
			PartNumber: int32(partNum),
			ETag:       upr.ETag,
		})
		partNum++
	}
	_, err = r.pickClient(obj).CompleteMultipartUpload(ctx, &s3.CompleteMultipartUploadInput{
		Bucket:   bucket,
		Key:      key,
		UploadId: mut.UploadId,
		MultipartUpload: &types.CompletedMultipartUpload{
			Parts: parts,
		},
	})
	if err != nil {
		r.pickClient(obj).AbortMultipartUpload(ctx, &s3.AbortMultipartUploadInput{
			Bucket:   bucket,
			Key:      key,
			UploadId: mut.UploadId,
		})
		r.log.Errorf("complete multipart upload %s error %v, call stack: %s", obj.Key, err, utilities.CollectStack())
		return nil, biz.NewInternalError("putObjectMultiPart.s3Err", nil)
	}
	return &biz.StorageObjectPutResult{
		Bucket: obj.Bucket,
		Key:    obj.Key,
	}, nil
}

func (r *simpleStorageRepo) GetObject(ctx context.Context, obj *biz.StorageObject) (*biz.StorageObjectMeta, io.ReadCloser, error) {
	key := obj.Key
	if url := obj.URL; url != "" {
		v, err := r.exactKeyFromURL(url)
		if err == nil {
			key = v
		}
	}
	res, err := r.pickClient(obj).GetObject(ctx, &s3.GetObjectInput{
		Bucket: r.getBucket(obj),
		Key:    &key,
	})
	if err != nil {
		return nil, nil, err
	}
	return &biz.StorageObjectMeta{
		ContentLength: res.ContentLength,
		ContentType:   lo.FromPtr(res.ContentType),
		Signature:     lo.FromPtr(res.ChecksumSHA256),
	}, res.Body, nil
}

func (r *simpleStorageRepo) GetSignedObjectAddr(ctx context.Context, obj *biz.StorageObject, ttl time.Duration) (*biz.PreSignedObject, error) {
	req, err := r.pickPresignClient(obj).PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket:                     r.getBucket(obj),
		Key:                        &obj.Key,
		ChecksumMode:               types.ChecksumModeEnabled,
		ResponseContentDisposition: aws.String("attachment; filename=" + obj.GetObjectName()),
	}, s3.WithPresignExpires(ttl))
	if err != nil {
		return nil, err
	}

	// r.log.Debugf("get object %s preSigned url: %s", obj.Key, req.URL)
	return &biz.PreSignedObject{
		ObjectUrl: req.URL,
		TTL:       ttl,
	}, nil
}

func (r *simpleStorageRepo) GetInternalSignedObjectAddr(ctx context.Context, obj *biz.StorageObject, ttl time.Duration) (*biz.PreSignedObject, error) {
	key := obj.Key
	if url := obj.URL; url != "" {
		v, err := r.exactKeyFromURL(url)
		if err == nil {
			key = v
		} else {
			return &biz.PreSignedObject{
				ObjectUrl: url,
				TTL:       ttl,
			}, nil
		}
	}
	req, err := r.internalPresignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket:       r.getBucket(obj),
		Key:          lo.ToPtr(key),
		ChecksumMode: types.ChecksumModeEnabled,
	}, s3.WithPresignExpires(ttl))
	if err != nil {
		return nil, err
	}

	// r.log.Debugf("get object %s preSigned url: %s", obj.Key, req.URL)
	return &biz.PreSignedObject{
		ObjectUrl: req.URL,
		TTL:       ttl,
	}, nil
}
func (r *simpleStorageRepo) RefreshSignedUrl(ctx context.Context, u string, ttl time.Duration) (string, error) {
	key, err := r.exactKeyFromURL(u)
	if err != nil {
		return "", err
	}
	ret, err := r.GetSignedObjectAddr(ctx, &biz.StorageObject{
		Bucket: r.data.conf.S3.BucketName,
		Key:    key,
	}, ttl)
	if err != nil {
		return "", err
	}
	return ret.ObjectUrl, nil
}

func (r *simpleStorageRepo) exactKeyFromURL(u string) (string, error) {
	parsed, err := urlUtil.Parse(u)
	if err != nil {
		return "", err
	}
	if r.hostImmutable {
		return strings.TrimPrefix(parsed.Path, "/"+r.data.conf.S3.BucketName), nil
	} else {
		return strings.TrimPrefix(parsed.Path, "/"), nil
	}
}

func (r *simpleStorageRepo) newThumbnailKey(o *biz.StorageObject, format string) string {
	name := o.Key
	if i := strings.LastIndexByte(name, '.'); i > 0 {
		ext := name[i:]
		name = strings.TrimSuffix(o.Key, ext)
	}
	return fmt.Sprintf("%s-thumbnail%s", name, format)
}

func (r *simpleStorageRepo) GenImageThumbnail(ctx context.Context, originalImage *biz.StorageObject) (*biz.ImageThumbnailResult, error) {
	start := time.Now()
	dataStream := originalImage.Data
	if dataStream == nil {
		res, err := r.s3Client.GetObject(ctx, &s3.GetObjectInput{
			Bucket: r.getBucket(originalImage),
			Key:    &originalImage.Key,
		})
		if err != nil {
			r.log.WithContext(ctx).Errorf("GenImageThumbnail.getoriginal %s failed %v", originalImage.Key, err)
			return nil, biz.NewInternalError("GenImageThumbnail.GetObject", map[string]string{"key": originalImage.Key})
		}
		defer res.Body.Close()
		dataStream = res.Body
	}
	data, err := io.ReadAll(dataStream)
	if err != nil {
		r.log.WithContext(ctx).Errorf("GenImageThumbnail.read %s failed %v", originalImage.Key, err)
		return nil, biz.NewInternalError("GenImageThumbnail.read", map[string]string{"key": originalImage.Key})
	}
	downFinished := time.Now()
	rc := make(chan *thumbnail.Result)
	r.data.thumbnailJobChan <- &thumbnail.Job{
		Content: data,
		Result:  rc,
	}
	var ret *thumbnail.Result
	select {
	case ret = <-rc:
		if ret.Err != nil {
			return nil, biz.NewInternalError("GenImageThumbnail.ExportWebp", map[string]string{"key": originalImage.Key})
		}
	case <-ctx.Done():
		return nil, biz.NewInternalError("GenImageThumbnail.ExportWebp.timeout", map[string]string{"key": originalImage.Key})
	}
	o := ret.Content
	transFinished := time.Now()
	size := int64(len(o))
	tk := r.newThumbnailKey(originalImage, ret.Format)
	_, err = r.PutObject(ctx, &biz.StorageObject{
		Key:  tk,
		Data: bytes.NewBuffer(o),
		Meta: &biz.StorageObjectMeta{ContentLength: size, ContentType: "image/webp"},
	})
	if err != nil {
		return nil, err
	}
	end := time.Now()
	cost := end.Sub(start)
	r.log.WithContext(ctx).Infof("GenImageThumbnail cost total %s, tran %s", cost, transFinished.Sub(downFinished))
	return &biz.ImageThumbnailResult{
		Key:          tk,
		OriginKey:    originalImage.Key,
		Size:         len(o),
		OriginalSize: len(data),
		Cost:         cost,
	}, nil
}

func (r *simpleStorageRepo) CheckObjectIfExists(ctx context.Context, obj *biz.StorageObject) (bool, error) {
	_, err := r.pickClient(obj).HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: r.getBucket(obj),
		Key:    &obj.Key,
	})
	if err != nil {
		var re *awshttp.ResponseError
		if errors.As(err, &re) {
			if re.HTTPStatusCode() == 404 || re.HTTPStatusCode() == 403 {
				return false, nil
			}
		}
		r.log.WithContext(ctx).Errorf("failed to check object %s exists: %v", obj.Key, err)
		return false, biz.NewInternalError("CheckObjectIfExists", map[string]string{"key": obj.Key})
	}
	return true, nil
}

func (r *simpleStorageRepo) DeleteObject(ctx context.Context, obj *biz.StorageObject) error {
	_, err := r.s3Client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: r.getBucket(obj),
		Key:    &obj.Key,
	})
	if err != nil {
		r.log.WithContext(ctx).Errorf("DeleteObject %s failed %v", obj.Key, err)
		return biz.NewInternalError("DeleteObject", map[string]string{"key": obj.Key})
	}
	return nil
}

func (r *simpleStorageRepo) GetSecondaryStorageBucket() (string, bool) {
	if r.data.conf.SecondaryS3 != nil && r.data.conf.SecondaryS3.BucketName != "" {
		return r.data.conf.SecondaryS3.BucketName, true
	}
	return "", false
}

func useVirtualHost(url string) bool {
	if v, err := urlUtil.Parse(url); err == nil {
		return strings.Contains(v.Host, "aliyuncs") || strings.Contains(v.Host, "volces")
	}
	return false
}

func newEndpointResolver(url string, r string) aws.EndpointResolverWithOptionsFunc {
	hostImmutable := !useVirtualHost(url)
	return func(service, region string, options ...interface{}) (aws.Endpoint, error) {
		return aws.Endpoint{
			URL:               url,
			HostnameImmutable: hostImmutable,
			SigningRegion:     r,
		}, nil
	}
}

func newSTSEndpointResolver(url string) sts.EndpointResolverFunc {
	hostImmutable := !useVirtualHost(url)
	return func(region string, options sts.EndpointResolverOptions) (aws.Endpoint, error) {
		return aws.Endpoint{
			URL:               url,
			HostnameImmutable: hostImmutable,
		}, nil
	}
}

func newPresignEndpointResolver(url string, r string) s3.EndpointResolverFunc {
	hostImmutable := !useVirtualHost(url)
	return func(region string, options s3.EndpointResolverOptions) (aws.Endpoint, error) {
		return aws.Endpoint{
			URL:               url,
			HostnameImmutable: hostImmutable,
			SigningRegion:     r,
		}, nil
	}
}

func newCredentialsProvider(c *conf.Data_S3) aws.CredentialsProviderFunc {
	return func(ctx context.Context) (aws.Credentials, error) {
		return aws.Credentials{
			AccessKeyID:     c.AccessKey,
			SecretAccessKey: c.SecretKey,
		}, nil
	}
}

func newHttpClient() s3.HTTPClient {
	return &http.Client{
		Transport: createS3Transport(),
	}
}

func createS3Transport() *http.Transport {
	dialer := &net.Dialer{
		Timeout:   15 * time.Second,
		KeepAlive: 5 * time.Second,
		DualStack: true,
	}
	return &http.Transport{
		Proxy:                 http.ProxyFromEnvironment,
		DialContext:           dialer.DialContext,
		MaxIdleConns:          256,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		MaxIdleConnsPerHost:   64,
		MaxConnsPerHost:       64,
		DisableCompression:    false,
	}
}
