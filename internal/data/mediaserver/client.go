package mediaserver

import (
	"context"
	"io"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
)

type RecordFileQuery struct {
	Live *biz.Media
	Date time.Time
}

type LiveRecordRequest struct {
	Live *biz.Media
	Path string
}

type Client interface {
	StartRecordLive(ctx context.Context, req *LiveRecordRequest) error
	IfLiveRecording(ctx context.Context, live *biz.Media) (bool, error)
	ListRecordFile(ctx context.Context, query *RecordFileQuery) ([]string, error)
	GetRecordFileStream(ctx context.Context, req *LiveRecordRequest) (io.ReadCloser, *biz.VideoRecordMeta, error)
	DelRecordFile(ctx context.Context, query *RecordFileQuery) error
	TakeASnap(ctx context.Context, videoURL string) (io.ReadCloser, error)
}

func NewMediaClient(c *conf.Data, logger log.Logger) Client {
	return newZ<PERSON>lient(logger, c)
}
