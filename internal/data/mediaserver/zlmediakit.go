package mediaserver

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/pkg/types"
)

type zlmediakitClient struct {
	log *log.Helper
	cfg *conf.Data_Media
	hc  *resty.Client
}

type zlkReponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func newZLClient(logger log.Logger, c *conf.Data) *zlmediakitClient {
	return &zlmediakitClient{
		log: log.NewHelper(logger),
		cfg: c.Media,
		hc:  resty.New().SetTimeout(10 * time.Second),
	}
}

func (c *zlmediakitClient) newReqUrl(operation string) string {
	if port := c.cfg.RestPort; port != 0 && port != 80 {
		return fmt.Sprintf("http://%s:%d/index/api/%s", c.cfg.Url, port, operation)
	}
	return fmt.Sprintf("http://%s/index/api/%s", c.cfg.Url, operation)
}

func (c *zlmediakitClient) newBaseQueryForLive(l *biz.Media) map[string]string {
	return map[string]string{
		"secret": c.cfg.AdminToken,
		"vhost":  "__defaultVhost__",
		"app":    "live",
		"stream": l.Key,
	}
}

func (c *zlmediakitClient) StartRecordLive(ctx context.Context, req *LiveRecordRequest) error {
	params := c.newBaseQueryForLive(req.Live)
	// 1 for mp4, 0 for hls
	params["type"] = "1"
	params["max_second"] = "3600"
	if req.Path != "" {
		params["customized_path"] = req.Path
	}
	res, err := c.hc.R().SetContext(ctx).SetQueryParams(params).SetResult(&zlkReponse{}).Get(c.newReqUrl("startRecord"))
	if err != nil {
		c.log.Errorf("StartRecordLive for live %d with key %s rpc error: %v ", req.Live.Id, req.Live.Key, err)
		return biz.NewInternalError("zlmeiakitClient.StartRecordLive.rpcErr", nil)
	}
	if res.IsError() {
		return biz.NewBadRequestError("zlmeiakitClient.StartRecordLive.resErr", nil)
	}
	ret := res.Result().(*zlkReponse)
	if ret.Code != 0 {
		if ret.Code == -500 {
			return biz.NewNotFoundError("Live", "stream", req.Live.Key)
		}
		return biz.NewBadRequestError("zlmeiakitClient.StartRecordLive.resErr."+ret.Msg, nil)
	}
	c.log.Infof("StartRecordLive %d with id %s", req.Live.Id, req.Live.Key)
	return nil
}

type zlkStatusResponse struct {
	zlkReponse
	Status bool `json:"status"`
}

func (c *zlmediakitClient) IfLiveRecording(ctx context.Context, live *biz.Media) (bool, error) {
	params := c.newBaseQueryForLive(live)
	params["type"] = "1"
	res, err := c.hc.R().SetContext(ctx).
		SetQueryParams(params).
		SetResult(&zlkStatusResponse{}).
		Get(c.newReqUrl("isRecording"))
	if err != nil {
		c.log.Errorf("IfLiveRecording for live %d with key %s rpc error: %v ", live.Id, live.Key, err)
		return false, biz.NewInternalError("zlmeiakitClient.IfLiveRecording.rpcErr", nil)
	}
	if res.IsError() {
		return false, biz.NewBadRequestError("zlmeiakitClient.IfLiveRecording.resErr", map[string]string{
			"res": res.String(),
		})
	}
	ret := res.Result().(*zlkStatusResponse)
	if ret.Code != 0 {
		if ret.Code == -500 {
			return false, biz.NewNotFoundError("Live", "stream", live.Key)
		}
		return false, biz.NewBadRequestError("zlmeiakitClient.IfLiveRecording.resErr."+ret.Msg, nil)
	}
	return ret.Status, nil
}

type zlkMP4RecordFileReponse struct {
	zlkReponse
	Data struct {
		Paths    []string `json:"paths"`
		RootPath string   `json:"rootPath"`
	} `json:"data"`
}

func (c *zlmediakitClient) ListRecordFile(ctx context.Context, query *RecordFileQuery) ([]string, error) {
	params := c.newBaseQueryForLive(query.Live)
	period := ""
	if query.Date.Unix() > 0 {
		period = query.Date.In(biz.TimeLocation).Format("2006-01-02")
		params["period"] = period
	}
	live := query.Live
	res, err := c.hc.R().SetContext(ctx).
		SetQueryParams(params).
		SetResult(&zlkMP4RecordFileReponse{}).
		Get(c.newReqUrl("getMp4RecordFile"))
	if err != nil {
		c.log.Errorf("IfLiveRecording for live %d with key %s rpc error: %v ", live.Id, live.Key, err)
		return nil, biz.NewInternalError("zlmeiakitClient.ListRecordFile.rpcErr", nil)
	}
	if res.IsError() {
		return nil, biz.NewBadRequestError("zlmeiakitClient.ListRecordFile.resErr", nil)
	}
	ret := res.Result().(*zlkMP4RecordFileReponse)
	if ret.Code != 0 {
		return nil, biz.NewBadRequestError("zlmeiakitClient.ListRecordFile.resErr."+ret.Msg, nil)
	}
	return lo.Map(ret.Data.Paths, func(it string, _ int) string {
		if period != "" {
			return strings.Join([]string{"", live.Key, period, it}, "/")
		}
		return strings.Join([]string{"", live.Key, it}, "/")
	}), nil
}

func (c *zlmediakitClient) GetRecordFileStream(ctx context.Context, req *LiveRecordRequest) (io.ReadCloser, *biz.VideoRecordMeta, error) {
	res, err := c.hc.R().SetContext(ctx).SetDoNotParseResponse(true).
		SetQueryParam("secret", c.cfg.AdminToken).
		Get(fmt.Sprintf("http://%s/record/live%s", c.cfg.Url, req.Path))
	if err != nil {
		return nil, nil, biz.NewInternalError("zlmeiakitClient.GetRecordFileStream.rpcErr", nil)
	}
	body := res.RawBody()
	if res.IsError() {
		data, err := io.ReadAll(body)
		body.Close()
		if err != nil {
			return nil, nil, biz.NewBadRequestError("zlmeiakitClient.GetRecordFileStream.emptyRes", nil)
		}
		if res.StatusCode() == http.StatusNotFound {
			return nil, nil, biz.NewNotFoundError("VideoRecord", "path", req.Path)
		}

		return nil, nil, biz.NewBadRequestError("zlmeiakitClient.ListRecordFile.resErr", map[string]string{
			"res": types.BytesToString(data),
		})
	}
	meta := &biz.VideoRecordMeta{
		ContentType:   res.Header().Get("Content-Type"),
		ContentLength: conv.Atoi(res.Header().Get("Content-Length")),
	}
	return body, meta, nil
}

func (c *zlmediakitClient) DelRecordFile(ctx context.Context, query *RecordFileQuery) error {
	params := c.newBaseQueryForLive(query.Live)
	if query.Date.Unix() > 0 {
		params["period"] = query.Date.In(biz.TimeLocation).Format("2006-01-02")
	}
	res, err := c.hc.R().SetContext(ctx).SetQueryParams(params).SetResult(&zlkReponse{}).Get(c.newReqUrl("deleteRecordDirectory"))
	if err != nil {
		return biz.NewInternalError("zlmeiakitClient.DelRecordFile.rpcErr", nil)
	}
	live := query.Live
	if res.IsError() {
		c.log.Errorf("DelRecordFile for live %d with key %s rpc error: %v ", live.Id, live.Key, err)
		return biz.NewBadRequestError("zlmeiakitClient.DelRecordFile.resErr", nil)
	}
	ret := res.Result().(*zlkReponse)
	if ret.Code != 0 {
		return biz.NewBadRequestError("zlmeiakitClient.DelRecordFile.resErr."+ret.Msg, nil)
	}
	return nil
}

func (c *zlmediakitClient) TakeASnap(ctx context.Context, videoURL string) (io.ReadCloser, error) {
	res, err := c.hc.R().SetContext(ctx).SetDoNotParseResponse(true).
		SetQueryParam("secret", c.cfg.AdminToken).
		SetQueryParam("url", videoURL).
		SetQueryParam("timeout_sec", "30").
		SetQueryParam("expire_sec", "10").
		Get(fmt.Sprintf("http://%s/index/api/getSnap", c.cfg.Url))
	if err != nil {
		return nil, biz.NewInternalError("zlmeiakitClient.TakeASnap.rpcErr", nil)
	}
	if res.IsError() {
		defer res.RawBody().Close()
		meta := make(map[string]string)
		resBody, err := io.ReadAll(res.RawBody())
		if err == nil {
			meta["res"] = string(resBody)
			meta["url"] = videoURL
		}
		return nil, biz.NewBadRequestError("zlmeiakitClient.TakeASnap.resErr", meta)
	}
	return res.RawBody(), nil
}
