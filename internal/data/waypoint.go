package data

import (
	"context"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
)

type waypointRepo struct {
	data *Data
	log  *log.Helper
}

func NewWaypointRepo(logger log.Logger, data *Data) biz.WaypointRepo {
	return &waypointRepo{
		data: data,
		log:  log.<PERSON>Helper(logger),
	}
}

func (r *waypointRepo) CreateWaypoints(ctx context.Context, body []*biz.Waypoint) error {
	waypoints := make([]*gorm.Waypoint, 0)
	for _, wp := range body {
		waypoint := &gorm.Waypoint{}
		waypoint.FromBizWaypoint(wp)
		waypoints = append(waypoints, waypoint)
	}
	return r.data.db.WithContext(ctx).CreateInBatches(waypoints, 100).Error
}

func (r *waypointRepo) UpdateWaypoint(ctx context.Context, id int64, body biz.AnyMap) error {
	waypoint := &gorm.Waypoint{}
	waypoint.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(waypoint).Updates(body).Error
}

func (r *waypointRepo) DeleteWaypoint(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&gorm.Waypoint{}, "id=?", id).Error
}

func (r *waypointRepo) GetWaypoint(ctx context.Context, query *biz.DetailQuery) (*biz.Waypoint, error) {
	waypoint := &gorm.Waypoint{}
	r.log.Warnf("get waypoint %d", query.Id)
	if err := r.data.db.WithContext(ctx).First(waypoint, "id = ?", query.Id).Error; err != nil {
		return nil, errors.New(500, "get waypoint from db failed", err.Error())
	}
	return waypoint.ToBizWaypoint(), nil
}

func (r *waypointRepo) ListWaypoints(ctx context.Context, query *biz.WaypointListQuery) (int32, []*biz.Waypoint, error) {
	var count int64
	r.log.Warnf("list waypoints query: %+v", query)
	waypoints := []*gorm.Waypoint{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Waypoint{}).Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	if query.AirlineId != nil {
		tx = tx.Where("airline_id = ?", *query.AirlineId)
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get waypoint count from db failed", err.Error())
	}
	if count == 0 {
		return 0, make([]*biz.Waypoint, 0), nil
	}
	if err := tx.Order("serial ASC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&waypoints).Error; err != nil {
		r.log.Errorf("get waypoints from db failed: %v", err)
		return 0, nil, errors.New(500, "get waypoints from db failed", err.Error())
	}
	list := lo.Map(waypoints, func(c *gorm.Waypoint, _ int) *biz.Waypoint {
		return c.ToBizWaypoint()
	})
	return int32(count), list, nil
}

func (r *waypointRepo) AirlineWaypoints(ctx context.Context, airlineId int64) ([]*biz.Waypoint, error) {
	var points []*gorm.Waypoint
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Waypoint{}).Where("airline_id = ?", airlineId).Limit(100).Find(&points).Error; err != nil {
		r.log.Errorf("get waypoints from db failed: %v", err)
		return nil, biz.NewInternalError("AirlineWaypoints.dbErr", map[string]string{"airlineId": conv.Itoa(airlineId)})
	}
	return lo.Map(points, func(it *gorm.Waypoint, _ int) *biz.Waypoint { return it.ToBizWaypoint() }), nil
}
