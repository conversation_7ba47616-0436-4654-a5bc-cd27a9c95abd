package data

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"
	orm "gorm.io/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
)

type voyageRepo struct {
	data *Data
	log  *log.Helper
}

func NewVoyageRepo(logger log.Logger, data *Data) biz.VoyageRepo {
	return &voyageRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *voyageRepo) CreateVoyage(ctx context.Context, body *biz.Voyage) (*biz.Voyage, error) {
	voyagedKey := fmt.Sprintf("%s:VOYAGE:START:%d", biz.SkaiCacheKeyPrefix, body.DeviceId)
	// 设置航次开始分布锁，避免并发创建多次，默认15秒内不可连续飞行
	if ok := r.data.rdb.SetNX(ctx, voyagedKey, voyagedKey, 15*time.Second).Val(); !ok {
		return nil, nil
	}
	voyage := &gorm.Voyage{}
	voyage.FromBizVoyage(body)
	if err := r.data.db.WithContext(ctx).Create(voyage).Error; err != nil {
		return nil, err
	}
	return voyage.ToBizVoyage(), nil
}

func (r *voyageRepo) UpdateVoyage(ctx context.Context, id int64, body biz.AnyMap) error {
	voyage := &gorm.Voyage{}
	voyage.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(voyage).Updates(body).Error
}

func (r *voyageRepo) IncressVoyageMediaCount(ctx context.Context, id int64, c biz.VoyageMediaCount) error {
	updates := make(map[string]any)
	if c.Images > 0 {
		updates["images"] = orm.Expr("images + ?", c.Images)
	}
	if c.Videos > 0 {
		updates["videos"] = orm.Expr("videos + ?", c.Videos)
	}
	if len(updates) == 0 {
		return nil
	}
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Voyage{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return biz.NewInternalError("IncressVoyageMediaCount.db", map[string]string{"id": conv.Itoa(id), "detail": err.Error()})
	}
	return nil
}

func (r *voyageRepo) GetVoyage(ctx context.Context, query *biz.DetailQuery) (*biz.Voyage, error) {
	voyage := &gorm.Voyage{}
	r.log.Warnf("get voyage %d", query.Id)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Voyage{})
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if err := tx.First(voyage, "id = ?", query.Id).Error; err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return nil, biz.NewNotFoundError("Voyage", "id", conv.Itoa(query.Id))
		}
		return nil, errors.New(500, "get voyage from db failed", err.Error())
	}
	return voyage.ToBizVoyage(), nil
}

func (r *voyageRepo) GetFlight(ctx context.Context, flightId string) (*biz.Voyage, error) {
	flight := &gorm.Voyage{}
	r.log.Warnf("get flight %v", flightId)
	if err := r.data.db.WithContext(ctx).First(flight, "flight_id = ?", flightId).Error; err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return nil, biz.NewNotFoundError("Voyage", "flight_id", flightId)
		}
		return nil, errors.New(500, "get flight from db failed", err.Error())
	}
	return flight.ToBizVoyage(), nil
}

func (r *voyageRepo) ListVoyages(ctx context.Context, query *biz.VoyageListQuery) (int32, []*biz.Voyage, error) {
	var count int64
	r.log.Warnf("list voyage query: %+v", query)
	voyage := []*gorm.Voyage{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Voyage{})
	if !query.NotCheckProject() {
		tx.Where("merchant_id = ANY(?) and is_success = true", pq.Int64Array(query.MerchantIds))
	}
	tx.Where("created_time between ? and ?", query.StartTime, query.EndTime)
	if query.DeviceId != nil {
		tx = tx.Where("device_id = ?", *query.DeviceId)
	}
	if query.AirlineId != nil {
		tx = tx.Where("airline_id = ?", *query.AirlineId)
	}
	if query.StartTime != nil && query.EndTime != nil {
		tx = tx.Where("created_time between ? and ?", *query.StartTime, *query.EndTime)
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get voyage count from db failed", err.Error())
	}
	if count == 0 {
		return 0, make([]*biz.Voyage, 0), nil
	}
	if err := tx.Order("created_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&voyage).Error; err != nil {
		r.log.Errorf("get voyage from db failed: %v", err)
		return 0, nil, errors.New(500, "get voyage from db failed", err.Error())
	}
	list := lo.Map(voyage, func(c *gorm.Voyage, _ int) *biz.Voyage {
		return c.ToBizVoyage()
	})
	return int32(count), list, nil
}

func (r *voyageRepo) LastVoyage(ctx context.Context, query *biz.VoyageLastQuery) (*biz.Voyage, error) {
	voyage := &gorm.Voyage{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Voyage{}).Where("device_id = ?", query.DeviceId)
	if query.IsFlown != nil {
		tx.Where("is_flown = ?", *query.IsFlown)
	}
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if query.TimeScope != nil {
		tx.Where("created_time between ? and ? ", query.TimeScope.StartTime, query.TimeScope.EndTime)
	}
	if err := tx.Last(voyage).Error; err != nil {
		return nil, err
	}
	return voyage.ToBizVoyage(), nil
}

func (r *voyageRepo) RangeVoyages(ctx context.Context, query *biz.VoyageOverviewQuery) ([]*biz.VoyageOverview, error) {
	overviews := make([]*biz.VoyageOverview, 0)
	r.log.Warnf("range voyage query: %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Voyage{}).Select("device_id, count(*) as times, sum(mileage) as mileages")
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if err := tx.Group("device_id").Scan(&overviews).Error; err != nil {
		r.log.Errorf("range voyage from db failed: %v", err)
		return nil, errors.New(500, "range voyage from db failed", err.Error())
	}
	return overviews, nil
}

func (r *voyageRepo) AggregateVoyages(ctx context.Context, query *biz.VoyageOverviewQuery) (*biz.VoyageOverview, error) {
	var overview *biz.VoyageOverview
	r.log.Warnf("aggregate voyage query: %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Voyage{}).Select("count(*) as times, sum(mileage) as mileages, sum(images) as images, sum(videos) as videos")
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?) and is_success = true", pq.Int64Array(query.MerchantIds))
	}
	if query.DeviceId != nil {
		tx = tx.Where("device_id = ?", *query.DeviceId)
	}
	if err := tx.Scan(&overview).Error; err != nil {
		r.log.Errorf("aggregate voyage from db failed: %v", err)
		return nil, errors.New(500, "aggregate voyage from db failed", err.Error())
	}
	return overview, nil
}

func (r *voyageRepo) GetJointDevice(ctx context.Context, voyageId int64) (*biz.JointDevice, error) {
	cachedKey := fmt.Sprintf("%s:VOYAGE:%d:JOINT:DEVICE", biz.SkaiCacheKeyPrefix, voyageId)
	value, err := r.data.rdb.Get(ctx, cachedKey).Result()
	if err != nil {
		r.log.Errorf("Get joint device within eventKey: %s failed: %+v", cachedKey, err)
		return nil, err
	}
	device := &biz.JointDevice{}
	if err := json.Unmarshal([]byte(value), device); err != nil {
		return nil, err
	}
	return device, nil
}

func (r *voyageRepo) CacheJointDevice(ctx context.Context, voyageId int64, device *biz.JointDevice) error {
	cachedKey := fmt.Sprintf("%s:VOYAGE:%d:JOINT:DEVICE", biz.SkaiCacheKeyPrefix, voyageId)
	value, err := json.Marshal(device)
	if err != nil {
		return err
	}
	if err := r.data.rdb.SetEX(ctx, cachedKey, value, time.Hour).Err(); err != nil {
		r.log.Errorf("Get joint device within eventKey: %s failed: %+v", cachedKey, err)
		return err
	}
	return nil
}
