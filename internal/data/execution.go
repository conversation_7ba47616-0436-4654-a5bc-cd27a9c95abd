package data

import (
	"context"
	"fmt"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
)

type executionRepo struct {
	data *Data
	log  *log.Helper
}

func NewExecutionRepo(logger log.Logger, data *Data) biz.ExecutionRepo {
	return &executionRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *executionRepo) CreateExecution(ctx context.Context, body *biz.Execution) (*biz.Execution, error) {
	execution := &gorm.Execution{}
	execution.FromBizExecution(body)
	if err := r.data.db.WithContext(ctx).Create(execution).Error; err != nil {
		return nil, err
	}
	return execution.ToBizExecution(), nil
}

func (r *executionRepo) ListExecutions(ctx context.Context, query *biz.ExecutionListQuery) (int32, []*biz.Execution, error) {
	var count int64
	executions := []*gorm.Execution{}
	r.log.Warnf("list executions query: %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Execution{}).Where("mission_id = ?", query.MissionId)
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if query.Status != nil {
		tx.Where("status = ?", *query.Status)
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get executions from db failed", err.Error())
	}
	if count == 0 {
		return 0, make([]*biz.Execution, 0), nil
	}
	if err := tx.Order("created_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&executions).Error; err != nil {
		r.log.Errorf("get executions from db failed: %v", err)
		return 0, nil, errors.New(500, "get executions from db failed", err.Error())
	}
	list := lo.Map(executions, func(o *gorm.Execution, _ int) *biz.Execution {
		return o.ToBizExecution()
	})
	return int32(count), list, nil
}

func (r *executionRepo) UpdateExecution(ctx context.Context, id int64, body map[string]interface{}) error {
	execution := &gorm.Execution{}
	execution.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(execution).Updates(body).Error
}

func (r *executionRepo) GetExecution(ctx context.Context, query *biz.ExecutionQuery) (*biz.Execution, error) {
	execution := &gorm.Execution{}
	r.log.Warnf("get execution %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Execution{}).Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	if query.Id > 0 {
		tx = tx.Where("id = ?", query.Id)
	}
	if query.VoyageId > 0 {
		tx.Where("voyage_id = ?", query.VoyageId)
	}
	if query.MissionId > 0 {
		tx.Where("mission_id = ?", query.MissionId)
	}
	if query.OperationId > 0 {
		tx.Where("operation_id = ?", query.OperationId)
	}
	if query.OccurredTime != nil {
		tx.Where("created_time < ?", *query.OccurredTime)
	}
	if err := tx.Order("created_time DESC").First(execution).Error; err != nil {
		return nil, err
	}
	return execution.ToBizExecution(), nil
}

func (r *executionRepo) GroupExecution(ctx context.Context, query *biz.GroupQuery) ([]*biz.GroupRet, error) {
	aggregator := make([]*biz.GroupRet, 0)
	r.log.Warnf("count execution query: %+v", query)
	queryStr := fmt.Sprintf("%s as radix, count(*) as count", query.GroupBy)
	if query.SumBy != nil {
		queryStr += fmt.Sprintf(", sum(%s) as total", *query.SumBy)
	}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Execution{}).Select(queryStr)
	tx.Where("merchant_id = ANY(?) and status = ?", pq.Int64Array(query.MerchantIds), biz.OperationStatusSuccess)
	tx.Where("created_time >= ?", query.StartTime)
	if len(query.ReferIds) > 0 {
		tx.Where("mission_id IN ?", query.ReferIds)
	}
	if err := tx.Group("radix").Scan(&aggregator).Error; err != nil {
		r.log.Errorf("count execution from db failed: %v", err)
		return nil, errors.New(500, "count execution from db failed", err.Error())
	}
	return aggregator, nil
}

func (r *executionRepo) TopNAlarmCount(ctx context.Context, query *biz.ExecutionTopNQuery) ([]*biz.CountRet, error) {
	counts := make([]*biz.CountRet, 0)
	r.log.Warnf("topn execution alarms query: %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Execution{}).Select("mission_id as radix, sum(alarm_count) as count")
	tx.Joins("left join sk_mission on sk_execution.mission_id = sk_mission.id and sk_mission.deleted_at is null")
	tx.Where("sk_execution.merchant_id = ANY(?) and sk_execution.created_time >= ?", pq.Int64Array(query.MerchantIds), query.StartTime)
	if err := tx.Group("radix").Order("count desc").Limit(query.Size).Scan(&counts).Error; err != nil {
		r.log.Errorf("topn execution alarms from db failed: %v", err)
		return nil, errors.New(500, "topn execution alarms from db failed", err.Error())
	}
	return counts, nil
}
