package data

import (
	"context"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-jwt/jwt/v4"
)

type configRepo struct {
	data *Data
	log  *log.Helper
}

func NewConfigRepo(logger log.Logger, data *Data) biz.ConfigRepo {
	return &configRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *configRepo) AccessConfig(ctx context.Context) (*biz.AccessConfig, error) {
	return &biz.AccessConfig{
		AppId:      r.data.conf.Dji.AppId,
		AppKey:     r.data.conf.Dji.AppKey,
		AppLicense: r.data.conf.Dji.AppLicense,
	}, nil
}

func (r *configRepo) ThingConfig(ctx context.Context, sn, token string) (*biz.ThingConfig, error) {
	mqtt := biz.MQTTConnect{
		Host:     r.data.conf.Mqtt.PublicUrl,
		Username: sn,
		Password: sn,
	}
	claims := jwt.MapClaims{}
	secret := []byte(r.data.sconf.Jwt.Secret)
	method := jwt.GetSigningMethod(r.data.sconf.Jwt.Algorithm)
	var err error
	if _, err = jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		if token.Method.Alg() != method.Alg() {
			return nil, jwt.ErrHashUnavailable
		}
		return secret, nil
	}); err != nil {
		return nil, err
	}
	claims["sn"] = sn
	claims["exp"] = time.Now().Add(2 * 30 * 24 * time.Hour).Unix()
	if token, err = jwt.NewWithClaims(method, claims).SignedString(secret); err != nil {
		return nil, err
	}
	api := biz.APIConnect{Host: r.data.conf.Skai.CloudUrl, Token: token}
	return &biz.ThingConfig{Mqtt: mqtt, Api: api}, nil
}
