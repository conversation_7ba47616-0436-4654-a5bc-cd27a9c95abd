package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/td"
)

type dockEventRepo struct {
	log  *log.Helper
	data *Data
}

func NewDockEventRepo(logger log.Logger, data *Data) biz.DockEventRepo {
	return &dockEventRepo{
		log:  log.<PERSON>Helper(logger),
		data: data,
	}
}

func (r *dockEventRepo) RecordDockEvent(ctx context.Context, et biz.ThingEvent, detail any) error {
	if detail == nil {
		return biz.NewBadRequestError("RecordDockEvent.nilData", nil)
	}
	if et.DeviceId == 0 || et.Sn == "" {
		return biz.NewBadRequestError("RecordDockEvent.noDock", nil)
	}
	data := &td.DockEvent{}
	data.FromEvent(et, data)
	sql := data.GenInsert()
	if ret, err := r.data.tdClient.Exec(ctx, td.DB, sql); err != nil {
		r.log.Errorf("RecordDockEvent sql %s err: %v", sql, err)
		return biz.NewInternalError("RecordDockProperties.insert", map[string]string{"detail": err.Error(), "device": data.Sn, "id": data.Id})
	} else if ret.Code != 0 {
		r.log.Errorf("RecordDockProperties withSQL: %s res: %v", sql, ret)
		return biz.NewInternalError("RecordDockProperties.insert", map[string]string{"detail": ret.Message, "device": data.Sn, "id": data.Id})
	}
	r.log.Debug("RecordDockEvent: ", sql)
	return nil
}
