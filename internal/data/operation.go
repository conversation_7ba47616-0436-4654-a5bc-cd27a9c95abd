package data

import (
	"context"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"
	"gorm.io/datatypes"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
)

type operationRepo struct {
	data *Data
	log  *log.Helper
}

func NewOperationRepo(logger log.Logger, data *Data) biz.OperationRepo {
	return &operationRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *operationRepo) CreateOperation(ctx context.Context, body *biz.Operation) (*biz.Operation, error) {
	operation := &gorm.Operation{
		BaseEntity: *gorm.NewBase(),
		TenantId:   body.TenantId,
		MerchantId: body.MerchantId,
		AvatarId:   body.AvatarId,
		Type:       body.Type,
		Source:     body.Source,
		SourceId:   body.SourceId,
		Status:     body.Status,
		From:       body.From,
		Content:    (datatypes.JSONMap)(body.Content),
	}
	if err := r.data.db.WithContext(ctx).Create(operation).Error; err != nil {
		return nil, err
	}
	return operation.ToBizOperation(), nil
}

func (r *operationRepo) ListOperation(ctx context.Context, query *biz.OperationListQuery) (count int64, list []*biz.Operation, err error) {
	operations := []*gorm.Operation{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Operation{}).Where("source_id = ? and tenant_id = ?", query.SourceId, query.TenantId)
	tx.Where("created_time between ? and ?", query.StartTime, query.EndTime)
	if query.Type != nil {
		tx.Where("type = ?", query.Type.String())
	}
	if len(query.Types) > 0 {
		tx.Where("type in ?", lo.Map(query.Types, func(t biz.OperationType, _ int) string {
			return t.String()
		}))
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get operations from db failed", err.Error())
	}
	if err := tx.Order("created_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&operations).Error; err != nil {
		r.log.Errorf("get operations from db failed: %v", err)
		return 0, nil, errors.New(500, "get operations from db failed", err.Error())
	}
	list = lo.Map(operations, func(o *gorm.Operation, _ int) *biz.Operation {
		return o.ToBizOperation()
	})
	return
}

func (r *operationRepo) UpdateOperation(ctx context.Context, id int64, body map[string]interface{}) error {
	operation := &gorm.Operation{}
	operation.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(operation).Updates(body).Error
}

func (r *operationRepo) GetOperation(ctx context.Context, id int64) (*biz.Operation, error) {
	operation := &gorm.Operation{}
	r.log.Warnf("get operation %d", id)
	if err := r.data.db.WithContext(ctx).First(operation, "id = ?", id).Error; err != nil {
		return nil, errors.New(500, "get camera from db failed", err.Error())
	}
	return operation.ToBizOperation(), nil
}
