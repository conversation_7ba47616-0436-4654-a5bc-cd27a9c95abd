package data

import (
	"context"
	"fmt"

	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
)

type deviceRepo struct {
	data *Data
	log  *log.Helper
}

func NewDeviceRepo(logger log.Logger, data *Data) biz.DeviceRepo {
	return &deviceRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *deviceRepo) CheckDevice(ctx context.Context, query *biz.DeviceCheck) (*biz.Device, error) {
	device := &gorm.Device{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Device{}).Where("source_sn = ?", query.SourceSn)
	if query.Model != nil {
		tx.Where("model = ?", *query.Model)
	}
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if err := tx.First(device).Error; err != nil {
		return nil, err
	}
	return device.ToBizDevice(), nil
}

func (r *deviceRepo) CreateDevice(ctx context.Context, body *biz.Device) (*biz.Device, error) {
	device := &gorm.Device{}
	device.FromBizDevice(body)
	if err := r.data.db.WithContext(ctx).Create(device).Error; err != nil {
		return nil, err
	}
	return device.ToBizDevice(), nil
}

func (r *deviceRepo) UpdateDevice(ctx context.Context, id int64, body biz.AnyMap) error {
	device := &gorm.Device{}
	device.BaseEntity.Id = id
	if err := r.data.db.WithContext(ctx).Model(device).Updates(body).Error; err != nil {
		return err
	}
	return nil
}

func (r *deviceRepo) IntegrateDevice(ctx context.Context, imsg biz.IntegrateMsg) error {
	body := &client.IntegrateList{List: []interface{}{imsg}}
	if _, err := r.data.deviceClient.Integration(ctx, &client.IntegrateBody{Body: body}); err != nil {
		return err
	}
	return nil
}

func (r *deviceRepo) DeleteDevice(ctx context.Context, id int64) error {
	if err := r.data.db.WithContext(ctx).Delete(&gorm.Device{}, "id=?", id).Error; err != nil {
		return err
	}
	return nil
}

func (r *deviceRepo) GetDevice(ctx context.Context, query *biz.DetailQuery) (*biz.Device, error) {
	device := &gorm.Device{}
	r.log.Warnf("get device %d", query.Id)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Device{}).Where("id = ?", query.Id)
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?) and deployment_status = true", pq.Int64Array(query.MerchantIds))
	}
	if err := tx.First(device).Error; err != nil {
		return nil, errors.New(500, "get device from db failed", err.Error())
	}
	return device.ToBizDevice(), nil
}

func (r *deviceRepo) JointDevices(ctx context.Context, query *biz.DeviceJointQuery) ([]*biz.Device, error) {
	r.log.Warnf("joint devices query: %+v", query)
	devices := []*gorm.Device{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Device{})
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?) and deployment_status = true", pq.Int64Array(query.MerchantIds))
	}
	if query.Status != nil {
		tx.Where("status = ?", *query.Status)
	}
	if query.Category != nil {
		tx.Where("category = ?", *query.Category)
	}
	if len(query.Lnglat) == 2 {
		selectStr := "*, ST_Distance(st_geographyfromtext('POINT(%f %f)'), deployment_lnglat::geography) as distance"
		tx.Select(fmt.Sprintf(selectStr, query.Lnglat[0], query.Lnglat[1]))
		tx.Order("distance ASC")
	}
	if err := tx.Order("deployment_time DESC").Find(&devices).Error; err != nil {
		r.log.Errorf("get devices from db failed: %v", err)
		return nil, errors.New(500, "get devices from db failed", err.Error())
	}
	list := lo.Map(devices, func(c *gorm.Device, _ int) *biz.Device {
		return c.ToBizDevice()
	})
	return list, nil
}

func (r *deviceRepo) ListDevices(ctx context.Context, query *biz.DeviceListQuery) (int32, []*biz.Device, error) {
	var count int64
	r.log.Warnf("list devices query: %+v", query)
	devices := []*gorm.Device{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Device{})
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?) and deployment_status = true", pq.Int64Array(query.MerchantIds))
	}
	if query.Search != nil {
		value := *query.Search
		tx = tx.Where("deployment_name LIKE ? OR sn LIKE ? OR array_to_string(deployment_tags, '$$', 'null') LIKE ?", "%"+value+"%", "%"+value+"%", "%"+value+"%")
	}
	if query.Type != nil {
		tx = tx.Where("type = ?", *query.Type)
	}
	if query.Model != nil {
		tx = tx.Where("model = ?", *query.Model)
	}
	if query.Category != nil {
		tx = tx.Where("category = ?", *query.Category)
	}
	if len(query.Models) > 0 {
		tx = tx.Where("model = ANY(?)", pq.StringArray(query.Models))
	}
	if query.NetworkStatus != nil {
		tx = tx.Where("network_status = ?", *query.NetworkStatus)
	}
	if len(query.Ids) != 0 {
		tx = tx.Where("id IN ?", query.Ids)
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get device count from db failed", err.Error())
	}
	if count == 0 {
		return 0, make([]*biz.Device, 0), nil
	}
	if query.Field != nil {
		tx.Order(*query.Field + " " + query.Order.String())
	}
	if err := tx.Order("deployment_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&devices).Error; err != nil {
		r.log.Errorf("get devices from db failed: %v", err)
		return 0, nil, errors.New(500, "get devices from db failed", err.Error())
	}
	list := lo.Map(devices, func(c *gorm.Device, _ int) *biz.Device {
		return c.ToBizDevice()
	})
	return int32(count), list, nil
}

func (r *deviceRepo) CountDevices(ctx context.Context, query *biz.DeviceCountQuery) ([]*biz.DeviceCounter, error) {
	counts := make([]*biz.DeviceCounter, 0)
	r.log.Warnf("count devices query: %+v", query)
	queryStr := fmt.Sprintf("%s as name, count(*) as count", query.CountBy)
	if query.Filter != nil {
		queryStr = fmt.Sprintf("%s, count(*) %s", queryStr, *query.Filter)
	}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Device{}).Select(queryStr)
	tx.Where("merchant_id = ANY(?) and deployment_status = true", pq.Int64Array(query.MerchantIds))
	if query.Id != nil {
		tx = tx.Where("id = ?", *query.Id)
	}
	if err := tx.Group("name").Scan(&counts).Error; err != nil {
		r.log.Errorf("count device from db failed: %v", err)
		return nil, errors.New(500, "count device from db failed", err.Error())
	}
	return counts, nil
}

func (r *deviceRepo) GetDeviceAccessConfig(ctx context.Context, dev *biz.Device) (biz.AnyMap, error) {
	if dev.Source == biz.SourceDJI {
		return map[string]any{
			"ntp_server_host": r.data.conf.Dji.Ntp,
			"app_id":          r.data.conf.Dji.AppId,
			"app_key":         r.data.conf.Dji.AppKey,
			"app_license":     r.data.conf.Dji.AppLicense,
		}, nil
	}
	return nil, errors.NotFound("GetDeviceAccessConfig.unknownSource", "设备接入配置不存在")
}
