package data

import (
	"context"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"
	"gitlab.sensoro.com/skai/skai/pkg/types"
)

type annotationRepo struct {
	log  *log.Helper
	data *Data
}

func NewAnnotationRepo(logger log.Logger, data *Data) biz.AnnotationRepo {
	return &annotationRepo{
		log:  log.<PERSON>elper(logger),
		data: data,
	}
}

func (r *annotationRepo) Create(ctx context.Context, a *biz.Annotation) error {
	ga := &gorm.Annotation{}
	ga.FromBiz(a)
	if err := r.data.db.WithContext(ctx).Create(ga).Error; err != nil {
		r.log.WithContext(ctx).Errorf("create annotation %+v dbfailed: %v", ga, err)
		return biz.NewInternalError("annotaion.create", nil)
	}
	a.Id = ga.Id
	a.CreatedTime = ga.CreatedTime
	a.UpdatedTime = ga.UpdatedTime
	return nil
}

func (r *annotationRepo) GetOne(ctx context.Context, id int64) (*biz.Annotation, error) {
	ga := &gorm.Annotation{}
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Annotation{}).Where("id =?", id).First(ga).Error; err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return nil, biz.NewNotFoundError("Annotation", "id", conv.Itoa(id))
		}
		r.log.WithContext(ctx).Errorf("get annotation %d dbfailed: %v", id, err)
		return nil, biz.NewInternalError("annotaion.get", nil)
	}
	ba := ga.ToBiz()
	mIds := lo.Map(ba.Media, func(m biz.MediaWithArea, _ int) int64 { return m.Id })
	var relatedMedia []*gorm.Media
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).
		Where("id = ANY(?)", pq.Int64Array(mIds)).Find(&relatedMedia).Error; err != nil {
		r.log.WithContext(ctx).Errorf("get annotation %d related media dbfailed: %v", id, err)
		return nil, biz.NewInternalError("annotaion.getMedia", nil)
	}
	ba.Media = lo.Map(ba.Media, func(m biz.MediaWithArea, _ int) biz.MediaWithArea {
		if rm, ok := lo.Find(relatedMedia, func(it *gorm.Media) bool { return m.Id == it.Id }); ok {
			return biz.MediaWithArea{
				Id:          m.Id,
				ObjectAreas: m.ObjectAreas,
				Item:        rm.ToBiz(),
			}
		}
		return m
	})
	return ba, nil
}

func (r *annotationRepo) GetByMediaId(ctx context.Context, mediaId int64) (*biz.Annotation, error) {
	media := &gorm.Media{}
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).Where("id =?", mediaId).Take(media).Error; err != nil {
		if gorm.IfRecordNotFoundErr(err) {
			return nil, biz.NewNotFoundError("Annotation", "media_id", conv.Itoa(mediaId))
		}
		r.log.WithContext(ctx).Errorf("get annotation by media id %d dbfailed: %v", mediaId, err)
		return nil, biz.NewInternalError("annotaion.getByMediaId", nil)
	}
	if media.AnnotationId == 1 {
		ann := biz.EmtpyAnnotation.Clone()
		ann.CreatedTime = media.UpdatedTime
		ann.MerchantId = media.MerchantId
		ann.Media = []biz.MediaWithArea{{Id: media.Id, Item: media.ToBiz()}}
		return ann, nil
	} else if media.AnnotationId == 0 {
		return &biz.Annotation{
			CreatedTime: media.UpdatedTime,
			MerchantId:  media.MerchantId,
			Media:       []biz.MediaWithArea{{Id: media.Id, Item: media.ToBiz()}},
		}, nil
	}
	return r.GetOne(ctx, media.AnnotationId)
}

func (r *annotationRepo) ListMediaAnnotation(ctx context.Context, query *biz.AnnotationListQuery) (int64, []*biz.MediaWithAnnotation, error) {
	qb := r.data.NewDBQuery(ctx).Model(&gorm.Media{}).
		Where("sk_media.merchant_id =ANY(?) AND type = ? AND sub_type IN (1,2,4)", pq.Int64Array(query.ProjectInfo.MerchantIds), int32(biz.MediaTypePhoto))
	if query.TimeScope == nil {
		query.TimeScope = biz.NewTimeScope(0, 0)
	}
	qb = qb.Where("sk_media.created_time between ? AND ?", query.TimeScope.StartTime, query.TimeScope.EndTime)
	if len(query.Types) > 0 {
		qts := lo.Map(query.Types, func(t biz.AiEventType, _ int) string { return t.String() })
		qb = qb.Where(
			"sk_media.annotation_id = ANY(ARRAY(?))",
			r.data.db.Model(&gorm.Annotation{}).Select("id").Where(
				"created_time between ? AND ? AND type = ANY(?)",
				query.TimeScope.StartTime.Add(-7*24*time.Hour), query.TimeScope.EndTime.Add(7*24*time.Hour), pq.StringArray(qts),
			),
		)
	}
	if len(query.States) > 0 {
		cond := &strings.Builder{}
		cond.WriteRune('(')
		if lo.Contains(query.States, 0) {
			cond.WriteString("sk_media.annotation_id=0")
		}
		if lo.Contains(query.States, 1) {
			if cond.Len() > 1 {
				cond.WriteString(" OR ")
			}
			cond.WriteString("sk_media.annotation_id > 0")
		}
		cond.WriteRune(')')
		qb.Where(cond.String())
	}
	var count int64
	if err := qb.Count(&count).Error; err != nil {
		r.log.WithContext(ctx).Errorf("get annotation media with query %+v count dbfailed: %v", query, err)
		return 0, nil, biz.NewInternalError("annotaion.listMedia.count", nil)
	}
	if count == 0 {
		return 0, make([]*biz.MediaWithAnnotation, 0), nil
	}

	var media []*meidaWithDeviceVoyage
	// count覆写了select导致不会查询到voyage，所以需要重新创建qb
	findQB := r.data.NewDBQuery(ctx).Model(&meidaWithDeviceVoyage{}).Joins("Voyage")
	findQB.Statement.Clauses["WHERE"] = qb.Statement.Clauses["WHERE"]
	if err := findQB.Order("sk_media.created_time DESC").Limit(query.Limit()).Offset(query.Offset()).Find(&media).Error; err != nil {
		r.log.WithContext(ctx).Errorf("get annotation media with query %+v dbfailed: %v", query, err)
		return 0, nil, biz.NewInternalError("annotaion.listMedia", nil)
	}
	aIds := types.NewHashSet[int64]()
	arilineIds := types.NewHashSet[int64]()
	for _, m := range media {
		arilineIds.Add(m.AirlineId)
		if m.AnnotationId > 1 {
			aIds.Add(m.AnnotationId)
		}
	}
	var annotations []*gorm.Annotation
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Annotation{}).
		Where("id = ANY(?)", pq.Int64Array(aIds.ToSlice())).Find(&annotations).Error; err != nil {
		r.log.WithContext(ctx).Errorf("get annotation with ids %+v dbfailed: %v", aIds, err)
		return 0, nil, biz.NewInternalError("annotaion.list.annotations", nil)
	}
	as := lo.SliceToMap(annotations, func(it *gorm.Annotation) (int64, *biz.Annotation) {
		return it.Id, it.ToBiz()
	})
	var airlines []*gorm.Airline
	if err := r.data.NewDBQuery(ctx).Model(&gorm.Airline{}).
		Where("id = ANY(?)", pq.Int64Array(arilineIds.ToSlice())).Find(&airlines).Error; err != nil {
		r.log.WithContext(ctx).Errorf("get airline with ids %+v dbfailed: %v", aIds, err)
		return 0, nil, biz.NewInternalError("annotaion.list.airline", nil)
	}
	airlineMap := lo.SliceToMap(airlines, func(it *gorm.Airline) (int64, *biz.Airline) {
		return it.Id, it.ToBizAirline()
	})
	return count, lo.Map(media, func(it *meidaWithDeviceVoyage, _ int) *biz.MediaWithAnnotation {
		bm := it.ToBiz()
		var v *biz.Voyage
		if it.Voyage != nil {
			v = it.Voyage.ToBizVoyage()
		}
		if a, ok := as[bm.AnnotationId]; ok {
			ma, exist := lo.Find(a.Media, func(ai biz.MediaWithArea) bool { return ai.Id == bm.Id })
			if exist {
				return &biz.MediaWithAnnotation{
					Media:       *bm,
					Annotation:  a,
					ObjectAreas: ma.ObjectAreas,
					Voyage:      v,
					Airline:     airlineMap[it.AirlineId],
				}
			}
		}
		var ann *biz.Annotation
		if bm.AnnotationId == 1 {
			ann = biz.EmtpyAnnotation.Clone()
			ann.CreatedTime = bm.UpdatedTime
		}
		return &biz.MediaWithAnnotation{
			Media:      *bm,
			Annotation: ann,
			Voyage:     v,
			Airline:    airlineMap[it.AirlineId],
		}
	}), nil

}
