package downlink

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/mitchellh/mapstructure"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/service/thing"
	"gitlab.sensoro.com/skai/skai/pkg/id"
)

type djiSender struct {
	dev       *biz.Device
	redConn   redis.UniversalClient
	mqttPuber biz.PacketPublisher
	conf      *conf.Data
}

func newDJISender(c *conf.Data, dev *biz.Device, redConn redis.UniversalClient, mqttPuber biz.PacketPublisher) Sender {
	return &djiSender{
		dev:       dev,
		redConn:   redConn,
		mqttPuber: mqttPuber,
		conf:      c,
	}
}

func (s *djiSender) RunService(ctx context.Context, serviceId string, serviceData any) error {
	if s.dev.Model == biz.ModelDJIDock || s.dev.Model == biz.ModelDJILite {
		return s.runDockService(ctx, serviceData.(*biz.DockService))
	}
	if s.dev.Model == biz.ModelDJIPilot {
		return s.runPilotService(ctx, serviceData.(*biz.DockService))
	}
	return biz.NewBadRequestError("dji.RunService.invalidModel", nil)
}
func (s *djiSender) GetState(ctx context.Context, downId string) (any, error) {
	state, _, err := s.getDockState(ctx, downId)
	return state, err
}

func (s *djiSender) HasMoreOperation(ctx context.Context, reply any) (bool, error) {

	r := reply.(*biz.DockServiceReply)
	serv, opts, err := s.getDockState(ctx, r.Id)
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		return false, err
	}
	r.ServiceId = serv.ServiceId
	switch serv.Identifier {
	case biz.DockServiceIdentifierTakeoff:
		if opts == nil && r.Status == biz.ServiceReplyStatusOk {
			if err := s.sendFlyghttaskExecuteTask(ctx, r.Id, serv); err != nil {
				return false, err
			}
			s.saveDockState(ctx, r.Id, serv, map[string]any{"s": 1})
			return true, nil
		} else {
			r.Data = map[string]any{
				"flightId": r.Id,
			}
			//r.Data["flightId"] = r.Id
		}
	case biz.DockServiceIdentifierLaunch:
		if r.Status == biz.ServiceReplyStatusOk {
			r.Data = map[string]any{"flightId": utilities.ToSEUUID(r.ServiceId)}
			return false, nil
		}
	case biz.DockServiceIdentifierStartPushLive:
		sp := &biz.StartPushLivePayload{}
		if err := s.transStatePayload(serv.Payload, sp); err != nil {
			return false, err
		}
		if opts == nil {
			if r.Code == 0 {
				serv.Payload = sp
				if sp.Live != nil {
					if cp := sp.Live.GetLivePosition(); cp == biz.LivePositionDock || cp == biz.LivePositionDockInside {
						s.saveDockState(ctx, r.Id, serv, map[string]any{"s": 2})
						s.sendAutoChangeCameraCmd(ctx, r.Id, serv)
					}
				}
			}
			// 重试下发指令太早大疆报操作失败，相机未开启直播，无法响应直播设置错误
			// if r.Code == 513006 {
			// 	time.Sleep(3 * time.Second)
			// 	s.saveDockState(ctx, r.Id, serv, map[string]any{"s": 1})
			// 	if p, ok := serv.Payload.(*biz.StartPushLivePayload); ok {
			// 		d := thing.NewStartLiveService(s.dev, p.Live, p.PushConfig, p.Clarity)
			// 		d.Tid = id.NewUUIDV1()
			// 		s.pubService(ctx, d)
			// 	}
			// 	return true, nil
			// }
		} else {
			if s, ok := opts["s"].(float64); ok && s == 2 && sp.Live != nil {
				r.Data = map[string]any{"position": biz.LivePositionDock, "liveId": conv.Itoa(sp.Live.Id)}
			}
		}
	}
	s.redConn.Del(ctx, s.newStateKey(r.Id))
	return false, nil
}

func (s *djiSender) runPilotService(ctx context.Context, service *biz.DockService) error {
	bid := utilities.ToSEUUID(service.ServiceId)
	var data any
	switch service.Identifier {
	case biz.DockServiceIdentifierSwitchLens:
		if payload, ok := service.Payload.(*biz.SwithLensPayload); ok {
			data = &thing.DJIDockLiveLensChangeService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSetLiveLen),
				Data: &thing.DJIDockLiveLensChangeCmd{
					VideoId:   payload.Live.Key,
					VideoType: payload.Lens.String(),
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runPilotService.switchLen.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierSwitchClarity:
		if payload, ok := service.Payload.(*biz.SwitchClarityPayload); ok {
			data = &thing.DJIDockLiveQualitySetService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSetLiveQuality),
				Data: &thing.DJIDockLiveQualitySetCmd{
					VideoId:      payload.Live.Key,
					VideoQuality: payload.Clarity,
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runPilotService.qualityUpdate.invalidPayload", nil)
		}
	default:
		return biz.NewBadRequestError("dji.runPilotService.unsupportedService", nil)
	}
	if err := s.saveDockState(ctx, bid, service, nil); err != nil {
		return biz.NewInternalError("dji.runDockService.saveState", nil)
	}
	return s.pubService(ctx, data)
}

func (s *djiSender) runDockService(ctx context.Context, service *biz.DockService) error {
	bid := utilities.ToSEUUID(service.ServiceId)
	if cmder, ok := djiCmders[service.Identifier]; ok {
		data, err := cmder(bid, s.dev, service)
		if err != nil {
			return err
		}
		if err := s.saveDockState(ctx, bid, service, nil); err != nil {
			return biz.NewInternalError("dji.runDockService.saveState", nil)
		}
		return s.pubService(ctx, data)
	}
	var data any
	switch service.Identifier {
	case biz.DockServiceIdentifierEnterDRCMode:
		var err error
		data, err = s.newEnterDRCModeCmd(bid)
		if err != nil {
			return err
		}
	case biz.DockServiceIdentifierControlLens,
		biz.DockServiceIdentifierTakePicture,
		biz.DockServiceIdentifierStartVideo, biz.DockServiceIdentifierStopVideo:
		getService := func(sid biz.DockServiceIdentifier) string {
			m := map[biz.DockServiceIdentifier]string{
				biz.DockServiceIdentifierControlLens: thing.DJIMethodGrabPayloadControl,
				biz.DockServiceIdentifierTakePicture: thing.DJIMethodTakePhoto,
				biz.DockServiceIdentifierStartVideo:  thing.DJIMethodStartRecording,
				biz.DockServiceIdentifierStopVideo:   thing.DJIMethodStopRecording,
			}
			return m[sid]
		}
		if payload, ok := service.Payload.(*biz.SubdevicePayload); ok {
			data = &thing.DJIDockCameraCmdService{
				DJIMessage: *s.newCmd(bid, getService(service.Identifier)),
				Data: &thing.DJIDockPayloadIndexData{
					PayloadIndex: payload.Subdevice.Index,
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.controLens.invalidPayload", map[string]string{
				"expect": "*biz.SubdevicePayload",
			})
		}
	case biz.DockServiceIdentifierSetFocalLength:
		if payload, ok := service.Payload.(*biz.SetFocalLengthPayload); ok {
			//vt, _ := payload.Live.GetVideoType([]biz.DockSubdevice{payload.Subdevice})
			data = &thing.DJIDockSetFocalLengthService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSetFocalLength),
				Data: &thing.DJIDockSetFocalLengthCmd{
					PayloadIndex: payload.Subdevice.Index,
					CameraType:   payload.Lens.String(),
					ZoomFactor:   payload.ZoomFactor,
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.setFocalLength.invalidPayload", map[string]string{
				"expect": "*biz.SetFocalLengthPayload",
			})
		}
	case biz.DockServiceIdentifierSwitchCMode:
		if payload, ok := service.Payload.(*biz.CameraModePaylaod); ok {
			data = &thing.DJIDockSwitchCameraModeService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSwitchCameraMode),
				Data: &thing.DJIDockSwitchCameraModeCmd{
					PayloadIndex: payload.Subdevice.Index,
					CameraMode:   int32(payload.Mode),
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.setFocalLength.invalidPayload", map[string]string{
				"expect": "*biz.DJIDockSwitchCameraModeService",
			})
		}
	case biz.DockServiceIdentifierExitDRCMode:
		data = &djiCmdWithData{
			DJIMessage: lo.FromPtr(s.newCmd(bid, thing.DJIMethodExitDRCMode)),
			Data:       map[string]any{},
		}
	case biz.DockServiceIdentifierSwitchLens:
		if payload, ok := service.Payload.(*biz.SwithLensPayload); ok {
			data = &thing.DJIDockLiveLensChangeService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSetLiveLen),
				Data: &thing.DJIDockLiveLensChangeCmd{
					VideoId:   payload.Live.Key,
					VideoType: payload.Lens.String(),
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.switchLen.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierSwitchClarity:
		if payload, ok := service.Payload.(*biz.SwitchClarityPayload); ok {
			data = &thing.DJIDockLiveQualitySetService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSetLiveQuality),
				Data: &thing.DJIDockLiveQualitySetCmd{
					VideoId:      payload.Live.Key,
					VideoQuality: payload.Clarity,
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.qualityUpdate.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierResetGimbal:
		if payload, ok := service.Payload.(*biz.ResetGimbalPayload); ok {
			data = &thing.DJIDockResetGimbalService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodResetGimbal),
				Data: &thing.DJIDockResetGimbalCmd{
					PayloadIndex: payload.Subdevice.Index,
					ResetMode:    payload.Mode,
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.resetGimbal.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierFlytoPoint, biz.DockServiceIdentifierUpdatePoint:
		if payload, ok := service.Payload.(*biz.FlytoPointPayload); ok {
			method := thing.DJIMethodFlyToPoint
			if service.Identifier == biz.DockServiceIdentifierUpdatePoint {
				method = thing.DJIMethodUpdateFlyToPoint
			}
			data = &thing.DJIDockFlyToPointService{
				DJIMessage: *s.newCmd(bid, method),
				Data: &thing.DJIDockFlyToPointCmd{
					FlyToId:  utilities.ToSEUUID(service.ServiceId),
					MaxSpeed: payload.MaxSpeed,
					Points: lo.Map(payload.Points, func(it biz.GuidePoint, _ int) thing.DJIDockFlyToPoint {
						return thing.DJIDockFlyToPoint{
							Longitude: it.Point[0],
							Latitude:  it.Point[1],
							Height:    it.Height,
						}
					}),
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.flyto.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierLookatPoint:
		if payload, ok := service.Payload.(*biz.LookatPointPayload); ok {
			data = &thing.DJIDockLookAtPointService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodLookAtPoint),
				Data: &thing.DJIDockLookAtPointCmd{
					Locked:       payload.Locked,
					Height:       payload.Height,
					Longitude:    payload.Lnglat[0],
					Latitude:     payload.Lnglat[1],
					PayloadIndex: payload.PayloadIndex,
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.lookat.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierStartSpeaker:
		if payload, ok := service.Payload.(*biz.StartSpeakerPayload); ok {
			data = s.newDjiStartSpeakerService(bid, payload)
		} else {
			return biz.NewBadRequestError("dji.runDockService.startSpeaker.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierStopSpeaker:
		if payload, ok := service.Payload.(*biz.SpeakerOperationPayload); ok {
			data = &djiCmdWithData{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSpeakerStop),
				Data:       map[string]any{"psdk_index": conv.Atoi(payload.Index)},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.stopSpeaker.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierReplaySpeaker:
		if payload, ok := service.Payload.(*biz.SpeakerOperationPayload); ok {
			data = &djiCmdWithData{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSpeakerStop),
				Data:       map[string]any{"psdk_index": conv.Atoi(payload.Index)},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.replaySpeaker.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierSetSpeakerVolume:
		if payload, ok := service.Payload.(*biz.SetSpeakerVolumePayload); ok {
			data = &djiCmdWithData{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSpeakerVolumeSet),
				Data:       map[string]any{"psdk_index": conv.Atoi(payload.Index), "play_volume": payload.Volume},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.setSpeakerVolume.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierSetSpeakerPlayMode:
		if payload, ok := service.Payload.(*biz.SetSpeakerPlayModePayload); ok {
			data = &djiCmdWithData{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSpeakerModeSet),
				Data:       map[string]any{"psdk_index": conv.Atoi(payload.Index), "play_mode": payload.PlayMode},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.setPlayMode", nil)
		}
	case biz.DockServiceIdentifierLaunch:
		if payload, ok := service.Payload.(*biz.LaunchPayload); ok {
			data = &thing.DJIDockTakeoffToPointService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodTakeOffToPoint),
				Data:       thing.NewDJIDockTakeoffToPointData(utilities.ToSEUUID(service.ServiceId), payload),
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.launch.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierEnterPOIMode:
		if payload, ok := service.Payload.(*biz.StartOrbitPayload); ok {
			data = &thing.DJIDockEnterPOIModeService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodEnterPOIMode),
				Data: thing.DJIDockEnterPOIModeCmd{
					Longitude: payload.Point.Point[0],
					Latitude:  payload.Point.Point[1],
					Height:    payload.Point.Height,
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.controlOrbit.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierUpdatePOISpeed:
		if payload, ok := service.Payload.(*biz.StartOrbitPayload); ok {
			data = &thing.DJIDockSetPOISpeedService{
				DJIMessage: *s.newCmd(bid, thing.DJIMethodSetPOICircleSpeed),
				Data: thing.DJIDockSetPOISpeedCmd{
					CircleSpeed: payload.Speed,
				},
			}
		} else {
			return biz.NewBadRequestError("dji.runDockService.controlOrbit.invalidPayload", nil)
		}
	case biz.DockServiceIdentifierExitPOIMode:
		data = &djiCmdWithData{
			DJIMessage: *s.newCmd(bid, thing.DJIMethodExitPOIMode),
			Data:       map[string]any{},
		}
	case biz.DockServiceIdentifierCancelReturn:
		data = &djiCmdWithData{
			DJIMessage: *s.newCmd(bid, thing.DJIMethodCancelReturnHome),
			Data:       map[string]any{},
		}
	default:
		return biz.NewBadRequestError("dji.runDockService.unsupportService", nil)
	}
	if err := s.saveDockState(ctx, bid, service, nil); err != nil {
		return biz.NewInternalError("dji.runDockService.saveState", nil)
	}
	return s.pubService(ctx, data)
}

func (s *djiSender) newDjiStartSpeakerService(bid string, payload *biz.StartSpeakerPayload) *djiCmdWithData {
	if payload.Mode == 0 {
		return &djiCmdWithData{
			DJIMessage: *s.newCmd(bid, thing.DJIMethodSpeakerPlayTTS),
			Data: map[string]any{
				"psdk_index": conv.Atoi(payload.Index),
				"tts": map[string]any{
					"md5":  payload.Signature,
					"name": payload.Name,
					"text": payload.Data,
				},
			},
		}
	}
	return &djiCmdWithData{
		DJIMessage: *s.newCmd(bid, thing.DJIMethodSpeakerPlayAudio),
		Data: map[string]any{
			"psdk_index": conv.Atoi(payload.Index),
			"file": map[string]any{
				"md5":    payload.Signature,
				"name":   payload.Name,
				"format": "pcm",
				"url":    payload.Data,
			},
		},
	}
}

func (s *djiSender) sendFlyghttaskExecuteTask(ctx context.Context, bid string, _ *biz.DockService) error {
	data := &thing.DJIDockFlightTaskExecuteService{
		DJIMessage: *s.newCmd(bid, thing.DJIMethodFlightTaskExecute),
		Data: &thing.DJIDockFlightTaskExecuteCmd{
			FlightId: bid,
		},
	}
	return s.pubService(ctx, data)
}

func (s *djiSender) newEnterDRCModeCmd(bid string) (*thing.DJIDockDRCModeService, error) {
	pass := make([]byte, 16)
	rand.Read(pass)
	addr := ""
	if v, err := url.Parse(s.conf.Mqtt.PublicUrl); err == nil {
		addr = v.Host
	} else {
		return nil, biz.NewInternalError("sendEnterDRCModeTask.parseMQTTUrl", nil)
	}
	data := &thing.DJIDockDRCModeService{
		DJIMessage: *s.newCmd(bid, thing.DJIMethodDRCMode),
		Data: &thing.DJIDockDRCModeCmd{
			MQTTBroker: &thing.DJIDockDRCMQTTBroker{
				Address:  addr,
				ClientId: fmt.Sprintf("DRC-%s", s.dev.Sn),
				Uername:  s.dev.SourceSn,
				// TODO: make a sign
				Password:   hex.EncodeToString(pass),
				ExpireTime: 3600,
			},
			OSDFrequency: 5,
			HSIFrequency: 1,
		},
	}
	return data, nil
}

func (s *djiSender) newCmd(bid string, cmd string) *thing.DJIMessage {
	return s.newMessage(id.NewUUIDV1(), bid, cmd)
}

func (s *djiSender) newMessage(tid, bid, cmd string) *thing.DJIMessage {
	return &thing.DJIMessage{
		Tid:       tid,
		Bid:       bid,
		Timestamp: time.Now().UnixMilli(),
		Gateway:   s.dev.SourceSn,
		Method:    cmd,
	}
}

type djiCmdWithData struct {
	thing.DJIMessage
	Data map[string]any `json:"data"`
}

func (s *djiSender) sendAutoChangeCameraCmd(ctx context.Context, bid string, ds *biz.DockService) error {
	dsp := ds.Payload.(*biz.StartPushLivePayload)
	ccs := &biz.DockService{
		Sn:         ds.Sn,
		DeviceId:   ds.DeviceId,
		ServiceId:  ds.ServiceId,
		Identifier: biz.DockServiceIdentifierChangeLiveCamera,
		Timeout:    ds.Timeout,
		Payload: &biz.ChangeLiveCameraPayload{
			Live:     dsp.Live,
			Position: biz.LivePositionDock,
		},
	}
	d, err := djiChangeLiveCameraCmder(bid, s.dev, ccs)
	if err != nil {
		return err
	}
	return s.pubService(ctx, d)
}

func (s *djiSender) newStateKey(bid string) string {
	return strings.Join([]string{biz.SkaiIssueKeyPrefix, "dji", bid}, ":")
}

func (s *djiSender) saveDockState(ctx context.Context, bid string, data *biz.DockService, options biz.AnyMap) error {
	state := &senderState[*biz.DockService]{
		Data:    data,
		Options: options,
	}
	value, err := json.Marshal(state)
	if err != nil {
		return err
	}
	return s.redConn.Set(ctx, s.newStateKey(bid), value, 300*time.Second).Err()
}

func (s *djiSender) getDockState(ctx context.Context, bid string) (*biz.DockService, biz.AnyMap, error) {
	value, err := s.redConn.Get(ctx, s.newStateKey(bid)).Bytes()
	if err != nil {
		return nil, nil, err
	}
	state := &senderState[*biz.DockService]{}
	if err := json.Unmarshal(value, state); err != nil {
		return nil, nil, err
	}
	return state.Data, state.Options, nil
}

func (s *djiSender) transStatePayload(sp any, payload any) error {
	return mapstructure.WeakDecode(sp, payload)
}

func (s *djiSender) pubService(ctx context.Context, data any) error {
	downData, _ := json.Marshal(data)
	if _, err := s.mqttPuber.Publish(ctx, fmt.Sprintf("thing/product/%s/services", s.dev.SourceSn), 1, downData); err != nil {
		return biz.NewInternalError("dji.runDockService.pub", map[string]string{"err": err.Error(), "dev": s.dev.Sn})
	}
	return nil
}
