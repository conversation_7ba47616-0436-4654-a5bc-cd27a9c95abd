package downlink

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
)

type Sender interface {
	RunService(ctx context.Context, serviceId string, serviceData any) error
	HasMoreOperation(ctx context.Context, reply any) (bool, error)
	GetState(ctx context.Context, downId string) (any, error)
}

type SenderFactory struct {
	redisClient redis.UniversalClient
	log         *log.Helper
	conf        *conf.Data
}

func NewSenderFactory(redisClient redis.UniversalClient, c *conf.Data, logger log.Logger) *SenderFactory {
	return &SenderFactory{
		redisClient: redisClient,
		log:         log.NewHelper(logger),
		conf:        c,
	}
}

func (f *SenderFactory) NewSender(dev *biz.Device, puber biz.PacketPublisher) (Sender, bool) {
	if dev.Model == biz.ModelDJIDock || dev.Model == biz.ModelDJILite || dev.Model == biz.ModelDJIPilot {
		return newDJISender(f.conf, dev, f.redisClient, puber), true
	}
	return nil, false
}

type senderState[T any] struct {
	Data    T          `json:"data"`
	Options biz.AnyMap `json:"opts,omitempty"`
}
