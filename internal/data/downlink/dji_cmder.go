package downlink

import (
	"time"

	"github.com/samber/lo"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/service/thing"
	"gitlab.sensoro.com/skai/skai/pkg/id"
)

type djiCmder func(bid string, dev *biz.Device, service *biz.DockService) (any, error)

var djiCmders = map[biz.DockServiceIdentifier]djiCmder{}

func registerDjiCmd(identifier biz.DockServiceIdentifier, cmd djiCmder) {
	djiCmders[identifier] = cmd
}

func init() {
	registerDjiCmd(biz.DockServiceIdentifierTakeoff, djiTakeOffCmder)
	registerDjiCmd(biz.DockServiceIdentifierStartPushLive, djiStartPushLiveCmder)
	registerDjiCmd(biz.DockServiceIdentifierReturn, newDJISimpleCMDer(thing.DJIMethodReturnHome))
	registerDjiCmd(biz.DockServiceIdentifierReboot, newDJISimpleCMDer(thing.DJIMethodDockReboot))
	registerDjiCmd(biz.DockServiceIdentifierControlAero, newDJISimpleCMDer(thing.DJIMethodGrabFlightControl))
	registerDjiCmd(biz.DockServiceIdentifierBackAirline, newDJISimpleCMDer(thing.DJIMethodFlightTaskRecover))
	registerDjiCmd(biz.DockServiceIdentifierPauseAirline, newDJISimpleCMDer(thing.DJIMethodFlightTaskPause))
	registerDjiCmd(biz.DockServiceIdentifierFlyoffPoint, newDJISimpleCMDer(thing.DJIMethodStopFlyToPoint))
	registerDjiCmd(biz.DockServiceIdentifierOpenDebugMode, newDJISimpleCMDer(thing.DJIMethodDebugMode))
	registerDjiCmd(biz.DockServiceIdentifierCloseDebugMode, newDJISimpleCMDer(thing.DJIMethodDebugModeClose))
	registerDjiCmd(biz.DockServiceIdentifierOpenCover, newDJISimpleCMDer(thing.DJIMethodOpenDockCover))
	registerDjiCmd(biz.DockServiceIdentifierCloseCover, newDJISimpleCMDer(thing.DJIMethodCloseDockCover))
	registerDjiCmd(biz.DockServiceIdentifierOpenDrone, newDJISimpleCMDer(thing.DJIMethodOpenDrone))
	registerDjiCmd(biz.DockServiceIdentifierCloseDrone, newDJISimpleCMDer(thing.DJIMethodCloseDrone))
	registerDjiCmd(biz.DockServiceIdentifierOpenPutter, newDJISimpleCMDer(thing.DJIMethodOpenPutter))
	registerDjiCmd(biz.DockServiceIdentifierClosePutter, newDJISimpleCMDer(thing.DJIMethodClosePutter))
	registerDjiCmd(biz.DockServiceIdentifierForceCloseCover, newDJISimpleCMDer(thing.DJIMethodForceCloseDockCover))
	registerDjiCmd(biz.DockServiceIdentifierOpenCharge, newDJISimpleCMDer(thing.DJIMethodOpenCharge))
	registerDjiCmd(biz.DockServiceIdentifierCloseCharge, newDJISimpleCMDer(thing.DJIMethodCloseCharge))
	registerDjiCmd(biz.DockServiceIdentifierListFileupload, djiLogQueryCmder)
	registerDjiCmd(biz.DockServiceIdentifierStartFileupload, djiLogUploadCmder)
	registerDjiCmd(biz.DockServiceIdentifierChangeLiveCamera, djiChangeLiveCameraCmder)
}

func newDJICmd(bid, cmd, sn string) *thing.DJIMessage {
	return newDJIMessage(id.NewUUIDV1(), bid, cmd, sn)
}

func newDJIMessage(tid, bid, cmd, sn string) *thing.DJIMessage {
	return &thing.DJIMessage{
		Tid:       tid,
		Bid:       bid,
		Timestamp: time.Now().UnixMilli(),
		Gateway:   sn,
		Method:    cmd,
	}
}

func newDJISimpleCMDer(djiMethod string) djiCmder {
	return func(bid string, dev *biz.Device, service *biz.DockService) (any, error) {
		return newDJICmd(bid, djiMethod, dev.SourceSn), nil
	}
}

func djiLogUploadCmder(bid string, dev *biz.Device, service *biz.DockService) (any, error) {
	if payload, ok := service.Payload.(*biz.StartFileuploadPayload); ok {
		return &djiCmdWithData{
			DJIMessage: *newDJICmd(bid, thing.DJIMethodLogUpload, dev.SourceSn),
			Data: map[string]any{
				"bucket": payload.Credential.Bucket,
				"credentials": map[string]any{
					"access_key_id":     payload.Credential.AccessKey,
					"access_key_secret": payload.Credential.AccessSecret,
					"expire":            payload.Credential.Expire,
					"security_token":    payload.Credential.SessionToken,
				},
				"endpoint": payload.Credential.Endpoint,
				"provider": payload.Credential.Provider,
				"region":   payload.Credential.Region,
				"params": map[string]any{
					"files": lo.Map(payload.Files, func(it *biz.FileUpload, _ int) map[string]any {
						return map[string]any{
							"list":       []map[string]int32{{"boot_index": it.BootIndex}},
							"module":     it.Module,
							"object_key": it.ObjectKey,
						}
					}),
				},
			},
		}, nil
	}
	return nil, biz.NewBadRequestError("dji.runDockService.logUpload.invalidPayload", nil)
}

func djiLogQueryCmder(bid string, dev *biz.Device, service *biz.DockService) (any, error) {
	if payload, ok := service.Payload.(*biz.ListFileuploadPayload); ok {
		return &djiCmdWithData{
			DJIMessage: *newDJICmd(bid, thing.DJIMethodQueryLogs, dev.SourceSn),
			Data: map[string]any{
				"module_list": lo.Filter(payload.Modules, func(it string, _ int) bool {
					return it == "0" || it == "3"
				}),
			},
		}, nil
	}
	return nil, biz.NewBadRequestError("dji.runDockService.logQuery.invalidPayload", nil)
}

func djiStartPushLiveCmder(bid string, dev *biz.Device, service *biz.DockService) (any, error) {
	if payload, ok := service.Payload.(*biz.StartPushLivePayload); ok {
		s := thing.NewStartLiveService(
			dev, payload.Live, payload.PushConfig, payload.Clarity,
		)
		s.Bid = bid
		return s, nil
	} else {
		return nil, biz.NewBadRequestError("dji.runDockService.startPushLive.invalidPayload", nil)
	}
}

func djiTakeOffCmder(bid string, dev *biz.Device, service *biz.DockService) (any, error) {
	if payload, ok := service.Payload.(*biz.TakeoffPayload); ok {
		// flightTaskData
		flightTaskData := &thing.DJIDockFlightTask{
			FlightId:    bid,
			TaskType:    int32(payload.TaskType),
			ExecuteTime: payload.ExecuteTime,
			File: thing.DJIFlightTaskFile{
				URL:         payload.KMZFile.Url,
				Fingerprint: payload.KMZFile.Fingerprint,
			},
			RthAltitude:           payload.ReturnAltitude,
			RthMode:               1,
			OutOfControlAction:    int32(payload.RCLostAction),
			ExitWaylineWhenRcLost: int32(payload.AirlineModeLost),
		}
		if payload.SimulateMission != nil {
			flightTaskData.SimulateMission = &thing.SimulateMission{
				IsEnable:  payload.SimulateMission.IsEnable,
				Longitude: payload.SimulateMission.Lnglat[0],
				Latitude:  payload.SimulateMission.Lnglat[1],
			}
		}
		return &thing.DJIDockFlightTaskPrepareService{
			DJIMessage: *newDJICmd(bid, thing.DJIMethodFlightTaskPrepare, dev.SourceSn),
			Data:       flightTaskData,
		}, nil
	} else {
		return nil, biz.NewBadRequestError("dji.runDockService.takeoff.invalidPayload", map[string]string{
			"expect": "*biz.TakeoffPayload",
		})
	}
}

func djiChangeLiveCameraCmder(bid string, dev *biz.Device, service *biz.DockService) (any, error) {
	if payload, ok := service.Payload.(*biz.ChangeLiveCameraPayload); ok {
		if payload.Live == nil {
			return nil, biz.NewBadRequestError("djiChangeLiveCameraCmder.payload", map[string]string{
				"field": "Live",
			})
		}
		p := 1
		if payload.Position == biz.LivePositionDockInside {
			p = 0
		}
		ds := &djiCmdWithData{
			DJIMessage: *newDJICmd(bid, thing.DJIMethodChangeLiveCamera, dev.SourceSn),
			Data: map[string]any{
				"video_id":        payload.Live.Key,
				"camera_position": p,
			},
		}
		return ds, nil
	}
	return nil, biz.NewBadRequestError("djiChangeLiveCameraCmder.paylaod", map[string]string{
		"expect": "*biz.ChangeLiveCameraPayload",
	})
}
