package data

import (
	"context"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/data/gorm"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
)

type airlineRepo struct {
	data *Data
	log  *log.Helper
}

func NewAirlineRepo(logger log.Logger, data *Data) biz.AirlineRepo {
	return &airlineRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *airlineRepo) CreateAirline(ctx context.Context, body *biz.Airline) (*biz.Airline, error) {
	airline := &gorm.Airline{}
	airline.FromBizAirline(body)
	if err := r.data.db.WithContext(ctx).Create(airline).Error; err != nil {
		return nil, errors.New(500, "insert airline to db failed", err.Error())
	}
	return airline.ToBizAirline(), nil
}

func (r *airlineRepo) UpdateAirline(ctx context.Context, id int64, body biz.AnyMap) error {
	airline := &gorm.Airline{}
	airline.BaseEntity.Id = id
	return r.data.db.WithContext(ctx).Model(airline).Updates(body).Error
}

func (r *airlineRepo) DeleteAirline(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&gorm.Airline{}, "id=?", id).Error
}

func (r *airlineRepo) GetAirline(ctx context.Context, query *biz.AirlineQuery) (*biz.Airline, error) {
	airline := &gorm.Airline{}
	r.log.Debugf("get airline %+v", query)
	tx := r.data.db.WithContext(ctx).Model(&gorm.Airline{})
	// 如果查询范围为1，表示查询软删除的航线，软删除航线merchantId为0
	if query.Scope == 1 {
		query.MerchantIds = append(query.MerchantIds, 0)
	}
	if len(query.MerchantIds) > 0 {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if query.Id > 0 {
		tx = tx.Where("id = ?", query.Id)
	}
	if err := tx.First(airline).Error; err != nil {
		return nil, err
	}
	return airline.ToBizAirline(), nil
}

func (r *airlineRepo) ListAirlines(ctx context.Context, query *biz.AirlineListQuery) (int32, []*biz.Airline, error) {
	r.log.Warnf("list devices query: %+v", query)
	if !query.Unscoped && len(query.MerchantIds) == 0 {
		return 0, make([]*biz.Airline, 0), nil
	}
	var count int64
	airlines := []*gorm.Airline{}
	tx := r.data.db.WithContext(ctx).Model(&gorm.Airline{})
	if !query.Unscoped {
		tx.Where("merchant_id = ANY(?)", pq.Int64Array(query.MerchantIds))
	}
	if len(query.Ids) != 0 {
		tx.Where("id IN ?", query.Ids)
	}
	if query.Type != nil {
		tx = tx.Where("type = ?", *query.Type)
	}
	if query.Search != nil {
		value := *query.Search
		tx = tx.Where("name LIKE ? OR array_to_string(tags, '$$', 'null') LIKE ?", "%"+value+"%", "%"+value+"%")
	}
	if len(query.DeviceIds) > 0 {
		tx = tx.Where("device_ids && ?", pq.Int64Array(query.DeviceIds))
	}
	if query.StartTime != nil && query.EndTime != nil {
		tx.Where("created_time between ? and ?", *query.StartTime, *query.EndTime)
	}
	if err := tx.Count(&count).Error; err != nil {
		return 0, nil, errors.New(500, "get airline count from db failed", err.Error())
	}
	if count == 0 {
		return 0, make([]*biz.Airline, 0), nil
	}
	if err := tx.Order("created_time DESC").Limit(query.Size).Offset((query.Page - 1) * query.Size).Find(&airlines).Error; err != nil {
		r.log.Errorf("get airlines from db failed: %v", err)
		return 0, nil, errors.New(500, "get airlines from db failed", err.Error())
	}
	list := lo.Map(airlines, func(c *gorm.Airline, _ int) *biz.Airline {
		return c.ToBizAirline()
	})
	return int32(count), list, nil
}
