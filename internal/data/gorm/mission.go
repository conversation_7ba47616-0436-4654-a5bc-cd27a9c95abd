package gorm

import (
	"encoding/json"
	"errors"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type Mission struct {
	BaseEntity
	DeletedAt    gorm.DeletedAt
	Name         string            `gorm:"index;comment:名称;type:varchar(50)"`
	Type         biz.MissionType   `gorm:"index;comment:类型;type:int;default:1"`
	Status       biz.MissionStatus `gorm:"comment:状态;type:int;default:0"`
	TenantId     int64             `gorm:"index;comment:租户;type:bigint"`
	MerchantId   int64             `gorm:"index;comment:商户;type:bigint"`
	AvatarId     int64             `gorm:"comment:创建者;type:bigint"`
	EditorId     int64             `gorm:"comment:编辑者;type:bigint"`
	DeviceId     int64             `gorm:"comment:设备标识;type:bigint"`
	AirlineId    int64             `gorm:"comment:航线标识;type:bigint"`
	Description  string            `gorm:"comment:描述;type:varchar(255)"`
	StartTime    time.Time         `gorm:"comment:开始时间;type:timestamp with time zone"`
	EndTime      time.Time         `gorm:"comment:结束时间;type:timestamp with time zone"`
	EditedTime   time.Time         `gorm:"comment:编辑时间;type:timestamp with time zone"`
	AlgConfigs   datatypes.JSON    `gorm:"comment:算法配置;type:jsonb"`
	NoticeRules  datatypes.JSON    `gorm:"comment:通知规则;type:jsonb"`
	ExecuteTimes datatypes.JSON    `gorm:"comment:执行时间;type:jsonb"`
}

func (Mission) TableName() string {
	return "sk_mission"
}

func (m *Mission) FromBizMission(bm *biz.Mission) error {
	if bm == nil {
		return errors.New("nil mission")
	}
	m.BaseEntity = *NewBase()
	m.Name = bm.Name
	m.Type = bm.Type
	m.Status = bm.Status
	m.AvatarId = bm.AvatarId
	m.EditorId = bm.EditorId
	m.DeviceId = bm.DeviceId
	m.AirlineId = bm.AirlineId
	m.TenantId = bm.TenantId
	m.MerchantId = bm.MerchantId
	m.Description = bm.Description
	m.StartTime = bm.StartTime
	m.EndTime = bm.EndTime
	m.EditedTime = bm.EditedTime
	algConfigs, _ := json.Marshal(bm.AlgConfigs)
	m.AlgConfigs = algConfigs
	noticeRules, _ := json.Marshal(bm.NoticeRules)
	m.NoticeRules = noticeRules
	executeTimes, _ := json.Marshal(bm.ExecuteTimes)
	m.ExecuteTimes = executeTimes
	return nil
}

func (m *Mission) ToBizMission() *biz.Mission {
	mission := &biz.Mission{}
	mission.Id = m.Id
	mission.Name = m.Name
	mission.Type = m.Type
	mission.Status = m.Status
	mission.AvatarId = m.AvatarId
	mission.EditorId = m.EditorId
	mission.DeviceId = m.DeviceId
	mission.AirlineId = m.AirlineId
	mission.TenantId = m.TenantId
	mission.MerchantId = m.MerchantId
	mission.Description = m.Description
	mission.StartTime = m.StartTime
	mission.EndTime = m.EndTime
	mission.EditedTime = m.EditedTime
	mission.CreatedTime = m.CreatedTime
	mission.UpdatedTime = m.UpdatedTime
	mission.IsDeleted = m.DeletedAt.Valid
	algConfigs := make([]*biz.AlgConfig, 0)
	json.Unmarshal(m.AlgConfigs, &algConfigs)
	mission.AlgConfigs = algConfigs
	noticeRules := make([]*biz.NoticeRule, 0)
	json.Unmarshal(m.NoticeRules, &noticeRules)
	mission.NoticeRules = noticeRules
	executeTimes := make([]*biz.ExecuteTime, 0)
	json.Unmarshal(m.ExecuteTimes, &executeTimes)
	mission.ExecuteTimes = executeTimes
	return mission
}
