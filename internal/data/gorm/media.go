package gorm

import (
	"encoding/json"
	"fmt"

	"github.com/tidwall/gjson"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type Media struct {
	BaseEntity
	Type           int32          `gorm:"comment:媒体类型;type:int"`
	SubType        int32          `gorm:"comment:子类型;type:int"`
	DeviceId       int64          `gorm:"type:bigint;index:media_dv_index"`
	VoyageId       int64          `gorm:"type:bigint;index:media_dv_index"`
	AirlineId      int64          `gorm:"type:bigint;index"`
	WaypointId     int64          `gorm:"type:bigint"`
	MerchantId     int64          `gorm:"type:bigint"`
	SubDeviceIndex string         `gorm:"type:varchar(255);"`
	Status         int32          `gorm:"type:int;default:0"`
	Key            string         `gorm:"comment:对象存储key或直播时的streamId;type:varchar(255)"`
	Name           string         `gorm:"type:varchar(255)"`
	Extra          datatypes.JSON `gorm:"type:jsonb"`
	AnnotationId   int64          `gorm:"type:bigint;default:0"`
	DeletedAt      gorm.DeletedAt `gorm:"index"`
}

func (Media) TableName() string {
	return "sk_media"
}

func (m *Media) ToBiz() *biz.Media {
	other := make(biz.AnyMap)

	var meta *biz.MediaMeta
	if v := gjson.GetBytes(m.Extra, "meta"); v.Exists() {
		meta = &biz.MediaMeta{}
		if err := json.Unmarshal([]byte(v.Raw), meta); err != nil {
			fmt.Println(err)
		}
	}
	if v := gjson.GetBytes(m.Extra, "other"); v.Exists() {
		json.Unmarshal([]byte(v.Raw), &other)
	}
	return &biz.Media{
		Id:             m.Id,
		Type:           biz.MediaType(m.Type),
		SubType:        m.SubType,
		AirlineId:      m.AirlineId,
		DeviceId:       m.DeviceId,
		VoyageId:       m.VoyageId,
		WaypointId:     m.WaypointId,
		MerchantId:     m.MerchantId,
		SubDeviceIndex: m.SubDeviceIndex,
		Meta:           meta,
		CreatedTime:    m.CreatedTime,
		UpdatedTime:    m.UpdatedTime,
		Status:         m.Status,
		Key:            m.Key,
		Name:           m.Name,
		Extra:          other,
		AnnotationId:   m.AnnotationId,
	}
}

func (m *Media) ToLive(dev *biz.Device) *biz.Live {
	bm := m.ToBiz()
	return &biz.Live{
		Id:             m.Id,
		DeviceId:       m.DeviceId,
		SubDeviceIndex: m.SubDeviceIndex,
		MerchantId:     m.MerchantId,
		CreatedTime:    m.CreatedTime,
		UpdatedTime:    m.UpdatedTime,
		Status:         m.Status,
		Key:            m.Key,
		Type:           biz.NewVideoType(m.Name),
		Clarity:        bm.GetLiveClarity(),
		Position:       bm.GetLivePosition(),
		Device:         dev,
	}
}

func (m *Media) FromBiz(bm *biz.Media) {
	if bm.Id == 0 {
		m.BaseEntity = *NewBase()
	} else {
		m.BaseEntity = BaseEntity{
			Id:          bm.Id,
			CreatedTime: bm.CreatedTime,
			UpdatedTime: bm.UpdatedTime,
		}
	}
	m.Type = int32(bm.Type)
	m.SubType = bm.SubType
	m.DeviceId = bm.DeviceId
	m.VoyageId = bm.VoyageId
	m.AirlineId = bm.AirlineId
	m.SubDeviceIndex = bm.SubDeviceIndex
	m.WaypointId = bm.WaypointId
	m.MerchantId = bm.MerchantId
	m.Status = bm.Status
	m.Key = bm.Key
	m.Name = bm.Name
	extra := make(map[string]any)
	if bm.Meta != nil {
		extra["meta"] = bm.Meta
	}
	if len(bm.Extra) > 0 {
		extra["other"] = bm.Extra
	}
	m.CreatedTime = bm.CreatedTime
	m.UpdatedTime = bm.UpdatedTime
	m.Extra, _ = json.Marshal(extra)
	m.AnnotationId = bm.AnnotationId
}
