package gorm

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type ArchiveTask struct {
	BaseEntity
	StartTime           time.Time    `gorm:"column:start_time;type:timestamp with time zone"`
	EndTime             time.Time    `gorm:"column:end_time;type:timestamp with time zone"`
	Cost                int64        `gorm:"column:cost"`
	ObjectCount         int64        `gorm:"column:object_count"`
	TotalSize           int64        `gorm:"column:total_size"`
	VoyageDownsizedTime sql.NullTime `gorm:"column:voyage_downsized_time;type:timestamp with time zone"`
	Downsized           int64        `gorm:"column:downsized"`
}

func (ArchiveTask) TableName() string {
	return "sk_archive_task"
}

func (a *ArchiveTask) FromBizArchiveTask(bt *biz.ArchiveTask) error {
	if bt == nil {
		return errors.New("nil archive task")
	}
	if bt.Id == 0 {
		a.BaseEntity = *NewBase()
	} else {
		a.BaseEntity = BaseEntity{
			Id:          bt.Id,
			CreatedTime: bt.CreatedTime,
			UpdatedTime: bt.UpdatedTime,
		}
	}
	a.StartTime = bt.TimeScope.StartTime
	a.EndTime = bt.TimeScope.EndTime
	a.Cost = int64(bt.Cost.Seconds())
	a.ObjectCount = bt.ObjectCount
	a.TotalSize = bt.TotalSize
	a.VoyageDownsizedTime = sql.NullTime{
		Time:  bt.VoyageDownsizedTime,
		Valid: !bt.VoyageDownsizedTime.IsZero(),
	}
	a.Downsized = bt.Downsized
	return nil
}

func (a *ArchiveTask) ToBiz() *biz.ArchiveTask {
	return &biz.ArchiveTask{
		Id:          a.Id,
		CreatedTime: a.CreatedTime,
		UpdatedTime: a.UpdatedTime,
		TimeScope: biz.TimeScope{
			StartTime: a.StartTime,
			EndTime:   a.EndTime,
		},
		Cost:                time.Duration(a.Cost) * time.Second,
		ObjectCount:         a.ObjectCount,
		TotalSize:           a.TotalSize,
		VoyageDownsizedTime: a.VoyageDownsizedTime.Time,
		Downsized:           a.Downsized,
	}
}
