package gorm

import (
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gorm.io/datatypes"
)

type Event struct {
	BaseEntity
	Sn           string            `gorm:"index;comment:设备SN;type:varchar(50)"`
	Type         biz.EventType     `gorm:"comment:事件类型;type:varchar(64)"`
	Code         string            `gorm:"comment:事件编码;type:varchar(32)"`
	Level        int32             `gorm:"comment:事件等级;type:int;default:1"`
	Place        int32             `gorm:"comment:事件场所;type:int;default:0"`
	ExtraData    datatypes.JSONMap `gorm:"comment:补充信息;type:jsonb;default:'{}'"`
	OccurredTime time.Time         `gorm:"comment:触发时间;type:timestamp without time zone"`
}

func (Event) TableName() string {
	return "sk_event"
}

func (a *Event) ToBizEvent() *biz.Event {
	event := &biz.Event{}
	event.Id = a.Id
	event.Sn = a.Sn
	event.Type = a.Type
	event.Code = a.Code
	event.Level = a.Level
	event.Place = a.Place
	event.ExtraData = a.ExtraData
	event.OccurredTime = a.OccurredTime
	event.CreatedTime = a.CreatedTime
	event.UpdatedTime = a.UpdatedTime
	return event
}
