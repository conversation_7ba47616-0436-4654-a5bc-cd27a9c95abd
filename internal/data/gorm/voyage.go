package gorm

import (
	"encoding/json"
	"errors"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gorm.io/datatypes"
)

type Voyage struct {
	BaseEntity
	Sn              string            `gorm:"comment:设备SN;type:varchar(50)"`
	Name            string            `gorm:"comment:名称;type:varchar(128)"`
	DeviceId        int64             `gorm:"index;comment:设备ID;type:bigint"`
	AirlineId       int64             `gorm:"index;comment:航线ID;type:bigint"`
	FlightId        string            `gorm:"index;comment:任务ID;type:varchar(64)"`
	IsFlown         bool              `gorm:"comment:是否飞行中;type:bool;default:false"`
	IsSuccess       bool              `gorm:"comment:是否成功;type:bool;default:false"`
	Status          string            `gorm:"comment:当前状态;type:varchar(32);default:begin"`
	Mileage         int32             `gorm:"comment:飞行里程;type:int;default:0"`
	Runtime         int32             `gorm:"comment:运行时间;type:int;default:0"`
	Images          int32             `gorm:"comment:图片数;type:int;default:0"`
	Videos          int32             `gorm:"comment:视频数;type:int;default:0"`
	DroneMediaTotal int32             `gorm:"comment:飞机拍摄媒体总数;type:int;default:0"`
	StartTime       time.Time         `gorm:"comment:开始时间;type:timestamp without time zone"`
	EndTime         time.Time         `gorm:"comment:结束时间;type:timestamp without time zone"`
	TenantId        int64             `gorm:"comment:租户ID;type:bigint"`
	MerchantId      int64             `gorm:"comment:资源库ID;type:bigint"`
	Statement       datatypes.JSONMap `gorm:"comment:冗余数据;type:jsonb;default:'{}'"`
	GuidePoints     datatypes.JSON    `gorm:"comment:指点列表;type:jsonb;default:'{}'"`
	Extra           datatypes.JSON    `gorm:"comment:扩展数据;type:jsonb;default:'{}'"`
}

func (Voyage) TableName() string {
	return "sk_voyage"
}

func (v *Voyage) FromBizVoyage(bv *biz.Voyage) error {
	if bv == nil {
		return errors.New("nil voyage")
	}
	v.BaseEntity = *NewBase()
	v.Sn = bv.Sn
	v.Name = bv.Name
	v.DeviceId = bv.DeviceId
	v.AirlineId = bv.AirlineId
	v.FlightId = bv.FlightId
	v.IsFlown = bv.IsFlown
	v.TenantId = bv.TenantId
	v.StartTime = bv.StartTime
	v.MerchantId = bv.MerchantId
	v.Videos = bv.Videos
	v.Images = bv.Images
	v.DroneMediaTotal = bv.DroneMediaTotal
	v.Extra, _ = json.Marshal(bv.Extra)
	return nil
}

func (v *Voyage) ToBizVoyage() *biz.Voyage {
	voyage := &biz.Voyage{}
	voyage.Id = v.Id
	voyage.Sn = v.Sn
	voyage.Name = v.Name
	voyage.DeviceId = v.DeviceId
	voyage.AirlineId = v.AirlineId
	voyage.FlightId = v.FlightId
	voyage.IsFlown = v.IsFlown
	voyage.StartTime = v.StartTime
	voyage.EndTime = v.EndTime
	voyage.Runtime = v.Runtime
	voyage.Status = v.Status
	voyage.Mileage = v.Mileage
	voyage.Statement = v.Statement
	voyage.TenantId = v.TenantId
	voyage.MerchantId = v.MerchantId
	voyage.CreatedTime = v.CreatedTime
	voyage.UpdatedTime = v.UpdatedTime
	voyage.Images = v.Images
	voyage.Videos = v.Videos
	guidePoints := make([]*biz.GuidePoint, 0)
	json.Unmarshal(v.GuidePoints, &guidePoints)
	voyage.GuidePoints = guidePoints
	voyage.DroneMediaTotal = v.DroneMediaTotal
	if len(v.Extra) > 0 {
		json.Unmarshal(v.Extra, &voyage.Extra)
	}
	return voyage
}
