package gorm

import (
	"encoding/json"
	"errors"

	"github.com/lib/pq"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/sql/geom/ewkb"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gorm.io/datatypes"
)

type Airline struct {
	BaseEntity
	Name            string         `gorm:"comment:航线名称;type:varchar(128)"`
	Type            string         `gorm:"comment:航线类型;type:varchar(128)"`
	Tags            pq.StringArray `gorm:"comment:航线标签;type:text[]"`
	Speed           float32        `gorm:"comment:全局飞行速度;type:float"`
	TranSpeed       float32        `gorm:"comment:全局过渡速度;type:float"`
	Height          float32        `gorm:"comment:全局航线高度;type:float"`
	ReturnHeight    float32        `gorm:"comment:全局返航高度;type:float"`
	SecurityHeight  float32        `gorm:"comment:安全起飞高度;type:float"`
	WaypointCount   int32          `gorm:"comment:航点总数统计;type:int"`
	EstimateMileage float32        `gorm:"comment:估算巡航里程;type:float"`
	FlytoMode       string         `gorm:"comment:飞向首行点模式;type:varchar(128)"`
	FinishAction    string         `gorm:"comment:结束动作;type:varchar(128)"`
	ExitOnRCLost    string         `gorm:"comment:失控状态;type:varchar(128)"`
	RCLostAction    string         `gorm:"comment:失控动作;type:varchar(128)"`
	Description     string         `gorm:"comment:备注描述;type:varchar(255)"`
	TenantId        int64          `gorm:"index;comment:租户;type:bigint"`
	MerchantId      int64          `gorm:"index;comment:资源库;type:bigint"`
	DeviceIds       pq.Int64Array  `gorm:"index;comment:关联设备;type:bigint[]"`
	KMZFile         datatypes.JSON `gorm:"comment:航线文件;type:jsonb"`
	FenceArea       *ewkb.Polygon  `gorm:"comment:区域面范围;type:geometry(Polygon,4326)"`
}

func (Airline) TableName() string {
	return "sk_airline"
}

func (a *Airline) FromBizAirline(ba *biz.Airline) error {
	if ba == nil {
		return errors.New("nil airline")
	}
	kmzFile, _ := json.Marshal(ba.KMZFile)
	a.BaseEntity = *NewBase()
	a.Name = ba.Name
	a.Type = ba.Type
	a.Tags = ba.Tags
	a.Speed = ba.Speed
	a.TranSpeed = ba.TranSpeed
	a.Height = ba.Height
	a.ReturnHeight = ba.ReturnHeight
	a.SecurityHeight = ba.SecurityHeight
	a.WaypointCount = ba.WaypointCount
	a.EstimateMileage = ba.EstimateMileage
	a.FlytoMode = ba.FlytoMode
	a.FinishAction = ba.FinishAction
	a.ExitOnRCLost = ba.ExitOnRCLost
	a.RCLostAction = ba.RCLostAction
	a.Description = ba.Description
	a.KMZFile = kmzFile
	a.DeviceIds = ba.DeviceIds
	a.TenantId = ba.TenantId
	a.MerchantId = ba.MerchantId
	if ba.FenceArea != nil {
		a.FenceArea = &ewkb.Polygon{Coordinates: lo.Map(ba.FenceArea.Coordinates, func(it biz.GeometryCoordinate, _ int) ewkb.GeometryCoordinate {
			return ewkb.GeometryCoordinate(it)
		})}
	}
	return nil
}

func (a *Airline) ToBizAirline() *biz.Airline {
	var file biz.KMZFile
	json.Unmarshal(a.KMZFile, &file)
	airline := &biz.Airline{}
	airline.Id = a.Id
	airline.Name = a.Name
	airline.Type = a.Type
	airline.KMZFile = file
	airline.Speed = a.Speed
	airline.TranSpeed = a.TranSpeed
	airline.Height = a.Height
	airline.ReturnHeight = a.ReturnHeight
	airline.SecurityHeight = a.SecurityHeight
	airline.WaypointCount = a.WaypointCount
	airline.EstimateMileage = a.EstimateMileage
	airline.FlytoMode = a.FlytoMode
	airline.FinishAction = a.FinishAction
	airline.ExitOnRCLost = a.ExitOnRCLost
	airline.RCLostAction = a.RCLostAction
	airline.Description = a.Description
	airline.DeviceIds = a.DeviceIds
	airline.TenantId = a.TenantId
	airline.MerchantId = a.MerchantId
	airline.CreatedTime = a.CreatedTime
	airline.UpdatedTime = a.UpdatedTime
	airline.Tags = lo.Ternary(a.Tags != nil, a.Tags, []string{})
	if a.FenceArea != nil {
		airline.FenceArea = &biz.Geometry{
			Type: "Polygon",
			Coordinates: lo.Map(a.FenceArea.Coordinates, func(it ewkb.GeometryCoordinate, _ int) biz.GeometryCoordinate {
				return biz.GeometryCoordinate(it)
			}),
		}
	}
	return airline
}
