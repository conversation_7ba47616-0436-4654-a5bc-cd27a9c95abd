package gorm

import (
	"errors"

	"gitlab.sensoro.com/go-sensoro/lins-common/sql/geom/ewkb"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type Waypoint struct {
	BaseEntity
	AirlineId   int64      `gorm:"index;comment:航线ID;type:bigint"`
	TenantId    int64      `gorm:"comment:租户;type:bigint"`
	MerchantId  int64      `gorm:"comment:资源库;type:bigint"`
	Serial      int        `gorm:"comment:序列号;type:int"`
	Height      float32    `gorm:"comment:高度;type:float"`
	Lnglat      ewkb.Point `gorm:"comment:经纬度;type:geometry(Point,4326)"`
	Speed       float32    `gorm:"comment:速度;type:float"`
	TurnMode    string     `gorm:"comment:转弯模式;type:varchar(64)"`
	TurnDamping float32    `gorm:"comment:转弯截距;type:float"`
}

func (Waypoint) TableName() string {
	return "sk_waypoint"
}

func (w *Waypoint) FromBizWaypoint(bw *biz.Waypoint) error {
	if bw == nil {
		return errors.New("nil waypoint")
	}
	w.BaseEntity = *NewBase()
	w.Speed = bw.Speed
	w.Serial = bw.Serial
	w.Height = bw.Height
	w.Lnglat = bw.Lnglat
	w.TurnMode = bw.TurnMode
	w.AirlineId = bw.AirlineId
	w.TenantId = bw.TenantId
	w.MerchantId = bw.MerchantId
	w.TurnDamping = bw.TurnDamping
	return nil
}

func (a *Waypoint) ToBizWaypoint() *biz.Waypoint {
	waypoint := &biz.Waypoint{}
	waypoint.Id = a.Id
	waypoint.Speed = a.Speed
	waypoint.Serial = a.Serial
	waypoint.Height = a.Height
	waypoint.Lnglat = a.Lnglat
	waypoint.TurnMode = a.TurnMode
	waypoint.AirlineId = a.AirlineId
	waypoint.TenantId = a.TenantId
	waypoint.MerchantId = a.MerchantId
	waypoint.TurnDamping = a.TurnDamping
	waypoint.CreatedTime = a.CreatedTime
	waypoint.UpdatedTime = a.UpdatedTime
	return waypoint
}
