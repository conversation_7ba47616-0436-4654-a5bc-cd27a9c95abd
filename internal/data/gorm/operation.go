package gorm

import (
	"gitlab.sensoro.com/skai/skai/internal/biz"

	"gorm.io/datatypes"
)

type Operation struct {
	BaseEntity
	Type       biz.OperationType   `gorm:"index;comment:类型;type:varchar(32)"`
	Status     biz.OperationStatus `gorm:"comment:状态;type:varchar(32)"`
	Source     biz.OperationSource `gorm:"comment:对象;type:varchar(32)"`
	SourceId   int64               `gorm:"index;comment:源ID;type:bigint"`
	TenantId   int64               `gorm:"comment:租户;type:bigint"`
	MerchantId int64               `gorm:"comment:商户;type:bigint"`
	AvatarId   int64               `gorm:"comment:用户;type:bigint"`
	From       string              `gorm:"comment:来源;type:varchar(32);default:WEB"`
	Message    string              `gorm:"comment:消息;type:varchar(128);"`
	Content    datatypes.JSONMap   `gorm:"comment:内容;type:jsonb;default:'{}'"`
}

func (Operation) TableName() string {
	return "sk_operation"
}

func (o *Operation) ToBizOperation() *biz.Operation {
	operation := &biz.Operation{}
	operation.Id = o.Id
	operation.Type = o.Type
	operation.From = o.From
	operation.Status = o.Status
	operation.Source = o.Source
	operation.Message = o.Message
	operation.Content = o.Content
	operation.SourceId = o.SourceId
	operation.AvatarId = o.AvatarId
	operation.TenantId = o.TenantId
	operation.MerchantId = o.MerchantId
	operation.CreatedTime = o.CreatedTime
	operation.UpdatedTime = o.UpdatedTime
	return operation
}
