package gorm

import "gorm.io/datatypes"

type Stream struct {
	// id is media_id
	BaseEntity
	// zlmedia streamId
	SourceId string         `gorm:"type:varchar(500);uniqueIndex"`
	MediaApp string         `gorm:"type:varchar(500)"`
	Source   int32          `gorm:"type:int"`
	GBId     int64          `gorm:"type:bigint"`
	Status   int16          `gorm:"type:smallint"`
	ServerId string         `gorm:"type:varchar(500)"`
	Extra    datatypes.JSON `gorm:"type:jsonb"`
}

func (Stream) TableName() string {
	return "sk_stream"
}
