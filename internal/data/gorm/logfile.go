package gorm

import (
	"errors"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type Logfile struct {
	BaseEntity
	Module      string    `gorm:"index;comment:模块;type:varchar(8)"`
	Url         string    `gorm:"index:idx_sk_logfile_ud;comment:地址;type:varchar(128)"`
	DeviceId    int64     `gorm:"index:idx_sk_logfile_ud;comment:设备;type:bigint"`
	Status      int32     `gorm:"comment:状态;type:int;default:0"`
	BootIndex   int32     `gorm:"comment:状态;type:int;default:0"`
	TenantId    int64     `gorm:"comment:租户;type:bigint"`
	MerchantId  int64     `gorm:"comment:资源库;type:bigint"`
	OperationId int64     `gorm:"comment:操作;type:bigint"`
	Size        int64     `gorm:"comment:大小;type:bigint"`
	StartTime   time.Time `gorm:"comment:开始时间;type:timestamp with time zone"`
	EndTime     time.Time `gorm:"comment:结束时间;type:timestamp with time zone"`
}

func (Logfile) TableName() string {
	return "sk_logfile"
}

func (l *Logfile) FromBizLogfile(bl *biz.Logfile) error {
	if bl == nil {
		return errors.New("nil logfile")
	}
	l.BaseEntity = *NewBase()
	l.Url = bl.Url
	l.Size = bl.Size
	l.Module = bl.Module
	l.BootIndex = bl.BootIndex
	l.DeviceId = bl.DeviceId
	l.TenantId = bl.TenantId
	l.MerchantId = bl.MerchantId
	l.OperationId = bl.OperationId
	l.StartTime = bl.StartTime
	l.EndTime = bl.EndTime
	return nil
}

func (l *Logfile) ToBizLogfile() *biz.Logfile {
	logfile := &biz.Logfile{}
	logfile.Id = l.Id
	logfile.Url = l.Url
	logfile.Size = l.Size
	logfile.Module = l.Module
	logfile.Status = l.Status
	logfile.BootIndex = l.BootIndex
	logfile.DeviceId = l.DeviceId
	logfile.TenantId = l.TenantId
	logfile.MerchantId = l.MerchantId
	logfile.OperationId = l.OperationId
	logfile.StartTime = l.StartTime
	logfile.EndTime = l.EndTime
	logfile.CreatedTime = l.CreatedTime
	logfile.UpdatedTime = l.UpdatedTime
	// 超过指定时长未成功，状态默认为失败，规则：30分钟 +（文件大小/1G）* 20分钟
	factor := float64(l.Size) / float64(1<<30) * 20
	overtime := (time.Duration(factor) + 30) * time.Minute
	if l.Status == 0 && l.CreatedTime.Add(overtime).Before(time.Now()) {
		logfile.Status = 2
	}
	return logfile
}
