package gorm

import (
	"encoding/json"
	"errors"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"

	"github.com/lib/pq"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/sql/geom/ewkb"
	"gorm.io/datatypes"
)

type Deployment struct {
	Name         string         `gorm:"comment:名称;type:varchar(128)"`
	Tags         pq.StringArray `gorm:"comment:标签;type:text[]"`
	Images       datatypes.JSON `gorm:"comment:图片;type:jsonb"`
	Contacts     datatypes.JSON `gorm:"comment:联系人列表;type:jsonb"`
	Status       bool           `gorm:"comment:状态;type:bool;default:false"`
	Lnglat       ewkb.Point     `gorm:"comment:经纬度;type:geometry(Point,4326)"`
	Altitude     float32        `gorm:"comment:海拔高度;type:float;default:0"`
	Location     string         `gorm:"comment:位置;type:varchar(255)"`
	Time         *time.Time     `gorm:"comment:时间;type:timestamp without time zone"`
	RelatedChIds pq.Int64Array  `gorm:"comment:关联通道;type:bigint[]"`
}

func (d *Deployment) ToBiz() *biz.Deployment {
	var images biz.Images
	json.Unmarshal(d.Images, &images)
	imageList := []string{images.DeviceImg, images.EnvImg}
	if images.ShopImg != nil {
		imageList = append(imageList, *(images.ShopImg))
	}
	contacts := make([]biz.Contact, 0)
	json.Unmarshal(d.Contacts, &contacts)
	tags := lo.Ternary(d.Tags != nil, d.Tags, []string{})
	return &biz.Deployment{
		Name:         d.Name,
		Tags:         tags,
		Images:       images,
		Contacts:     contacts,
		Status:       d.Status,
		Lnglat:       d.Lnglat,
		Altitude:     d.Altitude,
		Location:     d.Location,
		Time:         d.Time,
		ImageList:    imageList,
		RelatedChIds: d.RelatedChIds,
	}
}

type Device struct {
	BaseEntity
	Sn              string             `gorm:"index;comment:标识码;type:varchar(50)"`
	Type            string             `gorm:"index;comment:型号;type:varchar(32)"`
	Model           biz.DeviceModel    `gorm:"index;comment:模型;type:varchar(32)"`
	Category        biz.DeviceCategory `gorm:"comment:类型;type:varchar(32)"`
	Source          biz.DeviceSource   `gorm:"comment:来源;type:varchar(32);default:DJI"`
	Status          biz.Status         `gorm:"comment:状态;type:int;default:0"`
	SourceSn        string             `gorm:"comment:源SN;type:varchar(64)"`
	TenantId        int64              `gorm:"index;comment:租户;type:bigint"`
	MerchantId      int64              `gorm:"index;comment:商户;type:bigint"`
	FlyerId         int64              `gorm:"comment:起飞员;type:bigint"`
	AvatarId        int64              `gorm:"comment:操作员;type:bigint"`
	VoyageId        *int64             `gorm:"comment:航次标识;type:bigint"`
	AirlineId       *int64             `gorm:"comment:航线标识;type:bigint"`
	MissionId       *int64             `gorm:"comment:任务标识;type:bigint"`
	CabinStatus     bool               `gorm:"comment:在舱状态;type:bool;default:true"`
	LockStatus      biz.LockStatus     `gorm:"comment:锁定状态;type:int;default:0"`
	AeroMode        biz.AeroMode       `gorm:"comment:飞行模式;type:int;default:0"`
	NetworkType     int32              `gorm:"comment:网络类型;type:int;default:2"`
	NetworkStatus   bool               `gorm:"comment:网络状态;type:bool;default:false"`
	StatusFlower    string             `gorm:"comment:状态流转;type:varchar(32);default:''"`
	SignalQuality   string             `gorm:"comment:信号质量;type:varchar(32);default:NONE"`
	FirmwareVersion string             `gorm:"comment:固件版本;type:varchar(32)"`
	Deployment      *Deployment        `gorm:"comment:部署信息;Embedded;EmbeddedPrefix:deployment_"`
	Speaker         datatypes.JSON     `gorm:"comment:喊话器装置;type:jsonb;default:'{}'"`
	Subdevices      datatypes.JSON     `gorm:"comment:子设备列表;type:jsonb;default:'{}'"`
	AeroCameras     datatypes.JSON     `gorm:"comment:飞行相机列表;type:jsonb;default:'{}'"`
	PropData        datatypes.JSON     `gorm:"comment:属性信息;type:jsonb;default:'{}'"`
	ExtraData       datatypes.JSON     `gorm:"comment:补充信息;type:jsonb;default:'{}'"`
	LaunchData      datatypes.JSON     `gorm:"comment:上次起飞;type:jsonb;default:'{}'"`
	UppedTime       *time.Time         `gorm:"comment:上行时间;type:timestamp without time zone"`
}

func (Device) TableName() string {
	return "sk_device"
}

func (d *Device) FromBizDevice(bd *biz.Device) error {
	if bd == nil {
		return errors.New("nil device")
	}
	extraData, _ := json.Marshal(bd.ExtraData)
	d.BaseEntity = *NewBase()
	d.Sn = bd.Sn
	d.Type = bd.Type
	d.Model = bd.Model
	d.Category = bd.Category
	d.Status = bd.Status
	d.Source = bd.Source
	d.SourceSn = bd.SourceSn
	d.TenantId = bd.TenantId
	d.MerchantId = bd.MerchantId
	d.ExtraData = extraData
	if bd.Deployment != nil {
		d.Deployment = &Deployment{
			Name:         d.Deployment.Name,
			Tags:         d.Deployment.Tags,
			Images:       d.Deployment.Images,
			Contacts:     d.Deployment.Contacts,
			Status:       d.Deployment.Status,
			Lnglat:       d.Deployment.Lnglat,
			Location:     d.Deployment.Location,
			Time:         d.Deployment.Time,
			RelatedChIds: d.Deployment.RelatedChIds,
		}
	}
	return nil
}

func (d *Device) ToBizDevice() *biz.Device {
	device := &biz.Device{}
	device.Id = d.Id
	device.Sn = d.Sn
	device.Type = d.Type
	device.Model = d.Model
	device.Category = d.Category
	device.Status = d.Status
	device.Source = d.Source
	device.SourceSn = d.SourceSn
	device.TenantId = d.TenantId
	device.MerchantId = d.MerchantId
	device.FlyerId = d.FlyerId
	device.AvatarId = d.AvatarId
	device.VoyageId = d.VoyageId
	device.AirlineId = d.AirlineId
	device.MissionId = d.MissionId
	device.AeroMode = d.AeroMode
	device.LockStatus = d.LockStatus
	device.CabinStatus = d.CabinStatus
	device.NetworkType = d.NetworkType
	device.NetworkStatus = d.NetworkStatus
	device.Gradient = biz.GradientOnline
	device.StatusFlower = d.StatusFlower
	gradest, gradend := "", ""
	if len(d.StatusFlower) > 0 {
		gradest = d.StatusFlower[0:1]
	}
	device.Gradest = biz.Gradient(gradest)
	if len(d.StatusFlower) > 1 {
		gradend = d.StatusFlower[1:2]
	}
	device.Gradend = biz.Gradient(gradend)
	device.SignalQuality = d.SignalQuality
	device.FirmwareVersion = d.FirmwareVersion
	device.UppedTime = d.UppedTime
	device.CreatedTime = d.CreatedTime
	device.UpdatedTime = d.UpdatedTime
	var propData biz.DeviceProperty
	json.Unmarshal(d.PropData, &propData)
	device.PropData = &propData
	var extraData biz.ExtraData
	json.Unmarshal(d.ExtraData, &extraData)
	device.ExtraData = extraData
	var launchData biz.LaunchData
	json.Unmarshal(d.LaunchData, &launchData)
	device.LaunchData = launchData
	var speaker biz.SpeakerWidget
	json.Unmarshal(d.Speaker, &speaker)
	device.Speaker = &speaker
	aeroCameras := make([]*biz.AeroCamera, 0)
	json.Unmarshal(d.AeroCameras, &aeroCameras)
	device.AeroCameras = aeroCameras
	subdevices := make([]*biz.DockSubdevice, 0)
	json.Unmarshal(d.Subdevices, &subdevices)
	device.Subdevices = subdevices
	device.Deployment = d.Deployment.ToBiz()
	return device
}
