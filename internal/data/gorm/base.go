package gorm

import (
	"encoding/json"
	"errors"
	"time"

	"gitlab.sensoro.com/skai/skai/pkg/id"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type BaseEntity struct {
	Id          int64     `gorm:"primaryKey;type:bigint;autoIncrement:false"`
	UpdatedTime time.Time `gorm:"autoUpdateTime:milli"`
	CreatedTime time.Time `gorm:"autoCreateTime:milli"`
}

func NewBase() *BaseEntity {
	return &BaseEntity{
		Id: id.NextID(),
	}
}

type BaseEntityWithIndex struct {
	Id          int64     `gorm:"primaryKey;type:bigint;autoIncrement:false"`
	UpdatedTime time.Time `gorm:"autoUpdateTime:milli"`
	CreatedTime time.Time `gorm:"autoCreateTime:milli;index"`
}

func NewBaseWithIndex() BaseEntityWithIndex {
	now := time.Now()
	return BaseEntityWithIndex{
		Id:          id.NextID(),
		UpdatedTime: now,
		CreatedTime: now,
	}
}

func NewJSONData(v interface{}) (datatypes.JSON, error) {
	raw, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}
	return datatypes.JSON(raw), nil
}

func IfRecordNotFoundErr(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}
