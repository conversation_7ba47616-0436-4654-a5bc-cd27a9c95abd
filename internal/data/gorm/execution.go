package gorm

import (
	"encoding/json"
	"errors"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gorm.io/datatypes"
)

type Execution struct {
	BaseEntity
	Status      biz.OperationStatus `gorm:"comment:状态;type:varchar(32)"`
	Reason      biz.FailureReason   `gorm:"comment:原因;type:int;default:0"`
	TenantId    int64               `gorm:"index;comment:租户;type:bigint"`
	MerchantId  int64               `gorm:"index;comment:商户;type:bigint"`
	MissionId   int64               `gorm:"comment:任务标识;type:bigint"`
	VoyageId    int64               `gorm:"comment:航次标识;type:bigint"`
	DeviceId    int64               `gorm:"comment:设备标识;type:bigint"`
	AirlineId   int64               `gorm:"comment:航线标识;type:bigint"`
	AlarmCount  int32               `gorm:"comment:告警次数;type:int;default:0"`
	OperationId int64               `gorm:"comment:操作标识;type:bigint"`
	Mission     datatypes.JSON      `gorm:"comment:任务快照;type:jsonb"`
}

func (Execution) TableName() string {
	return "sk_execution"
}

func (e *Execution) FromBizExecution(be *biz.Execution) error {
	if be == nil {
		return errors.New("nil execution")
	}
	e.BaseEntity = *NewBase()
	e.Status = be.Status
	e.Reason = be.Reason
	e.MissionId = be.MissionId
	e.DeviceId = be.DeviceId
	e.AirlineId = be.AirlineId
	e.TenantId = be.TenantId
	e.MerchantId = be.MerchantId
	e.OperationId = be.OperationId
	snapMission, _ := json.Marshal(be.Mission)
	e.Mission = snapMission
	return nil
}

func (e *Execution) ToBizExecution() *biz.Execution {
	execution := &biz.Execution{}
	execution.Id = e.Id
	execution.Status = e.Status
	execution.Reason = e.Reason
	execution.MissionId = e.MissionId
	execution.VoyageId = e.VoyageId
	execution.DeviceId = e.DeviceId
	execution.AirlineId = e.AirlineId
	execution.TenantId = e.TenantId
	execution.MerchantId = e.MerchantId
	execution.AlarmCount = e.AlarmCount
	execution.OperationId = e.OperationId
	execution.CreatedTime = e.CreatedTime
	execution.UpdatedTime = e.UpdatedTime
	var snapMission biz.SnapMission
	json.Unmarshal(e.Mission, &snapMission)
	execution.Mission = &snapMission
	return execution
}
