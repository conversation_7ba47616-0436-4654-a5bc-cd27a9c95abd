package gorm

import (
	"encoding/json"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/pkg/id"
	"gorm.io/datatypes"
)

type Annotation struct {
	BaseEntityWithIndex
	TenantId    int64          `gorm:"comment:租户id"`
	MerchantId  int64          `gorm:"comment:商户id"`
	AvatarId    int64          `gorm:"comment:标注人"`
	VoyageId    int64          `gorm:"comment:关联航次"`
	SubjectId   int64          `gorm:"comment:预警主题id;"`
	Type        string         `gorm:"comment:预警事件类型;type:varchar(255)"`
	Media       datatypes.JSON `gorm:"comment:关联图片;type:jsonb;default:'{}'"`
	Description string         `gorm:"comment:描述;type:varchar(502)"`
}

func (Annotation) TableName() string {
	return "sk_annotation"
}

func (a *Annotation) FromBiz(ba *biz.Annotation) {
	if ba == nil {
		return
	}
	if ba.Id == 0 {
		a.BaseEntityWithIndex = NewBaseWithIndex()
		// id=1表示无事件标注
		if a.BaseEntityWithIndex.Id == 1 {
			a.BaseEntityWithIndex.Id = id.NextID()
		}
	} else {
		a.Id = ba.Id
		a.CreatedTime = ba.CreatedTime
		a.UpdatedTime = ba.UpdatedTime
	}
	a.TenantId = ba.TenantId
	a.MerchantId = ba.MerchantId
	a.AvatarId = ba.AvatarId
	a.VoyageId = ba.VoyageId
	a.SubjectId = ba.SubjectId
	a.Type = ba.Type.String()
	media, _ := NewJSONData(ba.Media)
	a.Media = media
	a.Description = ba.Description
}

func (a *Annotation) ToBiz() *biz.Annotation {
	ba := &biz.Annotation{
		Id:          a.Id,
		TenantId:    a.TenantId,
		MerchantId:  a.MerchantId,
		CreatedTime: a.CreatedTime,
		UpdatedTime: a.UpdatedTime,
		AvatarId:    a.AvatarId,
		VoyageId:    a.VoyageId,
		SubjectId:   a.SubjectId,
		Type:        biz.AiEventType(a.Type),
		Description: a.Description,
	}
	if len(a.Media) > 0 {
		ba.Media = make([]biz.MediaWithArea, 0)
		json.Unmarshal(a.Media, &ba.Media)
	}
	return ba
}
