package gorm

import (
	"encoding/json"
	"errors"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type Subject struct {
	BaseEntity
	DeletedAt    gorm.DeletedAt
	Name         string          `gorm:"comment:名称;type:varchar(32)"`
	EventType    biz.AiEventType `gorm:"comment:事件类型;type:varchar(32)"`
	Description  string          `gorm:"comment:描述;type:varchar(255)"`
	TenantId     int64           `gorm:"comment:租户;type:bigint"`
	ProjectId    int64           `gorm:"index;comment:项目;type:bigint"`
	AvatarId     int64           `gorm:"comment:创建者;type:bigint"`
	EditorId     int64           `gorm:"comment:编辑者;type:bigint"`
	ReportCount  int32           `gorm:"comment:上报数量;type:int;default:0"`
	PendingCount int32           `gorm:"comment:待处理数;type:int;default:0"`
	FinishCount  int32           `gorm:"comment:完结数量;type:int;default:0"`
	EditedTime   time.Time       `gorm:"comment:编辑时间;type:timestamp with time zone"`
	NoticeRules  datatypes.JSON  `gorm:"comment:通知规则;type:jsonb"`
}

func (Subject) TableName() string {
	return "sk_subject"
}

func (s *Subject) FromBizSubject(bs *biz.Subject) error {
	if bs == nil {
		return errors.New("nil subject")
	}
	s.BaseEntity = *NewBase()
	s.Name = bs.Name
	s.EventType = bs.EventType
	s.Description = bs.Description
	s.TenantId = bs.TenantId
	s.ProjectId = bs.ProjectId
	s.AvatarId = bs.AvatarId
	s.EditorId = bs.EditorId
	s.EditedTime = bs.EditedTime
	noticeRules, _ := json.Marshal(bs.NoticeRules)
	s.NoticeRules = noticeRules
	return nil
}

func (s *Subject) ToBizSubject() *biz.Subject {
	subject := &biz.Subject{}
	subject.Id = s.Id
	subject.Name = s.Name
	subject.EventType = s.EventType
	subject.Description = s.Description
	subject.ReportCount = s.ReportCount
	subject.PendingCount = s.PendingCount
	subject.FinishCount = s.FinishCount
	subject.TenantId = s.TenantId
	subject.ProjectId = s.ProjectId
	subject.AvatarId = s.AvatarId
	subject.EditorId = s.EditorId
	subject.EditedTime = s.EditedTime
	subject.CreatedTime = s.CreatedTime
	subject.UpdatedTime = s.UpdatedTime
	subject.IsDeleted = s.DeletedAt.Valid
	noticeRules := make([]*biz.NoticeRule, 0)
	json.Unmarshal(s.NoticeRules, &noticeRules)
	subject.NoticeRules = noticeRules
	return subject
}
