// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.3
// source: internal/conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server *Server `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data   *Data   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Http        *Server_HTTP        `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc        *Server_GRPC        `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
	Jwt         *Server_JWT         `protobuf:"bytes,3,opt,name=jwt,proto3" json:"jwt,omitempty"`
	Auth        *Server_Auth        `protobuf:"bytes,4,opt,name=auth,proto3" json:"auth,omitempty"`
	Push        *Server_Push        `protobuf:"bytes,5,opt,name=push,proto3" json:"push,omitempty"`
	Device      *Server_Device      `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`
	Analyser    *Server_Analyser    `protobuf:"bytes,7,opt,name=analyser,proto3" json:"analyser,omitempty"`
	Skai        *Server_Skai        `protobuf:"bytes,8,opt,name=skai,proto3" json:"skai,omitempty"`
	Delay       *Server_Delay       `protobuf:"bytes,9,opt,name=delay,proto3" json:"delay,omitempty"`
	Visual      *Server_Visual      `protobuf:"bytes,10,opt,name=visual,proto3" json:"visual,omitempty"`
	Tracer      string              `protobuf:"bytes,11,opt,name=tracer,proto3" json:"tracer,omitempty"`
	LogLevel    string              `protobuf:"bytes,12,opt,name=log_level,json=logLevel,proto3" json:"log_level,omitempty"`
	ArchiveTask *Server_ArchiveTask `protobuf:"bytes,13,opt,name=archiveTask,proto3" json:"archiveTask,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

func (x *Server) GetJwt() *Server_JWT {
	if x != nil {
		return x.Jwt
	}
	return nil
}

func (x *Server) GetAuth() *Server_Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *Server) GetPush() *Server_Push {
	if x != nil {
		return x.Push
	}
	return nil
}

func (x *Server) GetDevice() *Server_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *Server) GetAnalyser() *Server_Analyser {
	if x != nil {
		return x.Analyser
	}
	return nil
}

func (x *Server) GetSkai() *Server_Skai {
	if x != nil {
		return x.Skai
	}
	return nil
}

func (x *Server) GetDelay() *Server_Delay {
	if x != nil {
		return x.Delay
	}
	return nil
}

func (x *Server) GetVisual() *Server_Visual {
	if x != nil {
		return x.Visual
	}
	return nil
}

func (x *Server) GetTracer() string {
	if x != nil {
		return x.Tracer
	}
	return ""
}

func (x *Server) GetLogLevel() string {
	if x != nil {
		return x.LogLevel
	}
	return ""
}

func (x *Server) GetArchiveTask() *Server_ArchiveTask {
	if x != nil {
		return x.ArchiveTask
	}
	return nil
}

type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database    *Data_Database    `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Redis       *Data_Redis       `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	Kafka       *Data_Kafka       `protobuf:"bytes,3,opt,name=kafka,proto3" json:"kafka,omitempty"`
	Mqtt        *Data_Mqtt        `protobuf:"bytes,4,opt,name=mqtt,proto3" json:"mqtt,omitempty"`
	S3          *Data_S3          `protobuf:"bytes,5,opt,name=s3,proto3" json:"s3,omitempty"`
	Tdengine    *Data_TDengine    `protobuf:"bytes,6,opt,name=tdengine,proto3" json:"tdengine,omitempty"`
	Dji         *Data_DJI         `protobuf:"bytes,7,opt,name=dji,proto3" json:"dji,omitempty"`
	Skai        *Data_Skai        `protobuf:"bytes,8,opt,name=skai,proto3" json:"skai,omitempty"`
	Media       *Data_Media       `protobuf:"bytes,9,opt,name=media,proto3" json:"media,omitempty"`
	Snapshot    *Data_Snapshot    `protobuf:"bytes,10,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
	Maintenance *Data_Maintenance `protobuf:"bytes,11,opt,name=maintenance,proto3" json:"maintenance,omitempty"`
	Algorithm   *Data_Algorithm   `protobuf:"bytes,12,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
	SecondaryS3 *Data_S3          `protobuf:"bytes,13,opt,name=secondaryS3,proto3" json:"secondaryS3,omitempty"`
}

func (x *Data) Reset() {
	*x = Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *Data_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetKafka() *Data_Kafka {
	if x != nil {
		return x.Kafka
	}
	return nil
}

func (x *Data) GetMqtt() *Data_Mqtt {
	if x != nil {
		return x.Mqtt
	}
	return nil
}

func (x *Data) GetS3() *Data_S3 {
	if x != nil {
		return x.S3
	}
	return nil
}

func (x *Data) GetTdengine() *Data_TDengine {
	if x != nil {
		return x.Tdengine
	}
	return nil
}

func (x *Data) GetDji() *Data_DJI {
	if x != nil {
		return x.Dji
	}
	return nil
}

func (x *Data) GetSkai() *Data_Skai {
	if x != nil {
		return x.Skai
	}
	return nil
}

func (x *Data) GetMedia() *Data_Media {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *Data) GetSnapshot() *Data_Snapshot {
	if x != nil {
		return x.Snapshot
	}
	return nil
}

func (x *Data) GetMaintenance() *Data_Maintenance {
	if x != nil {
		return x.Maintenance
	}
	return nil
}

func (x *Data) GetAlgorithm() *Data_Algorithm {
	if x != nil {
		return x.Algorithm
	}
	return nil
}

func (x *Data) GetSecondaryS3() *Data_S3 {
	if x != nil {
		return x.SecondaryS3
	}
	return nil
}

type Server_HTTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_Auth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Server_Auth) Reset() {
	*x = Server_Auth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Auth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Auth) ProtoMessage() {}

func (x *Server_Auth) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Auth.ProtoReflect.Descriptor instead.
func (*Server_Auth) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 2}
}

func (x *Server_Auth) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Server_Push struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Server_Push) Reset() {
	*x = Server_Push{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Push) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Push) ProtoMessage() {}

func (x *Server_Push) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Push.ProtoReflect.Descriptor instead.
func (*Server_Push) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 3}
}

func (x *Server_Push) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Server_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Server_Device) Reset() {
	*x = Server_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Device) ProtoMessage() {}

func (x *Server_Device) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Device.ProtoReflect.Descriptor instead.
func (*Server_Device) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 4}
}

func (x *Server_Device) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Server_Analyser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Triton string `protobuf:"bytes,2,opt,name=triton,proto3" json:"triton,omitempty"`
}

func (x *Server_Analyser) Reset() {
	*x = Server_Analyser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Analyser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Analyser) ProtoMessage() {}

func (x *Server_Analyser) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Analyser.ProtoReflect.Descriptor instead.
func (*Server_Analyser) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 5}
}

func (x *Server_Analyser) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Server_Analyser) GetTriton() string {
	if x != nil {
		return x.Triton
	}
	return ""
}

type Server_Delay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url      string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Strategy string `protobuf:"bytes,2,opt,name=strategy,proto3" json:"strategy,omitempty"`
}

func (x *Server_Delay) Reset() {
	*x = Server_Delay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Delay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Delay) ProtoMessage() {}

func (x *Server_Delay) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Delay.ProtoReflect.Descriptor instead.
func (*Server_Delay) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 6}
}

func (x *Server_Delay) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Server_Delay) GetStrategy() string {
	if x != nil {
		return x.Strategy
	}
	return ""
}

type Server_Skai struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Server_Skai) Reset() {
	*x = Server_Skai{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Skai) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Skai) ProtoMessage() {}

func (x *Server_Skai) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Skai.ProtoReflect.Descriptor instead.
func (*Server_Skai) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 7}
}

func (x *Server_Skai) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Server_JWT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Algorithm string `protobuf:"bytes,1,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
	Secret    string `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty"`
}

func (x *Server_JWT) Reset() {
	*x = Server_JWT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_JWT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_JWT) ProtoMessage() {}

func (x *Server_JWT) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_JWT.ProtoReflect.Descriptor instead.
func (*Server_JWT) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 8}
}

func (x *Server_JWT) GetAlgorithm() string {
	if x != nil {
		return x.Algorithm
	}
	return ""
}

func (x *Server_JWT) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

type Server_Visual struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Server_Visual) Reset() {
	*x = Server_Visual{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Visual) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Visual) ProtoMessage() {}

func (x *Server_Visual) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Visual.ProtoReflect.Descriptor instead.
func (*Server_Visual) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 9}
}

func (x *Server_Visual) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Server_ArchiveTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Interval int32 `protobuf:"varint,1,opt,name=interval,proto3" json:"interval,omitempty"`
	// 至少多少天后的文件才会被归档
	MinFileLife int32 `protobuf:"varint,2,opt,name=minFileLife,proto3" json:"minFileLife,omitempty"`
}

func (x *Server_ArchiveTask) Reset() {
	*x = Server_ArchiveTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_ArchiveTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_ArchiveTask) ProtoMessage() {}

func (x *Server_ArchiveTask) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_ArchiveTask.ProtoReflect.Descriptor instead.
func (*Server_ArchiveTask) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 10}
}

func (x *Server_ArchiveTask) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *Server_ArchiveTask) GetMinFileLife() int32 {
	if x != nil {
		return x.MinFileLife
	}
	return 0
}

type Data_Database struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Driver      string `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Uri         string `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`
	MaxOpenConn int32  `protobuf:"varint,3,opt,name=maxOpenConn,proto3" json:"maxOpenConn,omitempty"`
	MaxIdleConn int32  `protobuf:"varint,4,opt,name=maxIdleConn,proto3" json:"maxIdleConn,omitempty"`
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Data_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Data_Database) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *Data_Database) GetMaxOpenConn() int32 {
	if x != nil {
		return x.MaxOpenConn
	}
	return 0
}

func (x *Data_Database) GetMaxIdleConn() int32 {
	if x != nil {
		return x.MaxIdleConn
	}
	return 0
}

type Data_Redis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// oneof cluster base
	ClientType   string               `protobuf:"bytes,1,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	Addr         string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Password     string               `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Db           int32                `protobuf:"varint,4,opt,name=db,proto3" json:"db,omitempty"`
	DialTimeout  *durationpb.Duration `protobuf:"bytes,6,opt,name=dial_timeout,json=dialTimeout,proto3" json:"dial_timeout,omitempty"`
	ReadTimeout  *durationpb.Duration `protobuf:"bytes,7,opt,name=read_timeout,json=readTimeout,proto3" json:"read_timeout,omitempty"`
	WriteTimeout *durationpb.Duration `protobuf:"bytes,8,opt,name=write_timeout,json=writeTimeout,proto3" json:"write_timeout,omitempty"`
}

func (x *Data_Redis) Reset() {
	*x = Data_Redis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Redis) ProtoMessage() {}

func (x *Data_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Redis.ProtoReflect.Descriptor instead.
func (*Data_Redis) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Data_Redis) GetClientType() string {
	if x != nil {
		return x.ClientType
	}
	return ""
}

func (x *Data_Redis) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Data_Redis) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Data_Redis) GetDb() int32 {
	if x != nil {
		return x.Db
	}
	return 0
}

func (x *Data_Redis) GetDialTimeout() *durationpb.Duration {
	if x != nil {
		return x.DialTimeout
	}
	return nil
}

func (x *Data_Redis) GetReadTimeout() *durationpb.Duration {
	if x != nil {
		return x.ReadTimeout
	}
	return nil
}

func (x *Data_Redis) GetWriteTimeout() *durationpb.Duration {
	if x != nil {
		return x.WriteTimeout
	}
	return nil
}

type Data_Kafka struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Brokers  string                `protobuf:"bytes,1,opt,name=brokers,proto3" json:"brokers,omitempty"`
	GroupId  string                `protobuf:"bytes,2,opt,name=groupId,proto3" json:"groupId,omitempty"`
	Produces *Data_Kafka_Producers `protobuf:"bytes,3,opt,name=produces,proto3" json:"produces,omitempty"`
	Consumes *Data_Kafka_Consumers `protobuf:"bytes,4,opt,name=consumes,proto3" json:"consumes,omitempty"`
}

func (x *Data_Kafka) Reset() {
	*x = Data_Kafka{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Kafka) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Kafka) ProtoMessage() {}

func (x *Data_Kafka) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Kafka.ProtoReflect.Descriptor instead.
func (*Data_Kafka) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Data_Kafka) GetBrokers() string {
	if x != nil {
		return x.Brokers
	}
	return ""
}

func (x *Data_Kafka) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Data_Kafka) GetProduces() *Data_Kafka_Producers {
	if x != nil {
		return x.Produces
	}
	return nil
}

func (x *Data_Kafka) GetConsumes() *Data_Kafka_Consumers {
	if x != nil {
		return x.Consumes
	}
	return nil
}

type Data_Mqtt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url         string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	SuperToken  string `protobuf:"bytes,2,opt,name=super_token,json=superToken,proto3" json:"super_token,omitempty"`
	RestfulUrl  string `protobuf:"bytes,3,opt,name=restful_url,json=restfulUrl,proto3" json:"restful_url,omitempty"`
	RestfulUser string `protobuf:"bytes,4,opt,name=restful_user,json=restfulUser,proto3" json:"restful_user,omitempty"`
	RestfulPass string `protobuf:"bytes,5,opt,name=restful_pass,json=restfulPass,proto3" json:"restful_pass,omitempty"`
	PublicUrl   string `protobuf:"bytes,6,opt,name=public_url,json=publicUrl,proto3" json:"public_url,omitempty"`
}

func (x *Data_Mqtt) Reset() {
	*x = Data_Mqtt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Mqtt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Mqtt) ProtoMessage() {}

func (x *Data_Mqtt) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Mqtt.ProtoReflect.Descriptor instead.
func (*Data_Mqtt) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 3}
}

func (x *Data_Mqtt) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Data_Mqtt) GetSuperToken() string {
	if x != nil {
		return x.SuperToken
	}
	return ""
}

func (x *Data_Mqtt) GetRestfulUrl() string {
	if x != nil {
		return x.RestfulUrl
	}
	return ""
}

func (x *Data_Mqtt) GetRestfulUser() string {
	if x != nil {
		return x.RestfulUser
	}
	return ""
}

func (x *Data_Mqtt) GetRestfulPass() string {
	if x != nil {
		return x.RestfulPass
	}
	return ""
}

func (x *Data_Mqtt) GetPublicUrl() string {
	if x != nil {
		return x.PublicUrl
	}
	return ""
}

type Data_S3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EndpointUrl string `protobuf:"bytes,1,opt,name=endpoint_url,json=endpointUrl,proto3" json:"endpoint_url,omitempty"`
	AccessKey   string `protobuf:"bytes,2,opt,name=access_key,json=accessKey,proto3" json:"access_key,omitempty"`
	SecretKey   string `protobuf:"bytes,3,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	BucketName  string `protobuf:"bytes,4,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	// yes or no
	PathStyle      string `protobuf:"bytes,5,opt,name=path_style,json=pathStyle,proto3" json:"path_style,omitempty"`
	PublicEndpoint string `protobuf:"bytes,6,opt,name=public_endpoint,json=publicEndpoint,proto3" json:"public_endpoint,omitempty"`
	Arn            string `protobuf:"bytes,7,opt,name=arn,proto3" json:"arn,omitempty"`
	Region         string `protobuf:"bytes,8,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *Data_S3) Reset() {
	*x = Data_S3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_S3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_S3) ProtoMessage() {}

func (x *Data_S3) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_S3.ProtoReflect.Descriptor instead.
func (*Data_S3) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 4}
}

func (x *Data_S3) GetEndpointUrl() string {
	if x != nil {
		return x.EndpointUrl
	}
	return ""
}

func (x *Data_S3) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *Data_S3) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *Data_S3) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *Data_S3) GetPathStyle() string {
	if x != nil {
		return x.PathStyle
	}
	return ""
}

func (x *Data_S3) GetPublicEndpoint() string {
	if x != nil {
		return x.PublicEndpoint
	}
	return ""
}

func (x *Data_S3) GetArn() string {
	if x != nil {
		return x.Arn
	}
	return ""
}

func (x *Data_S3) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type Data_TDengine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Brokers             string              `protobuf:"bytes,1,opt,name=brokers,proto3" json:"brokers,omitempty"`
	Port                int32               `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Auth                *Data_TDengine_Auth `protobuf:"bytes,3,opt,name=auth,proto3" json:"auth,omitempty"`
	MaxSockets          int32               `protobuf:"varint,4,opt,name=max_sockets,json=maxSockets,proto3" json:"max_sockets,omitempty"`
	HealthCheckInterval int32               `protobuf:"varint,5,opt,name=health_check_interval,json=healthCheckInterval,proto3" json:"health_check_interval,omitempty"`
}

func (x *Data_TDengine) Reset() {
	*x = Data_TDengine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_TDengine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_TDengine) ProtoMessage() {}

func (x *Data_TDengine) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_TDengine.ProtoReflect.Descriptor instead.
func (*Data_TDengine) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 5}
}

func (x *Data_TDengine) GetBrokers() string {
	if x != nil {
		return x.Brokers
	}
	return ""
}

func (x *Data_TDengine) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Data_TDengine) GetAuth() *Data_TDengine_Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *Data_TDengine) GetMaxSockets() int32 {
	if x != nil {
		return x.MaxSockets
	}
	return 0
}

func (x *Data_TDengine) GetHealthCheckInterval() int32 {
	if x != nil {
		return x.HealthCheckInterval
	}
	return 0
}

type Data_DJI struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ntp        string `protobuf:"bytes,1,opt,name=ntp,proto3" json:"ntp,omitempty"`
	Url        string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	AppId      string `protobuf:"bytes,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppKey     string `protobuf:"bytes,4,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`
	AppLicense string `protobuf:"bytes,5,opt,name=app_license,json=appLicense,proto3" json:"app_license,omitempty"`
}

func (x *Data_DJI) Reset() {
	*x = Data_DJI{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_DJI) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_DJI) ProtoMessage() {}

func (x *Data_DJI) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_DJI.ProtoReflect.Descriptor instead.
func (*Data_DJI) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 6}
}

func (x *Data_DJI) GetNtp() string {
	if x != nil {
		return x.Ntp
	}
	return ""
}

func (x *Data_DJI) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Data_DJI) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Data_DJI) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *Data_DJI) GetAppLicense() string {
	if x != nil {
		return x.AppLicense
	}
	return ""
}

type Data_Skai struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerEp  string `protobuf:"bytes,1,opt,name=server_ep,json=serverEp,proto3" json:"server_ep,omitempty"`
	ConnectEp string `protobuf:"bytes,2,opt,name=connect_ep,json=connectEp,proto3" json:"connect_ep,omitempty"`
	PublicUrl string `protobuf:"bytes,3,opt,name=public_url,json=publicUrl,proto3" json:"public_url,omitempty"`
	CloudUrl  string `protobuf:"bytes,4,opt,name=cloud_url,json=cloudUrl,proto3" json:"cloud_url,omitempty"`
}

func (x *Data_Skai) Reset() {
	*x = Data_Skai{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Skai) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Skai) ProtoMessage() {}

func (x *Data_Skai) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Skai.ProtoReflect.Descriptor instead.
func (*Data_Skai) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 7}
}

func (x *Data_Skai) GetServerEp() string {
	if x != nil {
		return x.ServerEp
	}
	return ""
}

func (x *Data_Skai) GetConnectEp() string {
	if x != nil {
		return x.ConnectEp
	}
	return ""
}

func (x *Data_Skai) GetPublicUrl() string {
	if x != nil {
		return x.PublicUrl
	}
	return ""
}

func (x *Data_Skai) GetCloudUrl() string {
	if x != nil {
		return x.CloudUrl
	}
	return ""
}

type Data_Media struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdminToken string `protobuf:"bytes,1,opt,name=admin_token,json=adminToken,proto3" json:"admin_token,omitempty"`
	Url        string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	PublicUrl  string `protobuf:"bytes,3,opt,name=public_url,json=publicUrl,proto3" json:"public_url,omitempty"`
	RestPort   int32  `protobuf:"varint,4,opt,name=rest_port,json=restPort,proto3" json:"rest_port,omitempty"`
	HttpPort   int32  `protobuf:"varint,5,opt,name=http_port,json=httpPort,proto3" json:"http_port,omitempty"`
	RtmpPort   int32  `protobuf:"varint,6,opt,name=rtmp_port,json=rtmpPort,proto3" json:"rtmp_port,omitempty"`
	RtspPort   int32  `protobuf:"varint,7,opt,name=rtsp_port,json=rtspPort,proto3" json:"rtsp_port,omitempty"`
}

func (x *Data_Media) Reset() {
	*x = Data_Media{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Media) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Media) ProtoMessage() {}

func (x *Data_Media) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Media.ProtoReflect.Descriptor instead.
func (*Data_Media) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 8}
}

func (x *Data_Media) GetAdminToken() string {
	if x != nil {
		return x.AdminToken
	}
	return ""
}

func (x *Data_Media) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Data_Media) GetPublicUrl() string {
	if x != nil {
		return x.PublicUrl
	}
	return ""
}

func (x *Data_Media) GetRestPort() int32 {
	if x != nil {
		return x.RestPort
	}
	return 0
}

func (x *Data_Media) GetHttpPort() int32 {
	if x != nil {
		return x.HttpPort
	}
	return 0
}

func (x *Data_Media) GetRtmpPort() int32 {
	if x != nil {
		return x.RtmpPort
	}
	return 0
}

func (x *Data_Media) GetRtspPort() int32 {
	if x != nil {
		return x.RtspPort
	}
	return 0
}

type Data_Snapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoint string `protobuf:"bytes,1,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
}

func (x *Data_Snapshot) Reset() {
	*x = Data_Snapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Snapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Snapshot) ProtoMessage() {}

func (x *Data_Snapshot) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Snapshot.ProtoReflect.Descriptor instead.
func (*Data_Snapshot) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 9}
}

func (x *Data_Snapshot) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

type Data_Maintenance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mobile   string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Template string `protobuf:"bytes,2,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *Data_Maintenance) Reset() {
	*x = Data_Maintenance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Maintenance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Maintenance) ProtoMessage() {}

func (x *Data_Maintenance) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Maintenance.ProtoReflect.Descriptor instead.
func (*Data_Maintenance) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 10}
}

func (x *Data_Maintenance) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *Data_Maintenance) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

type Data_Algorithm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SelfUrl      string `protobuf:"bytes,1,opt,name=self_url,json=selfUrl,proto3" json:"self_url,omitempty"`
	GddiUrl      string `protobuf:"bytes,2,opt,name=gddi_url,json=gddiUrl,proto3" json:"gddi_url,omitempty"`
	ChatUrl      string `protobuf:"bytes,3,opt,name=chat_url,json=chatUrl,proto3" json:"chat_url,omitempty"`
	GddiToken    string `protobuf:"bytes,4,opt,name=gddi_token,json=gddiToken,proto3" json:"gddi_token,omitempty"`
	GddiFloater  string `protobuf:"bytes,5,opt,name=gddi_floater,json=gddiFloater,proto3" json:"gddi_floater,omitempty"`
	GddiFirework string `protobuf:"bytes,6,opt,name=gddi_firework,json=gddiFirework,proto3" json:"gddi_firework,omitempty"`
	ChatSmokeye  string `protobuf:"bytes,7,opt,name=chat_smokeye,json=chatSmokeye,proto3" json:"chat_smokeye,omitempty"`
	ChatFloater  string `protobuf:"bytes,8,opt,name=chat_floater,json=chatFloater,proto3" json:"chat_floater,omitempty"`
}

func (x *Data_Algorithm) Reset() {
	*x = Data_Algorithm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Algorithm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Algorithm) ProtoMessage() {}

func (x *Data_Algorithm) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Algorithm.ProtoReflect.Descriptor instead.
func (*Data_Algorithm) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 11}
}

func (x *Data_Algorithm) GetSelfUrl() string {
	if x != nil {
		return x.SelfUrl
	}
	return ""
}

func (x *Data_Algorithm) GetGddiUrl() string {
	if x != nil {
		return x.GddiUrl
	}
	return ""
}

func (x *Data_Algorithm) GetChatUrl() string {
	if x != nil {
		return x.ChatUrl
	}
	return ""
}

func (x *Data_Algorithm) GetGddiToken() string {
	if x != nil {
		return x.GddiToken
	}
	return ""
}

func (x *Data_Algorithm) GetGddiFloater() string {
	if x != nil {
		return x.GddiFloater
	}
	return ""
}

func (x *Data_Algorithm) GetGddiFirework() string {
	if x != nil {
		return x.GddiFirework
	}
	return ""
}

func (x *Data_Algorithm) GetChatSmokeye() string {
	if x != nil {
		return x.ChatSmokeye
	}
	return ""
}

func (x *Data_Algorithm) GetChatFloater() string {
	if x != nil {
		return x.ChatFloater
	}
	return ""
}

type Data_KafkaOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Topic string `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *Data_KafkaOptions) Reset() {
	*x = Data_KafkaOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_KafkaOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_KafkaOptions) ProtoMessage() {}

func (x *Data_KafkaOptions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_KafkaOptions.ProtoReflect.Descriptor instead.
func (*Data_KafkaOptions) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2, 0}
}

func (x *Data_KafkaOptions) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type Data_Kafka_Producers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subsystem *Data_KafkaOptions `protobuf:"bytes,1,opt,name=subsystem,proto3" json:"subsystem,omitempty"`
}

func (x *Data_Kafka_Producers) Reset() {
	*x = Data_Kafka_Producers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Kafka_Producers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Kafka_Producers) ProtoMessage() {}

func (x *Data_Kafka_Producers) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Kafka_Producers.ProtoReflect.Descriptor instead.
func (*Data_Kafka_Producers) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2, 1}
}

func (x *Data_Kafka_Producers) GetSubsystem() *Data_KafkaOptions {
	if x != nil {
		return x.Subsystem
	}
	return nil
}

type Data_Kafka_Consumers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SnapshotEvent  *Data_KafkaOptions `protobuf:"bytes,1,opt,name=snapshotEvent,proto3" json:"snapshotEvent,omitempty"`
	AlarmDomainMsg *Data_KafkaOptions `protobuf:"bytes,2,opt,name=alarmDomainMsg,proto3" json:"alarmDomainMsg,omitempty"`
}

func (x *Data_Kafka_Consumers) Reset() {
	*x = Data_Kafka_Consumers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Kafka_Consumers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Kafka_Consumers) ProtoMessage() {}

func (x *Data_Kafka_Consumers) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Kafka_Consumers.ProtoReflect.Descriptor instead.
func (*Data_Kafka_Consumers) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2, 2}
}

func (x *Data_Kafka_Consumers) GetSnapshotEvent() *Data_KafkaOptions {
	if x != nil {
		return x.SnapshotEvent
	}
	return nil
}

func (x *Data_Kafka_Consumers) GetAlarmDomainMsg() *Data_KafkaOptions {
	if x != nil {
		return x.AlarmDomainMsg
	}
	return nil
}

type Data_TDengine_Auth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *Data_TDengine_Auth) Reset() {
	*x = Data_TDengine_Auth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_TDengine_Auth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_TDengine_Auth) ProtoMessage() {}

func (x *Data_TDengine_Auth) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_TDengine_Auth.ProtoReflect.Descriptor instead.
func (*Data_TDengine_Auth) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 5, 0}
}

func (x *Data_TDengine_Auth) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Data_TDengine_Auth) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

var File_internal_conf_conf_proto protoreflect.FileDescriptor

var file_internal_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5d, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x74, 0x73, 0x74,
	0x72, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12,
	0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xac, 0x09, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x2b, 0x0a, 0x04, 0x68, 0x74, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x04, 0x68, 0x74, 0x74, 0x70, 0x12, 0x2b, 0x0a,
	0x04, 0x67, 0x72, 0x70, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x47, 0x52, 0x50, 0x43, 0x52, 0x04, 0x67, 0x72, 0x70, 0x63, 0x12, 0x28, 0x0a, 0x03, 0x6a, 0x77,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4a, 0x57, 0x54, 0x52,
	0x03, 0x6a, 0x77, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x04, 0x61, 0x75, 0x74,
	0x68, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x75, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x52, 0x04, 0x70, 0x75, 0x73, 0x68, 0x12, 0x31,
	0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x37, 0x0a, 0x08, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x52, 0x08, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x73, 0x6b,
	0x61, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x6b, 0x61,
	0x69, 0x52, 0x04, 0x73, 0x6b, 0x61, 0x69, 0x12, 0x2e, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x61, 0x79,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x61, 0x79,
	0x52, 0x05, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x31, 0x0a, 0x06, 0x76, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x52, 0x06, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x40, 0x0a, 0x0b, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x0b, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x1a, 0x69, 0x0a, 0x04, 0x48, 0x54, 0x54, 0x50, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0x69, 0x0a, 0x04,
	0x47, 0x52, 0x50, 0x43, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12,
	0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64,
	0x64, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0x18, 0x0a, 0x04, 0x41, 0x75, 0x74, 0x68, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x1a, 0x18, 0x0a, 0x04, 0x50, 0x75, 0x73, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a, 0x1a, 0x0a, 0x06, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a, 0x34, 0x0a, 0x08, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x72, 0x69, 0x74, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x72, 0x69, 0x74, 0x6f, 0x6e, 0x1a, 0x35, 0x0a,
	0x05, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x1a, 0x18, 0x0a, 0x04, 0x53, 0x6b, 0x61, 0x69, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a, 0x3b,
	0x0a, 0x03, 0x4a, 0x57, 0x54, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74,
	0x68, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69,
	0x74, 0x68, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x1a, 0x1a, 0x0a, 0x06, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a, 0x4b, 0x0a, 0x0b, 0x41, 0x72, 0x63, 0x68, 0x69,
	0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x66,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65,
	0x4c, 0x69, 0x66, 0x65, 0x22, 0xf0, 0x17, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x05, 0x72, 0x65, 0x64,
	0x69, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x6b, 0x61, 0x66, 0x6b, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x52, 0x05, 0x6b, 0x61, 0x66, 0x6b, 0x61,
	0x12, 0x29, 0x0a, 0x04, 0x6d, 0x71, 0x74, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x4d, 0x71, 0x74, 0x74, 0x52, 0x04, 0x6d, 0x71, 0x74, 0x74, 0x12, 0x23, 0x0a, 0x02, 0x73,
	0x33, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x33, 0x52, 0x02, 0x73, 0x33,
	0x12, 0x35, 0x0a, 0x08, 0x74, 0x64, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x44, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52, 0x08, 0x74,
	0x64, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x26, 0x0a, 0x03, 0x64, 0x6a, 0x69, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x4a, 0x49, 0x52, 0x03, 0x64, 0x6a, 0x69, 0x12,
	0x29, 0x0a, 0x04, 0x73, 0x6b, 0x61, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x53, 0x6b, 0x61, 0x69, 0x52, 0x04, 0x73, 0x6b, 0x61, 0x69, 0x12, 0x2c, 0x0a, 0x05, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x35, 0x0a, 0x08, 0x73, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x08, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12,
	0x3e, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x38, 0x0a, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x52, 0x09,
	0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x35, 0x0a, 0x0b, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x53, 0x33, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x53, 0x33, 0x52, 0x0b, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x53, 0x33,
	0x1a, 0x78, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x4f, 0x70, 0x65,
	0x6e, 0x43, 0x6f, 0x6e, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78,
	0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x49,
	0x64, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d,
	0x61, 0x78, 0x49, 0x64, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x1a, 0xa4, 0x02, 0x0a, 0x05, 0x52,
	0x65, 0x64, 0x69, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x64, 0x62, 0x12, 0x3c, 0x0a, 0x0c, 0x64, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x64, 0x69, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x12, 0x3c, 0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x3e, 0x0a, 0x0d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x1a, 0xbf, 0x03, 0x0a, 0x05, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x62,
	0x72, 0x6f, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x72,
	0x6f, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x3c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x65, 0x72, 0x73, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x73, 0x12, 0x3c, 0x0a,
	0x08, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x73, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x73, 0x1a, 0x1f, 0x0a, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x1a, 0x49, 0x0a, 0x09,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x73, 0x12, 0x3c, 0x0a, 0x09, 0x73, 0x75, 0x62,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4b,
	0x61, 0x66, 0x6b, 0x61, 0x2e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x09, 0x73, 0x75,
	0x62, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x1a, 0x99, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x73, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4b,
	0x61, 0x66, 0x6b, 0x61, 0x2e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x73, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0e, 0x61,
	0x6c, 0x61, 0x72, 0x6d, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x2e, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x0e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x4d, 0x73, 0x67, 0x1a, 0xbf, 0x01, 0x0a, 0x04, 0x4d, 0x71, 0x74, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x75, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x70, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x74, 0x66, 0x75, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x74, 0x66, 0x75, 0x6c, 0x55, 0x72, 0x6c,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x66, 0x75, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x74, 0x66, 0x75, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x66, 0x75, 0x6c, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x74, 0x66,
	0x75, 0x6c, 0x50, 0x61, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x55, 0x72, 0x6c, 0x1a, 0xf8, 0x01, 0x0a, 0x02, 0x53, 0x33, 0x12, 0x21, 0x0a, 0x0c,
	0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x74, 0x68, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x72, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x72, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x1a, 0x81, 0x02, 0x0a, 0x08, 0x54, 0x44, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x32, 0x0a, 0x04, 0x61,
	0x75, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x44, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x73,
	0x12, 0x32, 0x0a, 0x15, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x1a, 0x3e, 0x0a, 0x04, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x1a, 0x7a, 0x0a, 0x03, 0x44, 0x4a, 0x49, 0x12, 0x10, 0x0a, 0x03, 0x6e,
	0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6e, 0x74, 0x70, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x4b, 0x65, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65,
	0x1a, 0x7e, 0x0a, 0x04, 0x53, 0x6b, 0x61, 0x69, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x5f, 0x65, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x45, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x5f, 0x65, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x45, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x55, 0x72, 0x6c,
	0x1a, 0xcd, 0x01, 0x0a, 0x05, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x72, 0x65, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x74, 0x74,
	0x70, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x68, 0x74,
	0x74, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x74, 0x6d, 0x70, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x74, 0x6d, 0x70, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x74, 0x73, 0x70, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x74, 0x73, 0x70, 0x50, 0x6f, 0x72, 0x74,
	0x1a, 0x26, 0x0a, 0x08, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x1a, 0x41, 0x0a, 0x0b, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x1a, 0x89, 0x02, 0x0a, 0x09,
	0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x6c,
	0x66, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x6c,
	0x66, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x64, 0x64, 0x69, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x64, 0x64, 0x69, 0x55, 0x72, 0x6c, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x64,
	0x64, 0x69, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x64, 0x64, 0x69, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x67, 0x64, 0x64,
	0x69, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x67, 0x64, 0x64, 0x69, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d,
	0x67, 0x64, 0x64, 0x69, 0x5f, 0x66, 0x69, 0x72, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x64, 0x64, 0x69, 0x46, 0x69, 0x72, 0x65, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x73, 0x6d, 0x6f, 0x6b, 0x65, 0x79,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x74, 0x53, 0x6d, 0x6f,
	0x6b, 0x65, 0x79, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x66, 0x6c, 0x6f,
	0x61, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x74,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x65, 0x72, 0x42, 0x31, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6b,
	0x61, 0x69, 0x2f, 0x73, 0x6b, 0x61, 0x69, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x3b, 0x63, 0x6f, 0x6e, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_internal_conf_conf_proto_rawDescOnce sync.Once
	file_internal_conf_conf_proto_rawDescData = file_internal_conf_conf_proto_rawDesc
)

func file_internal_conf_conf_proto_rawDescGZIP() []byte {
	file_internal_conf_conf_proto_rawDescOnce.Do(func() {
		file_internal_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_conf_conf_proto_rawDescData)
	})
	return file_internal_conf_conf_proto_rawDescData
}

var file_internal_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_internal_conf_conf_proto_goTypes = []interface{}{
	(*Bootstrap)(nil),            // 0: kratos.api.Bootstrap
	(*Server)(nil),               // 1: kratos.api.Server
	(*Data)(nil),                 // 2: kratos.api.Data
	(*Server_HTTP)(nil),          // 3: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),          // 4: kratos.api.Server.GRPC
	(*Server_Auth)(nil),          // 5: kratos.api.Server.Auth
	(*Server_Push)(nil),          // 6: kratos.api.Server.Push
	(*Server_Device)(nil),        // 7: kratos.api.Server.Device
	(*Server_Analyser)(nil),      // 8: kratos.api.Server.Analyser
	(*Server_Delay)(nil),         // 9: kratos.api.Server.Delay
	(*Server_Skai)(nil),          // 10: kratos.api.Server.Skai
	(*Server_JWT)(nil),           // 11: kratos.api.Server.JWT
	(*Server_Visual)(nil),        // 12: kratos.api.Server.Visual
	(*Server_ArchiveTask)(nil),   // 13: kratos.api.Server.ArchiveTask
	(*Data_Database)(nil),        // 14: kratos.api.Data.Database
	(*Data_Redis)(nil),           // 15: kratos.api.Data.Redis
	(*Data_Kafka)(nil),           // 16: kratos.api.Data.Kafka
	(*Data_Mqtt)(nil),            // 17: kratos.api.Data.Mqtt
	(*Data_S3)(nil),              // 18: kratos.api.Data.S3
	(*Data_TDengine)(nil),        // 19: kratos.api.Data.TDengine
	(*Data_DJI)(nil),             // 20: kratos.api.Data.DJI
	(*Data_Skai)(nil),            // 21: kratos.api.Data.Skai
	(*Data_Media)(nil),           // 22: kratos.api.Data.Media
	(*Data_Snapshot)(nil),        // 23: kratos.api.Data.Snapshot
	(*Data_Maintenance)(nil),     // 24: kratos.api.Data.Maintenance
	(*Data_Algorithm)(nil),       // 25: kratos.api.Data.Algorithm
	(*Data_KafkaOptions)(nil),    // 26: kratos.api.Data.Kafka.options
	(*Data_Kafka_Producers)(nil), // 27: kratos.api.Data.Kafka.Producers
	(*Data_Kafka_Consumers)(nil), // 28: kratos.api.Data.Kafka.Consumers
	(*Data_TDengine_Auth)(nil),   // 29: kratos.api.Data.TDengine.Auth
	(*durationpb.Duration)(nil),  // 30: google.protobuf.Duration
}
var file_internal_conf_conf_proto_depIdxs = []int32{
	1,  // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	2,  // 1: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	3,  // 2: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	4,  // 3: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	11, // 4: kratos.api.Server.jwt:type_name -> kratos.api.Server.JWT
	5,  // 5: kratos.api.Server.auth:type_name -> kratos.api.Server.Auth
	6,  // 6: kratos.api.Server.push:type_name -> kratos.api.Server.Push
	7,  // 7: kratos.api.Server.device:type_name -> kratos.api.Server.Device
	8,  // 8: kratos.api.Server.analyser:type_name -> kratos.api.Server.Analyser
	10, // 9: kratos.api.Server.skai:type_name -> kratos.api.Server.Skai
	9,  // 10: kratos.api.Server.delay:type_name -> kratos.api.Server.Delay
	12, // 11: kratos.api.Server.visual:type_name -> kratos.api.Server.Visual
	13, // 12: kratos.api.Server.archiveTask:type_name -> kratos.api.Server.ArchiveTask
	14, // 13: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	15, // 14: kratos.api.Data.redis:type_name -> kratos.api.Data.Redis
	16, // 15: kratos.api.Data.kafka:type_name -> kratos.api.Data.Kafka
	17, // 16: kratos.api.Data.mqtt:type_name -> kratos.api.Data.Mqtt
	18, // 17: kratos.api.Data.s3:type_name -> kratos.api.Data.S3
	19, // 18: kratos.api.Data.tdengine:type_name -> kratos.api.Data.TDengine
	20, // 19: kratos.api.Data.dji:type_name -> kratos.api.Data.DJI
	21, // 20: kratos.api.Data.skai:type_name -> kratos.api.Data.Skai
	22, // 21: kratos.api.Data.media:type_name -> kratos.api.Data.Media
	23, // 22: kratos.api.Data.snapshot:type_name -> kratos.api.Data.Snapshot
	24, // 23: kratos.api.Data.maintenance:type_name -> kratos.api.Data.Maintenance
	25, // 24: kratos.api.Data.algorithm:type_name -> kratos.api.Data.Algorithm
	18, // 25: kratos.api.Data.secondaryS3:type_name -> kratos.api.Data.S3
	30, // 26: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	30, // 27: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	30, // 28: kratos.api.Data.Redis.dial_timeout:type_name -> google.protobuf.Duration
	30, // 29: kratos.api.Data.Redis.read_timeout:type_name -> google.protobuf.Duration
	30, // 30: kratos.api.Data.Redis.write_timeout:type_name -> google.protobuf.Duration
	27, // 31: kratos.api.Data.Kafka.produces:type_name -> kratos.api.Data.Kafka.Producers
	28, // 32: kratos.api.Data.Kafka.consumes:type_name -> kratos.api.Data.Kafka.Consumers
	29, // 33: kratos.api.Data.TDengine.auth:type_name -> kratos.api.Data.TDengine.Auth
	26, // 34: kratos.api.Data.Kafka.Producers.subsystem:type_name -> kratos.api.Data.Kafka.options
	26, // 35: kratos.api.Data.Kafka.Consumers.snapshotEvent:type_name -> kratos.api.Data.Kafka.options
	26, // 36: kratos.api.Data.Kafka.Consumers.alarmDomainMsg:type_name -> kratos.api.Data.Kafka.options
	37, // [37:37] is the sub-list for method output_type
	37, // [37:37] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_internal_conf_conf_proto_init() }
func file_internal_conf_conf_proto_init() {
	if File_internal_conf_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_conf_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bootstrap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_HTTP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_GRPC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Auth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Push); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Analyser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Delay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Skai); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_JWT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Visual); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_ArchiveTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Database); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Redis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Kafka); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Mqtt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_S3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_TDengine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_DJI); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Skai); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Media); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Snapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Maintenance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Algorithm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_KafkaOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Kafka_Producers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Kafka_Consumers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_TDengine_Auth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_conf_conf_proto_goTypes,
		DependencyIndexes: file_internal_conf_conf_proto_depIdxs,
		MessageInfos:      file_internal_conf_conf_proto_msgTypes,
	}.Build()
	File_internal_conf_conf_proto = out.File
	file_internal_conf_conf_proto_rawDesc = nil
	file_internal_conf_conf_proto_goTypes = nil
	file_internal_conf_conf_proto_depIdxs = nil
}
