syntax = "proto3";
package kratos.api;

option go_package = "gitlab.sensoro.com/skai/skai/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message Auth {
    string url = 1;
  }
  message Push {
    string url = 1;
  }
  message Device {
    string url = 1;
  }
  message Analyser {
    string url = 1;
    string triton = 2;
  }
  message Delay {
    string url = 1;
    string strategy = 2;
  }
  message Skai {
    string url = 1;
  }
  message JWT {
    string algorithm = 1;
    string secret = 2;
  }
  message Visual {
    string url = 1;
  }
  message ArchiveTask {
    int32 interval = 1;
    // 至少多少天后的文件才会被归档
    int32 minFileLife = 2;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  JWT jwt = 3;
  Auth auth = 4;
  Push push = 5;
  Device device = 6;
  Analyser analyser = 7;
  Skai skai = 8;
  Delay delay = 9;
  Visual visual = 10;
  string tracer = 11;
  string log_level = 12;
  ArchiveTask archiveTask = 13;
}

message Data {
  message Database {
    string driver = 1;
    string uri = 2;
    int32 maxOpenConn = 3;
    int32 maxIdleConn = 4;
  }
  message Redis {
    // oneof cluster base
    string client_type = 1;
    string addr = 2;
    string password = 3;
    int32 db = 4;
    google.protobuf.Duration dial_timeout = 6;
    google.protobuf.Duration read_timeout = 7;
    google.protobuf.Duration write_timeout = 8;
  }
  message Kafka {
    message options {
      string topic = 1;
    }
    message Producers {
      options subsystem = 1;
    }
    message Consumers {
      options snapshotEvent = 1;
      options alarmDomainMsg = 2;
    }
    string brokers = 1;
    string groupId = 2;
    Producers produces = 3;
    Consumers consumes = 4;
  }
  message Mqtt {
    string url = 1;
    string super_token  = 2;
    string restful_url = 3;
    string restful_user = 4;
    string restful_pass = 5;
    string public_url = 6;
  }
  message S3 {
    string endpoint_url = 1;
    string access_key = 2;
    string secret_key = 3;
    string bucket_name = 4;
    // yes or no
    string path_style = 5;
    string public_endpoint = 6;
    string arn = 7;
    string region = 8;
  }
  message TDengine {
    string brokers = 1;
    int32 port = 2;
    message Auth {
      string username = 1;
      string password = 2;
    }
    Auth auth = 3;
    int32 max_sockets = 4;
    int32 health_check_interval = 5;
  }
  message DJI {
    string ntp = 1;
    string url = 2;
    string app_id = 3;
    string app_key = 4;
    string app_license = 5;
  }
  message Skai {
    string server_ep = 1;
    string connect_ep = 2;
    string public_url = 3;
    string cloud_url = 4;
  }
  message Media {
    string admin_token = 1;
    string url = 2;
    string public_url = 3;
    int32 rest_port = 4;
    int32 http_port = 5;
    int32 rtmp_port = 6;
    int32 rtsp_port = 7;
  }
  message Snapshot {
    string endpoint = 1;
  }
  message Maintenance {
    string mobile = 1;
    string template = 2;
  }
  message Algorithm {
    string self_url = 1;
    string gddi_url = 2;
    string chat_url = 3;
    string gddi_token = 4;
    string gddi_floater = 5;
    string gddi_firework = 6;
    string chat_smokeye = 7;
    string chat_floater = 8;
  }
  Database database = 1;
  Redis redis = 2;
  Kafka kafka = 3;
  Mqtt mqtt = 4;
  S3 s3 = 5;
  TDengine tdengine = 6;
  DJI dji = 7;
  Skai skai = 8;
  Media media = 9;
  Snapshot snapshot = 10;
  Maintenance maintenance = 11;
  Algorithm algorithm = 12;
  S3 secondaryS3 = 13;
}
