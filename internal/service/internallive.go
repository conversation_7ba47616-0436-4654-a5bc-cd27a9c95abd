package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	pb "gitlab.sensoro.com/skai/skai/api/media/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type InternalLiveService struct {
	pb.UnimplementedInternalLiveServer
	log *log.Helper
	lu  *biz.LiveUsecase
	du  *biz.DeviceUsecase
}

func NewInternalLiveService(
	logger log.Logger,
	lu *biz.LiveUsecase,
	du *biz.DeviceUsecase,
) *InternalLiveService {
	return &InternalLiveService{
		log: log.NewHelper(log.With(logger, "module", "service/internal")),
		lu:  lu,
		du:  du,
	}
}

func (s *InternalLiveService) LiveStatusChanged(ctx context.Context, req *pb.LiveStatusChangedRequest) (*pb.StatusReply, error) {
	live, err := s.lu.GetLive(ctx, req.LiveId)
	if err != nil {
		return nil, err
	}
	s.log.Infof("LiveStatusChanged %d key=%s status %d", live.Id, live.Key, req.Status)
	if p := live.GetLivePosition(); req.Status == 0 && p == biz.LivePositionDock {
		if err := s.lu.StartDockPushLive(ctx, live); err != nil {
			s.log.Errorf("LiveStatusChanged %d StartDockPushLive failed %v", live.Id, err)
			dev, e := s.du.GetDevice(ctx, &biz.DetailQuery{Id: live.DeviceId})
			if e != nil {
				s.log.Errorf("LiveStatusChanged %d GetDevice failed %v", live.Id, err)
				return nil, e
			}
			if dev.Status == biz.StatusOffline {
				err = s.lu.CreateStartLiveDelayTask(ctx, dev, live, 120*time.Second)
			}
			return nil, err
		}
	}
	return &pb.StatusReply{
		Message: SuccessMessage,
		Data: &pb.StatusReply_Data{
			Status: true,
		},
	}, nil
}
