package service

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/goccy/go-json"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"github.com/tidwall/gjson"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/service/thing"
	"gitlab.sensoro.com/skai/skai/pkg/id"
	"gitlab.sensoro.com/skai/skai/pkg/mqtt"
	"gitlab.sensoro.com/skai/skai/pkg/types"
)

func newMqttReply(topic string, v any) (*mqtt.Reply, error) {
	r, err := json.Marshal(v)
	return &mqtt.Reply{
		Topic: topic,
		Qos:   1,
		Data:  r,
	}, err
}
func (s *DJIConnectService) newRequestReplyTopic(sn string) string {
	return fmt.Sprintf("thing/product/%s/requests_reply", sn)
}

func (s *DJIConnectService) handleConfigRequest(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
	req := &thing.DJIRequestMessage{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	config, err := s.tu.GetDeviceAccessConfig(ctx, req.Gateway)
	if err != nil {
		s.log.Errorf("get dji access config failed for gateway %s, err %v", req.Gateway, err)
		return nil, nil
	}
	return newMqttReply(s.newRequestReplyTopic(req.Gateway), &thing.DJIRequestReply{
		DJIEventMessage: req.DJIEventMessage,
		Data:            config,
	})
}

func (s *DJIConnectService) handleFlightResourceGetRequest(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
	req := &thing.DJIDockFlightTaskResourceRequestMessage{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	if req.Data == nil {
		s.log.Errorf("invalid DJIDockFlightTaskResourceRequestMessage %s form %s no data field", m.Payload, req.Gateway)
		return nil, nil
	}
	gateway := req.Gateway
	if gateway == "" {
		gateway = s.getDJISourceSNFromTopic(m.Topic)
	}
	dev, err := s.tu.GetDevice(ctx, req.Gateway)
	if err != nil {
		var ret int32 = 500
		if errors.IsNotFound(err) {
			ret = 404
		}
		s.log.Errorf("handle DJIDockFlightTaskResourceRequestMessage req get device %s failed %v", req.Gateway, err)
		return newMqttReply(s.newRequestReplyTopic(gateway), &thing.DJIReplyMessage{
			DJIMessage: req.DJIMessage,
			Data:       thing.DJIReplyData{Result: ret},
		})
	}
	event := req.ToBiz(dev)
	if r, err := s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
		s.log.Errorf("rpc DJIDockFlightTaskResourceRequestMessage for device %s failed %v", req.Gateway, err)
		return nil, nil
	} else {
		return newMqttReply(s.newRequestReplyTopic(gateway), &thing.DJIReplyMessage{
			DJIMessage: req.DJIMessage,
			Data: thing.DJIReplyData{
				Output: map[string]any{
					"file": r.Data["kmzFile"],
				},
			},
		})
	}
}

func (s *DJIConnectService) handleDockOrganizationStatusRequest(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
	req := &thing.DJIDockOrganizationRequest{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	sn := s.getDJISourceSNFromTopic(m.Topic)
	dev, err := s.tu.GetDevice(ctx, sn)
	if err != nil {
		s.log.Errorf("handle handleDockOrganizationStatusRequest req get device %s failed %v", sn, err)
		return newMqttReply(s.newRequestReplyTopic(sn), &thing.DJIReplyMessage{
			DJIMessage: req.DJIMessage,
			Data:       thing.DJIReplyData{Result: 404},
		})
	}
	merchant, err := s.tu.GetDeviceMerchant(ctx, dev)
	if err != nil {
		s.log.Errorf("handle handleDockOrganizationStatusRequest req GetDeviceMerchant %s failed %v", sn, err)
		return newMqttReply(s.newRequestReplyTopic(sn), &thing.DJIReplyMessage{
			DJIMessage: req.DJIMessage,
			Data:       thing.DJIReplyData{Result: 500},
		})
	}
	return newMqttReply(s.newRequestReplyTopic(sn), thing.NewDJIDockOrganizationReply(req, dev, merchant))
}

func (s *DJIConnectService) handleDockOrganizationGetRequest(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
	req := &thing.DJIDockOrganizationGetRequest{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	sn := s.getDJISourceSNFromTopic(m.Topic)
	dev, err := s.tu.GetDevice(ctx, sn)
	if err != nil {
		s.log.Errorf("handle handleDockOrganizationGetRequest req get device %s failed %v", sn, err)
		return newMqttReply(s.newRequestReplyTopic(sn), &thing.DJIReplyMessage{
			DJIMessage: req.DJIMessage,
			Data:       thing.DJIReplyData{Result: 404},
		})
	}
	merchant, err := s.tu.GetDeviceMerchant(ctx, dev)
	if err != nil {
		s.log.Errorf("handle handleDockOrganizationGetRequest req GetDeviceMerchant %s failed %v", sn, err)
		return newMqttReply(s.newRequestReplyTopic(sn), &thing.DJIReplyMessage{
			DJIMessage: req.DJIMessage,
			Data:       thing.DJIReplyData{Result: 500},
		})
	}
	return newMqttReply(s.newRequestReplyTopic(sn), thing.NewDJIDockOrganizationInfoReply(req, merchant))
}

func (s *DJIConnectService) handleDockOrganizationBindRequest(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
	req := &thing.DJIDockOrganizationBindRequest{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	sn := s.getDJISourceSNFromTopic(m.Topic)
	dev, err := s.tu.GetDevice(ctx, sn)
	if err != nil {
		s.log.Errorf("handle handleDockOrganizationBindRequest req get device %s failed %v", sn, err)
		return newMqttReply(s.newRequestReplyTopic(sn), &thing.DJIReplyMessage{
			DJIMessage: req.DJIMessage,
			Data:       thing.DJIReplyData{Result: 404},
		})
	}
	merchant, err := s.tu.GetDeviceMerchant(ctx, dev)
	if err != nil {
		s.log.Errorf("handle handleDockOrganizationBindRequest req GetDeviceMerchant %s failed %v", sn, err)
		return newMqttReply(s.newRequestReplyTopic(sn), &thing.DJIReplyMessage{
			DJIMessage: req.DJIMessage,
			Data:       thing.DJIReplyData{Result: 500},
		})
	}
	return newMqttReply(s.newRequestReplyTopic(sn), thing.DJIDockOrganizationBindReply(req, merchant))
}

func (s *DJIConnectService) handleStorageConfigGetRequest(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
	req := &thing.DJIDockStorageConfigGetRequestMessage{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	gateway := req.Gateway
	if gateway == "" {
		gateway = s.getDJISourceSNFromTopic(m.Topic)
	}
	dev, err := s.tu.GetDevice(ctx, gateway)
	if err != nil {
		s.log.Errorf("handleStorageConfigGetRequest.getDevice for device %s failed %v", gateway, err)
		return nil, nil
	}
	c, err := s.tu.GetVoyageMediaUploadConfig(ctx, dev)
	if err != nil {
		s.log.Errorf("handleStorageConfigGetRequest.getconfig for device %s failed %v", gateway, err)
		return nil, nil
	}
	return newMqttReply(s.newRequestReplyTopic(gateway), &thing.DJIReplyMessage{
		DJIMessage: req.DJIMessage,
		Data: thing.DJIReplyData{
			Output: map[string]any{
				"bucket": c.Credentials.Bucket,
				"credentials": map[string]any{
					"access_key_id":     c.Credentials.AccessKey,
					"access_key_secret": c.Credentials.AccessSecret,
					"expire":            c.Credentials.Expire,
					"security_token":    c.Credentials.SessionToken,
				},
				"endpoint":          c.Credentials.Endpoint,
				"object_key_prefix": c.KeyPrefix,
				"provider":          c.Credentials.Provider,
				"region":            c.Credentials.Region,
			},
		},
	})
}

func (s *DJIConnectService) handleGatewayOSD(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
	gateway := gjson.GetBytes(m.Payload, "gateway").String()
	dev, err := s.tu.GetDevice(ctx, gateway)
	if err != nil {
		s.log.Warnf("device %s not found for dji osd message %s", gateway, m.Payload)
		return nil, nil
	}
	if dev.Model == biz.ModelDJIDock || dev.Model == biz.ModelDJILite {
		return s.handleDockOSD(ctx, m, dev)
	}
	if dev.Model == biz.ModelDJIPilot {
		return s.handleControllerOSD(ctx, m, dev)
	}
	s.log.Warnf("unsupport model %s for msg %s", dev.Model, m.Payload)
	return nil, nil
}

func (s *DJIConnectService) handleControllerOSD(ctx context.Context, m *mqtt.Message, dev *biz.Device) (*mqtt.Reply, error) {
	req := &thing.DJIRemoteControllerOSDMessage{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	p := req.ToBiz(dev)
	if err := s.tu.PushControllerProperties(ctx, p); err != nil {
		s.log.Errorf("rpc controller %s properties %s to skai err %v", p.Sn, p.Id, err)
		return nil, nil
	}
	return nil, nil
}

type dockOSDMerger struct {
	tu  *biz.ThingUsecase
	log *log.Helper
}

func (m *dockOSDMerger) Merge(list []*biz.DockProperties) *biz.DockProperties {
	l := len(list)
	if l == 0 {
		return nil
	}
	props := list[0]
	if l == 1 {
		return props
	}
	slices.SortFunc(list, func(a, b *biz.DockProperties) int { return a.Timestamp.Compare(b.Timestamp) })
	// 使用最新的
	for i := 1; i < l; i++ {
		it := list[i]
		if it.State != nil {
			props.State = it.State
		}
		if it.PositionState != nil {
			props.PositionState = it.PositionState
		}
		if it.NetworkState != nil {
			props.NetworkState = it.NetworkState
		}
		if it.FlightTaskState != nil {
			props.FlightTaskState = it.FlightTaskState
		}
		if it.EnvironmentState != nil {
			props.EnvironmentState = it.EnvironmentState
		}
		if it.WirelessLinkState != nil {
			props.WirelessLinkState = it.WirelessLinkState
		}
		if it.ElecPowerState != nil {
			props.ElecPowerState = it.ElecPowerState
		}
		if it.BatteryChargeState != nil {
			props.BatteryChargeState = it.BatteryChargeState
		}
	}
	return props
}

func (m *dockOSDMerger) Sink(p *biz.DockProperties) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := m.tu.PushDockProperties(ctx, p); err != nil {
		m.log.Errorf("rpc dock %s properties %s to skai err %v", p.Sn, p.Id, err)
		return
	}
	m.log.Debugf("sinkDockOSD %s %s", p.Sn, p.Id)
}

func (s *DJIConnectService) handleDockOSD(_ context.Context, m *mqtt.Message, dev *biz.Device) (*mqtt.Reply, error) {
	req := &thing.DJIDockOsdMessage{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	data := gjson.GetBytes(m.Payload, "data")
	dataKeys := types.NewHashSet[string]()
	data.ForEach(func(key, value gjson.Result) bool {
		k := key.Str
		dataKeys.Add(k)
		return true
	})
	p := req.ToBiz(dev, dataKeys)
	s.log.Debugf("cache dock %s properties %s to skai", p.Sn, p.Id)
	s.dockOsdMergeRunner.Add(dev.Id, p)
	// if err := s.tu.PushDockProperties(ctx, p); err != nil {
	// 	s.log.Errorf("rpc dock %s properties %s to skai err %v", p.Sn, p.Id, err)
	// 	return nil, nil
	// }
	return nil, nil
}

func (s *DJIConnectService) handleDJIDroneOSD(ctx context.Context, droneSn string, m *mqtt.Message) (*mqtt.Reply, error) {
	req := &thing.DJIDroneOsdMessage{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, nil
	}
	dev, err := s.tu.GetDevice(ctx, req.Gateway)
	if err != nil {
		s.log.Errorf("get device %s failed ", req.Gateway)
		return nil, nil
	}
	data := gjson.GetBytes(m.Payload, "data")
	dataKeys := types.NewHashSet[string]()
	data.ForEach(func(key, value gjson.Result) bool {
		dataKeys.Add(key.String())
		return true
	})
	p := req.ToDockDroneBiz(dev, droneSn, dataKeys, data.Raw)
	s.log.Debugf("rpc dock drone %s properties %s to skai", p.Sn, p.Id)
	if err = s.tu.PushGatewayDroneProperties(ctx, p); err != nil {
		s.log.Errorf("rpc dock drone %s properties %s to skai err %v", p.Sn, p.Id, err)
		return nil, nil
	}
	// if dev.Status == biz.StatusExecution && (req.Data.ModeCode == 0 || req.Data.ModeCode == 14) {
	// 	lives, err := s.mu.GetDeviceAllLives(ctx, dev)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	live, ok := lo.Find(lives, func(it *biz.Media) bool { return it.Status == 1 && !strings.HasPrefix(it.SubDeviceIndex, "165-0") })
	// 	if ok {
	// 		return newMqttReply(
	// 			fmt.Sprintf("thing/product/%s/services", dev.SourceSn),
	// 			thing.NewDJIStopLiveService(dev, live),
	// 		)
	// 	}
	// }
	return nil, nil
}

func (s *DJIConnectService) handleControllerState(ctx context.Context, gateway string, m *mqtt.Message, dev *biz.Device) (*mqtt.Reply, error) {
	req := &thing.DJIRemoteControllerStateMessage{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, err
	}
	s.log.Infof("controller %s state changed %+v", gateway, req)
	if req.Data != nil {
		if req.Data.LiveCapacity != nil {
			return s.onLiveCapacityChanged(ctx, dev, req.Data.LiveCapacity)
		}
		if len(req.Data.LiveStatus) > 0 {
			return s.onLiveStatusChanges(ctx, dev, req.Data.LiveStatus)
		}
	}
	p := req.ToBiz(dev)
	if err := s.tu.PushControllerProperties(ctx, p); err != nil {
		s.log.Errorf("rpc controller %s state %s to skai err %v", p.Sn, p.Id, err)
		return nil, nil
	}
	return nil, nil
}

func (s *DJIConnectService) handleDockState(ctx context.Context, gateway string, m *mqtt.Message, dev *biz.Device) (*mqtt.Reply, error) {
	req := &thing.DJIDockStateMessage{}
	if err := json.Unmarshal(m.Payload, req); err != nil {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
		return nil, err
	}
	s.log.Infof("dock %s state changed %+v", gateway, req.Data)
	if req.Data != nil {
		if req.Data.LiveCapacity != nil {
			return s.onLiveCapacityChanged(ctx, dev, req.Data.LiveCapacity)
		}
		if len(req.Data.LiveStatus) > 0 {
			return s.onLiveStatusChanges(ctx, dev, req.Data.LiveStatus)
		}
	}
	p := req.ToBiz(dev)
	s.log.Debugf("rpc dock %s state %s to skai", p.Sn, p.Id)
	if err := s.tu.PushDockProperties(ctx, p); err != nil {
		s.log.Errorf("rpc dock %s state %s to skai failed %v", p.Sn, p.Id, err)
	}
	return nil, nil
}

func (s *DJIConnectService) handleGatewayState(ctx context.Context, gateway string, m *mqtt.Message) (*mqtt.Reply, error) {
	dev, err := s.tu.GetDevice(ctx, gateway)
	if err != nil {
		s.log.Errorf("get device %s failed %v", gateway, err)
		return nil, nil
	}
	if dev.Model == biz.ModelDJIDock || dev.Model == biz.ModelDJILite {
		return s.handleDockState(ctx, gateway, m, dev)
	}
	if dev.Model == biz.ModelDJIPilot {
		return s.handleControllerState(ctx, gateway, m, dev)
	}
	s.log.Warnf("unsupported model %s with sn %s message: %s", dev.Model, gateway, m.Payload)
	return nil, nil
}

func (s *DJIConnectService) onLiveStatusChanges(ctx context.Context, dev *biz.Device, ls []thing.DJILiveStatus) (*mqtt.Reply, error) {
	// ls := req.Data.LiveStatus
	lives, err := s.mu.GetDeviceAllLives(ctx, dev)
	if err != nil {
		s.log.Errorf("onLiveStatusChanges.GetDeviceAllLives for dev %d r err %v", dev.Id, err)
		return nil, nil
	}
	for _, v := range ls {
		l, ok := lo.Find(lives, func(it *biz.Media) bool { return it.Key == v.VideoId })
		if ok && l.Status != v.Status {
			vt, _ := l.GetVideoType(dev.Subdevices)
			if err = s.mu.UpdateLiveStatus(ctx, l.Id, biz.LiveStatus{
				Status:  v.Status,
				Clarity: v.VideoClarity,
				Type:    vt,
			}); err != nil {
				s.log.Errorf("update live %d status failed %v ", l.Id, err)
			}
		}
	}
	return nil, nil
}

func (s *DJIConnectService) onLiveCapacityChanged(ctx context.Context, dev *biz.Device, lc *thing.DJILiveCapacity) (*mqtt.Reply, error) {
	// lc := req.Data.LiveCapacity
	dbCameras := lo.Filter(dev.Subdevices, func(sd *biz.DockSubdevice, _ int) bool {
		return sd.Domain == biz.SubDeviceDomainCamera
	})
	var addSubs []*biz.DockSubdevice
	var cameras []*biz.DockSubdevice
	for _, liveDevice := range lc.DeviceList {
		for _, deviceCamera := range liveDevice.CameraList {
			sd := deviceCamera.ToSubDevice(liveDevice.Sn)
			cameras = append(cameras, sd)
			if !lo.ContainsBy(dbCameras, func(c *biz.DockSubdevice) bool {
				return c.Index == deviceCamera.CameraIndex
			}) {
				addSubs = append(addSubs, sd)
			}
		}
	}
	if len(addSubs) > 0 {
		dev.Subdevices = append(dev.Subdevices, addSubs...)
		ts := time.Now()
		event := &biz.DockTopoUpdateEvent{
			ThingEvent: biz.ThingEvent{
				Id:           id.NewUUIDV1(),
				DeviceId:     dev.Id,
				Sn:           dev.Sn,
				OccurredTime: ts,
				RxTime:       ts,
				Type:         biz.ThingModelEventTypeUpdateTopo,
			},
			DeviceModel: dev.Model.String(),
			Subdevices:  append(dev.Subdevices, addSubs...),
		}
		defer func() {
			if r, err := s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
				s.log.Errorf("onLiveCapacityChanged.PushEvent.err for dev %d res %+v err %v", dev.Id, r, err)
			} else {
				s.log.Info("onLiveCapacityChanged.PushEvent for dev %d res %+v", dev.Id, r)
			}
		}()
	}

	lives, err := s.mu.GetDeviceAllLives(ctx, dev)
	if err != nil {
		s.log.Errorf("onLiveCapacityChanged.GetDeviceAllLives for dev %d r err %v", dev.Id, err)
		return nil, nil
	}
	if dev.Model == biz.ModelDJIPilot && len(cameras) == 0 {
		// TODO: 批量更改
		for _, l := range lives {
			if l.Status == 1 {
				vt, _ := l.GetVideoType(dev.Subdevices)
				s.mu.UpdateLiveStatus(ctx, l.Id, biz.LiveStatus{Status: 0, Clarity: l.GetLiveClarity(), Type: vt})
			}
		}
	}
	var addLives []*biz.Media
	for _, c := range cameras {
		if !lo.ContainsBy(lives, func(l *biz.Media) bool {
			return l.SubDeviceIndex == c.Index
		}) {
			info, ok := c.GetCameraInfo()
			if ok && len(info.Videos) > 0 {
				for _, v := range info.Videos {
					k := s.tu.CreateLiveKeyForSubDevice(dev, *c, v.Index)
					addLives = append(addLives, &biz.Media{
						Type:           biz.MediaTypeLive,
						DeviceId:       dev.Id,
						MerchantId:     dev.MerchantId,
						SubDeviceIndex: c.Index,
						Key:            k,
						Name:           v.Index[:strings.Index(v.Index, "-")],
						Extra: map[string]any{
							"position": thing.FindDJICameraPosition(c.Index),
							"index":    v.Index,
						},
					})
				}
			}
		}
	}
	if len(addLives) > 0 {
		if err := s.mu.CreateLives(ctx, addLives); err != nil {
			s.log.Errorf("onLiveCapacityChanged.CreateLives for dev %d r err %v", dev.Id, err)
			return nil, nil
		}
		lives = append(lives, addLives...)
	}

	if defaultCamera := dev.GetGimbal(); defaultCamera != nil {
		lives := lo.Filter(lives, func(it *biz.Media, _ int) bool { return it.SubDeviceIndex == defaultCamera.Index })
		if len(lives) > 0 {
			backup, ok := lo.Find(lives, func(it *biz.Media) bool {
				vt, _ := it.GetVideoType(cameras)
				return vt == biz.VideoTypeNormal
			})
			if !ok {
				backup = lo.Sample(lives)
			}
			live := lo.FindOrElse(lives, backup, func(it *biz.Media) bool {
				vt, _ := it.GetVideoType(cameras)
				return vt == biz.DefaultVideoType
			})
			pc, err := s.mu.GetLivePushConfig(ctx, dev, live)
			if err != nil {
				s.log.Errorf("onLiveCapacityChanged.GetLivePushConfig for device %d failed %v", dev.Id, err)
				return nil, err
			}
			// 立即尝试开启直播大疆会报513006错误
			s.createStartDroneLiveTask(ctx, dev, live)
			// s.log.Infof("onLiveCapacityChanged auto start live for drone %s media %d key %s", drone.Sn, live.Id, live.Key)
			return newMqttReply(
				fmt.Sprintf("thing/product/%s/services", dev.SourceSn),
				thing.NewStartLiveService(dev, live, pc, biz.DefaultDroneliveClarity),
			)
		}
	}
	return nil, nil
}

func (s *DJIConnectService) createStartDroneLiveTask(ctx context.Context, dev *biz.Device, live *biz.Media) {
	task := &biz.DelayTask{
		Times:    1,
		Delay:    30 * time.Second,
		Source:   "SKAI-CONNECT",
		Key:      fmt.Sprintf("%s:CONNECT:LIVE:%d", biz.SkaiDelayKeyPrefix, live.Id),
		Callback: fmt.Sprintf("/internal/v1/media/live/%d/callback/start", live.Id),
		Payload: map[string]interface{}{
			"deviceId": conv.Itoa(dev.Id),
		},
	}
	if err := s.du.CreateTask(ctx, task); err != nil {
		s.log.Errorf("createStartDroneLiveTask for device %d failed %v", dev.Id, err)
	} else {
		s.log.Infof("createStartDroneLiveTask for device %d live %d", dev.Id, live.Id)
	}
}
