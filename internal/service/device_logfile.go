package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func (s *DeviceService) RemoteLogfiles(ctx context.Context, req *pb.RemoteLogfilesRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	if device.Status == biz.StatusExecution {
		return nil, errors.BadRequest("40000002", "无人机正在执行飞行任务，请结束后再试")
	}
	if device.Status == biz.StatusOffline {
		return nil, errors.BadRequest("40000002", "设备不在线")
	}
	lc, _, err := s.du.ListLogfile(ctx, &biz.LogfileListQuery{
		ProjectInfo: *projectInfo,
		DeviceId:    device.Id,
		Page:        1,
		Size:        0,
		Status:      []int32{0},
		StartTime:   time.Now().Add(-time.Hour * 2),
		EndTime:     time.Now(),
	})
	if err != nil {
		return nil, err
	}
	if lc > 0 {
		return nil, errors.BadRequest("40000002", "无人机正在上传日志，请结束后再试")
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeRemoteLogfile,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    biz.AnyMap{"SourceId": device.Id, "Modules": req.Modules},
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierListFileupload,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    &biz.ListFileuploadPayload{Modules: req.Modules},
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) UploadLogfiles(ctx context.Context, req *pb.UploadLogfilesRequest) (*pb.OperationReply, error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeUploadLogfile,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    biz.AnyMap{"SourceId": device.Id},
	})
	if err != nil {
		return nil, err
	}
	logfiles := make([]*biz.Logfile, 0)
	remfiles := make([]*biz.FileUpload, 0)
	for _, file := range req.Files {
		logfile := &biz.Logfile{}
		copier.Copy(logfile, file)
		logfile.DeviceId = device.Id
		logfile.TenantId = device.TenantId
		logfile.MerchantId = device.MerchantId
		logfile.OperationId = operation.Id
		logfile.StartTime = time.Unix(file.StartTime, 0)
		logfile.EndTime = time.Unix(file.EndTime, 0)
		logfile.Url = fmt.Sprintf("7d/%s/%d_%d_%s.log", device.Sn, file.StartTime, file.BootIndex, time.Now().Format("02150405"))
		logfiles = append(logfiles, logfile)
		remfiles = append(remfiles, &biz.FileUpload{Module: file.Module, BootIndex: file.BootIndex, ObjectKey: logfile.Url})
	}
	s.du.CreateLogfiles(ctx, logfiles)
	sc, err := s.tu.GetVoyageMediaUploadConfig(ctx, device)
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierStartFileupload,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    &biz.StartFileuploadPayload{Files: remfiles, Credential: &sc.Credentials},
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) ListLogfiles(ctx context.Context, req *pb.ListLogfilesRequest) (ret *pb.LogfilesReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	modules := lo.Filter(strings.Split(req.Modules, ","), func(module string, _ int) bool {
		return module != ""
	})
	endTime := time.Now()
	startTime := endTime
	if device.Deployment.Time != nil {
		startTime = *device.Deployment.Time
	}
	if req.StartTime > startTime.UnixMilli() {
		startTime = time.UnixMilli(req.StartTime)
	}
	if req.EndTime > startTime.UnixMilli() {
		endTime = time.UnixMilli(req.EndTime)
	}
	total, logfiles, err := s.du.ListLogfile(ctx, &biz.LogfileListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.Size),
		DeviceId:    req.Id,
		Modules:     modules,
		StartTime:   startTime,
		EndTime:     endTime,
	})
	if err != nil {
		return nil, errors.BadRequest("40000002", "操作记录不存在")
	}
	list := lo.Map(logfiles, func(log *biz.Logfile, _ int) *pb.Logfile {
		logfile := &pb.Logfile{}
		copier.CopyWithOption(logfile, log, copier.Option{
			IgnoreEmpty: true,
			DeepCopy:    true,
			Converters:  []copier.TypeConverter{biz.TimeCopierConverter},
		})
		return logfile
	})
	return &pb.LogfilesReply{
		Message: SuccessMessage,
		Data: &pb.LogfilesReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			Total: int32(total),
			List:  list,
		},
	}, nil
}

func (s *DeviceService) DownloadLogfile(ctx context.Context, req *pb.DownloadLogfilesRequest) (*pb.DownloadReply, error) {
	projectInfo, err := s.au.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	if _, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo}); err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	logfile, err := s.du.GetLogfile(ctx, &biz.DetailQuery{Id: req.FileId, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "日志不存在")
	}
	return &pb.DownloadReply{
		Message: SuccessMessage,
		Data:    &pb.DownloadReplyDowndata{Url: logfile.Url, Fingerprint: logfile.Module},
	}, nil
}
