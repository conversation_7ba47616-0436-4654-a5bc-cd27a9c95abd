package service

import (
	"context"

	"gitlab.sensoro.com/skai/skai/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
)

type ProjectMerchantsAccessReq struct {
	ProjectID   int64
	AvatarID    int64
	MerchantIDs []int64
}

type AuthService struct {
	log *log.Helper
	au  *biz.AuthUsecase
}

func NewAuthService(logger log.Logger, au *biz.AuthUsecase) *AuthService {
	return &AuthService{log: log.<PERSON><PERSON>elper(logger), au: au}
}

func (s *AuthService) GetProjectMerchants(ctx context.Context, projectID int64) ([]*biz.<PERSON>, error) {
	return s.au.GetProjectMerchants(ctx, projectID)
}
