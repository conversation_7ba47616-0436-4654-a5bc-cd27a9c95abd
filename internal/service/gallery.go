package service

import (
	"context"

	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/media/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type GalleryService struct {
	pb.UnimplementedGalleryServer
	au *biz.AuthUsecase
	mu *biz.MediaUsecase
}

func NewGalleryService(
	au *biz.AuthUsecase,
	mu *biz.MediaUsecase,
) *GalleryService {
	return &GalleryService{
		mu: mu,
		au: au,
	}
}

func (s *GalleryService) DeleteGalleryMedia(ctx context.Context, req *pb.DeleteGalleryMediaRequest) (*pb.DeleteGalleryMediaReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}

	_, err = s.mu.RemoveMedia(ctx, biz.MediaRemovement{
		ProjectInfo: *biz.NewProjectInfo(av),
		Ids:         req.Ids,
		VoyageIds:   req.VoyageIds,
	})
	if err != nil {
		return nil, err
	}

	return &pb.DeleteGalleryMediaReply{
		Message: SuccessMessage,
		Data: &pb.DeleteGalleryMediaReply_Data{
			Status: true,
		},
	}, nil
}
func (s *GalleryService) ListGalleryMedia(ctx context.Context, req *pb.ListGalleryMediaRequest) (*pb.ListGalleryMediaReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	lq := biz.NewSimpleListQuery(req.Page, req.Size)
	lq.TimeScope = biz.NewTimeScope(req.StartTime, req.EndTime)
	query := &biz.GalleryMediaListQuery{
		ProjectInfo:   *biz.NewProjectInfo(av),
		BaseListQuery: *lq,
		Search:        req.Search,
		Types: lo.Map(req.Types, func(it string, _ int) int32 {
			return int32(pb.BuildMediaType(it))
		}),
		DeviceCategories: lo.Map(req.Categories, func(it string, _ int) biz.DeviceCategory {
			return biz.DeviceCategory(it)
		}),
		DeviceTypes: req.DeviceTypes,
		DeviceIds:   req.DeviceIds,
		AirlineIds:  req.AirlineIds,
		LenTypes: lo.Filter(req.LenTypes, func(it string, _ int) bool {
			_, ok := biz.MediaSubTypeValueMapper[it]
			return ok
		}),
	}
	for _, ft := range req.FileTypes {
		switch ft {
		case "P":
			query.IsPanorama = true
		case "V":
			query.IsVideo = true
		case "FV":
			query.IsFullRecord = true
		case "I":
			query.IsPhoto = true
		}
		// if t, ok := biz.MediaSubTypeValueMapper[ft]; ok {
		// 	query.SubTypes = append(query.SubTypes, t)
		// } else {
		// 	if ft == "P" {
		// 		query.IsPanorama = true
		// 	}
		// 	if ft == "V" && !lo.Contains(query.Types, int32(biz.MediaTypeVideo)) {
		// 		query.IsVideo = true
		// 	}
		// 	if ft == "FV" {
		// 		query.IsFullRecord = true
		// 	}
		// }
	}
	fileCount, err := s.mu.CountGalleryMedia(ctx, query.ProjectInfo)
	if err != nil {
		return nil, err
	}
	total, list, err := s.mu.ListGalleryMedia(ctx, query)
	if err != nil {
		return nil, err
	}

	return &pb.ListGalleryMediaReply{
		Message: SuccessMessage,
		Data: &pb.ListGalleryMediaReply_Data{
			Total: total,
			List: lo.Map(list, func(it biz.MediaWithRelatedDevice, _ int) *pb.GalleryMediaItem {
				return pb.NewGalleryMediaItem(it.Media, it.Device, it.Airline, it.Voyage)
			}),
			Page:       req.Page,
			Size:       req.Size,
			TotalFiles: float64(fileCount),
		},
	}, nil
}

func (s *GalleryService) CleanUpTmpDownloadableFile(ctx context.Context, req *pb.CleanUpTmpDownloadableFileRequest) (*pb.CleanUpTmpDownloadableFileReply, error) {
	if err := s.mu.CleanTmpDownlodFile(ctx, req.Body.Key); err != nil {
		return nil, err
	}
	return &pb.CleanUpTmpDownloadableFileReply{
		Message: SuccessMessage,
		Data: &pb.CleanUpTmpDownloadableFileReply_Data{
			Status: true,
		},
	}, nil
}

func (s *GalleryService) GetVideoMediaDownloadUrl(ctx context.Context, req *pb.GetVideoMediaDownloadUrlRequest) (*pb.GetVideoMediaDownloadUrlReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	ret, err := s.mu.GetVideoMediaDownloadUrl(ctx, biz.MediaDetailQuery{
		ProjectInfo: *biz.NewProjectInfo(av),
		MediaId:     req.Id,
	})
	if err != nil {
		return nil, err
	}
	return &pb.GetVideoMediaDownloadUrlReply{
		Message: SuccessMessage,
		Data: &pb.GetVideoMediaDownloadUrlReply_Data{
			Url: ret.URL,
		},
	}, nil

}
