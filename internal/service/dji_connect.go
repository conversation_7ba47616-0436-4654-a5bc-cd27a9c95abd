package service

import (
	"context"
	"fmt"
	"path"
	"strings"
	"time"

	"github.com/goccy/go-json"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"github.com/tidwall/gjson"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	pb "gitlab.sensoro.com/skai/skai/api/cloud/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/service/thing"
	"gitlab.sensoro.com/skai/skai/pkg/mqtt"
	"gitlab.sensoro.com/skai/skai/pkg/types"
)

type DJIConnectService struct {
	log                *log.Helper
	au                 *biz.AiEventUsecase
	tu                 *biz.ThingUsecase
	mu                 *biz.MediaUsecase
	mq                 *biz.MQTTUsecase
	du                 *biz.DelayUsecase
	su                 *biz.MissionUsecase
	dockOsdMergeRunner *types.MessageMergeExecutor[int64, *biz.DockProperties]
}

func NewDJIConnectService(
	logger log.Logger,
	au *biz.AiEventUsecase,
	tu *biz.ThingUsecase,
	mu *biz.MediaUsecase,
	mq *biz.MQTTUsecase,
	du *biz.DelayUsecase,
	su *biz.MissionUsecase,
) *DJIConnectService {
	me := types.NewMessageMergeExecutor[int64, *biz.DockProperties](&dockOSDMerger{tu: tu, log: log.NewHelper(logger)}, time.Millisecond*600, 10)
	go utilities.NewRecovedGOFunc(func() error {
		me.Run()
		return nil
	})()
	return &DJIConnectService{
		log:                log.NewHelper(logger),
		au:                 au,
		tu:                 tu,
		mu:                 mu,
		mq:                 mq,
		du:                 du,
		su:                 su,
		dockOsdMergeRunner: me,
	}
}

func (s *DJIConnectService) Handlers() []mqtt.MessageHandler {
	return []mqtt.MessageHandler{
		s.newRequestHandler(),
		s.newOSDHandler(),
		s.newStateHandler(),
		s.newDJIStatusHandler(),
		s.newDJIDockEventHanlder(),
		s.newDJIServiceReplyHandler(),
	}
}

func (s *DJIConnectService) newRequestHandler() mqtt.MessageHandler {
	return newDJIHandler(s, "thing/product/+/requests", func(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
		method := gjson.GetBytes(m.Payload, "method").String()
		switch method {
		case thing.DJIMethodConfig:
			return s.handleConfigRequest(ctx, m)
		case thing.DJIMethodFlightTaskResourceGet:
			return s.handleFlightResourceGetRequest(ctx, m)
		case thing.DJIMethodOrganizationStatus:
			return s.handleDockOrganizationStatusRequest(ctx, m)
		case thing.DJIMethodOrganizationGet:
			return s.handleDockOrganizationGetRequest(ctx, m)
		case thing.DJIMethodOrganizationBind:
			return s.handleDockOrganizationBindRequest(ctx, m)
		case thing.DJIMethodStoregeConfigGet:
			return s.handleStorageConfigGetRequest(ctx, m)
		default:
			s.log.Warnf("unknown method %s data %s", method, m.Payload)
			return nil, nil
		}
	})
}

func (s *DJIConnectService) newOSDHandler() mqtt.MessageHandler {
	return newDJIHandler(s, "thing/product/+/osd", func(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
		gateway := gjson.GetBytes(m.Payload, "gateway").String()
		tSn := s.getDJISourceSNFromTopic(m.Topic)
		if tSn == "" || tSn == gateway {
			return s.handleGatewayOSD(ctx, m)
		}
		return s.handleDJIDroneOSD(ctx, tSn, m)
	})
}

func (s *DJIConnectService) newStateHandler() mqtt.MessageHandler {
	return newDJIHandler(s, "thing/product/+/state", func(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
		gateway := gjson.GetBytes(m.Payload, "gateway").String()
		tSn := s.getDJISourceSNFromTopic(m.Topic)
		if tSn == gateway || tSn == "" {
			return s.handleGatewayState(ctx, gateway, m)
		} else {
			req := &thing.DJIDroneStateMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
				return nil, err
			}
			s.log.Infof("dock %s drone %s state changed %+v", gateway, tSn, req)
			dev, err := s.tu.GetDevice(ctx, gateway)
			if err != nil {
				s.log.Errorf("get device %s failed %v", req.Gateway, err)
				return nil, nil
			}
			p := req.ToDockDroneBiz(dev, tSn)
			s.log.Debugf("rpc dock drone %s state %s to skai", p.Sn, p.Id)
			if err = s.tu.PushGatewayDroneProperties(ctx, p); err != nil {
				s.log.Errorf("rpc dock drone %s state %s to skai failed %v", p.Sn, p.Id, err)
			}
		}
		return nil, nil
	})
}

func (s *DJIConnectService) newDJIStatusHandler() mqtt.MessageHandler {
	return newDJIHandler(s, "sys/product/+/status", func(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
		req := &thing.DJIGatewayTopoMessage{}
		if err := json.Unmarshal(m.Payload, req); err != nil {
			s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
			return nil, nil
		}
		reply := &thing.DJIStatusReplyMessage{
			DJIMessage: req.DJIMessage,
			Data:       thing.DJIStatusReplyData{},
		}
		sn := s.getDJISourceSNFromTopic(m.Topic)
		if dev, err := s.tu.GetDevice(ctx, sn); err == nil {
			event := req.ToBiz(dev)
			if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
				s.log.Errorf("handle %s topo update err %v", event.Sn, err)
				return nil, nil
			}
		} else {
			s.log.Errorf("get device %s for topo update err %v", sn, err)
			reply.Data.Result = 404
		}
		reply.Timestamp = time.Now().UnixMilli()
		replyData, _ := json.Marshal(reply)
		return &mqtt.Reply{
			Topic: fmt.Sprintf("sys/product/%s/status_reply", sn),
			Qos:   1,
			Data:  replyData,
		}, nil
	})
}

func (s *DJIConnectService) newDJIServiceReplyHandler() mqtt.MessageHandler {
	return newDJIHandler(s, "thing/product/+/services_reply", func(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
		reply := &thing.DJIStatusReplyMessage{}
		if err := json.Unmarshal(m.Payload, reply); err != nil {
			s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
			return nil, nil
		}
		sn := reply.Gateway
		// 遥控器
		if sn == "" {
			state, err := s.tu.GetDownlinkState(ctx, biz.ModelDJIPilot, reply.Bid)
			if err != nil {
				s.log.Errorf("find no state for serviceReply: %s err %v", m.Payload, err)
				return nil, fmt.Errorf("find no state for serviceReply %s method %s", reply.Bid, reply.Method)
			}
			sn = state.(*biz.DockService).Sn
		}
		if dev, err := s.tu.GetDevice(ctx, sn); err == nil {
			status := reply.Data.Output.Status
			if status == "" {
				status = lo.Ternary(reply.Data.Result == 0, biz.ServiceReplyStatusOk.String(), biz.ServiceReplyStatusFailed.String())
			}
			em := conv.Itoa(int64(reply.Data.Result))
			if v, ok := biz.ServiceReplyCodeDescMap[em]; ok {
				em = v
			}
			sr := &biz.DockServiceReply{
				Code:       reply.Data.Result,
				Id:         reply.Bid,
				RxTime:     time.Now(),
				Timestamp:  time.UnixMilli(reply.Timestamp),
				Sn:         dev.Sn,
				DeviceId:   dev.Id,
				Identifier: string(thing.NewDockServiceIdentifier(reply.Method)),
				Status:     biz.ServiceReplyStatus(status),
				Message:    em,
			}
			if reply.Method == thing.DJIMethodQueryLogs {
				rd := &thing.DJIDockLogListReplyData{}
				if err := json.Unmarshal([]byte(gjson.GetBytes(m.Payload, "data").Raw), rd); err == nil {
					sr.Data = rd.AsReplyData()
				}
			}
			if err := s.tu.PushServiceReply(ctx, dev, sr); err != nil {
				s.log.Errorf("handle %s service reply for %s err %v", dev.Sn, reply.Bid, err)
			}
			return nil, nil
		} else {
			s.log.Errorf("get device %s for serviceReply update err %v", reply.Gateway, err)
			return nil, nil
		}
	})
}

func (s *DJIConnectService) newDJIDockEventHanlder() mqtt.MessageHandler {
	handleEventParseErr := func(m *mqtt.Message, err error) {
		s.log.Errorf("parse json for topic %s msg %s err %v", m.Topic, m.Payload, err)
	}
	newEventReply := func(deviceSn string, e thing.DJIEventMessage, code int32) (*mqtt.Reply, error) {
		if e.NeedReply == 0 {
			return nil, nil
		}
		reply := &thing.DJIReplyMessage{
			DJIMessage: e.DJIMessage,
			Data: thing.DJIReplyData{
				Result: code,
			},
		}
		reply.Timestamp = time.Now().UnixMilli()
		data, _ := json.Marshal(reply)
		return &mqtt.Reply{
			Topic: fmt.Sprintf("thing/product/%s/events_reply", deviceSn),
			Qos:   1,
			Data:  data,
		}, nil
	}
	return newDJIHandler(s, "thing/product/+/events", func(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error) {
		method := gjson.GetBytes(m.Payload, "method").String()
		tSn := s.getDJISourceSNFromTopic(m.Topic)
		switch method {
		case thing.DJIMethodFlightTaskProgress:
			req := &thing.DJIDockFlightTaskProgressMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				event := req.ToBiz(dev)
				if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
					s.log.Errorf("handle %s DJIMethodFlightTaskProgress err %v", event.Sn, err)
					return nil, nil
				}
			} else {
				s.log.Errorf("get device %s for DJIMethodFlightTaskProgress err %v", req.Gateway, err)
			}
			return newEventReply(tSn, req.DJIEventMessage, 0)
		case thing.DJIMethodHMS:
			req := &thing.DJIDockHMSEventMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				events := req.ToBizs(dev)
				for _, event := range events {
					event.ThingEvent.RxTime = event.RxTime.Add(1 * time.Millisecond)
					if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
						s.log.Errorf("handle %s DJIMethodHMS err %v", event.Sn, err)
						return nil, nil
					}
				}
			} else {
				s.log.Errorf("get device %s for DJIMethodHMS err %v", req.Gateway, err)
			}
			return newEventReply(tSn, req.DJIEventMessage, 0)
		case thing.DJIMethodDRCStatusNotify:
			req := &thing.DJIDockDRCStatusNotifyMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, tSn); err == nil {
				event := req.ToBiz(dev)
				if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
					s.log.Errorf("handle %s DJIMethodDRCStatusNotify err %v", event.Sn, err)
					return nil, nil
				}
			} else {
				s.log.Errorf("get device %s for DJIMethodDRCStatusNotify err %v", req.Gateway, err)
			}
			return newEventReply(tSn, req.DJIEventMessage, 0)
		case thing.DJIMethodFilghtTaskFileUpload:
			req := &thing.DJIDockMediaUploadResultMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if req.Data.File == nil {
				s.log.Errorf("recvDJIMethodFilghtTaskFileUpload invalid, empty file: %s", m.Payload)
				return newEventReply(tSn, req.DJIEventMessage, 400)
			}
			dev, err := s.tu.GetDevice(ctx, req.Gateway)
			if err != nil {
				return newEventReply(tSn, req.DJIEventMessage, 50001)
			}
			voyage, err := s.tu.GetVoyageByFlight(ctx, req.Data.File.Ext.FlightId, true)
			if err != nil {
				s.log.Error("handleDJIMethodFilghtTaskFileUpload.GetVoyageByFlight failed:", err)
				return newEventReply(req.Gateway, req.DJIEventMessage, 50002)
			}
			if extName := strings.ToLower(path.Ext(req.Data.File.Name)); extName != "" && lo.Contains([]string{".obs", ".rtk", ".mrk", ".nav", ".dat"}, extName) {
				s.log.Infof("recvDJIMethodFilghtTaskFileUpload invalid, skip: %s", m.Payload)
				s.tu.SaveVoyagePPKOrRTCM(ctx, voyage, req.Data.File.ObjectKey)
				return newEventReply(tSn, req.DJIEventMessage, 0)
			}
			if mediaCount := req.Data.FlightTask.ExpectedFileCount + 1; voyage.DroneMediaTotal != mediaCount {
				if err = s.tu.UpdateVoyageMediaTotal(ctx, voyage.Id, mediaCount); err != nil {
					s.log.Error("handleDJIMethodFilghtTaskFileUpload.UpdateVoyageMediaTotal failed:", err)
				}
			}
			ps, err := s.tu.GetAirlineWaypoints(ctx, voyage.AirlineId)
			if err != nil {
				s.log.Error("handleDJIMethodFilghtTaskFileUpload.GetAirlineWaypoints failed:", err)
				return newEventReply(req.Gateway, req.DJIEventMessage, 50002)
			}
			m := req.ToMedia(dev, voyage, ps)
			if err := s.mu.Create(ctx, m); err != nil {
				s.log.Error("handleDJIMethodFilghtTaskFileUpload.CreateMedia failed:", err)
				return newEventReply(req.Gateway, req.DJIEventMessage, 50003)
			}
			// 媒体库新图片通知消息
			go utilities.NewRecovedGOFunc(func() error {
				actx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()
				s.mu.PushMediaUploadedMessage(actx, m, req.Data.FlightTask.ExpectedFileCount+1, req.Data.FlightTask.UploadedFileCount+1)
				return nil
			})()
			// 大模型算法解析图片
			if m.Type == biz.MediaTypePhoto {
				go utilities.NewRecovedGOFunc(func() error {
					actx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
					defer cancel()
					device := &biz.JointDevice{}
					device.Copy(dev)
					execution, err := s.su.GetExecution(actx, &biz.ExecutionQuery{
						VoyageId:    voyage.Id,
						ProjectInfo: biz.ProjectInfo{MerchantIds: []int64{voyage.MerchantId}},
					})
					if err != nil {
						s.log.Warnf("handleDJIMethodFilghtTaskFileUpload.GetExecution failed: %+v", err)
						return nil
					}
					mission := execution.Mission
					if mission == nil {
						s.log.Warnf("handleDJIMethodFilghtTaskFileUpload.Execution.MissionSnap is nil")
						return nil
					}
					mission.LastExecution = execution
					config, ok := lo.Find(mission.AlgConfigs, func(item *biz.AlgConfig) bool {
						return item.Type == biz.AlgTypePathshot
					})
					if !ok || config == nil {
						s.log.Warnf("handleDJIMethodFilghtTaskFileUpload.Pathshot.Algorithm is empty")
						return nil
					}
					if config.Name == biz.AlgNameUnknown {
						s.log.Warnf("handleDJIMethodFilghtTaskFileUpload.AiEventType is unknown: %s", config.Name)
						return nil
					}
					algoData, err := s.au.PathshotDetect(actx, config.Name, m)
					if err != nil || algoData.ObjectCount == 0 {
						s.log.Errorf("handleDJIMethodFilghtTaskFileUpload.HandlePathshot failed: %+v", err)
						return nil
					}
					s.log.Debugf("handleDJIMethodFilghtTaskFileUpload.HandlePathshot.AlgData: %+v", algoData)
					imageUrl, _ := s.mu.GetSignMediaURL(actx, m, 5*time.Minute)
					blEvent := pb.TransformAlgoEventToLinsEvent(device, mission, &biz.AlgoEventDto{
						AlgoDetectData: *algoData,
						ImageUrl:       imageUrl,
						AlgType:        biz.AlgTypePathshot,
						Timestamp:      m.Meta.ShootTime.UnixMilli(),
						Lnglat:         lo.Map(m.Meta.ShootLnglat, func(p float64, _ int) float32 { return float32(p) }),
						Location:       device.Deployment.Location,
					})
					s.log.Debugf("handleDJIMethodFilghtTaskFileUpload.HandlePathshot.AlgoEvent: %+v", *blEvent)
					if err := s.au.PushAIEvent(actx, blEvent); err != nil {
						s.log.Errorf("handleDJIMethodFilghtTaskFileUpload.PushAIEvent failed:", err)
						return nil
					}
					return nil
				})()
			}
			return newEventReply(tSn, req.DJIEventMessage, 0)
		// case thing.DJIMethodExitHomingNotify:
		case thing.DJIMethodFlyToPointProgress:
			req := &thing.DJIFlyToProgressMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				event := req.ToBiz(dev)
				if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
					s.log.Errorf("handle %s DJIMethodFlyToPointProgress err %v", event.Sn, err)
					return nil, nil
				}
			} else {
				s.log.Errorf("get device %s for DJIMethodFlyToPointProgress err %v", req.Gateway, err)
			}
			return newEventReply(tSn, req.DJIEventMessage, 0)
		case thing.DJIMethodTakeoffToPointProgress:
			req := &thing.DJITakeoffPointProgressMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				event := req.ToBiz(dev)
				if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
					s.log.Errorf("handle %s DJIMethodTakeoffToPointProgress err %v", event.Sn, err)
					return nil, nil
				}
			} else {
				s.log.Errorf("get device %s for DJIMethodTakeoffToPointProgress err %v", req.Gateway, err)
			}
			return newEventReply(tSn, req.DJIEventMessage, 0)
		case thing.DJIMethodSpeakerAudioPlayProgress, thing.DJIMethodSpeakerTTSPlayProgress:
			req := &thing.DJISpeakerPalyProgresEvent{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				event := req.ToBiz(dev, lo.Ternary[int32](method == thing.DJIMethodSpeakerAudioPlayProgress, 1, 0))
				if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
					s.log.Errorf("handle %s DJIMethodSpeakerAudioPlayProgress err %v", event.Sn, err)
					return nil, nil
				}
			} else {
				s.log.Errorf("get device %s for DJIMethodSpeakerAudioPlayProgress err %v", req.Gateway, err)
			}
		case thing.DJIMethodPOIStatusNotify:
			req := &thing.DJIPOIStatusMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				event := req.ToBiz(dev)
				if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
					s.log.Errorf("handle %s DJIMethodPOIStatusNotify err %v", event.Sn, err)
					return nil, nil
				}
			} else {
				s.log.Errorf("get device %s for DJIMethodPOIStatusNotify err %v", req.Gateway, err)
			}
			return newEventReply(tSn, req.DJIEventMessage, 0)
		//case thing.DJIMethodOpenDockCover, thing.DJIMethodCloseDockCover, thing.DJIMethodOpenDrone, thing.DJIMethodCloseDrone, thing.DJIMethodDockReboot:
		// 开关舱门开关飞机无进度字段，忽略
		case thing.DJIMethodDockReboot:
			req := &thing.DJIJobProgressMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				event := req.ToBiz(dev)
				if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
					s.log.Errorf("handle %s DJIMethod %s err %v", event.Sn, req.Method, err)
					return nil, nil
				}
			} else {
				s.log.Errorf("get device %s for djijobprogress err %v", req.Gateway, err)
			}
			return newEventReply(tSn, req.DJIEventMessage, 0)
		case thing.DJIMethodLogUploadProgress:
			req := &thing.DJIFileUploadProgressMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				event := req.ToBiz(dev)
				if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
					s.log.Errorf("handle %s DJIMethodLogUploadProgress err %v", event.Sn, err)
					return nil, nil
				}
			} else {
				s.log.Errorf("get device %s for DJIMethodLogUploadProgress err %v", req.Gateway, err)
			}
		// 遥控器事件
		case "":
			req := &thing.DJIRemoteControllerHMSEventMessage{}
			if err := json.Unmarshal(m.Payload, req); err != nil {
				handleEventParseErr(m, err)
				return nil, nil
			}
			if dev, err := s.tu.GetDevice(ctx, req.Gateway); err == nil {
				events := req.ToBizs(dev)
				for _, event := range events {
					if _, err = s.tu.PushEvent(ctx, event.ThingEvent, event); err != nil {
						s.log.Errorf("handle %s DJIRemoteControllerHMSEventMessage err %v", event.Sn, err)
						return nil, nil
					}
				}
			} else {
				s.log.Errorf("get device %s for DJIRemoteControllerHMSEventMessage err %v", req.Gateway, err)
			}
		default:
			s.log.Infof("ingore event %s detail %s", method, m.Payload)
			e := thing.DJIEventMessage{}
			if err := json.Unmarshal(m.Payload, &e); err != nil {
				s.log.Errorf("handle %s err %v", method, err)
				return nil, nil
			}
			return newEventReply(tSn, e, 0)
		}
		return nil, nil
	})
}

// https://developer.dji.com/doc/cloud-api-tutorial/cn/server-api-reference/mqtt/topic-definition.html
func (s *DJIConnectService) getDJISourceSNFromTopic(t string) string {
	v := strings.Split(t, "/")
	if len(v) > 3 {
		return v[2]
	}
	return ""
}

func newDJIHandler(s *DJIConnectService, topic string, h func(context.Context, *mqtt.Message) (*mqtt.Reply, error)) *djiHandler {
	return &djiHandler{
		log:     s.log,
		tu:      s.tu,
		topic:   topic,
		handler: h,
	}
}

type djiHandler struct {
	log     *log.Helper
	tu      *biz.ThingUsecase
	topic   string
	handler func(ctx context.Context, m *mqtt.Message) (*mqtt.Reply, error)
}

func (h *djiHandler) Sub() (topic string, qos int) {
	return h.topic, 1
}

func (h *djiHandler) Handle(m *mqtt.Message) (reply *mqtt.Reply, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	// h.log.Debugf("handle msg %s from topic %s", m.Payload, m.Topic)
	return h.handler(ctx, m)
}
