package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func (s *DeviceService) AeromodeDevice(ctx context.Context, req *pb.AeromodeRequest) (ret *pb.CommonReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 飞行控制不为开启状态，无法切换飞行模式
	if !device.IsControlled(biz.AeroControl) {
		return nil, errors.BadRequest("40000003", "设备飞行控制未开启，请勿操作")
	}
	// 当前设备是否支持该指令飞行模式
	if !device.IsAviated(biz.AeroMode(req.AeroMode)) {
		return nil, errors.BadRequest("40000003", "设备不支持该飞行模式，请勿操作")
	}
	// 构造用户信息，任务巡航时获取当前用户信息
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if device.MissionId != nil {
		avatar, _ = s.du.GetAvatar(ctx, projectInfo.AvatarId)
	}
	// 操作人权限判断
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 当前切换的飞行模式
	aeroMode := lo.Ternary(req.Enable, biz.AeroMode(req.AeroMode), biz.NullMode)
	switch aeroMode {
	case biz.FreeMode:
		// 连续自由飞行模式
		if device.IsFreed() {
			s.log.Debugf("设备为连续%s模式", aeroMode.Name())
		}
		// 指点飞行中切换自由飞行模式
		if device.IsPointed() {
			return nil, errors.BadRequest("40000003", "当前无人机正在指点飞行过程，请先暂停或等待无人机到达目标点")
		}
		// 环绕飞行中切换自由飞行模式
		if device.IsOrbited() {
			return nil, errors.BadRequest("40000003", "当前无人机正在环绕飞行过程，请先点击退出")
		}
	case biz.PointMode:
		// 连续指点飞行模式
		if device.IsPointed() {
			s.log.Debugf("设备为连续%s模式", aeroMode.Name())
		} else {
			// 自由飞行中/环绕飞行中切换指点飞行模式
			s.log.Debugf("设备由%s切换%s模式", device.AeroMode, aeroMode.Name())
		}
	case biz.OrbitMode:
		// 连续环绕飞行模式
		if device.IsOrbited() {
			return nil, errors.BadRequest("40000003", "当前无人机正在环绕飞行过程，请先点击退出")
		}
		// 自由飞行中切换环绕飞行模式
		if device.IsFreed() {
			s.log.Debugf("设备由%s切换%s模式", device.AeroMode, aeroMode.Name())
		}
		// 指点飞行中切换环绕飞行模式
		if device.IsPointed() {
			return nil, errors.BadRequest("40000003", "当前无人机正在指点飞行过程，请先暂停或等待无人机到达目标点")
		}
	default:
		return nil, errors.BadRequest("40000003", "暂不支持该飞行模式，请重新操作")
	}
	if err := s.du.UpdateDevice(ctx, device.Id, device.Aviate(aeroMode)); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *DeviceService) FreeDevice(ctx context.Context, req *pb.FreeRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 飞行控制不为开启状态，无法切换飞行模式
	if !device.IsControlled(biz.AeroControl) {
		return nil, errors.BadRequest("40000003", "设备飞行控制未开启，请勿操作")
	}
	// 当前设备是否支持自由飞行模式
	if !device.IsAviated(biz.FreeMode) {
		return nil, errors.BadRequest("40000003", "设备不支持自由飞行，请勿操作")
	}
	// 当前有未完成的指点飞行任务，禁止操作
	if device.IsPointed() {
		return nil, errors.BadRequest("40000003", "当前无人机正在指点飞行过程，请先暂停或等待无人机到达目标点")
	}
	// 当前有未完成的环绕飞行任务，禁止操作
	if device.IsOrbited() {
		return nil, errors.BadRequest("40000003", "当前无人机正在环绕飞行过程，请先点击退出")
	}
	// 设备电量低于自由飞行最小值，则无法操作
	if device.IsFused(biz.FREE_MIN_BATTERY) {
		return nil, errors.BadRequest("40000003", "当前电量低于30%，禁止进入自由飞行模式")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 显式设置为自由飞行模式
	s.du.UpdateDevice(ctx, device.Id, device.Aviate(biz.FreeMode))
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeEnterFree,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    biz.AnyMap{"Frequency": req.Frequency},
	})
	if err != nil {
		return nil, err
	}
	payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierEnterDRCMode,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) PointDevice(ctx context.Context, req *pb.PointRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 飞行控制不为开启状态，无法切换飞行模式
	if !device.IsControlled(biz.AeroControl) {
		return nil, errors.BadRequest("40000003", "设备飞行控制未开启，请勿操作")
	}
	// 当前设备是否支持指点飞行模式
	if !device.IsAviated(biz.PointMode) {
		return nil, errors.BadRequest("40000003", "设备不支持指点飞行，请勿操作")
	}
	// 飞向目标操作前提条件：指点飞行模式
	if device.AeroMode != biz.PointMode {
		return nil, errors.BadRequest("40000003", "当前用户未开启指点飞行，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 自由飞行/环绕飞行切换到指点飞行模式，隐式退出当前模式
	if device.IsFreed() || device.IsOrbited() {
		opType := biz.TypeExitFree
		identifier := biz.DockServiceIdentifierExitDRCMode
		if device.IsOrbited() {
			opType = biz.TypeExitOrbit
			identifier = biz.DockServiceIdentifierExitPOIMode
		}
		payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
		if operation, err := s.du.CreateOperation(ctx, &biz.Operation{
			MerchantId: device.MerchantId,
			AvatarId:   projectInfo.AvatarId,
			TenantId:   device.TenantId,
			SourceId:   device.Id,
			Type:       opType,
			Source:     biz.SourceDevice,
			From:       "WEB",
			Status:     biz.OperationStatusPending,
			Content:    utilities.StructToMap(payload),
		}); err == nil {
			s.tu.SendDockService(ctx, &biz.DockService{
				Sn:         device.SourceSn,
				DeviceId:   device.Id,
				Identifier: identifier,
				ServiceId:  operation.Id,
				Timeout:    biz.ServiceTimeout,
				Payload:    payload,
			})
			// 延迟100ms再继续下行命令
			time.Sleep(100 * time.Millisecond)
		}
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeFlytoPoint,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    biz.AnyMap{"MaxSpeed": req.Speed, "Lnglat": req.Lnglat, "Height": req.Height},
	})
	if err != nil {
		return nil, err
	}
	guidePoint := biz.GuidePoint{Point: req.Lnglat, Height: req.Height}
	payload := &biz.FlytoPointPayload{MaxSpeed: int32(req.Speed), Points: []biz.GuidePoint{guidePoint}}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierFlytoPoint,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) RepointDevice(ctx context.Context, req *pb.PointRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 飞行控制不为开启状态，无法切换飞行模式
	if !device.IsControlled(biz.AeroControl) {
		return nil, errors.BadRequest("40000003", "设备飞行控制未开启，请勿操作")
	}
	// 当前设备是否支持指点飞行模式
	if !device.IsAviated(biz.PointMode) {
		return nil, errors.BadRequest("40000003", "设备不支持指点飞行，请勿操作")
	}
	// 飞向目标操作前提条件：指点飞行模式
	if device.AeroMode != biz.PointMode {
		return nil, errors.BadRequest("40000003", "当前用户未开启指点飞行，请勿操作")
	}
	// 更换目标操作前提条件：在指点飞行中
	if !device.IsPointed() {
		return nil, errors.BadRequest("40000003", "当前用户不在指点飞行过程，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeFlywardPoint,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    biz.AnyMap{"MaxSpeed": req.Speed, "Lnglat": req.Lnglat, "Height": req.Height},
	})
	if err != nil {
		return nil, err
	}
	guidePoint := biz.GuidePoint{Point: req.Lnglat, Height: req.Height}
	payload := &biz.FlytoPointPayload{MaxSpeed: int32(req.Speed), Points: []biz.GuidePoint{guidePoint}}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierUpdatePoint,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) OrbitDevice(ctx context.Context, req *pb.PointRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 飞行控制不为开启状态，无法切换飞行模式
	if !device.IsControlled(biz.AeroControl) {
		return nil, errors.BadRequest("40000003", "设备飞行控制未开启，请勿操作")
	}
	// 当前设备是否支持环绕飞行模式
	if !device.IsAviated(biz.OrbitMode) {
		return nil, errors.BadRequest("40000003", "设备不支持环绕飞行，请勿操作")
	}
	// 当前有未完成的指点飞行任务，禁止操作
	if device.IsPointed() {
		return nil, errors.BadRequest("40000003", "当前无人机正在指点飞行过程，请先暂停或等待无人机到达目标点")
	}
	// 当前有未完成的环绕飞行任务，禁止操作
	if device.IsOrbited() {
		return nil, errors.BadRequest("40000003", "当前无人机正在环绕飞行过程，请先点击退出")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 自由飞行切换到环绕飞行模式，隐式退出自由飞行
	if device.IsFreed() {
		opType := biz.TypeExitFree
		payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
		if operation, err := s.du.CreateOperation(ctx, &biz.Operation{
			MerchantId: device.MerchantId,
			AvatarId:   projectInfo.AvatarId,
			TenantId:   device.TenantId,
			SourceId:   device.Id,
			Type:       opType,
			Source:     biz.SourceDevice,
			From:       "WEB",
			Status:     biz.OperationStatusPending,
			Content:    utilities.StructToMap(payload),
		}); err == nil {
			s.tu.SendDockService(ctx, &biz.DockService{
				Sn:         device.SourceSn,
				DeviceId:   device.Id,
				Identifier: biz.DockServiceIdentifierExitDRCMode,
				ServiceId:  operation.Id,
				Timeout:    biz.ServiceTimeout,
				Payload:    payload,
			})
			// 延迟100ms再继续下行命令
			time.Sleep(100 * time.Millisecond)
		}
	}
	// 环绕飞行必须开启镜头控制，如果未开启，则隐式开启
	if !device.IsControlled(biz.LensControl) {
		opType := biz.TypeControlLens
		payload := &biz.SubdevicePayload{}
		// 查找对应的云台camera子设备
		if subdevice := device.GetGimbal(); subdevice != nil {
			payload.Subdevice = *subdevice
		}
		if operation, err := s.du.CreateOperation(ctx, &biz.Operation{
			MerchantId: device.MerchantId,
			AvatarId:   projectInfo.AvatarId,
			TenantId:   device.TenantId,
			SourceId:   device.Id,
			Type:       opType,
			Source:     biz.SourceDevice,
			From:       "WEB",
			Status:     biz.OperationStatusPending,
			Content:    utilities.StructToMap(payload),
		}); err == nil {
			s.tu.SendDockService(ctx, &biz.DockService{
				Sn:         device.SourceSn,
				DeviceId:   device.Id,
				Identifier: biz.DockServiceIdentifierControlLens,
				ServiceId:  operation.Id,
				Timeout:    biz.ServiceTimeout,
				Payload:    payload,
			})
			// 锁定设备状态，防止多次控制
			s.du.UpdateDevice(ctx, device.Id, device.Control(opType.ToControlAction(), true))
			// 延迟100ms再继续下行命令
			time.Sleep(100 * time.Millisecond)
		}
	}
	// 创建操作记录
	payload := &biz.StartOrbitPayload{Speed: req.Speed, Radius: req.Radius, Point: biz.GuidePoint{Point: req.Lnglat, Height: req.Height}}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeEnterOrbit,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行进入环绕模式命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierEnterPOIMode,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) OrbeedDevice(ctx context.Context, req *pb.OrbeedRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 当前设备是否支持环绕飞行模式
	if !device.IsAviated(biz.OrbitMode) {
		return nil, errors.BadRequest("40000003", "设备不支持环绕飞行，请勿操作")
	}
	// 设置环绕速度前提条件：在环绕飞行中
	if !device.IsOrbited() {
		return nil, errors.BadRequest("40000003", "当前无人机不在环绕飞行中，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeSpeedOrbit,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    biz.AnyMap{"Speed": req.Speed},
	})
	if err != nil {
		return nil, err
	}
	// 下行设置环绕速度命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierUpdatePOISpeed,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    &biz.StartOrbitPayload{Speed: req.Speed},
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) CrashDevice(ctx context.Context, req *pb.CommonRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 急停操作前提条件：开启飞行控制
	if !device.IsControlled(biz.AeroControl) {
		return nil, errors.BadRequest("40000003", "当前用户未开启飞行控制，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 根据当前在飞行任务转换急停指令
	opType := biz.TypeUnknown
	if device.IsFreed() {
		opType = biz.TypeExitFree
	} else if device.IsPointed() {
		opType = biz.TypeFlyoffPoint
	} else if device.IsOrbited() {
		opType = biz.TypeExitOrbit
	}
	if opType == biz.TypeUnknown {
		return nil, errors.BadRequest("40000003", "当前没有正在执行任务，请勿操作")
	}
	// 创建操作记录
	payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: opType.ToServiceIdentifier(),
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) DriveDevice(ctx context.Context, req *pb.DriveRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 操作前提条件：开启飞行控制
	if !device.IsControlled(biz.AeroControl) {
		return nil, errors.BadRequest("40000003", "当前用户未开启飞行控制，请勿操作")
	}
	// 当前设备是否支持自由飞行模式
	if !device.IsAviated(biz.FreeMode) {
		return nil, errors.BadRequest("40000003", "设备不支持自由飞行，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	opType := biz.TypeExecuteFree
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Message:    "success",
		Status:     biz.OperationStatusSuccess,
		Content:    biz.AnyMap{"XSpeed": req.XSpeed, "YSpeed": req.YSpeed, "HSpeed": req.HSpeed, "WSpeed": req.WSpeed},
	})
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}
