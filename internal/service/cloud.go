package service

import (
	"context"
	"io"
	"net/http"
	"unicode"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	json "github.com/goccy/go-json"
	"github.com/jinzhu/now"
	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/cloud/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/pkg/id"
)

type CloudService struct {
	pb.UnimplementedCloudServer
	log *log.Helper
	cu  *biz.CloudUsecase
}

func NewCloudService(logger log.Logger, cu *biz.CloudUsecase) *CloudService {
	return &CloudService{log: log.NewHelper(logger), cu: cu}
}

func (s *CloudService) FastUploadMedia(ctx context.Context, req *pb.FastUploadRequest) (*pb.CommonReply, error) {
	exist, err := s.cu.CheckDeviceMediaFile(ctx, &biz.MediaFileWithSignature{
		DroneModel:     req.Ext.DroneModelKey,
		IsOriginal:     req.Ext.IsOriginal,
		PayloadModel:   req.Ext.PayloadModelKey,
		Signature:      req.Fingerprint,
		ShortSignature: req.Ext.TinyFingerprint,
	})
	if err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Code:    lo.Ternary[int32](exist, 0, 404),
		Message: SuccessMessage,
	}, nil
}

// func (s *CloudService) TinyFingerprints(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
// 	projectInfo, err := s.cu.GetProjectInfo(ctx)
// 	if err != nil {
// 		return nil, errors.BadRequest("40000002", err.Error())
// 	}
// 	return &pb.CommonReply{
// 		Message: SuccessMessage,
// 		Data:    &pb.CommonReplyOkData{Status: projectInfo.ProjectId > 0},
// 	}, nil
// }

func (s *CloudService) UploadedMedia(ctx context.Context, req *pb.UploadMediaRequest) (*pb.UploadMediaReply, error) {
	if req.Ext == nil {
		return nil, biz.NewBadRequestError("UploadedMedia.ext", nil)
	}
	if req.Metadata == nil {
		return nil, biz.NewBadRequestError("UploadedMedia.metadata", nil)
	}
	shootTime, err := now.Parse(string(lo.Filter([]rune(req.Metadata.CreatedTime), func(it rune, _ int) bool {
		return unicode.IsPrint(it)
	})))
	if err != nil {
		return nil, biz.NewBadRequestError("UploadedMedia.createdTime", nil)
	}
	md := &biz.UploadedMediaData{
		DroneSn:   req.Ext.Sn,
		Name:      req.Name,
		ObjectKey: req.ObjectKey,
		//Path:      req.Path,
		FlightId: req.Path,
		Meta: &biz.MediaMeta{
			Yaw:            req.Metadata.GimbalYawDegree,
			Altitude:       req.Metadata.AbsoluteAltitude,
			RelativeHeight: req.Metadata.RelativeAltitude,
			ShootLnglat:    []float64{req.Metadata.ShootPosition.Lng, req.Metadata.ShootPosition.Lat},
			IsOriginal:     req.Ext.IsOriginal,
			ShootTime:      shootTime,
		},
		Signature:      req.Fingerprint,
		ShortSignature: req.Ext.TinyFingerprint,
		SubDeviceIndex: req.Ext.PayloadModelKey,
	}
	if req.SubFileType != nil {
		t := *req.SubFileType
		md.IfPanorama = lo.ToPtr(lo.Ternary(t == 1, true, false))
	}
	if err := s.cu.UploadedMedia(ctx, md); err != nil {
		return nil, err
	}
	return &pb.UploadMediaReply{
		Message: SuccessMessage,
		Data: &pb.UploadMediaReply_Data{
			ObjectKey: md.ObjectKey,
		},
	}, nil
}

func (s *CloudService) UploadedMediaGroup(ctx context.Context, req *pb.UploadMediaGroupRequest) (*pb.CommonReply, error) {
	s.log.WithContext(ctx).Infof("UploadedMediaGroup %+v", req)
	return &pb.CommonReply{
		Message: SuccessMessage,
	}, nil
}

func (s *CloudService) GenerateSTS(ctx context.Context, _ *pb.CommonRequest) (*pb.STSReply, error) {
	sts, err := s.cu.GetPilotMediaUploadConfiig(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.STSReply{
		Message: SuccessMessage,
		Data: &pb.STSReply_STSReplyData{
			Bucket: sts.Credentials.Bucket,
			Credentials: &pb.STSCredential{
				AccessKeyId:     sts.Credentials.AccessKey,
				AccessKeySecret: sts.Credentials.AccessSecret,
				Expire:          float64(sts.Credentials.Expire),
				SecurityToken:   sts.Credentials.SessionToken,
			},
			Endpoint:        sts.Credentials.Endpoint,
			ObjectKeyPrefix: sts.KeyPrefix,
			Provider:        sts.Credentials.Provider,
			Region:          sts.Credentials.Region,
		},
	}, nil
}

func (s *CloudService) ListWaylines(ctx context.Context, req *pb.WaylineListRequest) (*pb.WaylineListReply, error) {
	projectInfo, err := s.cu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	total, airlines, err := s.cu.ListAirline(ctx, &biz.AirlineListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.PageSize),
	})
	if err != nil {
		return nil, err
	}
	pagination := &pb.Pagination{Total: total, Page: req.Page, PageSize: req.PageSize}
	list := lo.Map(airlines, func(a *biz.Airline, _ int) *pb.Wayline {
		return &pb.Wayline{
			Id:          id.FlakeToUUID(a.Id),
			Name:        a.Name,
			UpdatedTime: float64(a.UpdatedTime.UnixMilli()),
		}
	})
	return &pb.WaylineListReply{
		Message: SuccessMessage,
		Data: &pb.WaylineListReplyListData{
			List:       list,
			Pagination: pagination,
		},
	}, nil
}

func (s *CloudService) GetWaylineUrl(ctx context.Context, req *pb.WaylineUrlRequest) (*pb.WaylineUrlReply, error) {
	projectInfo, err := s.cu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	airline, err := s.cu.GetAirline(ctx, &biz.AirlineQuery{Id: id.UUIDToFlake(req.Wid), ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航线不存在")
	}
	return &pb.WaylineUrlReply{
		Message: SuccessMessage,
		Data:    &pb.WaylineUrlReplyOkData{Url: airline.KMZFile.Url},
	}, nil
}

func (s *CloudService) UploadedWayline(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.cu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyOkData{Status: projectInfo.ProjectId > 0},
	}, nil
}

func (s *CloudService) FavoriteWaylines(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.cu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyOkData{Status: projectInfo.ProjectId > 0},
	}, nil
}

func (s *CloudService) UnfavoriteWaylines(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.cu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyOkData{Status: projectInfo.ProjectId > 0},
	}, nil
}

func (s *CloudService) NewExistMediaFilesHandler() http.HandlerFunc {
	return wrapHttpHandlerFunc(s.log, func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}
		body, err := io.ReadAll(r.Body)
		if err != nil {
			ReponseHttpErr(err, w)
			return
		}
		if len(body) == 0 {
			ResponseJson(map[string]any{
				"code":    0,
				"message": SuccessMessage,
				"data": map[string]any{
					"tiny_fingerprints": []string{},
				},
			}, w)
			return
		}
		var shortShignatures []string
		if err := json.Unmarshal(body, &shortShignatures); err != nil {
			ReponseHttpErr(err, w)
			return
		}
		list, err := s.cu.GetDeviceExistMediaFiles(r.Context(), shortShignatures)
		if err != nil {
			ReponseHttpErr(err, w)
			return
		}
		ResponseJson(map[string]any{
			"code":    0,
			"message": SuccessMessage,
			"data": map[string]any{
				"tiny_fingerprints": lo.FilterMap(list, func(it *biz.Media, _ int) (string, bool) { return it.GetShortSignature() }),
			},
		}, w)
	})
}
