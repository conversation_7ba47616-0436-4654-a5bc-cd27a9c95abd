package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type DeviceOperationService struct {
	pb.UnimplementedDeviceOperationServer
	log *log.Helper
	au  *biz.AuthUsecase
	du  *biz.DeviceUsecase
	ou  *biz.OperationUsecase
	lu  *biz.LiveUsecase
}

func NewDeviceOperationService(
	logger log.Logger,
	au *biz.AuthUsecase,
	du *biz.DeviceUsecase,
	ou *biz.OperationUsecase,
	lu *biz.LiveUsecase,
) *DeviceOperationService {
	return &DeviceOperationService{
		log: log.NewHelper(logger),
		au:  au,
		du:  du,
		ou:  ou,
		lu:  lu,
	}
}

var errDeviceNotInDebugMode = errors.BadRequest("debugModeOperation", "设备未处于调试模式")

func (s *DeviceOperationService) DebugMode(ctx context.Context, req *pb.DeviceOperationRequest) (*pb.OperationReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	dev, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *biz.NewProjectInfo(av)})
	if err != nil {
		return nil, err
	}
	ot := lo.Ternary(req.Action == "open", biz.TypeOpenDebug, biz.TypeCloseDebug)
	op, err := s.ou.ExecServiceOperation(ctx, dev, &biz.Operation{
		MerchantId: dev.MerchantId,
		AvatarId:   av.AvatarId,
		TenantId:   dev.TenantId,
		SourceId:   dev.Id,
		Type:       ot,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(&biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}),
	})
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(op),
	}, nil
}

func (s *DeviceOperationService) OperateCover(ctx context.Context, req *pb.DeviceOperationRequest) (*pb.OperationReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	dev, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *biz.NewProjectInfo(av)})
	if err != nil {
		return nil, err
	}
	if !dev.InDebugMode() {
		return nil, errDeviceNotInDebugMode
	}
	ot := lo.Ternary(req.Action == "open", biz.TypeOpenCover, biz.TypeCloseCover)
	op, err := s.ou.ExecServiceOperation(ctx, dev, &biz.Operation{
		MerchantId: dev.MerchantId,
		AvatarId:   av.AvatarId,
		TenantId:   dev.TenantId,
		SourceId:   dev.Id,
		Type:       ot,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(&biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}),
	})
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(op),
	}, nil
}

func (s *DeviceOperationService) OperateReboot(ctx context.Context, req *pb.DeviceOperationRequest) (*pb.OperationReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	dev, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *biz.NewProjectInfo(av)})
	if err != nil {
		return nil, err
	}
	if !dev.InDebugMode() {
		return nil, errDeviceNotInDebugMode
	}
	op, err := s.ou.ExecServiceOperation(ctx, dev, &biz.Operation{
		MerchantId: dev.MerchantId,
		AvatarId:   av.AvatarId,
		TenantId:   dev.TenantId,
		SourceId:   dev.Id,
		Type:       biz.TypeReboot,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(&biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}),
	})
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(op),
	}, nil
}

func (s *DeviceOperationService) OperateDroneSwitch(ctx context.Context, req *pb.DeviceOperationRequest) (*pb.OperationReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	dev, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *biz.NewProjectInfo(av)})
	if err != nil {
		return nil, err
	}
	if !dev.InDebugMode() {
		return nil, errDeviceNotInDebugMode
	}
	ot := lo.Ternary(req.Action == "open", biz.TypeOpenDrone, biz.TypeCloseDrone)
	op, err := s.ou.ExecServiceOperation(ctx, dev, &biz.Operation{
		MerchantId: dev.MerchantId,
		AvatarId:   av.AvatarId,
		TenantId:   dev.TenantId,
		SourceId:   dev.Id,
		Type:       ot,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(&biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}),
	})
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(op),
	}, nil
}

func (s *DeviceOperationService) OperatePutter(ctx context.Context, req *pb.DeviceOperationRequest) (*pb.OperationReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	dev, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *biz.NewProjectInfo(av)})
	if err != nil {
		return nil, err
	}
	if !dev.InDebugMode() {
		return nil, errDeviceNotInDebugMode
	}
	ot := lo.Ternary(req.Action == "open", biz.TypeOpenPutter, biz.TypeClosePutter)
	op, err := s.ou.ExecServiceOperation(ctx, dev, &biz.Operation{
		MerchantId: dev.MerchantId,
		AvatarId:   av.AvatarId,
		TenantId:   dev.TenantId,
		SourceId:   dev.Id,
		Type:       ot,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(&biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}),
	})
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(op),
	}, nil

}

func (s *DeviceOperationService) ForceOperateCover(ctx context.Context, req *pb.DeviceForceCoverRequest) (*pb.OperationReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	dev, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *biz.NewProjectInfo(av)})
	if err != nil {
		return nil, err
	}
	if !dev.InDebugMode() {
		return nil, errDeviceNotInDebugMode
	}
	if dev.PropData != nil {
		if dev.PropData.PutterState != nil && lo.FromPtr(dev.PropData.PutterState) != 0 {
			return nil, errors.BadRequest("forceCoverOperation.putter", "推杆未闭合")
		}
		if dev.PropData.CabinStatus != nil && lo.FromPtr(dev.PropData.CabinStatus) == 1 {
			return nil, errors.BadRequest("forceCoverOperation.cabin", "飞机在舱内")
		}
	}
	op, err := s.ou.ExecServiceOperation(ctx, dev, &biz.Operation{
		MerchantId: dev.MerchantId,
		AvatarId:   av.AvatarId,
		TenantId:   dev.TenantId,
		SourceId:   dev.Id,
		Type:       biz.TypeForceCloseCover,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(&biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}),
	})
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(op),
	}, nil
}

func (s *DeviceOperationService) ChangeLiveCamera(ctx context.Context, req *pb.DeviceLiveCameraRequest) (*pb.OperationReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	dev, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *biz.NewProjectInfo(av)})
	if err != nil {
		return nil, err
	}
	if dev.Model != biz.ModelDJILite {
		return nil, errors.BadRequest("ChangeLiveCamera.req.model", "切换直播相机仅支持二代机场")
	}
	l, err := s.lu.GetLive(ctx, req.LiveId)
	if err != nil {
		return nil, err
	}
	if l.DeviceId != dev.Id {
		return nil, errors.BadRequest("ChangeLiveCamera.req.livecId", "live is not belonged to device")
	}
	payload := &biz.ChangeLiveCameraPayload{Live: l, Position: req.Position}
	op, err := s.ou.ExecServiceOperationWithPayload(ctx, dev, &biz.Operation{
		MerchantId: dev.MerchantId,
		AvatarId:   av.AvatarId,
		TenantId:   dev.TenantId,
		SourceId:   dev.Id,
		Type:       biz.TypeChangeLiveCamera,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	}, payload)
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(op),
	}, nil
}

func (s *DeviceOperationService) OperateBatteryCharge(ctx context.Context, req *pb.DeviceOperationRequest) (*pb.OperationReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	dev, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *biz.NewProjectInfo(av)})
	if err != nil {
		return nil, err
	}
	if !dev.InDebugMode() {
		return nil, errDeviceNotInDebugMode
	}
	ot := lo.Ternary(req.Action == "open", biz.TypeOpenCharge, biz.TypeCloseCharge)
	op, err := s.ou.ExecServiceOperation(ctx, dev, &biz.Operation{
		MerchantId: dev.MerchantId,
		AvatarId:   av.AvatarId,
		TenantId:   dev.TenantId,
		SourceId:   dev.Id,
		Type:       ot,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(&biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}),
	})
	if err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(op),
	}, nil
}
