package service

import (
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type StorageService struct {
	log *log.Helper
	su  *biz.StorageUsecase
}

func NewStorageService(
	logger log.Logger,
	su *biz.StorageUsecase,
) *StorageService {
	return &StorageService{
		log: log.NewHelper(logger),
		su:  su,
	}
}

func (s *StorageService) NewPublicFileHandlerFunc() http.HandlerFunc {
	return wrapHttpHandlerFunc(s.log, func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet {
			s.getFile(w, r)
			return
		}
		if r.Method == http.MethodPut {
			s.putFile(w, r)
			return
		}
		w.WriteHeader(http.StatusMethodNotAllowed)
	})
}

func (s *StorageService) NewM3U8ProxyHandlerFunc() http.HandlerFunc {
	return wrapHttpHandlerFunc(s.log, func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodOptions {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "*")
			w.Header().Set("Access-Control-Allow-Headers", "*")
			w.Header().Set("Access-Control-Max-Age", "50")
			w.WriteHeader(http.StatusOK)
			return
		}
		if r.Method != http.MethodGet {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}
		m, data, err := s.su.PorxyM3U8File(r.Context(), r.URL.Query())
		if err != nil {
			ReponseHttpErr(err, w)
			return
		}
		defer data.Close()
		w.Header().Set("Content-Length", strconv.FormatInt(m.ContentLength, 10))
		w.Header().Set("Content-Type", "application/octet-stream")
		// w.Header().Set("Content-encoding", "none")
		w.Header().Set("vary", "Origin")
		w.WriteHeader(http.StatusOK)
		if _, err := io.Copy(w, data); err != nil {
			s.log.Errorf("proxyM3U8 %s failed %v", r.URL.Path, err)
		}
	})
}

func (s *StorageService) getFile(w http.ResponseWriter, r *http.Request) {
	m, data, err := s.su.GetPublicFile(r.Context(), r.URL.Path)
	if err != nil {
		ReponseHttpErr(err, w)
		return
	}
	defer data.Close()
	w.Header().Set("Content-Type", m.ContentType)
	w.WriteHeader(http.StatusOK)
	_, err = io.Copy(w, data)
	if err != nil && err != io.EOF {
		s.log.Errorf("getFile %s failed %v", r.URL.Path, err)
	}
}

func (s *StorageService) putFile(w http.ResponseWriter, r *http.Request) {
	if err := r.ParseMultipartForm(32 << 20); err != nil {
		ReponseHttpErr(err, w)
		return
	}
	file, header, err := r.FormFile("file")
	if err != nil {
		ReponseHttpErr(err, w)
		return
	}
	defer file.Close()
	ret, err := s.su.UploadPublicFile(r.Context(), &biz.PublicFile{
		Name: header.Filename,
		Data: file,
		Meta: &biz.StorageObjectMeta{ContentLength: header.Size, ContentType: header.Header.Get("Content-Type")},
	})
	if err != nil {
		ReponseHttpErr(err, w)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(fmt.Sprintf(`{"code":0, "data":{"path": "%s"}}`, ret)))
}
