package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/configs/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/pkg/id"
)

type ConfigService struct {
	pb.UnimplementedConfigServer
	log *log.Helper
	cu  *biz.ConfigUsecase
}

func NewConfigService(logger log.Logger, cu *biz.ConfigUsecase) *ConfigService {
	return &ConfigService{log: log.NewHelper(logger), cu: cu}
}

func (s *ConfigService) AccessConfig(ctx context.Context, req *pb.EmptyRequest) (*pb.AccessConfigReply, error) {
	if _, err := s.cu.GetProjectInfo(ctx); err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	config, err := s.cu.GetAccessConfig(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", "校验配置不存在")
	}
	accessConfig := &pb.AccessConfigReplyOkData{}
	copier.Copy(accessConfig, config)
	return &pb.AccessConfigReply{
		Message: SuccessMessage,
		Data:    accessConfig,
	}, nil
}

func (s *ConfigService) ThingConfig(ctx context.Context, req *pb.SNThingRequest) (*pb.ThingConfigReply, error) {
	projectInfo, err := s.cu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	config, err := s.cu.GetThingConfig(ctx, req.Sn)
	if err != nil {
		return nil, errors.BadRequest("40000002", "上云配置不存在")
	}
	config.Workspace = biz.Workspace{
		Id:   id.FlakeToUUID(projectInfo.MerchantId),
		Name: "",
	}
	thingConfig := &pb.ThingConfigReplyOkData{}
	copier.Copy(thingConfig, config)
	return &pb.ThingConfigReply{
		Message: SuccessMessage,
		Data:    thingConfig,
	}, nil
}

func (s *ConfigService) OperationConfig(ctx context.Context, req *pb.EmptyRequest) (*pb.OperationConfigReply, error) {
	if _, err := s.cu.GetProjectInfo(ctx); err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	optypes := []biz.OperationType{
		biz.TypeDeploy,
		biz.TypeLaunch, biz.TypeTakeoff, biz.TypeCruise,
		biz.TypeReturn, biz.TypeBacknow, biz.TypeAutoback, biz.TypeCancel, biz.TypeReboot,
		biz.TypePauseAirline, biz.TypeBackAirline,
		biz.TypeControlLens, biz.TypeReleaseLens, biz.TypeSwitchLens, biz.TypeSwitchZoom,
		biz.TypeSwitchClairty, biz.TypeResetGimbal, biz.TypeTakePicture, biz.TypeStartVideo, biz.TypeStopVideo,
		biz.TypeControlSpeaker, biz.TypeReleaseSpeaker,
		biz.TypeSetPlaymode, biz.TypeTTSSpeaker,
		// biz.TypeAudioSpeaker,
		biz.TypeStopSpeaker, biz.TypeAdjustVolume,
		biz.TypeControlAero, biz.TypeReleaseAero,
		biz.TypeEnterFree, biz.TypeExitFree, biz.TypeExecuteFree,
		biz.TypeEnterOrbit, biz.TypeExitOrbit, biz.TypeSpeedOrbit,
		biz.TypeFlytoPoint, biz.TypeFlyoffPoint, biz.TypeFlywardPoint,
		// biz.TypeRCLostAction,biz.TypeWLLostAction,biz.TypeLimitDistance,
		biz.TypeRemoteLogfile, biz.TypeUploadLogfile,
		biz.TypeCloseDebug, biz.TypeOpenDebug,
		biz.TypeCloseDrone, biz.TypeOpenDrone,
		biz.TypeOpenPutter, biz.TypeClosePutter,
		biz.TypeCloseCover, biz.TypeOpenCover, biz.TypeForceCloseCover,
		biz.TypeOpenCharge,
		biz.TypeUnknown,
	}
	config := &pb.OperationConfigReplyListData{
		Total: int32(len(optypes)),
		List: lo.Map(optypes, func(ot biz.OperationType, _ int) *pb.OperationConfigReplyOptype {
			return &pb.OperationConfigReplyOptype{Type: ot.String(), Name: ot.Translate()}
		}),
	}
	return &pb.OperationConfigReply{
		Message: SuccessMessage,
		Data:    config,
	}, nil
}
