package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type ArchiveTaskService struct {
	log *log.Helper
	uc  *biz.ArchiveTaskUsecase
}

func NewArchiveTaskService(
	logger log.Logger,
	uc *biz.ArchiveTaskUsecase,
) *ArchiveTaskService {
	return &ArchiveTaskService{
		log: log.NewHelper(logger),
		uc:  uc,
	}
}

func (s *ArchiveTaskService) RunNextTask(ctx context.Context, taskInterval time.Duration) error {
	if err := s.uc.GetTaskLock(ctx, taskInterval-30*time.Second); err != nil {
		s.log.Errorf("get task lock error: %v", err)
		return err
	}
	lt, err := s.uc.FindLast(ctx)
	if err != nil {
		if errors.IsNotFound(err) {
			lt = &biz.ArchiveTask{
				TimeScope: biz.TimeScope{
					EndTime: time.Now().Add(-180 * 24 * time.Hour),
				},
			}
		} else {
			return err
		}
	}
	nt := &biz.ArchiveTask{
		TimeScope: biz.TimeScope{
			StartTime: lt.TimeScope.EndTime,
			EndTime:   lt.TimeScope.EndTime.Add(24 * time.Hour),
		},
	}
	if err := s.uc.RunArchiveTask(ctx, nt); err != nil {
		return err
	}
	if err := s.uc.RunVoyageVideoRecordDownsizeTask(ctx, lt, nt); err != nil {
		return err
	}
	return nil
}
