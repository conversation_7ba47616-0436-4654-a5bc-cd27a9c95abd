package service

import (
	"fmt"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"github.com/xuri/excelize/v2"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func (s *DeviceService) NewExportDeviceEventsHandlerFunc() http.HandlerFunc {
	return wrapHttpHandlerFunc(s.log, func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}
		query := r.URL.Query()
		deviceId := conv.Atoi(query.Get("deviceId"))
		if deviceId == 0 {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"code":40000001, "message":"参数不正确, 设备ID必传"}`))
			return
		}
		device, err := s.du.GetDevice(r.Context(), &biz.DetailQuery{Id: deviceId})
		if err != nil || !device.IsDeployed() {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"code":40003004, "message":"设备不存在"}`))
			return
		}
		deployTime := time.Now()
		if device.Deployment.Time != nil {
			deployTime = *device.Deployment.Time
		}
		startTime, endTime := deployTime, deployTime
		if ts := conv.Atoi(query.Get("startTime")); ts > deployTime.UnixMilli() {
			startTime = time.UnixMilli(ts)
		}
		if ts := conv.Atoi(query.Get("endTime")); ts > deployTime.UnixMilli() {
			endTime = time.UnixMilli(ts)
		}
		projectInfo, err := s.du.GetProjectInfo(r.Context())
		if err != nil {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"code":40300001, "message":"Token校验不正确"}`))
			return
		}
		dataCh, err := s.du.ExportEvent(r.Context(), &biz.EventListQuery{
			ProjectInfo: *projectInfo,
			Sn:          device.Sn,
			StartTime:   startTime,
			EndTime:     endTime,
		})
		if err != nil {
			s.log.Error("do ExportDeviceEvents err ", err)
			ReponseHttpErr(err, w)
			return
		}
		f := excelize.NewFile()
		defer f.Close()
		sw, err := f.NewStreamWriter("Sheet1")
		if err != nil {
			s.log.Error("get StreanWriter failed: ", err)
			ReponseHttpErr(err, w)
			return
		}
		sw.SetRow("A1", []any{"时间", "事件类型", "事件说明"})
		rowId := 1
		for e := range dataCh {
			rowId++
			s.log.Debugf("append row %d, value %+v", rowId, r)
			event := pb.BuildDeviceEvent(e)
			row := make([]interface{}, 3)
			row[0] = e.OccurredTime.In(biz.TimeLocation).Format("2006-01-02 15:04:05")
			row[1] = event.Type
			row[2] = event.Description
			cell, err := excelize.CoordinatesToCellName(1, rowId)
			if err != nil {
				s.log.Error("get StreanWriter failed: ", err)
				ReponseHttpErr(err, w)
				break
			}
			if err := sw.SetRow(cell, row); err != nil {
				ReponseHttpErr(err, w)
				break
			}
		}
		if err = sw.Flush(); err != nil {
			s.log.Error("flush StreanWriter failed: ", err)
			ReponseHttpErr(err, w)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/octet-stream")
		f.WriteTo(w)
	})
}

func (s *DeviceService) NewExportDeviceDatalogsHandlerFunc() http.HandlerFunc {
	return wrapHttpHandlerFunc(s.log, func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}
		query := r.URL.Query()
		deviceId := conv.Atoi(query.Get("deviceId"))
		if deviceId == 0 {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"code":40000001, "message":"参数不正确, 设备ID必传"}`))
			return
		}
		pi, err := s.du.GetProjectInfo(r.Context())
		if err != nil {
			w.WriteHeader(http.StatusUnauthorized)
			w.Write([]byte(`{"code":40000001, "message":"token 不存在"}`))
			return
		}
		device, err := s.du.GetDevice(r.Context(), &biz.DetailQuery{Id: deviceId, ProjectInfo: *pi})
		if err != nil || !device.IsDeployed() {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"code":40003004, "message":"设备不存在"}`))
			return
		}
		deployTime := time.Now()
		if device.Deployment.Time != nil {
			deployTime = *device.Deployment.Time
		}
		startTime, endTime := deployTime, deployTime
		if ts := conv.Atoi(query.Get("startTime")); ts > 0 {
			startTime = time.UnixMilli(ts)
		}
		if ts := conv.Atoi(query.Get("endTime")); ts > 0 {
			endTime = time.UnixMilli(ts)
		}
		var builder func(v any) []any
		switch device.Model {
		case biz.ModelDJIDock, biz.ModelDJILite:
			builder = func(v any) []any {
				if v == nil {
					return nil
				}
				return pb.BuildDockDeviceDataLogRow(v.(*biz.DockProperties))
			}
		case biz.ModelDJIPilot:
			builder = pb.BuildControllerDeviceDataLogRow
		default:
			s.log.Error("NewExportDeviceDatalogsHandlerFunc with unsupported model ", device.Model, device.Sn)
			ReponseHttpErr(errors.BadRequest("unsupportedModel", "参数非法，不支持的设备类型"), w)
			return
		}
		dataCh, err := s.du.ExportDockDeviceDatalog(r.Context(), &biz.DeviceDatalogListQuery{
			Id:     device.Id,
			Start:  startTime,
			End:    endTime,
			Device: device,
			Sort:   biz.Sort{Order: biz.ListSortOrderDesc},
		})
		if err != nil {
			s.log.Error("do ExportDeviceDatalog err ", err)
			ReponseHttpErr(err, w)
			return
		}
		f := excelize.NewFile()
		defer f.Close()
		sw, err := f.NewStreamWriter("Sheet1")
		if err != nil {
			s.log.Error("get StreanWriter failed: ", err)
			ReponseHttpErr(err, w)
			return
		}
		sw.SetRow("A1", lo.Map(pb.ExportLogHeaders, func(v string, _ int) any { return v }))
		rowId := 1
		for r := range dataCh {
			rowId++
			s.log.Debugf("append row %d, value %+v", rowId, r)
			// row := pb.BuildDockDeviceDataLogRow(r)
			row := builder(r)
			cell, err := excelize.CoordinatesToCellName(1, rowId)
			if err != nil {
				s.log.Error("get StreanWriter failed: ", err)
				ReponseHttpErr(err, w)
				break
			}
			if err := sw.SetRow(cell, row); err != nil {
				ReponseHttpErr(err, w)
				break
			}
		}
		if err = sw.Flush(); err != nil {
			s.log.Error("flush StreanWriter failed: ", err)
			ReponseHttpErr(err, w)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/octet-stream")
		tf := "200601021504"
		w.Header().Set("Content-Disposition", fmt.Sprintf(`attachment; filename="%d_%s-%s.xlsx"`, deviceId, startTime.Format(tf), endTime.Format(tf)))
		f.WriteTo(w)
	})
}
