package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/jinzhu/copier"
	"github.com/snownd/cake"
	"github.com/tidwall/gjson"
	pb "gitlab.sensoro.com/skai/skai/api/session/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type SessionService struct {
	pb.UnimplementedSessionServer
	log *log.Helper
	su  *biz.SessionUsecase
}

func NewSessionService(logger log.Logger, su *biz.SessionUsecase) *SessionService {
	return &SessionService{log: log.NewHelper(logger), su: su}
}

func (s *SessionService) Login(ctx context.Context, req *pb.LoginRequest) (*pb.LoginReply, error) {
	hr, ok := http.RequestFromServerContext(ctx)
	if !ok {
		return nil, errors.Unauthorized("CTX获取源Request失败", "未知错误，请重试")
	}
	session, err := s.su.LoginByUsername(ctx, biz.UsernameLoginDto{
		Body:    biz.UsernameLogin{Username: req.Username, Password: req.Password},
		Headers: &biz.LoginHeaders{Platform: hr.Header.Get("x-lins-platform"), AppService: hr.Header.Get("x-lins-appservice")},
	})
	if err != nil {
		var rErr cake.RequestError
		if errors.As(err, &rErr) && rErr.StatusCode() != 200 {
			return nil, errors.New(rErr.StatusCode(), gjson.GetBytes(rErr.Body(), "detail").String(), gjson.GetBytes(rErr.Body(), "message").String())
		} else {
			return nil, errors.Unauthorized("用户名或密码错误", err.Error())
		}
	}
	if session.Project.Id == 0 {
		return nil, errors.BadRequest("[40100009] Avatar has no project", "用户暂未加入任何项目，请联系管理员")
	}
	sessionData := &pb.LoginReplySessionData{}
	copier.Copy(sessionData, session)
	return &pb.LoginReply{
		Data:    sessionData,
		Message: SuccessMessage,
	}, nil
}

func (s *SessionService) Logout(ctx context.Context, req *pb.EmptyRequest) (*pb.CommonReply, error) {
	if err := s.su.Logout(ctx); err != nil {
		var rErr cake.RequestError
		if errors.As(err, &rErr) && rErr.StatusCode() != 200 {
			return nil, errors.New(rErr.StatusCode(), gjson.GetBytes(rErr.Body(), "detail").String(), gjson.GetBytes(rErr.Body(), "message").String())
		} else {
			return nil, errors.BadRequest("网络错误，请稍后重试", err.Error())
		}
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}
