package service

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	json "github.com/goccy/go-json"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func (s *DeviceService) CallbackDevice(ctx context.Context, req *pb.CallbackRequest) (*pb.CommonReply, error) {
	reply := &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Did})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	switch req.Body.Type {
	case "device":
		if req.Body.Action == "offline" {
			// relax 10s buffer for device offline, if upped last seconds dont seen offline
			if device.UppedTime == nil || (*device.UppedTime).Unix() > time.Now().Add(-10*time.Second).Unix() {
				return reply, nil
			}
			s.du.OfflineDevice(ctx, device)
			// 状态变更事件
			statusMsg := &biz.StatusChangedMsg{}
			statusMsg.PaddingData(device, 0)
			s.du.IntegrateDevice(ctx, statusMsg)
			pushData, _ := json.Marshal((&biz.DeviceProperty{Status: device.Status.String()}).Convert())
			s.du.PublishDevice(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/property", device.MerchantId, device.Id), pushData, 0)
		} else if req.Body.Action == "dislink" {
			// check device is still offline
			if device.Status != biz.StatusOffline {
				return reply, nil
			}
			// 如果设备失联，则短信通知
			if err := s.du.DislinkDevice(ctx, device); err != nil {
				s.log.Errorf("Dislink device failed: %v", err)
			}
		} else if req.Body.Action == "autoback" {
			// check device is return or not
			if !device.IsControlled(biz.FlyControl) {
				s.log.Errorf("Device is already return")
				return reply, nil
			}
			// force back flight if device autoback
			if err := s.cu.ForceBackFlight(ctx, device); err != nil {
				s.log.Errorf("Force back flight failed: %v", err)
			}
		}
	case "service":
		if req.Body.Action == "timeout" {
			// check operation existed by serviceId[SourceId]
			operation, err := s.du.GetOperation(ctx, conv.Atoi(req.Body.SourceId))
			if err != nil {
				return reply, nil
			}
			// set operation as timeout failed by serviceId[SourceId]
			body := operation.Update(biz.OperationStatusTimeout, "下行指令响应超时，请稍后重试")
			s.du.UpdateOperation(ctx, operation.Id, body)
			if biz.JobOperations.Contains(operation.Type) {
				progress := biz.AnyMap{
					"code":          504,
					"status":        "timeout",
					"progress":      0,
					"operationType": operation.Type.String(),
					"operationId":   conv.Itoa(operation.Id),
					"deviceId":      conv.Itoa(device.Id),
					"timestamp":     operation.CreatedTime,
				}
				pd, _ := json.Marshal(progress)
				s.du.PublishDevice(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/opProgress", operation.MerchantId, device.Id), pd, 0)
			}
			pushData, _ := json.Marshal(operation)
			// 推送用户级别操作变更，如果是返航操作则升级为设备级别
			if lo.Contains([]biz.OperationType{biz.TypeReturn, biz.TypeBacknow, biz.TypeAutoback}, operation.Type) {
				s.du.PublishDevice(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/return", device.MerchantId, device.Id), pushData, 0)
			} else {
				s.du.PublishDevice(ctx, fmt.Sprintf("skai/avatar/%d/operation/%d", operation.AvatarId, operation.Id), pushData, 0)
			}
			// update execution status if mission service
			projectInfo := biz.ProjectInfo{TenantId: operation.TenantId, AvatarId: operation.AvatarId, MerchantIds: []int64{operation.MerchantId}}
			if execution, err := s.mu.GetExecution(ctx, &biz.ExecutionQuery{ProjectInfo: projectInfo, OperationId: operation.Id}); err == nil {
				s.mu.UpdateExecution(ctx, execution.Id, execution.Update(operation.Status, biz.ReasonTimeout))
			}
			// rollback device lockStatus within lockIndex by opType
			var action biz.ActionControl
			switch operation.Type {
			case biz.TypeTakeoff, biz.TypeLaunch, biz.TypeCruise, biz.TypeReturn, biz.TypeBacknow, biz.TypeAutoback, biz.TypeCancel:
				action = biz.FlyControl
			case biz.TypeBackAirline, biz.TypePauseAirline:
				action = biz.HoverControl
			case biz.TypeReleaseLens, biz.TypeControlLens:
				action = biz.LensControl
			case biz.TypeReleaseAero, biz.TypeControlAero:
				action = biz.AeroControl
			}
			s.du.UpdateDevice(ctx, device.Id, device.Control(action, false))
			// push device change message
			pushData, _ = json.Marshal(device)
			s.du.PublishDevice(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/change", device.MerchantId, device.Id), pushData, 0)
			return reply, nil
		}
	default:
		return reply, nil
	}
	return reply, nil
}

func (s *DeviceService) ExecuteDevice(ctx context.Context, req *pb.CallbackRequest) (*pb.CommonReply, error) {
	reply := &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}
	/*
		任务执行时间为分钟粒度，延时任务是秒级，所以存在误差，需跟当前秒数对比
		1. 可能提前触发任务，大于50秒即代表提前，则延迟一定秒数再执行
		2. 可能延后触发任务，小于10秒即代表延后，则暂无影响仍继续执行
	*/
	second := time.Now().In(biz.TimeLocation).Second()
	if second > 50 {
		sleep := time.Duration(60-second) * time.Second
		s.log.Infof("Delayer execute within sleep %ds", sleep)
		time.Sleep(sleep)
	}
	// 设置下次执行时间
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Did})
	if err != nil || !device.Deployment.Status {
		return reply, nil
	}
	s.mu.ResetNextSchedule(ctx, device)
	misionId := conv.Atoi(req.Body.SourceId)
	if misionId == 0 {
		s.log.Infof("Delayer execute within empty mission")
		return reply, nil
	}
	mission, err := s.mu.GetMission(ctx, &biz.DetailQuery{Id: misionId})
	if err != nil {
		s.log.Infof("Delayer execute within absent mission")
		return reply, nil
	}
	if mission.Status != biz.StatusDoing {
		s.log.Infof("Delayer execute within undoing mission")
		return reply, nil
	}
	snapMission := &biz.SnapMission{}
	snapMission.Copy(mission)
	execution := &biz.Execution{
		Status:     biz.OperationStatusFailure,
		Reason:     biz.ReasonNone,
		TenantId:   mission.TenantId,
		MerchantId: mission.MerchantId,
		MissionId:  misionId,
		Mission:    snapMission,
		DeviceId:   mission.DeviceId,
		AirlineId:  mission.AirlineId,
	}
	// 设备已删除无法起飞
	if device.MerchantId != mission.MerchantId {
		execution.Reason = biz.ReasonDevice
		s.mu.CreateExecution(ctx, execution)
		s.mu.UpdateMission(ctx, misionId, mission.Shift(biz.StatusPause), nil)
		s.log.Infof("Delayer execute within absent device")
		return reply, nil
	}
	// 设备离线无法起飞
	if device.Status == biz.StatusOffline {
		execution.Reason = biz.ReasonOffline
		s.mu.CreateExecution(ctx, execution)
		s.log.Infof("Delayer execute within offline device")
		return reply, nil
	}
	// 设备巡航中无法起飞
	if device.Status == biz.StatusExecution {
		execution.Reason = biz.ReasonFlying
		s.mu.CreateExecution(ctx, execution)
		s.log.Infof("Delayer execute within process flying")
		return reply, nil
	}
	// 设备调试中无法起飞
	if device.Status == biz.StatusDebug {
		execution.Reason = biz.ReasonIndebug
		s.mu.CreateExecution(ctx, execution)
		s.log.Infof("Delayer execute within debug device")
		return reply, nil
	}
	// 无人机不在机舱无法起飞
	if !device.CabinStatus {
		execution.Reason = biz.ReasonOutcabin
		s.mu.CreateExecution(ctx, execution)
		s.log.Infof("Delayer execute within empty drone")
		return reply, nil
	}
	// 查询设备最近1min日志
	if _, logs, err := s.du.LastDockDatalog(ctx, device.Sn, time.Second*62); err == nil {
		// 如果存在日志，则判断是否为强风或有雨
		isSuited := lo.SomeBy(logs, func(log *biz.DockProperties) bool {
			return log.EnvironmentState != nil && (log.EnvironmentState.WindSpeed > 11 || log.EnvironmentState.Rainfall > 0)
		})
		// 大风或有雨无法起飞
		if isSuited {
			execution.Reason = biz.ReasonWeather
			s.mu.CreateExecution(ctx, execution)
			s.log.Infof("Delayer execute within windy weather")
			return reply, nil
		}
	}
	projectInfo := biz.ProjectInfo{TenantId: mission.TenantId, AvatarId: mission.AvatarId, MerchantIds: []int64{mission.MerchantId}}
	airline, err := s.au.GetAirline(ctx, &biz.AirlineQuery{Id: mission.AirlineId, ProjectInfo: projectInfo, WithSigned: true})
	// 航线已删除无法起飞
	if err != nil {
		execution.Reason = biz.ReasonAirline
		s.mu.CreateExecution(ctx, execution)
		s.mu.UpdateMission(ctx, misionId, mission.Shift(biz.StatusPause), nil)
		s.log.Infof("Delayer execute within absent airline")
		return reply, nil
	}
	// 航线未关联设备无法起飞
	if len(airline.DeviceIds) == 0 {
		execution.Reason = biz.ReasonUnbound
		s.mu.CreateExecution(ctx, execution)
		s.mu.UpdateMission(ctx, misionId, mission.Shift(biz.StatusPause), nil)
		s.log.Infof("Delayer execute within unbound device")
		return reply, nil
	}
	// 航线关联设备变更无法起飞
	if !lo.Contains(airline.DeviceIds, mission.DeviceId) {
		execution.Reason = biz.ReasonRemoved
		s.mu.CreateExecution(ctx, execution)
		s.mu.UpdateMission(ctx, misionId, mission.Shift(biz.StatusPause), nil)
		s.log.Infof("Delayer execute within removed device")
		return reply, nil
	}
	// 创建操作记录
	algorithm := ""
	if len(mission.AlgConfigs) > 0 {
		algorithm = mission.AlgConfigs[0].Name.String()
	}
	timeout := biz.ServiceTimeout * 4
	payload := &biz.TakeoffPayload{
		TaskType:        0,
		AirlineId:       airline.Id,
		AirlineName:     airline.Name,
		MissionId:       misionId,
		Algorithm:       algorithm,
		KMZFile:         airline.KMZFile,
		ExecuteTime:     time.Now().UnixMilli() + 1000,
		ReturnMode:      0,
		ReturnAltitude:  airline.ReturnHeight,
		AirlineType:     map[string]int32{"waypoint": 0, "mapping2d": 1}[airline.Type],
		RCLostAction:    map[string]int32{"goBack": 0, "hover": 1, "landing": 2}[airline.RCLostAction],
		AirlineModeLost: map[string]int32{"goContinue": 0, "executeLostAction": 1}[airline.ExitOnRCLost],
	}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: mission.MerchantId,
		AvatarId:   mission.AvatarId,
		TenantId:   mission.TenantId,
		SourceId:   device.Id,
		Timeout:    timeout,
		Type:       biz.TypeCruise,
		Source:     biz.SourceMission,
		From:       "MISSION",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	execution.OperationId = operation.Id
	execution.Status = biz.OperationStatusExecute
	s.mu.CreateExecution(ctx, execution)
	// 锁定设备状态，防止多次控制
	if err := s.du.UpdateDevice(ctx, device.Id, device.Control(biz.FlyControl, true)); err != nil {
		return nil, err
	}
	// 下行起飞命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierTakeoff,
		ServiceId:  operation.Id,
		Timeout:    timeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return reply, nil
}
