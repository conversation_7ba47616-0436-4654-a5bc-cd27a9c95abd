package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/subjects/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type SubjectService struct {
	pb.UnimplementedSubjectServer
	log *log.Helper
	su  *biz.SubjectUsecase
}

func NewSubjectService(logger log.Logger, su *biz.SubjectUsecase) *SubjectService {
	return &SubjectService{log: log.NewHelper(logger), su: su}
}

func (s *SubjectService) CreateSubject(ctx context.Context, req *pb.SubjectItem) (*pb.CommonReply, error) {
	projectInfo, err := s.su.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	etype := biz.AiEventType(req.EventType)
	noticeRules := make([]*biz.NoticeRule, 0)
	for _, nr := range req.NoticeRules {
		bnr := &biz.NoticeRule{}
		copier.Copy(bnr, nr)
		noticeRules = append(noticeRules, bnr)
	}
	subject := &biz.Subject{
		Name:        req.Name,
		EventType:   etype,
		Description: req.Description,
		TenantId:    projectInfo.TenantId,
		ProjectId:   projectInfo.ProjectId,
		AvatarId:    projectInfo.AvatarId,
		EditorId:    projectInfo.AvatarId,
		EditedTime:  time.Now(),
		NoticeRules: noticeRules,
	}
	s.su.CreateSubject(ctx, subject)
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *SubjectService) ListSubject(ctx context.Context, req *pb.ListRequest) (*pb.ListReply, error) {
	projectInfo, err := s.su.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	total, subjects, err := s.su.ListSubject(ctx, &biz.SubjectListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.Size),
		Search:      req.Search,
		EventType:   req.EventType,
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(subjects, func(bs *biz.Subject, _ int) *pb.SubjectItem {
		return s.bizToPBSubject(bs)
	})
	return &pb.ListReply{
		Message: SuccessMessage,
		Data: &pb.ListReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			List:  list,
			Total: total,
		},
	}, nil
}

func (s *SubjectService) GetSubject(ctx context.Context, req *pb.GetRequest) (*pb.SubjectReply, error) {
	projectInfo, err := s.su.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	subject, err := s.su.GetSubject(ctx, &biz.DetailQuery{Id: req.Id, Unscoped: req.Scope == 1, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "主题不存在")
	}
	return &pb.SubjectReply{
		Message: SuccessMessage,
		Data:    s.bizToPBSubject(subject),
	}, nil
}

func (s *SubjectService) UpdateSubject(ctx context.Context, req *pb.SubjectItem) (ret *pb.CommonReply, err error) {
	projectInfo, err := s.su.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	subject, err := s.su.GetSubject(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "主题不存在")
	}
	noticeRules := make([]*biz.NoticeRule, 0)
	for _, nr := range req.NoticeRules {
		bnr := &biz.NoticeRule{}
		copier.Copy(bnr, nr)
		noticeRules = append(noticeRules, bnr)
	}
	body := &biz.Subject{
		Name: req.Name,
		// EventType:   req.EventType,
		Description: req.Description,
		EditorId:    projectInfo.AvatarId,
		NoticeRules: noticeRules,
	}
	if err := s.su.UpdateSubject(ctx, subject.Id, subject.Change(body)); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *SubjectService) DeleteSubject(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.su.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	subject, err := s.su.GetSubject(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "主题不存在")
	}
	if err := s.su.DeleteSubject(ctx, subject.Id); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *SubjectService) bizToPBSubject(bs *biz.Subject) *pb.SubjectItem {
	si := &pb.SubjectItem{}
	copier.CopyWithOption(si, bs, copier.Option{
		IgnoreEmpty: true,
		DeepCopy:    false,
		Converters:  []copier.TypeConverter{biz.TimeCopierConverter},
	})
	return si
}
