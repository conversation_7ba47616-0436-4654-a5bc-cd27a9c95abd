package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/waypoints/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type WaypointService struct {
	pb.UnimplementedWaypointServer
	log *log.Helper
	wu  *biz.WaypointUsecase
}

func NewWaypointService(logger log.Logger, wu *biz.WaypointUsecase) *WaypointService {
	return &WaypointService{log: log.NewHelper(logger), wu: wu}
}

func (s *WaypointService) AddWaypoints(ctx context.Context, req *pb.AddRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.wu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	airline, err := s.wu.BelongAirline(ctx, &biz.AirlineQuery{ProjectInfo: *projectInfo, Id: req.AirlineId})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航线不存在")
	}
	waypoints := make([]*biz.Waypoint, 0)
	if len(req.List) > 0 {
		for _, p := range req.List {
			w := &biz.Waypoint{
				AirlineId:  airline.Id,
				TenantId:   airline.TenantId,
				MerchantId: airline.MerchantId,
			}
			copier.Copy(w, p)
			waypoints = append(waypoints, w)
		}
	}
	s.wu.CreateWaypoints(ctx, waypoints)
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: len(waypoints) > 0},
	}, nil
}

func (s *WaypointService) ListWaypoint(ctx context.Context, req *pb.ListRequest) (*pb.ListReply, error) {
	projectInfo, err := s.wu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	total, waypoints, err := s.wu.ListWaypoint(ctx, &biz.WaypointListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.Size),
		AirlineId:   req.AirlineId,
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(waypoints, func(w *biz.Waypoint, _ int) *pb.WaypointItem {
		return s.bizToPBWaypoint(w)
	})
	return &pb.ListReply{
		Message: SuccessMessage,
		Data: &pb.ListReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			List:  list,
			Total: total,
		},
	}, nil
}

func (s *WaypointService) GetWaypoint(ctx context.Context, req *pb.CommonRequest) (*pb.WaypointReply, error) {
	projectInfo, err := s.wu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	waypoint, err := s.wu.GetWaypoint(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航点不存在")
	}
	return &pb.WaypointReply{
		Message: SuccessMessage,
		Data:    s.bizToPBWaypoint(waypoint),
	}, nil
}

func (s *WaypointService) UpdateWaypoint(ctx context.Context, req *pb.CommonRequest) (ret *pb.CommonReply, err error) {
	projectInfo, err := s.wu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	waypoint, err := s.wu.GetWaypoint(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航点不存在")
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: waypoint.Id == req.Id},
	}, nil
}

func (s *WaypointService) DeleteWaypoint(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	return nil, nil
}

func (s *WaypointService) bizToPBWaypoint(bw *biz.Waypoint) *pb.WaypointItem {
	wi := &pb.WaypointItem{}
	copier.CopyWithOption(wi, bw, copier.Option{
		IgnoreEmpty: true,
		DeepCopy:    false,
		Converters:  []copier.TypeConverter{biz.TimeCopierConverter},
	})
	return wi
}
