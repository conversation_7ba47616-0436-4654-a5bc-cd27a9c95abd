package service

import (
	"context"
	"strings"
	"time"

	pb "gitlab.sensoro.com/skai/skai/api/cloud/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	json "github.com/goccy/go-json"
	"github.com/samber/lo"
	"github.com/tidwall/conv"

	"gitlab.sensoro.com/go-sensoro/lins-common/kafka"
)

var _ kafka.Consumer = (*LinsConsumer)(nil)

type LinsConsumer struct {
	c   *conf.Data
	au  *biz.AiEventUsecase
	du  *biz.DeviceUsecase
	mu  *biz.MissionUsecase
	su  *biz.SubjectUsecase
	vu  *biz.VoyageUsecase
	log *log.Helper
}

func NewLinsConsumer(
	logger log.Logger,
	c *conf.Data,
	au *biz.AiEventUsecase,
	du *biz.DeviceUsecase,
	mu *biz.MissionUsecase,
	su *biz.SubjectUsecase,
	vu *biz.VoyageUsecase,
) *LinsConsumer {
	return &LinsConsumer{c, au, du, mu, su, vu, log.NewHelper(logger)}
}

func (lc *LinsConsumer) ConsumeMap() kafka.ConsumeMap {
	return kafka.ConsumeMap{
		lc.c.Kafka.Consumes.SnapshotEvent.Topic:  lc.newSnapshoterHandler(),
		lc.c.Kafka.Consumes.AlarmDomainMsg.Topic: lc.newAlarmDomainHandler(),
	}
}

func (lc *LinsConsumer) newSnapshoterHandler() kafka.Consume {
	return func(ctx context.Context, msg kafka.Message) error {
		m := &biz.SnapshotKafkaMessage{}
		if err := json.Unmarshal(msg.Value, m); err != nil {
			lc.log.Errorf("SnapshotConsumer invalid msg %s, key=%s", msg.Value, msg.Key)
			return nil
		}
		if m.Data.Task.Stream.Source != "SKAI" {
			lc.log.Warnf("SnapshotConsumer handle without valid source: %s", m.Data.Task.Stream.Source)
			return nil
		}
		voyageId := m.Data.Task.Id
		voyage, err := lc.vu.GetVoyage(ctx, &biz.DetailQuery{Id: voyageId})
		if err != nil {
			lc.log.Warnf("SnapshotConsumer handle without exist voyage: %s", voyageId)
			return nil
		}
		bd, err := lc.du.GetDevice(ctx, &biz.DetailQuery{Id: voyage.DeviceId})
		// device, err := fc.vu.GetVoyageJointDevice(ctx, &biz.DetailQuery{Id: voyageId})
		if err != nil {
			lc.log.Warnf("SnapshotConsumer handle without exist device by voyageId: %d", voyageId)
			return nil
		}
		device := &biz.JointDevice{}
		device.Copy(bd)
		if device.MissionId == nil {
			lc.log.Warnf("SnapshotConsumer handle without exist mission cruise")
			return nil
		}
		bm, err := lc.mu.GetMission(ctx, &biz.DetailQuery{Id: *device.MissionId})
		if err != nil {
			lc.log.Warnf("SnapshotConsumer handle without exist mission: %d", *device.MissionId)
			return nil
		}
		mission := &biz.SnapMission{}
		mission.Copy(bm)
		if mission.Status != biz.StatusDoing {
			lc.log.Warnf("SnapshotConsumer handle without doing mission: %d", mission.Id)
			return nil
		}
		config, ok := lo.Find(mission.AlgConfigs, func(item *biz.AlgConfig) bool {
			return item.Type == biz.AlgTypeSnapshot
		})
		if !ok || config == nil {
			lc.log.Warnf("SnapshotConsumer handle without snapshot algorithm")
			return nil
		}
		if config.Name == biz.AlgNameUnknown {
			lc.log.Warnf("SnapshotConsumer handle without support algorithm")
			return nil
		}
		algoData, err := lc.au.SnapshotDetect(ctx, config.Name, m.Data)
		if err != nil {
			lc.log.Errorf("SnapshotConsumer handle snapshot error %s", err.Error())
			return nil
		}
		lc.log.Debugf("SnapshotConsumer handle with algoData: %+v", algoData)
		if config.Name == biz.AlgNamePerson { // 人群聚集算法，比对阈值大小
			if algoData.ObjectCount > config.Threshold {
				lc.log.Warnf("SnapshotConsumer handle without invalid person")
				return nil
			}
		} else if lo.Contains([]biz.AlgName{biz.AlgNameSmokeye, biz.AlgNameFloater}, config.Name) { // 烟火监测和漂浮物算法，目标物大于1
			if algoData.ObjectCount < 1 {
				lc.log.Warnf("SnapshotConsumer handle without invalid target")
				return nil
			}
		}
		// 查询本次任务执行记录，封装至事件元数据，用于预警审核通过后加一
		projectInfo := biz.ProjectInfo{MerchantIds: []int64{device.MerchantId}}
		mission.LastExecution, _ = lc.mu.GetExecution(ctx, &biz.ExecutionQuery{
			VoyageId:     voyageId,
			MissionId:    mission.Id,
			ProjectInfo:  projectInfo,
			OccurredTime: lo.ToPtr(time.UnixMilli(m.Data.Timestamp)),
		})
		// 查询最接近的上行记录中经纬度，默认设备部署经纬度
		lnglat := device.Deployment.Lnglat
		if vmLnglat, err := lc.vu.GetVoyageMomentLnglat(ctx, device.Sn, m.Data.Timestamp); err == nil && vmLnglat != nil {
			lnglat = vmLnglat
		}
		blEvent := pb.TransformAlgoEventToLinsEvent(device, mission, &biz.AlgoEventDto{
			AlgoDetectData: *algoData,
			ImageUrl:       m.Data.Url,
			AlgType:        biz.AlgTypeSnapshot,
			Timestamp:      m.Data.Timestamp,
			Lnglat:         lo.Map(lnglat, func(p float64, _ int) float32 { return float32(p) }),
			Location:       device.Deployment.Location,
		})
		if err := lc.au.PushAIEvent(ctx, blEvent); err != nil {
			lc.log.Errorf("SnapshotConsumer push event %d err %v", blEvent.Entry.CustomId, err)
			return nil
		}
		return nil
	}
}

func (lc *LinsConsumer) newAlarmDomainHandler() kafka.Consume {
	return func(ctx context.Context, msg kafka.Message) error {
		m := &biz.AlarmDomainKafkaMessage{}
		if err := json.Unmarshal(msg.Value, m); err != nil {
			lc.log.Errorf("AlarmDomainConsumer invalid msg %s, key=%s", msg.Value, msg.Key)
			return nil
		}
		if m.Data.Type == "Approval" {
			approval := m.Data.Approval
			if !approval.Approval || approval.Status != "approved" {
				lc.log.Debugf("AlarmDomainConsumer handle without valid approval: %t, status: %s", approval.Approval, approval.Status)
				return nil
			}
			event := m.Data.AlarmEvents[0]
			if event == nil {
				lc.log.Debugf("AlarmDomainConsumer handle <Approval> without valid event")
				return nil
			}
			var executionId int64
			if eid, ok := event.Metadata.DroneAlgoEvent["executionId"].(string); ok {
				executionId = conv.Atoi(eid)
			}
			// 执行记录预警数加一
			projectInfo := biz.ProjectInfo{MerchantIds: []int64{conv.Atoi(m.Data.MerchantId)}}
			if execution, err := lc.mu.GetExecution(ctx, &biz.ExecutionQuery{Id: executionId, ProjectInfo: projectInfo}); err == nil {
				lc.log.Warnf("AlarmDomainConsumer increment execution: %s one alarmCount", executionId)
				return lc.mu.UpdateExecution(ctx, execution.Id, execution.Increment(1))
			}
		} else if m.Data.Type == "StatusChanged" {
			currStatus := m.Data.CurrentStatus
			if !lo.Contains([]string{"processing", "finished"}, currStatus) {
				lc.log.Debugf("AlarmDomainConsumer handle <StatusChanged> without valid status: %s", currStatus)
				return nil
			}
			customIds := strings.Split(m.Data.CustomId, ":")
			if len(customIds) < 2 {
				lc.log.Debugf("AlarmDomainConsumer handle without valid annotation")
				return nil
			}
			subjectId := conv.Atoi(customIds[1])
			lc.log.Debugf("AlarmDomainConsumer handle message: %s, subjectId: %+v", m.MsgId, subjectId)
			// 更新主题待处理数和已完结数
			projectInfo := biz.ProjectInfo{MerchantIds: []int64{conv.Atoi(m.Data.MerchantId)}}
			if subject, err := lc.su.GetSubject(ctx, &biz.DetailQuery{Id: subjectId, ProjectInfo: projectInfo}); err == nil {
				if currStatus == "processing" {
					lc.log.Warnf("AlarmDomainConsumer decrement subject: %s one pending", subjectId)
					return lc.su.UpdateSubject(ctx, subject.Id, subject.PendingIncr(-1))
				} else if currStatus == "finished" {
					lc.log.Warnf("AlarmDomainConsumer increment subject: %s one finishCount", subjectId)
					return lc.su.UpdateSubject(ctx, subject.Id, subject.FinishIncr(1))
				}
			}
		} else {
			lc.log.Debugf("AlarmDomainConsumer handle without valid type: %s", m.Data.Type)
			return nil
		}
		return nil
	}
}
