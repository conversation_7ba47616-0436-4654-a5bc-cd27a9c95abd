package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	json "github.com/goccy/go-json"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func (s *DeviceService) ShoutDevice(ctx context.Context, req *pb.ReturnRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 构造用户信息，任务巡航时获取当前用户信息
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if device.MissionId != nil {
		avatar, _ = s.du.GetAvatar(ctx, projectInfo.AvatarId)
	}
	// 操作人权限判断
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	opType := lo.Ternary(req.Enable, biz.TypeControlSpeaker, biz.TypeReleaseSpeaker)
	action := opType.ToControlAction()
	// 创建操作记录
	payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Message:    "success",
		Status:     biz.OperationStatusSuccess,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 无下行命令, 进入中间过度状态
	device.Control(action, true)
	// 直接更新为成功
	if err := s.du.UpdateDevice(ctx, device.Id, device.Control(action, true)); err != nil {
		return nil, err
	}
	// 延迟1秒推送操作信息/设备变更
	go func() {
		gctx, cancel := context.WithTimeout(context.Background(), biz.ServiceTimeout)
		defer cancel()
		pushData, _ := json.Marshal(operation)
		s.du.PublishDevice(gctx, fmt.Sprintf("skai/avatar/%d/operation/%d", operation.AvatarId, operation.Id), pushData, 1*time.Second)
		pushData, _ = json.Marshal(device)
		s.du.PublishDevice(gctx, fmt.Sprintf("skai/merchant/%d/device/%d/change", device.MerchantId, device.Id), pushData, 1*time.Second)
	}()
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) SpeakDevice(ctx context.Context, req *pb.SpeakRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 根据index查找喊话器
	if device.Speaker == nil || device.Speaker.Index != req.Index {
		return nil, errors.BadRequest("40000002", "喊话器子装置不存在")
	}
	var payload any
	name := time.Now().Format("20060102150405")
	hash := md5.Sum([]byte(req.Content))
	opType := biz.TypeStopSpeaker
	basePayload := biz.SpeakerOperationPayload{Index: req.Index}
	if req.Action == "start" {
		// 当前是否处于喊话中
		if device.IsSpeaking() {
			return nil, errors.BadRequest("40000002", "请先停止当前喊话，再重新进行喊话配置")
		}
		opType = lo.Ternary(req.Mode == 0, biz.TypeTTSSpeaker, biz.TypeAudioSpeaker)
		payload = &biz.StartSpeakerPayload{
			Name: name,
			Mode: req.Mode,
			Data: req.Content,
			// 录音模式时固定格式和MD5签名
			Format:    "pcm",
			Signature: hex.EncodeToString(hash[:]),
			// 子装置索引信息
			SpeakerOperationPayload: basePayload,
		}
	} else {
		payload = &basePayload
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: opType.ToServiceIdentifier(),
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) VolumeDevice(ctx context.Context, req *pb.VolumeRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 根据index查找喊话器
	if device.Speaker == nil || device.Speaker.Index != req.Index {
		return nil, errors.BadRequest("40000002", "喊话器子装置不存在")
	}
	// 创建操作记录
	basePayload := biz.SpeakerOperationPayload{Index: req.Index}
	payload := &biz.SetSpeakerVolumePayload{
		Volume: req.Volume,
		// 子装置索引信息
		SpeakerOperationPayload: basePayload,
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeAdjustVolume,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierSetSpeakerVolume,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) PlaymodeDevice(ctx context.Context, req *pb.PlaymodeRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 根据index查找喊话器
	if device.Speaker == nil || device.Speaker.Index != req.Index {
		return nil, errors.BadRequest("40000002", "喊话器子装置不存在")
	}
	// 创建操作记录
	basePayload := biz.SpeakerOperationPayload{Index: req.Index}
	payload := &biz.SetSpeakerPlayModePayload{
		PlayMode: req.Playmode,
		// 子装置索引信息
		SpeakerOperationPayload: basePayload,
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeSetPlaymode,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierSetSpeakerPlayMode,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}
