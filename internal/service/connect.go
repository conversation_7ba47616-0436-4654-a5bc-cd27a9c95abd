package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/connects/v1"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type ConnectService struct {
	pb.UnimplementedConnectServer
	log *log.Helper
	au  *biz.AirlineUsecase
	cu  *biz.ConnectUsecase
}

func NewConnectService(logger log.Logger, au *biz.AirlineUsecase, cu *biz.ConnectUsecase) *ConnectService {
	return &ConnectService{log: log.NewHelper(logger), au: au, cu: cu}
}

func (s *ConnectService) EventUp(ctx context.Context, req *pb.EventUpRequest) (*pb.EventUpReply, error) {
	var event any
	etype := biz.ThingModelEventType(req.Type)
	switch etype {
	case biz.ThingModelEventTypeHMS:
		event = pb.TobizDockHealMoniterEvent(req)
	case biz.ThingModelEventTypeUpdateTopo:
		event = pb.TobizDockTopoUpdateEvent(req)
	case biz.ThingModelEventTypeFlightTaskProgress:
		event = pb.TobizDockFlightTaskProgressEvent(req)
	case biz.ThingModelEventTypeFlightTaskResourceGet:
		event = pb.TobizDockFlightTaskResourceRequestEvent(req)
	case biz.ThingModelEventTypeFlytoPointProgress:
		fppEvent := &biz.FlytoPointProgressEvent{}
		thingEvent, err := pb.ToSimpleBizEvent(req, fppEvent)
		if err != nil {
			return nil, err
		}
		fppEvent.ThingEvent = *thingEvent
		event = fppEvent
	case biz.ThingModelEventTypeLaunchPointProgress:
		lppEvent := &biz.LaunchPointProgressEvent{}
		thingEvent, err := pb.ToSimpleBizEvent(req, lppEvent)
		if err != nil {
			return nil, err
		}
		lppEvent.ThingEvent = *thingEvent
		event = lppEvent
	case biz.ThingModelEventTypeOrbitPointNotify:
		opnEvent := &biz.OrbitPointNotifyEvent{}
		thingEvent, err := pb.ToSimpleBizEvent(req, opnEvent)
		if err != nil {
			return nil, err
		}
		opnEvent.ThingEvent = *thingEvent
		event = opnEvent
	case biz.ThingModelEventTypeSpeakerStatus:
		spsEvent := &biz.SpeakerPlayStatusEvent{}
		thingEvent, err := pb.ToSimpleBizEvent(req, spsEvent)
		if err != nil {
			return nil, err
		}
		spsEvent.ThingEvent = *thingEvent
		event = spsEvent
	case biz.ThingModelEventTypeOperationProgress:
		opEvent := &biz.OperationProgressEvent{}
		thingEvent, err := pb.ToSimpleBizEvent(req, opEvent)
		if err != nil {
			return nil, err
		}
		opEvent.ThingEvent = *thingEvent
		event = opEvent
	case biz.ThingModelEventTypeFileuploadProgress:
		fpEvent := &biz.FileuploadProgressEvent{}
		thingEvent, err := pb.ToSimpleBizEvent(req, fpEvent)
		if err != nil {
			return nil, err
		}
		fpEvent.ThingEvent = *thingEvent
		event = fpEvent
	default:
		s.log.Infof("Connect service unsupport event %d", req.Type)
	}
	reply := &biz.EventUpReply{}
	// check existed device
	if device, err := s.cu.GetDevice(ctx, &biz.DetailQuery{Id: req.DeviceId}); err == nil {
		reply = s.cu.EventUp(ctx, device, etype, event)
	}
	if etype == biz.ThingModelEventTypeFlightTaskResourceGet && reply.AirlineId != 0 {
		if airline, err := s.au.GetAirline(ctx, &biz.AirlineQuery{Id: reply.AirlineId, WithSigned: true}); err == nil {
			reply.Data = biz.AnyMap{"kmzFile": biz.AnyMap{"url": airline.KMZFile.Url, "fingerprint": airline.KMZFile.Fingerprint}}
		}
	}
	euReply := &pb.EventUpReply{Message: SuccessMessage}
	if reply.Data != nil {
		euReply.Data, _ = structpb.NewStruct(reply.Data)
	}
	return euReply, nil
}

func (s *ConnectService) PropertyUp(ctx context.Context, req *pb.PropertyUpRequest) (*pb.CommonReply, error) {
	// check existed device
	if device, err := s.cu.GetDevice(ctx, &biz.DetailQuery{Id: req.DeviceId}); err == nil {
		device.PropData.Model = device.Model.String()
		device.PropData.MsgType = lo.Ternary(device.Category == biz.CategoryDock, 1, 2)
		deviceProp := &biz.DeviceProperty{}
		copier.Copy(deviceProp, device.PropData)
		body := pb.TobizDeviceProperty(device.PropData, req)
		// 1.无效上行消息忽略
		if body.MsgType > 0 {
			s.cu.PropertyUp(ctx, device, body, deviceProp)
		}
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *ConnectService) ServiceReply(ctx context.Context, req *pb.ServiceReplyRequest) (*pb.CommonReply, error) {
	// check existed device
	if device, err := s.cu.GetDevice(ctx, &biz.DetailQuery{Id: req.DeviceId}); err == nil {
		s.cu.ServiceReply(ctx, device, pb.TobizServiceReply(req))
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}
