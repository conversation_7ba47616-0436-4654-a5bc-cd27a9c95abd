package service

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	json "github.com/goccy/go-json"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func (s *DeviceService) TakeoffDevice(ctx context.Context, req *pb.TakeoffRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 设备状态不为空闲中，无法起飞
	if device.Status != biz.StatusStandby {
		return nil, errors.BadRequest("40000003", "设备当前状态不为空闲中，无法执行起飞操作")
	}
	// 无人机不在机舱，无法起飞
	if !device.CabinStatus {
		return nil, errors.BadRequest("40000003", "无人机当前不在机舱中，无法执行起飞操作")
	}
	// 设备起飞控制不为关闭状态，表示不在机舱无法起飞
	if !device.LockStatus.Ctrlable(biz.FlyControl, biz.DisabledStatus) {
		return nil, errors.BadRequest("40000003", "设备正在起飞或起飞成功，请勿重复操作")
	}
	airline, err := s.au.GetAirline(ctx, &biz.AirlineQuery{Id: req.AirlineId, ProjectInfo: *projectInfo, WithSigned: true})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航线不存在")
	}
	// 航线关联设备变更无法起飞
	if !lo.Contains(airline.DeviceIds, device.Id) {
		return nil, errors.BadRequest("40000003", "设备关联航线关系变更，无法执行起飞操作")
	}
	// 创建操作记录
	payload := &biz.TakeoffPayload{
		TaskType:        0,
		AirlineId:       req.AirlineId,
		AirlineName:     airline.Name,
		Algorithm:       req.Algorithm,
		KMZFile:         airline.KMZFile,
		ExecuteTime:     time.Now().UnixMilli() + 1000,
		ReturnMode:      0,
		ReturnAltitude:  airline.ReturnHeight,
		AirlineType:     map[string]int32{"waypoint": 0, "mapping2d": 1}[airline.Type],
		RCLostAction:    map[string]int32{"goBack": 0, "hover": 1, "landing": 2}[airline.RCLostAction],
		AirlineModeLost: map[string]int32{"goContinue": 0, "executeLostAction": 1}[airline.ExitOnRCLost],
	}
	if req.SimulateMission != nil {
		isEnable := lo.Ternary(req.SimulateMission.IsEnable, 1, 0)
		payload.SimulateMission = &biz.Simulator{IsEnable: isEnable, Lnglat: req.SimulateMission.Lnglat}
	}
	timeout := biz.ServiceTimeout * 4
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    timeout,
		Type:       biz.TypeTakeoff,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 锁定设备状态，防止多次控制
	if err := s.du.UpdateDevice(ctx, device.Id, device.Control(biz.FlyControl, true)); err != nil {
		return nil, err
	}
	// 下行起飞命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierTakeoff,
		ServiceId:  operation.Id,
		Timeout:    timeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) LaunchDevice(ctx context.Context, req *pb.LaunchRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 设备状态不为空闲中，无法起飞
	if device.Status != biz.StatusStandby {
		return nil, errors.BadRequest("40000003", "设备当前状态不为空闲中，无法执行起飞操作")
	}
	// 无人机不在机舱，无法起飞
	if !device.CabinStatus {
		return nil, errors.BadRequest("40000003", "无人机当前不在机舱中，无法执行起飞操作")
	}
	// 设备无精确海拔高，无法起飞
	if device.Deployment.Altitude <= 0 {
		return nil, errors.BadRequest("40000003", "设备暂无精确海拔高度，无法执行起飞操作")
	}
	// 设备起飞控制不为关闭状态，表示不在机舱无法起飞
	if !device.LockStatus.Ctrlable(biz.FlyControl, biz.DisabledStatus) {
		return nil, errors.BadRequest("40000003", "设备正在起飞或起飞成功，请勿重复操作")
	}
	payload := &biz.LaunchPayload{
		Speed:          req.Speed,
		Height:         req.Height,
		Lnglat:         req.Lnglat,
		Algorithm:      req.Algorithm,
		SecurityHeight: req.SecurityHeight,
		CmderHeight:    req.CmderHeight,
		ReturnAltitude: req.ReturnAltitude,
		RCLostAction:   req.RCLostAction,
		CmderMode:      1,
		ReturnMode:     1,
		CmderModeLost:  1,
		// 计算目标高度: 目标起飞高度 = 机舱海拔高度 + 指点起飞高度
		TargetHeight: device.Deployment.Altitude + req.Height,
	}
	if req.SimulateMission != nil {
		isEnable := lo.Ternary(req.SimulateMission.IsEnable, 1, 0)
		payload.SimulateMission = &biz.Simulator{IsEnable: isEnable, Lnglat: req.SimulateMission.Lnglat}
	}
	// 创建操作记录
	timeout := biz.ServiceTimeout * 4
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    timeout,
		Type:       biz.TypeLaunch,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 锁定设备状态，防止多次控制
	if err := s.du.UpdateDevice(ctx, device.Id, device.Control(biz.FlyControl, true)); err != nil {
		return nil, err
	}
	// 下行起飞命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierLaunch,
		ServiceId:  operation.Id,
		Timeout:    timeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) ReturnDevice(ctx context.Context, req *pb.ReturnRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	opType := lo.Ternary(req.Enable, biz.TypeReturn, biz.TypeCancel)
	action := opType.ToControlAction()
	// 一键返航: 设备起飞控制为关闭状态，表示已经在返航，无法操作
	if req.Enable && !device.IsControlled(action) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 取消返航: 设备起飞控制为开启状态，表示不在返航中，无法操作
	if !req.Enable {
		if device.IsControlled(action) {
			return nil, errors.BadRequest("40000003", "设备未处于返航过程中，请勿操作")
		}
		// 电量低于取消返航最小值
		if device.IsFused(biz.CANCEL_MIN_BATTERY) {
			return nil, errors.BadRequest("40000003", "当前无人机电量低于20%，禁止取消返航")
		}
	}
	// 构造用户信息，任务巡航时获取当前用户信息
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if device.MissionId != nil {
		avatar, _ = s.du.GetAvatar(ctx, projectInfo.AvatarId)
	}
	// 操作人权限判断
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 创建操作记录
	payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 锁定设备状态，防止多次控制
	if err := s.du.UpdateDevice(ctx, device.Id, device.Control(action, true)); err != nil {
		return nil, err
	}
	// 下行返航命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: opType.ToServiceIdentifier(),
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) AutobackDevice(ctx context.Context, req *pb.ReturnRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 构造用户信息，任务巡航时获取当前用户信息
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if device.MissionId != nil {
		avatar, _ = s.du.GetAvatar(ctx, projectInfo.AvatarId)
	}
	// 操作人权限判断
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 清空自动返航倒计时
	if err := s.du.DeleteTask(ctx, req.Id); err != nil {
		return nil, errors.BadRequest("40000003", "其他用户已经执行，请勿重复操作")
	}
	// 取消自动返航则直接返回成功
	if !req.Enable {
		return &pb.OperationReply{Message: SuccessMessage, Data: &pb.Operation{}}, nil
	}
	opType := biz.TypeAutoback
	action := opType.ToControlAction()
	// 自动返航: 设备起飞控制为关闭状态，表示已经在返航，无法操作
	if !device.IsControlled(action) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 创建操作记录
	payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 锁定设备状态，防止多次控制
	if err := s.du.UpdateDevice(ctx, device.Id, device.Control(action, true)); err != nil {
		return nil, err
	}
	// 下行返航命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: opType.ToServiceIdentifier(),
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    &pb.Operation{},
	}, nil
}

func (s *DeviceService) RouteDevice(ctx context.Context, req *pb.ReturnRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	opType := lo.Ternary(req.Enable, biz.TypeBackAirline, biz.TypePauseAirline)
	action := opType.ToControlAction()
	// 恢复航线: 设备悬停控制为关闭状态，航线执行中无法恢复航线
	if req.Enable && !device.IsControlled(action) {
		return nil, errors.BadRequest("40000003", "设备正在航线飞行中，请勿操作")
	}
	// 暂停航线: 设备悬停控制为开启状态，设备悬停中无法暂停航线
	if !req.Enable && device.IsControlled(action) {
		return nil, errors.BadRequest("40000003", "设备不在航线飞行中，请勿操作")
	}
	// 构造用户信息，任务巡航时获取当前用户信息
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if device.MissionId != nil {
		avatar, _ = s.du.GetAvatar(ctx, projectInfo.AvatarId)
	}
	// 操作人权限判断
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 创建操作记录
	payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 锁定设备状态，防止多次控制
	if err := s.du.UpdateDevice(ctx, device.Id, device.Control(action, true)); err != nil {
		return nil, err
	}
	// 下行命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: opType.ToServiceIdentifier(),
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) ControlDevice(ctx context.Context, req *pb.ControlRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 起飞控制不为开启状态，表示已经在返航无法操作
	if !device.IsControlled(biz.FlyControl) {
		return nil, errors.BadRequest("40000003", "设备已经在返航过程中，请勿操作")
	}
	// 开启操作：当前支持开启镜头/飞行控制
	opType := biz.OperationType(req.Operate)
	if !lo.Contains([]biz.OperationType{biz.TypeControlLens, biz.TypeControlAero}, opType) {
		return nil, errors.BadRequest("40000003", "暂不支持的开启控制，请重新操作")
	}
	action := opType.ToControlAction()
	// 操作为非关闭状态，不可重复开启
	if !device.IsReleased(action) {
		return nil, errors.BadRequest("40000003", fmt.Sprintf("当前用户已在%s中，请勿重复操作", action.Name()))
	}
	// 构造用户信息，任务巡航时获取当前用户信息
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if device.MissionId != nil {
		avatar, _ = s.du.GetAvatar(ctx, projectInfo.AvatarId)
	}
	// 操作人权限判断
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	payload := &biz.SubdevicePayload{}
	identifier := biz.DockServiceIdentifierUnknown
	// 飞行控制需要开启悬停控制
	if action == biz.AeroControl {
		identifier = biz.DockServiceIdentifierControlAero
		if !device.IsControlled(biz.HoverControl) {
			return nil, errors.BadRequest("40000003", "请先暂停航线，再打开飞行控制")
		}
	}
	// 镜头控制需要子设备信息
	if action == biz.LensControl {
		identifier = biz.DockServiceIdentifierControlLens
		// 查找云台子设备，不存在则不允许操作
		if gimbal := device.GetGimbal(); gimbal != nil {
			payload.Subdevice = *gimbal
		} else {
			return nil, errors.BadRequest("40000002", "云台子设备不存在")
		}
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 锁定设备状态，防止多次控制
	if err := s.du.UpdateDevice(ctx, device.Id, device.Control(action, true)); err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: identifier,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) ReleaseDevice(ctx context.Context, req *pb.ControlRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 关闭操作：当前支持关闭镜头/飞行控制
	opType := biz.OperationType(req.Operate)
	if !lo.Contains([]biz.OperationType{biz.TypeReleaseLens, biz.TypeReleaseAero}, opType) {
		return nil, errors.BadRequest("40000003", "暂不支持的关闭控制，请重新操作")
	}
	action := opType.ToControlAction()
	// 操作为非开启状态，不可关闭
	if !device.IsControlled(action) {
		return nil, errors.BadRequest("40000003", fmt.Sprintf("当前用户未开启%s，请勿操作", action.Name()))
	}
	// 录像中禁止关闭镜头控制
	if action == biz.LensControl && device.IsVideoed() {
		return nil, errors.BadRequest("40000003", fmt.Sprintf("当前正在录像中，不可关闭%s", action.Name()))
	}
	// 指令飞行任务中，不可关闭镜头/飞行控制
	if device.IsFreed() || device.IsPointed() || device.IsOrbited() {
		return nil, errors.BadRequest("40000003", fmt.Sprintf("当前正在进行指令飞行，不可关闭%s", action.Name()))
	}
	// 构造用户信息，任务巡航时获取当前用户信息
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if device.MissionId != nil {
		avatar, _ = s.du.GetAvatar(ctx, projectInfo.AvatarId)
	}
	// 操作人权限判断
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Message:    "success",
		Status:     biz.OperationStatusSuccess,
		Content:    biz.AnyMap{"OperationTime": time.Now().UnixMilli()},
	})
	if err != nil {
		return nil, err
	}
	updateInfo := biz.AnyMap{}
	// 关闭飞行控制，则置空飞行模式
	if action == biz.AeroControl {
		updateInfo = lo.Assign(device.Aviate(biz.NullMode), device.ExtraData.ResetAero())
	}
	// 无下行命令, 1.进入中间过度状态,2.直接更新为成功
	device.Control(action, true)
	updateInfo = lo.Assign(updateInfo, device.Control(action, true))
	if err := s.du.UpdateDevice(ctx, device.Id, updateInfo); err != nil {
		return nil, err
	}
	// 延迟1秒推送操作信息/设备变更
	go func() {
		gctx, cancel := context.WithTimeout(context.Background(), biz.ServiceTimeout)
		defer cancel()
		pushData, _ := json.Marshal(operation)
		s.du.PublishDevice(gctx, fmt.Sprintf("skai/avatar/%d/operation/%d", operation.AvatarId, operation.Id), pushData, 1*time.Second)
		pushData, _ = json.Marshal(device)
		s.du.PublishDevice(gctx, fmt.Sprintf("skai/merchant/%d/device/%d/change", device.MerchantId, device.Id), pushData, 1*time.Second)
	}()
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}
