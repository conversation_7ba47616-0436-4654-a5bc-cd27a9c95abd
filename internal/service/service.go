package service

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(
	NewAdminService,
	NewAuthService,
	NewConnectService,
	NewDeviceService,
	NewAirlineService,
	NewWaypointService,
	NewSessionService,
	NewVoyageService,
	NewOnemapService,
	NewDJIConnectService,
	NewMediaService,
	NewStorageService,
	NewCloudService,
	NewConfigService,
	NewGalleryService,
	NewMissionService,
	NewLinsConsumer,
	NewDeviceOperationService,
	NewInternalLiveService,
	NewArchiveTaskService,
	NewSubjectService,
	NewAnnotationService,
)

const (
	SuccessCode    = 0
	SuccessMessage = "Ok"
)

func wrapHttpHandlerFunc(log *log.Helper, f http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, req *http.Request) {
		{
			start := time.Now()
			defer func() {
				if r := recover(); r != nil {
					err := utilities.FromPanicValue(r)
					log.Errorf("recoverd from err %s", err.Error())
					w.WriteHeader(http.StatusInternalServerError)
					w.Header().Set("Content-Type", "application/json")
					w.Write([]byte(`{"code":500, "message":"网络错误"}`))
				} else {
					log.Infof("request %s %s, cost %s", req.Method, req.URL.Path, time.Since(start))
				}
			}()
			f(w, req)
		}
	}
}

func ReponseHttpErr(err error, w http.ResponseWriter) {
	w.Header().Set("Content-Type", "application/json")
	ke := &errors.Error{}
	if errors.As(err, &ke) {
		w.WriteHeader(int(ke.GetCode()))
		w.Write([]byte(fmt.Sprintf(`{"code": %d, "message":"%s", "reason":"%s"}`, ke.Code, ke.Message, ke.Reason)))
		return
	}
	w.WriteHeader(http.StatusInternalServerError)
	w.Write([]byte(`{"code":500, "message":"网络错误"}`))
}

func ResponseJson(data any, w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	_, err = w.Write(body)
	return err
}
