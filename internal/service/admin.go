package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	pb "gitlab.sensoro.com/skai/skai/api/admin/v1"
	pbc "gitlab.sensoro.com/skai/skai/api/connects/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type AdminService struct {
	pb.UnimplementedAdminServer
	log *log.Helper
	cu  *biz.ConnectUsecase
	du  *biz.DeviceUsecase
}

func NewAdminService(logger log.Logger, cu *biz.ConnectUsecase, du *biz.DeviceUsecase) *AdminService {
	return &AdminService{log: log.NewHelper(logger), cu: cu, du: du}
}

func (s *AdminService) RegisterDevice(ctx context.Context, req *pb.RegisterRequest) (*pb.CommonReply, error) {
	device, _ := s.du.CheckDevice(ctx, &biz.DeviceCheck{SourceSn: req.Sn})
	if device != nil && device.Id > 0 {
		return nil, errors.BadRequest("40003004", "设备已被注册")
	}
	model := biz.DeviceModel(req.Model)
	source := biz.DeviceSource(req.Source)
	category := biz.DeviceCategory(req.Category)
	if _, err := s.du.CreateDevice(ctx, &biz.Device{
		Sn:        req.Sn,
		Type:      req.Type,
		Model:     model,
		Category:  category,
		Source:    source,
		SourceSn:  req.Sn,
		ExtraData: map[string]interface{}{"registerFrom": "API"},
	}); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *AdminService) EventUp(ctx context.Context, req *pbc.EventUpRequest) (*pb.CommonReply, error) {
	var event any
	etype := biz.ThingModelEventType(req.Type)
	switch etype {
	case biz.ThingModelEventTypeHMS:
		event = pbc.TobizDockHealMoniterEvent(req)
	case biz.ThingModelEventTypeUpdateTopo:
		event = pbc.TobizDockTopoUpdateEvent(req)
	case biz.ThingModelEventTypeFlightTaskProgress:
		event = pbc.TobizDockFlightTaskProgressEvent(req)
	case biz.ThingModelEventTypeFlightTaskResourceGet:
		event = pbc.TobizDockFlightTaskResourceRequestEvent(req)
	default:
		s.log.Infof("Connect service unsupport event %d", req.Type)
	}
	// check existed device
	if device, err := s.cu.GetDevice(ctx, &biz.DetailQuery{Id: req.DeviceId}); err == nil {
		s.cu.EventUp(ctx, device, etype, event)
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *AdminService) PropertyUp(ctx context.Context, req *pbc.PropertyUpRequest) (*pb.CommonReply, error) {
	// check existed device
	if device, err := s.du.CheckDevice(ctx, &biz.DeviceCheck{SourceSn: req.Sn}); err == nil {
		deviceProp := &biz.DeviceProperty{}
		copier.Copy(deviceProp, device.PropData)
		s.cu.PropertyUp(ctx, device, pbc.TobizDeviceProperty(device.PropData, req), deviceProp)
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *AdminService) ServiceReply(ctx context.Context, req *pbc.ServiceReplyRequest) (*pb.CommonReply, error) {
	// check existed device
	if device, err := s.cu.GetDevice(ctx, &biz.DetailQuery{Id: req.DeviceId}); err == nil {
		s.cu.ServiceReply(ctx, device, pbc.TobizServiceReply(req))
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *AdminService) DeleteDevice(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	if err := s.du.DeleteDevice(ctx, req.Id); err != nil {
		return nil, errors.BadRequest("40003004", "设备删除失败")
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}
