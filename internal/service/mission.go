package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	pb "gitlab.sensoro.com/skai/skai/api/missions/v1"
	"golang.org/x/exp/slices"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type MissionService struct {
	pb.UnimplementedMissionServer
	log *log.Helper
	au  *biz.AirlineUsecase
	du  *biz.DeviceUsecase
	mu  *biz.MissionUsecase
	tu  *biz.ThingUsecase
}

func NewMissionService(logger log.Logger, au *biz.AirlineUsecase, du *biz.DeviceUsecase, mu *biz.MissionUsecase, tu *biz.ThingUsecase) *MissionService {
	return &MissionService{log: log.<PERSON><PERSON>elper(logger), au: au, du: du, mu: mu, tu: tu}
}

func (s *MissionService) CreateMission(ctx context.Context, req *pb.MissionItem) (*pb.CommonReply, error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	mtype := biz.MissionType(req.Type)
	if !lo.Contains([]biz.MissionType{biz.MissionTypeLong, biz.MissionTypeCustom}, mtype) {
		return nil, errors.BadRequest("40000002", "任务类型不支持")
	}
	airline, err := s.au.GetAirline(ctx, &biz.AirlineQuery{Id: req.AirlineId, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "当前航线已不存在，请重新选择")
	}
	if !lo.Contains(airline.DeviceIds, req.DeviceId) {
		return nil, errors.BadRequest("40000002", "航线未关联该设备，请重新选择")
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.DeviceId, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "关联设备不存在，请重新选择")
	}
	algConfigs := make([]*biz.AlgConfig, 0)
	for _, ac := range req.AlgConfigs {
		if ac.Threshold > 1000 {
			return nil, errors.BadRequest("算法阈值配置过大", "算法阈值上限为1000，请重新填写")
		}
		if biz.AlgType(ac.Type) == biz.AlgTypeSnapshot {
			// 大模型算法，抽帧周期范围为20-60秒
			if lo.Contains([]biz.AlgName{biz.AlgNameSmokeye, biz.AlgNameFloater}, biz.AlgName(ac.Name)) {
				if ac.Interval < 20 || ac.Interval > 60 {
					return nil, errors.BadRequest("监测周期不合法", "监测周期为20-60秒，请重新填写")
				}
			} else {
				if ac.Interval < 1 || ac.Interval > 10 {
					return nil, errors.BadRequest("监测周期不合法", "监测周期为1-10秒，请重新填写")
				}
			}
		}
		algConfigs = append(algConfigs, &biz.AlgConfig{
			Interval:  ac.Interval,
			Threshold: ac.Threshold,
			Type:      biz.AlgType(ac.Type),
			Name:      biz.AlgName(ac.Name),
			LensType:  biz.VideoType(ac.LensType),
		})
	}
	startTime := time.Now()
	if mtype == biz.MissionTypeCustom && req.StartTime > 0 {
		startTime = time.UnixMilli(int64(req.StartTime))
	}
	endTime := time.UnixMilli(biz.MaxTimestamp)
	if mtype == biz.MissionTypeCustom && req.EndTime > 0 {
		endTime = time.UnixMilli(int64(req.EndTime))
	}
	executeTimes := make([]*biz.ExecuteTime, 0)
	schedules, err := s.mu.ScheduleMissions(ctx, &biz.MissionScheduleQuery{
		ProjectInfo: *projectInfo,
		DeviceId:    req.DeviceId,
		StartTime:   startTime,
		EndTime:     endTime,
	})
	if err != nil {
		return nil, err
	}
	// 任务内部时间互斥校验
	moment := int32(0)
	sortedTimes := req.ExecuteTimes
	slices.SortFunc(sortedTimes, func(a, b *pb.ExecuteTime) int { return int(a.Moment - b.Moment) })
	for i, et := range sortedTimes {
		if moment+90*60 >= et.Moment {
			return nil, errors.BadRequest(fmt.Sprintf("第[%d]条时间冲突", i+1), "执行时间冲突，请重新填写")
		}
		moment = et.Moment
	}
	// 任务之间时间互斥校验
	for i, et := range req.ExecuteTimes {
		bet := &biz.ExecuteTime{}
		copier.Copy(bet, et)
		if !bet.IsValid(schedules) {
			return nil, errors.BadRequest(fmt.Sprintf("第[%d]条时间冲突", i+1), "执行时间冲突，请重新填写")
		}
		executeTimes = append(executeTimes, bet)
	}
	noticeRules := make([]*biz.NoticeRule, 0)
	for _, nr := range req.NoticeRules {
		bnr := &biz.NoticeRule{}
		copier.Copy(bnr, nr)
		noticeRules = append(noticeRules, bnr)
	}
	mission := &biz.Mission{
		Name:         req.Name,
		Type:         mtype,
		Description:  req.Description,
		StartTime:    startTime,
		EndTime:      endTime,
		AirlineId:    req.AirlineId,
		Airline:      airline,
		DeviceId:     req.DeviceId,
		Device:       device,
		AlgConfigs:   algConfigs,
		NoticeRules:  noticeRules,
		ExecuteTimes: executeTimes,
		MerchantId:   projectInfo.MerchantId,
		TenantId:     projectInfo.TenantId,
		AvatarId:     projectInfo.AvatarId,
		EditorId:     projectInfo.AvatarId,
		EditedTime:   time.Now(),
	}
	mission.Status = mission.Flowing()
	if _, err := s.mu.CreateMission(ctx, mission); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *MissionService) ListMission(ctx context.Context, req *pb.ListRequest) (*pb.ListReply, error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	var status *biz.MissionStatus
	if req.Status != nil {
		status = lo.ToPtr(biz.MissionStatus(*req.Status))
	}
	deviceIds := make([]int64, 0)
	// 任务支持多个设备ID查询航线
	if req.DeviceIds != "" {
		deviceIds = lo.Map(strings.Split(req.DeviceIds, ","), func(id string, _ int) int64 {
			return conv.Atoi(id)
		})
	}
	total, missions, err := s.mu.ListMission(ctx, &biz.MissionListQuery{
		ProjectInfo: *projectInfo,
		DeviceIds:   deviceIds,
		Page:        int(req.Page),
		Size:        int(req.Size),
		Search:      req.Search,
		Status:      status,
	})
	if err != nil {
		return nil, err
	}
	deviceIds = lo.Map(missions, func(m *biz.Mission, _ int) int64 { return m.DeviceId })
	_, devices, err := s.du.ListDevice(ctx, &biz.DeviceListQuery{ProjectInfo: *projectInfo, Ids: deviceIds, Size: int(req.Size), Page: 1})
	if err != nil {
		return nil, err
	}
	deviceMap := lo.KeyBy(devices, func(d *biz.Device) int64 { return d.Id })
	list := lo.Map(missions, func(m *biz.Mission, _ int) *pb.MissionItem {
		m.Device = deviceMap[m.DeviceId]
		return s.bizToPBMission(m)
	})
	return &pb.ListReply{
		Message: SuccessMessage,
		Data: &pb.ListReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			List:  list,
			Total: total,
		},
	}, nil
}

func (s *MissionService) GetMission(ctx context.Context, req *pb.GetRequest) (*pb.MissionReply, error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	query := &biz.DetailQuery{Id: req.Id, Unscoped: req.Unscoped, ProjectInfo: *projectInfo}
	mission, err := s.mu.GetMission(ctx, query)
	if err != nil {
		return nil, errors.BadRequest("40000002", "任务不存在")
	}
	mission.Avatar, _ = s.mu.GetAvatar(ctx, mission.AvatarId)
	mission.Editor, _ = s.mu.GetAvatar(ctx, mission.EditorId)
	mission.Device, _ = s.du.GetDevice(ctx, &biz.DetailQuery{Id: mission.DeviceId, ProjectInfo: *projectInfo})
	mission.Airline, _ = s.au.GetAirline(ctx, &biz.AirlineQuery{Id: mission.AirlineId, ProjectInfo: *projectInfo})
	return &pb.MissionReply{
		Message: SuccessMessage,
		Data:    s.bizToPBMission(mission),
	}, nil
}

func (s *MissionService) BatchMission(ctx context.Context, req *pb.BatchRequest) (ret *pb.BatchReply, err error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	page, size := 1, len(req.Ids)
	status := biz.MissionStatus(req.Status)
	if !lo.Contains([]biz.MissionStatus{biz.StatusPause, biz.StatusDoing}, status) {
		return nil, errors.BadRequest("40000002", "任务状态批量操作不支持")
	}
	list := make([]*pb.BatchReplyDetail, 0)
	var successCount, failureCount int32 = 0, 0
	total, missions, err := s.mu.ListMission(ctx, &biz.MissionListQuery{ProjectInfo: *projectInfo, Ids: req.Ids, Size: size, Page: page})
	if err != nil || total == 0 {
		return nil, errors.BadRequest("40000002", "选择任务不存在")
	}
	opTypeMap := map[biz.MissionStatus]string{1: "启用", 2: "暂停", 3: "结束", 4: "删除"}
	// 暂停任务需要校验任务状态
	if status == biz.StatusPause {
		for _, mission := range missions {
			detail := &pb.BatchReplyDetail{Id: mission.Id, Name: mission.Name, Type: opTypeMap[status], Status: "失败", Reason: ""}
			if mission.Status != biz.StatusDoing {
				failureCount++
				detail.Reason = "任务状态已失效，无法暂停"
				list = append(list, detail)
				continue
			}
			// 设置状态为暂停
			mission.Status = biz.StatusPause
			if err := s.mu.UpdateMission(ctx, mission.Id, biz.AnyMap{
				"status": mission.Status, "editor_id": projectInfo.AvatarId, "edited_time": time.Now(),
			}, nil); err != nil {
				failureCount++
				detail.Reason = "任务暂停失败，联系技术人员"
				list = append(list, detail)
			}
			successCount++
			detail.Status = "成功"
			list = append(list, detail)
		}
		return &pb.BatchReply{
			Message: SuccessMessage,
			Data:    &pb.BatchReplyListData{SuccessCount: successCount, FailureCount: failureCount, List: list},
		}, nil
	}
	// 启用任务需要校验关联航线和设备
	deviceIds := lo.Map(missions, func(m *biz.Mission, _ int) int64 { return m.DeviceId })
	airlineIds := lo.Map(missions, func(m *biz.Mission, _ int) int64 { return m.AirlineId })
	_, devices, err := s.du.ListDevice(ctx, &biz.DeviceListQuery{ProjectInfo: *projectInfo, Ids: deviceIds, Size: size, Page: page})
	if err != nil {
		return nil, err
	}
	_, airlines, err := s.au.ListAirline(ctx, &biz.AirlineListQuery{ProjectInfo: *projectInfo, Ids: airlineIds, Size: size, Page: page})
	if err != nil {
		return nil, err
	}
	deviceMap := lo.KeyBy(devices, func(d *biz.Device) int64 { return d.Id })
	airlineMap := lo.KeyBy(airlines, func(a *biz.Airline) int64 { return a.Id })
	for _, mission := range missions {
		detail := &pb.BatchReplyDetail{Id: mission.Id, Name: mission.Name, Type: opTypeMap[status], Status: "失败", Reason: ""}
		if mission.Status != biz.StatusPause {
			failureCount++
			detail.Reason = "任务状态已失效，无法启用"
			list = append(list, detail)
			continue
		}
		mission.Airline = airlineMap[mission.AirlineId]
		if airlineMap[mission.AirlineId] == nil {
			failureCount++
			detail.Reason = "关联航线已删除，无法启用"
			list = append(list, detail)
			continue
		}
		if !lo.Contains(mission.Airline.DeviceIds, mission.DeviceId) {
			failureCount++
			detail.Reason = "绑定关系已变更，无法启用"
			list = append(list, detail)
			continue
		}
		mission.Device = deviceMap[mission.DeviceId]
		if mission.Device == nil {
			failureCount++
			detail.Reason = "关联设备已删除，无法启用"
			list = append(list, detail)
			continue
		}
		// 根据实际起止时间计算状态
		mission.Status = mission.Flowing()
		if err := s.mu.UpdateMission(ctx, mission.Id, biz.AnyMap{
			"status": mission.Status, "editor_id": projectInfo.AvatarId, "edited_time": time.Now(),
		}, nil); err != nil {
			failureCount++
			detail.Reason = "任务启用失败，联系技术人员"
			list = append(list, detail)
		}
		successCount++
		detail.Status = "成功"
		list = append(list, detail)
	}
	return &pb.BatchReply{
		Message: SuccessMessage,
		Data:    &pb.BatchReplyListData{SuccessCount: successCount, FailureCount: failureCount, List: list},
	}, nil
}

func (s *MissionService) SwitchMission(ctx context.Context, req *pb.SwitchRequest) (ret *pb.CommonReply, err error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	mission, err := s.mu.GetMission(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "任务不存在")
	}
	if req.Enabled {
		if mission.Status != biz.StatusPause {
			return nil, errors.BadRequest("40000002", "任务状态不为已暂停，无法启用")
		}
		if mission.Airline, err = s.au.GetAirline(ctx, &biz.AirlineQuery{Id: mission.AirlineId, ProjectInfo: *projectInfo}); err != nil {
			return nil, errors.BadRequest("40000002", "关联航线已删除，无法启用")
		}
		if !lo.Contains(mission.Airline.DeviceIds, mission.DeviceId) {
			return nil, errors.BadRequest("40000002", "绑定关系已变更，无法启用")
		}
		if mission.Device, err = s.du.GetDevice(ctx, &biz.DetailQuery{Id: mission.DeviceId, ProjectInfo: *projectInfo}); err != nil {
			return nil, errors.BadRequest("40000002", "关联设备已删除，无法启用")
		}
		// 根据实际起止时间计算状态
		mission.Status = mission.Flowing()
	} else {
		if mission.Status != biz.StatusDoing {
			return nil, errors.BadRequest("40000002", "任务状态不为进行中，无法暂停")
		}
		// 设置状态为暂停
		mission.Status = biz.StatusPause
	}
	if err := s.mu.UpdateMission(ctx, mission.Id, biz.AnyMap{
		"status": mission.Status, "editor_id": projectInfo.AvatarId, "edited_time": time.Now(),
	}, nil); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *MissionService) UpdateMission(ctx context.Context, req *pb.MissionItem) (ret *pb.CommonReply, err error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	mtype := biz.MissionType(req.Type)
	if !lo.Contains([]biz.MissionType{biz.MissionTypeLong, biz.MissionTypeCustom}, mtype) {
		return nil, errors.BadRequest("40000002", "任务类型不支持")
	}
	mission, err := s.mu.GetMission(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "任务不存在")
	}
	if mission.Status == biz.StatusFinish {
		return nil, errors.BadRequest("40000002", "任务已结束，禁止编辑")
	}
	if mission.Airline, err = s.au.GetAirline(ctx, &biz.AirlineQuery{Id: req.AirlineId, ProjectInfo: *projectInfo}); err != nil {
		return nil, errors.BadRequest("40000002", "当前航线已不存在，请重新选择")
	}
	if !lo.Contains(mission.Airline.DeviceIds, req.DeviceId) {
		return nil, errors.BadRequest("40000002", "航线未关联该设备，请重新选择")
	}
	if mission.Device, err = s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.DeviceId, ProjectInfo: *projectInfo}); err != nil {
		return nil, errors.BadRequest("40000002", "关联设备不存在，请重新选择")
	}
	algConfigs := make([]*biz.AlgConfig, 0)
	for _, ac := range req.AlgConfigs {
		if ac.Threshold > 1000 {
			return nil, errors.BadRequest("算法阈值配置过大", "算法阈值上限为1000，请重新填写")
		}
		if biz.AlgType(ac.Type) == biz.AlgTypeSnapshot {
			// 大模型算法，抽帧周期范围为20-60秒
			if lo.Contains([]biz.AlgName{biz.AlgNameSmokeye, biz.AlgNameFloater}, biz.AlgName(ac.Name)) {
				if ac.Interval < 20 || ac.Interval > 60 {
					return nil, errors.BadRequest("监测周期不合法", "监测周期为20-60秒，请重新填写")
				}
			} else {
				if ac.Interval < 1 || ac.Interval > 10 {
					return nil, errors.BadRequest("监测周期不合法", "监测周期为1-10秒，请重新填写")
				}
			}
		}
		algConfigs = append(algConfigs, &biz.AlgConfig{
			Interval:  ac.Interval,
			Threshold: ac.Threshold,
			Type:      biz.AlgType(ac.Type),
			Name:      biz.AlgName(ac.Name),
			LensType:  biz.VideoType(ac.LensType),
		})
	}
	// 任务状态进行中，且关联设备为本次任务巡航中
	if mission.Status == biz.StatusDoing && mission.Device.MissionId != nil && *mission.Device.MissionId == req.Id {
		// 任务原来无算法配置
		if len(mission.AlgConfigs) == 0 {
			// 算法名称不可添加
			if len(algConfigs) > 0 {
				return nil, errors.BadRequest("关联设备巡航中算法名称不可添加", "关联设备巡航中算法名称不可添加")
			}
		} else {
			// 1. 算法名称不可清空
			if len(algConfigs) == 0 {
				return nil, errors.BadRequest("关联设备巡航中算法名称不可清空", "关联设备巡航中算法名称不可清空")
			}
			// 2. 算法名称不可修改
			if algConfigs[0].Name != mission.AlgConfigs[0].Name {
				return nil, errors.BadRequest("关联设备巡航中算法名称不可修改", "关联设备巡航中算法名称不可修改")
			}
			// 3. 监测周期不可修改
			if algConfigs[0].Interval != mission.AlgConfigs[0].Interval {
				return nil, errors.BadRequest("关联设备巡航中监测周期不可修改", "关联设备巡航中监测周期不可修改")
			}
		}
	}
	// 获取设备调度计划列表
	startTime := time.Now()
	if mtype == biz.MissionTypeCustom && req.StartTime > 0 {
		startTime = time.UnixMilli(int64(req.StartTime))
	}
	endTime := time.UnixMilli(biz.MaxTimestamp)
	if mtype == biz.MissionTypeCustom && req.EndTime > 0 {
		endTime = time.UnixMilli(int64(req.EndTime))
	}
	executeTimes := make([]*biz.ExecuteTime, 0)
	schedules, err := s.mu.ScheduleMissions(ctx, &biz.MissionScheduleQuery{
		ProjectInfo: *projectInfo,
		DeviceId:    req.DeviceId,
		ExcludeId:   req.Id,
		StartTime:   startTime,
		EndTime:     endTime,
	})
	if err != nil {
		return nil, err
	}
	// 任务内部时间互斥校验
	moment := int32(0)
	sortedTimes := req.ExecuteTimes
	slices.SortFunc(sortedTimes, func(a, b *pb.ExecuteTime) int { return int(a.Moment - b.Moment) })
	for i, et := range sortedTimes {
		if moment+90*60 >= et.Moment {
			return nil, errors.BadRequest(fmt.Sprintf("第[%d]条时间冲突", i+1), "执行时间冲突，请重新填写")
		}
		moment = et.Moment
	}
	// 任务之间时间互斥校验
	for i, et := range req.ExecuteTimes {
		bet := &biz.ExecuteTime{}
		copier.Copy(bet, et)
		if !bet.IsValid(schedules) {
			return nil, errors.BadRequest(fmt.Sprintf("第[%d]条时间冲突", i+1), "执行时间冲突，请重新填写")
		}
		executeTimes = append(executeTimes, bet)
	}
	noticeRules := make([]*biz.NoticeRule, 0)
	for _, nr := range req.NoticeRules {
		bnr := &biz.NoticeRule{}
		copier.Copy(bnr, nr)
		noticeRules = append(noticeRules, bnr)
	}
	body := &biz.Mission{
		Name:         req.Name,
		Type:         mtype,
		Description:  req.Description,
		StartTime:    startTime,
		EndTime:      endTime,
		Status:       mission.Status,
		AirlineId:    req.AirlineId,
		DeviceId:     req.DeviceId,
		AlgConfigs:   algConfigs,
		NoticeRules:  noticeRules,
		ExecuteTimes: executeTimes,
		EditorId:     projectInfo.AvatarId,
	}
	if err := s.mu.UpdateMission(ctx, mission.Id, mission.Change(body), mission); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *MissionService) StatsMission(ctx context.Context, req *pb.EmptyRequest) (*pb.StatsReply, error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	counters, err := s.mu.CountMission(ctx, &biz.CountQuery{ProjectInfo: *projectInfo, CountBy: "status"})
	if err != nil {
		return nil, err
	}
	list := lo.Map(counters, func(c *biz.CountRet, _ int) *pb.StatsReplyStatusCount {
		return &pb.StatsReplyStatusCount{Status: int32(c.Radix), Count: c.Count}
	})
	return &pb.StatsReply{
		Message: SuccessMessage,
		Data:    &pb.StatsReplyListData{List: list, Total: int32(len(list))},
	}, nil
}

func (s *MissionService) AlarmsMission(ctx context.Context, req *pb.AlarmsRequest) (*pb.AlarmsReply, error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	timeScope := biz.NewTimeScope(req.StartTime, req.EndTime)
	counters, err := s.mu.AlarmsMission(ctx, &biz.ExecutionTopNQuery{ProjectInfo: *projectInfo, StartTime: timeScope.StartTime, Size: 5})
	if err != nil {
		return nil, err
	}
	list := lo.Map(counters, func(m *biz.Mission, _ int) *pb.AlarmsReplyTaskCount {
		return &pb.AlarmsReplyTaskCount{Name: m.Name, Count: m.AlarmCount}
	})
	return &pb.AlarmsReply{
		Message: SuccessMessage,
		Data:    &pb.AlarmsReplyListData{List: list, Total: int32(len(list))},
	}, nil
}

func (s *MissionService) DeleteMission(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	mission, err := s.mu.GetMission(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "任务不存在")
	}
	if mission.Status == biz.StatusDoing {
		device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: mission.DeviceId, ProjectInfo: *projectInfo})
		if err == nil && device.MissionId != nil && *device.MissionId == req.Id {
			return nil, errors.BadRequest("40000002", "任务关联设备正在巡航中，禁止删除")
		}
	}
	if err := s.mu.DeleteMission(ctx, mission.Id); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *MissionService) ListExecution(ctx context.Context, req *pb.ExecutionsRequest) (*pb.ExecutionsReply, error) {
	projectInfo, err := s.mu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	total, executions, err := s.mu.ListExecution(ctx, &biz.ExecutionListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.Size),
		MissionId:   req.Id,
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(executions, func(be *biz.Execution, _ int) *pb.ExecutionsReplyExecution {
		re := &pb.ExecutionsReplyExecution{}
		copier.CopyWithOption(re, be, copier.Option{
			IgnoreEmpty: true,
			DeepCopy:    false,
			Converters:  []copier.TypeConverter{biz.TimeCopierConverter},
		})
		re.Reason = be.Reason.String()
		return re
	})
	return &pb.ExecutionsReply{
		Message: SuccessMessage,
		Data: &pb.ExecutionsReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			List:  list,
			Total: total,
		},
	}, nil
}

func (s *MissionService) StatusMission(ctx context.Context, req *pb.StatusRequest) (*pb.CommonReply, error) {
	reply := &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}
	mission, err := s.mu.GetMission(ctx, &biz.DetailQuery{Id: req.Mid})
	if err != nil {
		return reply, nil
	}
	switch req.Body.Action {
	case "doing":
		if mission.Status != biz.StatusAwait {
			return reply, nil
		}
		status := mission.Flowing()
		s.mu.UpdateMission(ctx, mission.Id, mission.Shift(status), nil)
	case "finish":
		if mission.Status == biz.StatusFinish {
			return reply, nil
		}
		status := mission.Flowing()
		s.mu.UpdateMission(ctx, mission.Id, mission.Shift(status), nil)
	default:
		return reply, nil
	}
	return reply, nil
}

func (s *MissionService) bizToPBMission(bm *biz.Mission) *pb.MissionItem {
	mi := &pb.MissionItem{}
	copier.CopyWithOption(mi, bm, copier.Option{
		IgnoreEmpty: true,
		DeepCopy:    false,
		Converters:  []copier.TypeConverter{biz.TimeCopierConverter},
	})
	if bm.Device != nil {
		mi.Device.Name = bm.Device.Deployment.Name
	}
	return mi
}
