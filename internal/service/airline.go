package service

import (
	"context"
	"strings"
	"time"

	"github.com/asmarques/geodist"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	pb "gitlab.sensoro.com/skai/skai/api/airlines/v1"
	"gitlab.sensoro.com/skai/skai/pkg/kmz"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type AirlineService struct {
	pb.UnimplementedAirlineServer
	log *log.Helper
	au  *biz.AirlineUsecase
	mu  *biz.MissionUsecase
}

func NewAirlineService(logger log.Logger, au *biz.AirlineUsecase, mu *biz.MissionUsecase) *AirlineService {
	return &AirlineService{log: log.<PERSON>Helper(logger), au: au, mu: mu}
}

func (s *AirlineService) CreateAirline(ctx context.Context, req *pb.AddRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.au.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	paths := strings.Split(req.Path, "v1/storage/")
	if len(paths) < 2 || paths[1] == "" {
		return nil, errors.BadRequest("40000002", "文件地址不正确")
	}
	_, reader, err := s.au.GetKmzFile(ctx, &biz.StorageObject{Key: paths[1]})
	if err != nil {
		return nil, errors.BadRequest("40000002", "下载航线源文件错误")
	}
	fingerprint, err := kmz.Sign(biz.AssetsDir, reader)
	if err != nil {
		return nil, errors.BadRequest("40000002", "本地存储文件失败")
	}
	if err := kmz.Unzip(biz.AssetsDir); err != nil {
		return nil, errors.BadRequest("40000002", "解压航线文件失败")
	}
	kline, err := kmz.ParseTemplate(biz.AssetsDir)
	if err != nil {
		return nil, errors.BadRequest("40000002", "解析模板文件错误，请重新上传")
	}
	points, err := kmz.ParseWaypoints(biz.AssetsDir)
	if err != nil {
		return nil, errors.BadRequest("40000002", "解析航点文件错误，请重新上传")
	}
	maxSerial, minSerial := 0, 0
	for _, p := range points {
		if p.Serial > maxSerial {
			maxSerial = p.Serial
		}
		if p.Serial < minSerial {
			minSerial = p.Serial
		}
	}
	if maxSerial-minSerial > len(points)-1 {
		return nil, errors.BadRequest("40000002", "航点序号不连续")
	}
	var estimateMileage float64 = 0
	lo.Reduce(points, func(w, p *kmz.Waypoint, _ int) *kmz.Waypoint {
		lastPoint := geodist.Point{Long: w.Lnglat[0], Lat: w.Lnglat[1]}
		currPoint := geodist.Point{Long: p.Lnglat[0], Lat: p.Lnglat[1]}
		estimateMileage += geodist.HaversineDistance(lastPoint, currPoint) * 1000
		return p
	}, points[0])
	var fenceArea *biz.Geometry
	if kline.FenceArea != nil {
		fenceArea = &biz.Geometry{
			Type: kline.FenceArea.Type,
			Coordinates: lo.Map(kline.FenceArea.Coordinates, func(pc kmz.PolygonCoord, _ int) biz.GeometryCoordinate {
				return biz.GeometryCoordinate(pc)
			}),
		}
	}
	airline := &biz.Airline{
		Name:            req.Name,
		Tags:            req.Tags,
		Type:            string(kline.Type),
		WaypointCount:   int32(len(points)),
		EstimateMileage: float32(estimateMileage),
		FenceArea:       fenceArea,
		DeviceIds:       req.DeviceIds,
		Description:     req.Description,
		MerchantId:      projectInfo.MerchantId,
		TenantId:        projectInfo.TenantId,
		KMZFile:         biz.KMZFile{Url: paths[1], Fingerprint: fingerprint},
	}
	copier.Copy(airline, kline)
	if airline, err = s.au.CreateAirline(ctx, airline); err != nil {
		return nil, err
	}
	s.au.CreateWaypoints(ctx, lo.Map(points, func(point *kmz.Waypoint, _ int) *biz.Waypoint {
		waypoint := &biz.Waypoint{AirlineId: airline.Id, MerchantId: airline.MerchantId, TenantId: airline.TenantId}
		copier.Copy(waypoint, point)
		return waypoint
	}))
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *AirlineService) ListAirline(ctx context.Context, req *pb.ListRequest) (*pb.ListReply, error) {
	projectInfo, err := s.au.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	var startTime, endTime *time.Time
	if req.StartTime > 0 {
		startTime = lo.ToPtr(time.UnixMilli(req.StartTime))
	}
	if req.EndTime > 0 {
		endTime = lo.ToPtr(time.UnixMilli(req.EndTime))
	}
	deviceIds := make([]int64, 0)
	// 媒体库支持多个设备ID查询航线
	if req.DeviceIds != "" {
		deviceIds = lo.Map(strings.Split(req.DeviceIds, ","), func(id string, _ int) int64 {
			return conv.Atoi(id)
		})
	}
	// 一张图针对单个设备ID查询航线
	if req.DeviceId != nil {
		deviceIds = append(deviceIds, *req.DeviceId)
	}
	total, airlines, err := s.au.ListAirline(ctx, &biz.AirlineListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.Size),
		DeviceIds:   deviceIds,
		Search:      req.Search,
		Type:        req.Type,
		StartTime:   startTime,
		EndTime:     endTime,
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(airlines, func(a *biz.Airline, _ int) *pb.AirlineItem {
		return s.bizToPBAirline(a)
	})
	return &pb.ListReply{
		Message: SuccessMessage,
		Data: &pb.ListReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			List:  list,
			Total: total,
		},
	}, nil
}

func (s *AirlineService) GetAirline(ctx context.Context, req *pb.GetRequest) (*pb.AirlineReply, error) {
	projectInfo, err := s.au.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	query := &biz.AirlineQuery{Id: req.Id, Scope: req.Scope, WithDevice: req.WithDevice, ProjectInfo: *projectInfo}
	airline, err := s.au.GetAirline(ctx, query)
	if err != nil {
		return nil, errors.BadRequest("40000002", "航线不存在")
	}
	return &pb.AirlineReply{
		Message: SuccessMessage,
		Data:    s.bizToPBAirline(airline),
	}, nil
}

func (s *AirlineService) UpdateAirline(ctx context.Context, req *pb.UpdateRequest) (ret *pb.CommonReply, err error) {
	projectInfo, err := s.au.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	airline, err := s.au.GetAirline(ctx, &biz.AirlineQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航线不存在")
	}
	change := airline.Change(&biz.Changable{Name: req.Name, Tags: req.Tags, DeviceIds: req.DeviceIds, Description: req.Description})
	if err := s.au.UpdateAirline(ctx, airline.Id, change); err != nil {
		return nil, err
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *AirlineService) DeleteAirline(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.au.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	airline, err := s.au.GetAirline(ctx, &biz.AirlineQuery{Id: req.Id, WithDevice: true, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航线不存在")
	}
	if lo.SomeBy(airline.Devices, func(device *biz.Device) bool {
		controls := device.LockStatus.Scatter()
		return controls[0] == biz.EnablingStatus
	}) {
		return nil, errors.BadRequest("40000002", "航线正在被设备加载，禁止删除")
	}
	if err := s.au.UpdateAirline(ctx, airline.Id, airline.Recycle()); err != nil {
		return nil, err
	}
	if _, missions, err := s.mu.ListMission(ctx, &biz.MissionListQuery{Size: 100, AirlineId: &airline.Id, ProjectInfo: *projectInfo}); err == nil {
		for _, mission := range missions {
			s.mu.UpdateMission(ctx, mission.Id, lo.Assign(mission.Release(), mission.Shift(biz.StatusPause)), nil)
		}
	}
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *AirlineService) DownloadAirline(ctx context.Context, req *pb.CommonRequest) (*pb.DownloadReply, error) {
	projectInfo, err := s.au.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	airline, err := s.au.GetAirline(ctx, &biz.AirlineQuery{Id: req.Id, ProjectInfo: *projectInfo, WithSigned: true})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航线不存在")
	}
	kmzFile := &pb.DownloadReplyKmzFile{}
	copier.Copy(kmzFile, airline.KMZFile)
	return &pb.DownloadReply{
		Message: SuccessMessage,
		Data:    kmzFile,
	}, nil
}

func (s *AirlineService) bizToPBAirline(ba *biz.Airline) *pb.AirlineItem {
	ai := &pb.AirlineItem{}
	copier.CopyWithOption(ai, ba, copier.Option{
		IgnoreEmpty: true,
		DeepCopy:    false,
		Converters:  []copier.TypeConverter{biz.TimeCopierConverter},
	})
	ai.Devices = lo.Map(ba.Devices, func(device *biz.Device, _ int) *pb.AirlineItemBriefDevice {
		return &pb.AirlineItemBriefDevice{
			Id:            device.Id,
			Sn:            device.Sn,
			Type:          device.Type,
			Model:         device.Model.String(),
			Name:          device.Deployment.Name,
			Status:        device.Status.String(),
			NetworkStatus: device.NetworkStatus,
			Category:      device.Category.String(),
			Lnglat:        device.Deployment.Lnglat,
			Location:      device.Deployment.Location,
		}
	})
	if ba.FenceArea != nil {
		fenceArea := &structpb.Struct{Fields: make(map[string]*structpb.Value, 2)}
		fenceArea.Fields["type"] = structpb.NewStringValue(ba.FenceArea.Type)
		cl := lo.Map(ba.FenceArea.Coordinates, func(it biz.GeometryCoordinate, _ int) any {
			return it.ToAnySlice()
		})
		coordinates, err := structpb.NewList(cl)
		if err == nil {
			fenceArea.Fields["coordinates"] = structpb.NewListValue(coordinates)
		}
		ai.FenceArea = fenceArea
	}
	return ai
}
