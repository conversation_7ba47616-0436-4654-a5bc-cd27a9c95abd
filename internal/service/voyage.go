package service

import (
	"context"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"github.com/xuri/excelize/v2"
	pb "gitlab.sensoro.com/skai/skai/api/voyages/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type VoyageService struct {
	pb.UnimplementedVoyageServer
	log *log.Helper
	du  *biz.DeviceUsecase
	vu  *biz.VoyageUsecase
}

func NewVoyageService(logger log.Logger, du *biz.DeviceUsecase, vu *biz.VoyageUsecase) *VoyageService {
	return &VoyageService{log: log.NewHelper(logger), du: du, vu: vu}
}

func (s *VoyageService) ListVoyage(ctx context.Context, req *pb.ListRequest) (*pb.ListReply, error) {
	projectInfo, err := s.vu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: *req.DeviceId, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	endTime := lo.ToPtr(time.Now())
	startTime := device.Deployment.Time
	if req.EndTime > 0 && req.EndTime < startTime.UnixMilli() {
		endTime = startTime
	}
	if req.StartTime > startTime.UnixMilli() {
		startTime = lo.ToPtr(time.UnixMilli(req.StartTime))
	}
	if req.EndTime > startTime.UnixMilli() {
		endTime = lo.ToPtr(time.UnixMilli(req.EndTime))
	}
	total, voyages, err := s.vu.ListVoyage(ctx, &biz.VoyageListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.Size),
		DeviceId:    req.DeviceId,
		AirlineId:   req.AirlineId,
		StartTime:   startTime,
		EndTime:     endTime,
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(voyages, func(v *biz.Voyage, _ int) *pb.VoyageItem {
		return s.bizToPBVoyage(v)
	})
	return &pb.ListReply{
		Message: SuccessMessage,
		Data: &pb.ListReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			List:  list,
			Total: total,
		},
	}, nil
}

func (s *VoyageService) GetVoyage(ctx context.Context, req *pb.CommonRequest) (*pb.VoyageReply, error) {
	projectInfo, err := s.vu.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	voyage, err := s.vu.GetVoyage(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "航次不存在")
	}
	return &pb.VoyageReply{
		Message: SuccessMessage,
		Data:    s.bizToPBVoyage(voyage),
	}, nil
}

func (s *VoyageService) UpdateVoyage(ctx context.Context, req *pb.UpdateRequest) (ret *pb.CommonReply, err error) {
	return nil, nil
}

func (s *VoyageService) NewExportVoyageListHandlerFunc() http.HandlerFunc {
	return wrapHttpHandlerFunc(s.log, func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}
		query := r.URL.Query()
		deviceId := conv.Atoi(query.Get("deviceId"))
		if deviceId == 0 {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"code":40000001, "message":"参数不正确,设备ID必传"}`))
			return
		}
		projectInfo, err := s.vu.GetProjectInfo(r.Context())
		if err != nil {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"code":40300001, "message":"Token校验不正确"}`))
			return
		}
		device, err := s.du.GetDevice(r.Context(), &biz.DetailQuery{Id: deviceId, ProjectInfo: *projectInfo})
		if err != nil {
			return
		}
		endTime := lo.ToPtr(time.Now())
		startTime := device.Deployment.Time
		if conv.Atoi(query.Get("endTime")) > 0 && conv.Atoi(query.Get("endTime")) < startTime.UnixMilli() {
			endTime = startTime
		}
		if ts := conv.Atoi(query.Get("startTime")); ts > startTime.UnixMilli() {
			startTime = lo.ToPtr(time.UnixMilli(ts))
		}
		if ts := conv.Atoi(query.Get("endTime")); ts > startTime.UnixMilli() {
			endTime = lo.ToPtr(time.UnixMilli(ts))
		}
		_, voyages, err := s.vu.ListVoyage(r.Context(), &biz.VoyageListQuery{
			ProjectInfo: *projectInfo,
			DeviceId:    lo.ToPtr(deviceId),
			StartTime:   startTime,
			EndTime:     endTime,
			Size:        1000,
			Page:        1,
		})
		if err != nil {
			s.log.Error("do ExportVoyage err ", err)
			ReponseHttpErr(err, w)
			return
		}
		f := excelize.NewFile()
		defer f.Close()
		sw, err := f.NewStreamWriter("Sheet1")
		if err != nil {
			s.log.Error("get StreanWriter failed: ", err)
			ReponseHttpErr(err, w)
			return
		}
		sw.SetRow("A1", []any{"执行航线", "开始时间", "结束时间", "持续时间", "飞行里程"})
		rowId := 1
		for _, v := range voyages {
			rowId++
			s.log.Debugf("append row %d, value %+v", rowId, v)
			airlineName := lo.Ternary(device.Category == biz.CategoryDock, "一键飞行巡航", "未按航线飞行")
			if v.Airline != nil {
				airlineName = v.Airline.Name
			}
			row := make([]interface{}, 5)
			row[0] = airlineName
			row[1] = v.StartTime.In(biz.TimeLocation).Format("2006-01-02 15:04:05")
			row[2] = v.EndTime.In(biz.TimeLocation).Format("2006-01-02 15:04:05")
			row[3] = v.Runtime
			row[4] = v.Mileage
			cell, err := excelize.CoordinatesToCellName(1, rowId)
			if err != nil {
				s.log.Error("get StreanWriter failed: ", err)
				ReponseHttpErr(err, w)
				break
			}
			if err := sw.SetRow(cell, row); err != nil {
				ReponseHttpErr(err, w)
				break
			}
		}
		if err = sw.Flush(); err != nil {
			s.log.Error("flush StreanWriter failed: ", err)
			ReponseHttpErr(err, w)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/octet-stream")
		f.WriteTo(w)
	})
}

func (s *VoyageService) bizToPBVoyage(bv *biz.Voyage) *pb.VoyageItem {
	vi := &pb.VoyageItem{}
	copier.CopyWithOption(vi, bv, copier.Option{
		IgnoreEmpty: true,
		DeepCopy:    true,
		Converters:  []copier.TypeConverter{biz.TimeCopierConverter},
	})
	return vi
}

func (s *VoyageService) GetPath(ctx context.Context, req *pb.CommonRequest) (*pb.VoyagePathReply, error) {
	pi, err := s.vu.GetProjectInfo(ctx)
	if err != nil {
		return nil, err
	}
	ps, err := s.vu.GetVoyageFligthPath(ctx, &biz.DetailQuery{
		ProjectInfo: *pi,
		Id:          req.Id,
	})
	if err != nil {
		return nil, err
	}
	ps = lo.Filter(ps, func(item biz.VoyagePathPoint, index int) bool {
		l := len(item.PointLntlat)
		return l == 2 && (item.PointLntlat[0] != 0 || item.PointLntlat[1] != 0)
	})
	return &pb.VoyagePathReply{
		Message: SuccessMessage,
		Data: &pb.VoyagePathReply_Data{
			Total: int32(len(ps)),
			List: lo.Map(ps, func(it biz.VoyagePathPoint, _ int) *pb.VoyageFlightPoint {
				return &pb.VoyageFlightPoint{
					Timestamp: float64(it.Timestamp.UnixMilli()),
					Point:     it.PointLntlat,
					Horizon:   it.HorizontalSpeed,
					Vertical:  it.VerticalSpeed,
					Height:    it.Height,
					Elevation: it.Elevation,
				}
			}),
		},
	}, nil
}
