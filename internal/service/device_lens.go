package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"
	"gitlab.sensoro.com/skai/skai/pkg/id"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

func (s *DeviceService) LensDevice(ctx context.Context, req *pb.LensRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 创建操作记录
	payload := &biz.SwithLensPayload{
		Lens: biz.NewVideoType(req.Lens),
		Live: &biz.Media{Id: req.LiveId, Key: req.Key},
	}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeSwitchLens,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierSwitchLens,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) ClarityDevice(ctx context.Context, req *pb.ClarityRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 创建操作记录
	payload := &biz.SwitchClarityPayload{
		Clarity: req.Clarity,
		Live:    &biz.Media{Id: req.LiveId, Key: req.Key},
	}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeSwitchClairty,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierSwitchClarity,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) GimbalDevice(ctx context.Context, req *pb.GimbalRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 云台操作前提条件：开启镜头控制
	if !device.IsControlled(biz.LensControl) {
		return nil, errors.BadRequest("40000003", "当前用户未开启镜头控制，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 查找云台子设备，不存在则不允许操作
	gimbal := device.GetGimbal()
	if gimbal == nil {
		return nil, errors.BadRequest("40000002", "云台子设备不存在")
	}
	// 创建操作记录
	payload := &biz.ResetGimbalPayload{Mode: req.Mode, Subdevice: *gimbal}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeResetGimbal,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierResetGimbal,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) LookatDevice(ctx context.Context, req *pb.LookatRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 操作前提条件：开启镜头控制
	if !device.IsControlled(biz.LensControl) {
		return nil, errors.BadRequest("40000003", "当前用户未开启镜头控制，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	payload := &biz.LookatPointPayload{Locked: req.Locked, Lnglat: req.Lnglat, Height: req.Height}
	// 查找云台子设备，不存在则不允许操作
	if gimbal := device.GetGimbal(); gimbal != nil {
		payload.PayloadIndex = gimbal.Index
	} else {
		return nil, errors.BadRequest("40000002", "云台子设备不存在")
	}
	// 创建操作记录
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeLookatPoint,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierLookatPoint,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) ZoomDevice(ctx context.Context, req *pb.ZoomRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 变倍操作前提条件：开启镜头控制
	if !device.IsControlled(biz.LensControl) {
		return nil, errors.BadRequest("40000003", "当前用户未开启镜头控制，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 查找云台子设备，不存在则不允许操作
	gimbal := device.GetGimbal()
	if gimbal == nil {
		return nil, errors.BadRequest("40000002", "云台子设备不存在")
	}
	// 创建操作记录
	payload := &biz.SetFocalLengthPayload{
		Subdevice:  *gimbal,
		ZoomFactor: req.Factor,
		Lens:       biz.NewVideoType(req.Lens),
		Live:       &biz.Media{Id: req.LiveId, Key: req.Key},
	}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeSwitchZoom,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierSetFocalLength,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) PictureDevice(ctx context.Context, req *pb.PictureRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 拍照操作前提条件：开启镜头控制
	if !device.IsControlled(biz.LensControl) {
		return nil, errors.BadRequest("40000003", "当前用户未开启镜头控制，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 查找云台子设备，不存在则不允许操作
	gimbal := device.GetGimbal()
	if gimbal == nil {
		return nil, errors.BadRequest("40000002", "云台子设备不存在")
	}
	// 查找云台相机，不存在则不允许操作
	camera := device.GetCamera(gimbal)
	if camera == nil {
		return nil, errors.BadRequest("40000002", "云台相机不存在")
	}
	// 拍照和录像是互斥操作，需要做保护处理
	if device.IsVideoed() {
		return nil, errors.BadRequest("40000003", "相机处于录像模式，不支持拍照")
	}
	// 相机当前非拍照模式，则下行命令切换为拍照
	if camera.GetMode() != biz.PictureMode {
		if err := s.tu.SendDockService(ctx, &biz.DockService{
			Sn:         device.SourceSn,
			DeviceId:   device.Id,
			Identifier: biz.DockServiceIdentifierSwitchCMode,
			ServiceId:  id.NextID(),
			Timeout:    biz.ServiceTimeout,
			Payload:    &biz.CameraModePaylaod{Subdevice: *gimbal, Mode: biz.PictureMode},
		}); err != nil {
			return nil, errors.BadRequest("40000003", "切换相机模式失败，请稍后重试")
		}
		// 延迟100ms再继续下行拍照命令
		time.Sleep(100 * time.Millisecond)
	}
	// 创建操作记录
	payload := &biz.SubdevicePayload{Subdevice: *gimbal}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.TypeTakePicture,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: biz.DockServiceIdentifierTakePicture,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) VideoDevice(ctx context.Context, req *pb.VideoRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 录像操作前提条件：开启镜头控制
	if !device.IsControlled(biz.LensControl) {
		return nil, errors.BadRequest("40000003", "当前用户未开启镜头控制，请勿操作")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 查找云台子设备，不存在则不允许操作
	gimbal := device.GetGimbal()
	if gimbal == nil {
		return nil, errors.BadRequest("40000002", "云台子设备不存在")
	}
	// 查找云台相机，不存在则不允许操作
	camera := device.GetCamera(gimbal)
	if camera == nil {
		return nil, errors.BadRequest("40000002", "云台相机不存在")
	}
	opType := biz.TypeStopVideo
	identifier := biz.DockServiceIdentifierStopVideo
	if req.Action == "start" {
		opType = biz.TypeStartVideo
		identifier = biz.DockServiceIdentifierStartVideo
		// 相机当前非录像模式，则下行命令切换为录像
		if camera.GetMode() != biz.VideoMode {
			if err := s.tu.SendDockService(ctx, &biz.DockService{
				Sn:         device.SourceSn,
				DeviceId:   device.Id,
				Identifier: biz.DockServiceIdentifierSwitchCMode,
				ServiceId:  id.NextID(),
				Timeout:    biz.ServiceTimeout,
				Payload:    &biz.CameraModePaylaod{Subdevice: *gimbal, Mode: biz.VideoMode},
			}); err != nil {
				return nil, errors.BadRequest("40000003", "切换相机模式失败，请稍后重试")
			}
			// 延迟100ms再继续下行开始录像命令
			time.Sleep(100 * time.Millisecond)
		}
	} else {
		if !device.IsVideoed() {
			return nil, errors.BadRequest("40000003", "相机不在录像中，不支持停止")
		}
	}
	// 创建操作记录
	payload := &biz.SubdevicePayload{Subdevice: *gimbal}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       opType,
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行控制权命令
	if err := s.tu.SendDockService(ctx, &biz.DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: identifier,
		ServiceId:  operation.Id,
		Timeout:    biz.ServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}
