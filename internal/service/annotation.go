package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	pb "gitlab.sensoro.com/skai/skai/api/annotations/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type AnnotationService struct {
	pb.UnimplementedAnnotationServer
	log    *log.Helper
	authUC *biz.AuthUsecase
	annUC  *biz.AnnotationUsecase
}

func NewAnnotationService(
	logger log.Logger,
	authUC *biz.AuthUsecase,
	annUC *biz.AnnotationUsecase,
) *AnnotationService {
	return &AnnotationService{
		log:    log.<PERSON><PERSON>(logger),
		authUC: authUC,
		annUC:  annUC,
	}
}

func (s *AnnotationService) CreateAnnotation(ctx context.Context, req *pb.CreateAnnotationRequest) (*pb.CreateAnnotationReply, error) {
	av, err := s.authUC.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	ann, err := s.annUC.Create(ctx, &biz.AnnotationCreateReq{
		ProjectInfo: *biz.NewProjectInfo(av),
		Av:          av,
		SubjectId:   req.SubjectId,
		Media: lo.Map(req.Media, func(it *pb.BreifMedia, _ int) biz.MediaWithArea {
			return biz.MediaWithArea{
				Id: it.Id,
				ObjectAreas: lo.Map(it.Areas, func(a *pb.Area, _ int) biz.ObjectArea {
					return pb.ToBizAnnotationArea(a)
				}),
			}
		}),
		Description: req.Description,
	})
	if err != nil {
		return nil, err
	}

	return &pb.CreateAnnotationReply{
		Message: SuccessMessage,
		Data: &pb.CreateAnnotationReply_Data{
			Id:          ann.Id,
			Type:        ann.Type.String(),
			CreatedTime: float64(ann.CreatedTime.UnixMilli()),
			Description: ann.Description,
			Media: lo.Map(ann.Media, func(m biz.MediaWithArea, _ int) *pb.CreateAnnotationReply_DataItem {
				return &pb.CreateAnnotationReply_DataItem{
					Id: m.Id,
					Areas: lo.Map(m.ObjectAreas, func(oa biz.ObjectArea, _ int) *pb.Area {
						return pb.NewArea(oa)
					}),
					Item: pb.NewMediaWithAnnotation(&biz.MediaWithAnnotation{Media: *m.Item}),
				}
			}),
		},
	}, nil
}
func (s *AnnotationService) GetAnnotation(ctx context.Context, req *pb.GetAnnotationRequest) (*pb.GetAnnotationReply, error) {
	av, err := s.authUC.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	ann, err := s.annUC.GetByMediaId(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if !av.HasMerchantAccess(ann.MerchantId) {
		return nil, biz.NewNotFoundError("Annotation", "id", conv.Itoa(req.Id))
	}

	return &pb.GetAnnotationReply{
		Message: SuccessMessage,
		Data: &pb.GetAnnotationReply_AnnotationItem{
			Id:          ann.Id,
			Type:        ann.Type.String(),
			CreatedTime: float64(ann.CreatedTime.UnixMilli()),
			Description: ann.Description,
			Media: lo.Map(ann.Media, func(m biz.MediaWithArea, _ int) *pb.BreifMedia {
				r := &pb.BreifMedia{
					Id: m.Id,
					Areas: lo.Map(m.ObjectAreas, func(oa biz.ObjectArea, _ int) *pb.Area {
						return pb.NewArea(oa)
					}),
				}
				if m.Item != nil {
					r.Url = m.Item.URL
					r.Thumbnail = m.Item.GetThumbnailUrl()
				}
				return r
			}),
			Subject: pb.NewSubject(ann.Subject),
		},
	}, nil
}
func (s *AnnotationService) ListAnnotation(ctx context.Context, req *pb.ListAnnotationRequest) (*pb.ListAnnotationReply, error) {
	av, err := s.authUC.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	query := &biz.AnnotationListQuery{
		ProjectInfo: *biz.NewProjectInfo(av),
		BaseListQuery: biz.BaseListQuery{
			Page:      req.Page,
			Size:      req.Size,
			TimeScope: biz.NewTimeScope(req.StartTime, req.EndTime),
		},
		Types: lo.Map(req.AnnotateTypes, func(t string, _ int) biz.AiEventType {
			return biz.AiEventType(t)
		}),
		States: req.AnnotateStates,
	}
	total, list, err := s.annUC.ListMediaAnnotation(ctx, query)
	if err != nil {
		return nil, err
	}

	return &pb.ListAnnotationReply{
		Message: SuccessMessage,
		Data: &pb.ListAnnotationReply_Data{
			Total: int32(total),
			Page:  req.Page,
			Size:  req.Size,
			List: lo.Map(list, func(it *biz.MediaWithAnnotation, _ int) *pb.MediaWithAnnotation {
				return pb.NewMediaWithAnnotation(it)
			}),
		},
	}, nil
}
