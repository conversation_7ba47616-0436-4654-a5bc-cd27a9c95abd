package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/emqx/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type EmqxCallbackService struct {
	pb.UnimplementedEmqxCallbackServer
	log    *log.Helper
	mqttUc *biz.MQTTUsecase
}

func NewEmqxCallbackService(
	logger log.Logger,
	mqttUc *biz.MQTTUsecase,
) *EmqxCallbackService {
	return &EmqxCallbackService{
		log:    log.New<PERSON>elper(logger),
		mqttUc: mqttUc,
	}
}

func (s *EmqxCallbackService) AuthClient(ctx context.Context, req *pb.AuthMqttClientRequest) (*pb.AuthMqttClientReply, error) {
	ret, err := s.mqttUc.CanClientConnect(ctx, &biz.MQTTClientInfo{
		Username: req.Username,
		Password: req.Password,
		ClientId: req.ClientId,
	})
	if err != nil {
		s.log.Errorf("client %s with username %s try connect to emqx failed %v", req.ClientId, req.Username, err)
		return &pb.AuthMqttClientReply{
			Result: "deny",
		}, nil
	}
	return &pb.AuthMqttClientReply{
		Result:      lo.Ternary(ret.Ok, "allow", "deny"),
		IsSuperuser: ret.IsSuper,
	}, nil
}
func (s *EmqxCallbackService) ACL(ctx context.Context, req *pb.ACLRequest) (*pb.ACLReply, error) {
	var action biz.MQTTClientTopicAccessAction
	if req.Action == "publish" {
		action = biz.MQTTClientTopicAccessActionPublish
	} else if req.Action == "subscribe" {
		action = biz.MQTTClientTopicAccessActionSubscribe
	} else {
		return nil, biz.NewBadRequestError("emqx.ACL.invalidAction", nil)
	}
	ret, err := s.mqttUc.HasAccessToTopic(ctx, &biz.MQTTClientTopicAccessInfo{
		Username: req.Username,
		ClientId: req.ClientId,
		Topic:    req.Topic,
		Action:   action,
	})
	if err != nil {
		s.log.Errorf("client %s with username %s try %s topic %s failed %v", req.ClientId, req.Username, req.Action, req.Topic, err)
		return &pb.ACLReply{
			Result: "deny",
		}, nil
	}
	return &pb.ACLReply{
		Result: lo.Ternary(ret.Ok, "allow", "deny"),
	}, nil
}
