package service

import (
	"context"
	"strings"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	pb "gitlab.sensoro.com/skai/skai/api/onemap/v1"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type OnemapService struct {
	pb.UnimplementedOnemapServer
	log *log.Helper
	ou  *biz.OnemapUsecase
	lu  *biz.LiveUsecase
}

func NewOnemapService(logger log.Logger, ou *biz.OnemapUsecase, lu *biz.LiveUsecase) *OnemapService {
	return &OnemapService{log: log.NewHelper(logger), ou: ou, lu: lu}
}

func (s *OnemapService) Statistics(ctx context.Context, req *pb.StatisticsRequest) (*pb.StatisticsReply, error) {
	projectInfo, err := s.ou.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	statData, err := s.ou.Statistics(ctx, &biz.StatQuery{ProjectInfo: *projectInfo, DeviceId: req.DeviceId})
	if err != nil {
		return nil, err
	}
	return &pb.StatisticsReply{
		Message: SuccessMessage,
		Data: &pb.StatisticsReplyStatData{
			DockCount:      statData.DockCount,
			OnlineDock:     statData.OnlineDock,
			PilotCount:     statData.PilotCount,
			OnlinePilot:    statData.OnlinePilot,
			ImageCount:     statData.ImageCount,
			VideoCount:     statData.VideoCount,
			VoyageTimes:    statData.VoyageTimes,
			VoyageMileages: statData.VoyageMileages,
		},
	}, nil
}

func (s *OnemapService) ListDevice(ctx context.Context, req *pb.ListRequest) (*pb.ListReply, error) {
	projectInfo, err := s.ou.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	sort := biz.Sort{Field: lo.ToPtr("status"), Order: biz.ListSortOrderDesc}
	total, devices, err := s.ou.ListDevice(ctx, &biz.DeviceListQuery{
		Page:        1,
		Size:        1000,
		Sort:        sort,
		Search:      req.Search,
		ProjectInfo: *projectInfo,
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(devices, func(d *biz.Device, _ int) *pb.ListReplyDevice {
		propData, _ := structpb.NewStruct(d.PropData.Convert())
		return &pb.ListReplyDevice{
			Id:           d.Id,
			Sn:           d.Sn,
			Type:         d.Type,
			PropData:     propData,
			TenantId:     d.TenantId,
			MerchantId:   d.MerchantId,
			CabinStatus:  d.CabinStatus,
			VoyageTimes:  d.VoyageTimes,
			Model:        d.Model.String(),
			Status:       d.Status.String(),
			Category:     d.Category.String(),
			Name:         d.Deployment.Name,
			LockStatus:   int32(d.LockStatus),
			Lnglat:       d.Deployment.Lnglat,
			Location:     d.Deployment.Location,
			Subdevices:   s.convertSubdevice(d.Subdevices),
			RelatedChIds: d.Deployment.RelatedChIds,
		}
	})
	return &pb.ListReply{
		Message: SuccessMessage,
		Data: &pb.ListReplyListData{
			List:  list,
			Total: total,
		},
	}, nil
}

func (s *OnemapService) GetDevice(ctx context.Context, req *pb.CommonRequest) (*pb.DeviceReply, error) {
	projectInfo, err := s.ou.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.ou.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	if l, ok := lo.Find(device.LiveVideos, func(item *biz.Media) bool {
		if lp := item.GetLivePosition(); (lp == biz.LivePositionDock || lp == biz.LivePositionDockInside) && item.Status == 0 {
			return true
		}
		return false
	}); ok {
		if err = s.lu.StartDockPushLive(ctx, l); err != nil {
			s.log.WithContext(ctx).Errorf("GetDevice.startPushFailed %d detail %+v", device.Id, err)
		}
	}
	controls := device.LockStatus.Scatter()
	propData, _ := structpb.NewStruct(device.PropData.Convert())
	extraData, _ := structpb.NewStruct(device.ExtraData)
	launchData, _ := structpb.NewStruct(device.LaunchData)
	var uppedTime *float64
	if device.UppedTime != nil {
		uppedTime = lo.ToPtr(float64(device.UppedTime.UnixMilli()))
	}
	deviceItem := &pb.DeviceReplyItem{
		PropData:       propData,
		ExtraData:      extraData,
		LaunchData:     launchData,
		UppedTime:      uppedTime,
		Id:             device.Id,
		Sn:             device.Sn,
		Type:           device.Type,
		MerchantId:     device.MerchantId,
		Model:          device.Model.String(),
		Name:           device.Deployment.Name,
		Status:         device.Status.String(),
		Category:       device.Category.String(),
		Lnglat:         device.Deployment.Lnglat,
		Location:       device.Deployment.Location,
		AeroMode:       int32(device.AeroMode),
		FlyControl:     int32(controls[0]),
		HoverControl:   int32(controls[1]),
		LensControl:    int32(controls[2]),
		AeroControl:    int32(controls[3]),
		SpeakerControl: int32(controls[4]),
		VideoStatus:    device.IsVideoed(),
		NetworkStatus:  device.NetworkStatus,
		SignalQuality:  device.SignalQuality,
		FlyerId:        lo.ToPtr(device.FlyerId),
		Subdevices:     s.convertSubdevice(device.Subdevices),
		CreatedTime:    float64(device.CreatedTime.UnixMilli()),
		UpdatedTime:    float64(device.UpdatedTime.UnixMilli()),
		LiveVideos: lo.Map(device.LiveVideos, func(media *biz.Media, _ int) *pb.DeviceReplyLiveVideo {
			lvt, lvts := media.GetVideoType(device.Subdevices)
			return &pb.DeviceReplyLiveVideo{
				Id:         media.Id,
				Key:        media.Key,
				Url:        media.URL,
				Name:       media.Name,
				Status:     media.Status,
				Type:       lvt.String(),
				Index:      media.SubDeviceIndex,
				Position:   media.GetLivePosition(),
				Clarity:    media.GetLiveClarity(),
				Switchable: lo.Map(lvts, func(it biz.VideoType, _ int) string { return it.String() }),
			}
		}),
		RelatedChIds: device.Deployment.RelatedChIds,
	}
	if device.AirlineId != nil && *device.AirlineId > 0 {
		deviceItem.AirlineId = device.AirlineId
	}
	if device.MissionId != nil && *device.MissionId > 0 {
		deviceItem.MissionId = device.MissionId
	}
	if device.Speaker != nil && device.Speaker.Sn != "" {
		deviceItem.Speaker = s.convertSpeaker(device.Speaker)
	}
	if device.AvatarId > 0 {
		deviceItem.AvatarId = lo.ToPtr(device.AvatarId)
		if avatar, err := s.ou.GetAvatar(ctx, device.AvatarId); err == nil {
			deviceItem.Avatar = &pb.DeviceReplyBriefAvatar{
				Id:       avatar.Id,
				Mobile:   avatar.Mobile,
				Avatar:   avatar.Avatar,
				Nickname: avatar.Nickname,
			}
		}
	}
	if camera := device.GetCamera(device.GetGimbal()); camera != nil {
		// deviceItem.VideoStatus = camera.IsVideoed()
		gimbal := &pb.DeviceReplyBriefGimbal{}
		copier.Copy(gimbal, camera)
		deviceItem.Gimbal = gimbal
	}
	if device.LastVoyage != nil {
		deviceItem.LastVoyage = &pb.DeviceReplyBriefVoyage{
			Id:        device.LastVoyage.Id,
			Sn:        device.LastVoyage.Sn,
			DeviceId:  device.LastVoyage.DeviceId,
			AirlineId: device.LastVoyage.AirlineId,
			IsFlown:   device.LastVoyage.IsFlown,
			Runtime:   device.LastVoyage.Runtime,
			Mileage:   device.LastVoyage.Mileage,
			Status:    device.LastVoyage.Status,
			EndTime:   float64(device.LastVoyage.EndTime.UnixMilli()),
			StartTime: float64(device.LastVoyage.StartTime.UnixMilli()),
		}
	}
	return &pb.DeviceReply{Message: SuccessMessage, Data: deviceItem}, nil
}

func (s *OnemapService) ListJoint(ctx context.Context, req *pb.JointRequest) (*pb.ListReply, error) {
	projectInfo, err := s.ou.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	if req.Lnglat == "" {
		return nil, errors.BadRequest("40000002", "经纬度参数不能为空")
	}
	lnglat := strings.Split(req.Lnglat, ",")
	if len(lnglat) != 2 {
		return nil, errors.BadRequest("40000002", "经纬度参数格式错误")
	}
	devices, err := s.ou.JointDevice(ctx, &biz.DeviceJointQuery{
		ProjectInfo: *projectInfo,
		Category:    lo.ToPtr(biz.CategoryDock),
		Status:      lo.ToPtr(biz.StatusStandby),
		Lnglat: lo.Map(lnglat, func(item string, _ int) float64 {
			return conv.Atof(item)
		}),
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(devices, func(d *biz.Device, _ int) *pb.ListReplyDevice {
		propData, _ := structpb.NewStruct(d.PropData.Convert())
		return &pb.ListReplyDevice{
			Id:           d.Id,
			Sn:           d.Sn,
			Type:         d.Type,
			PropData:     propData,
			TenantId:     d.TenantId,
			MerchantId:   d.MerchantId,
			CabinStatus:  d.CabinStatus,
			VoyageTimes:  d.VoyageTimes,
			Model:        d.Model.String(),
			Status:       d.Status.String(),
			Category:     d.Category.String(),
			Name:         d.Deployment.Name,
			LockStatus:   int32(d.LockStatus),
			Lnglat:       d.Deployment.Lnglat,
			Location:     d.Deployment.Location,
			Subdevices:   s.convertSubdevice(d.Subdevices),
			RelatedChIds: d.Deployment.RelatedChIds,
		}
	})
	return &pb.ListReply{
		Message: SuccessMessage,
		Data: &pb.ListReplyListData{
			List:  list,
			Total: int32(len(list)),
		},
	}, nil
}

func (s *OnemapService) convertSpeaker(speaker *biz.SpeakerWidget) *pb.DeviceReplyBriefSpeaker {
	brief := &pb.DeviceReplyBriefSpeaker{}
	copier.Copy(brief, speaker)
	return brief
}

func (s *OnemapService) convertSubdevice(subdevices []*biz.DockSubdevice) []*structpb.Struct {
	return lo.Map(subdevices, func(device *biz.DockSubdevice, _ int) *structpb.Struct {
		data, _ := structpb.NewStruct(map[string]interface{}{
			"sn":     device.Sn,
			"type":   device.Type,
			"index":  device.Index,
			"extra":  device.Extra,
			"domain": int(device.Domain),
			"status": device.Status,
		})
		return data
	})
}
