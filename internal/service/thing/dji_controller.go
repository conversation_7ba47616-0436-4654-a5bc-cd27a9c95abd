package thing

import (
	"fmt"
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type DJIRemoteControllerOSDData struct {
	// 剩余电量
	CapacityPercent           int32                   `json:"capacity_percent"`
	Latitude                  float64                 `json:"latitude"`
	Longitude                 float64                 `json:"longitude"`
	LiveStatus                DJIControllerLiveStatus `json:"live_status"`
	TransmissionSignalQuality int32                   `json:"transmission_signal_quality"`
	WirelessLinkState         struct {
		FrequencyBand   float64 `json:"frequency_band"`
		UpwardQuality   int32   `json:"upward_quality"`
		DownloadQuality int32   `json:"download_quality"`
	} `json:"wireless_link_state"`
	WirelessLink *DJIWirelessLink `json:"wireless_link"`
}

type DJIControllerLiveStatus struct {
	LiveTime      float64 `json:"live_time"`
	LiveTrendline float64 `json:"live_trendline"`
	VideoId       string  `json:"video_id"`
	VideoQuality  int32   `json:"video_quality"`
}

type DJIRemoteControllerStateData struct {
	FirmwareVersion string           `json:"firmwareVersion,omitempty"`
	LiveCapacity    *DJILiveCapacity `json:"live_capacity,omitempty"`
	// 网关当前整体直播状态推送
	LiveStatus []DJILiveStatus `json:"live_status,omitempty"`
}

type DJIRemoteControllerHMSData struct {
	Event  string `json:"event"`
	Method string `json:"method"`
	Output struct {
		Codes []struct {
			ComponentIndex int32 `json:"component_index"`
			Id             int64 `json:"id"`
			Level          int32 `json:"level"`
			SensorIndex    int32 `json:"sensor_index"`
		} `json:"codes"`
	} `json:"output"`
}

type DJIRemoteControllerOSDMessage struct {
	DJIMessage
	Data *DJIRemoteControllerOSDData `json:"data"`
}

func (m *DJIRemoteControllerOSDMessage) ToBiz(dev *biz.Device) *biz.RemoteControllerProperties {
	p := &biz.RemoteControllerProperties{
		Id:        m.Tid,
		RxTime:    time.Now(),
		Timestamp: time.UnixMilli(m.Timestamp),
		DeviceId:  dev.Id,
		Sn:        dev.Sn,
	}
	if m.Data != nil {
		data := m.Data
		p.State = &biz.RemoteControllerState{
			CapacityPercent: data.CapacityPercent,
			Longitude:       data.Longitude,
			Latitude:        data.Latitude,
		}
		if data.WirelessLink != nil {
			p.WirelessLinkState = data.WirelessLink.ToBiz()
		}
	}
	return p
}

type DJIRemoteControllerStateMessage struct {
	DJIMessage
	Data *DJIRemoteControllerStateData `json:"data"`
}

func (m *DJIRemoteControllerStateMessage) ToBiz(dev *biz.Device) *biz.RemoteControllerProperties {
	p := &biz.RemoteControllerProperties{
		Id:        m.Tid,
		RxTime:    time.Now(),
		Timestamp: time.UnixMilli(m.Timestamp),
		DeviceId:  dev.Id,
		Sn:        dev.Sn,
	}
	if data := m.Data; data != nil {
		if data.FirmwareVersion != "" {
			p.VersionState = &biz.VersionState{Firmware: data.FirmwareVersion}
		}
	}
	return p
}

type DJIRemoteControllerHMSEventMessage struct {
	DJIMessage
	Data *DJIRemoteControllerHMSData `json:"data"`
}

func (m *DJIRemoteControllerHMSEventMessage) ToBizs(dev *biz.Device) []*biz.DockHealMonitorEvent {
	if m.Data == nil {
		return nil
	}
	l := len(m.Data.Output.Codes)
	if l == 0 {
		return nil
	}
	now := time.Now()
	events := make([]*biz.DockHealMonitorEvent, l)
	for i, code := range m.Data.Output.Codes {
		events[i] = &biz.DockHealMonitorEvent{
			ThingEvent: biz.ThingEvent{
				Id:           m.Bid,
				DeviceId:     dev.Id,
				Sn:           dev.Sn,
				OccurredTime: time.UnixMilli(m.Timestamp),
				// td 相同时间覆盖问题
				RxTime: now.Add(1 * time.Millisecond),
				Type:   biz.ThingModelEventTypeHMS,
			},
			Level:  code.Level,
			Module: 3,
			// %#x 会使0x大写不能使用
			Code:   fmt.Sprintf("0x%X", code.Id),
			Source: 1,
			Extra: map[string]any{"args": map[string]any{
				"component_index": code.ComponentIndex,
				"sensor_index":    code.SensorIndex,
			}},
		}
	}
	return events
}
