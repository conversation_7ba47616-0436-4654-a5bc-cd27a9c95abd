package thing

import (
	"encoding/json"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type DJIDockOsdMessageData struct {
	// 网关设备的经度
	Longitude float64 `json:"longitude" validate:"min=0,max=180"`
	// 网关设备的纬度
	Latitude float64 `json:"latitude" validate:"min=0,max=180"`
	// 机场状态	 {"0":"空闲中","1":"现场调试","2":"远程调试","3":"固件升级中","4":"作业中"}
	ModeCode int32 `json:"mode_code" validate:"max=4"`
	// 机场任务状态	 {"0":"作业准备中","1":"飞行作业中","2":"作业后状态恢复","3":"自定义飞行区更新中","4":"地形障碍物更新中","5":"任务空闲","256":"未知状态"}
	FlightTaskStepCode int32 `json:"flight_task_step_code"`
	// 子设备状态
	SubDevice *struct {
		DeviceOnlineStatus int32  `json:"device_online_status"`
		DevicePaired       int32  `json:"device_paired"`
		DeviceSn           string `json:"device_sn"`
		DeviceModelKey     string `json:"device_model_key"`
	} `json:"sub_device"`
	// 舱盖状态	 {"0":"关闭","1":"打开","2":"半开","3":"舱盖状态异常"}
	CoverState int32 `json:"cover_state" validate:"max=3"`
	// 推杆状态	 {"0":"关闭","1":"打开","2":"半开","3":"推杆状态异常"}
	PutterState int32 `json:"putter_state" validate:"max=3"`
	// 补光灯状态	 {"0":"关闭","1":"打开"}
	SupplementLightState int32 `json:"supplement_light_state" validate:"max=1"`
	// 飞机是否在舱	 {"0":"舱外","1":"舱内"}
	DroneInDock int32 `json:"drone_in_dock" valodate:"max=1"`

	NetworkState DJINetworkState `json:"network_state"`
	// 机场累计作业次数
	JobNumber int32 `json:"job_number"`
	// 机场累计运行时长	 秒
	AccTime         int64 `json:"acc_time"`
	MediaFileDetail struct {
		RemainUpload int32 `json:"remain_upload"`
	} `json:"media_file_detail"`
	// 图传质量（废弃）
	Sdr          interface{}      `json:"sdr,omitempty"`
	WirelessLink *DJIWirelessLink `json:"wireless_link"`
	// 降雨量 {"0":"无雨","1":"小雨","2":"中雨","3":"大雨"}
	Rainfall int32 `json:"rainfall"`
	// 风速 米每秒
	WindSpeed float32 `json:"wind_speed"`
	// 环境温度	摄氏度
	EnvironmentTemperature float32 `json:"environment_temperature"`
	// 环境湿度	%RH
	EnvironmentHumidity float32 `json:"environment_humidity"`
	// 舱内温度	 摄氏度
	Temperature float32 `json:"temperature"`
	// 舱内湿度
	Humidity float32 `json:"humidity"`
	// 备用电池电压 单位毫伏
	// BackupBatteryVoltage int32 `json:"backup_battery_voltage"`
	// 市电电压 单位毫伏
	ElectricSupplyVoltage int32 `json:"electric_supply_voltage"`
	// 工作电压 毫伏
	WorkingVoltage int32 `json:"working_voltage"`
	// 工作电流 毫安
	WorkingCurrent int32 `json:"working_current"`
	// 存储容量
	Storage DJIStorage `json:"storage"`
	// 首次上电时间，毫秒时间戳
	FirstPowerOn int64 `json:"first_power_on"`
	// 激活时间，秒级时间戳
	ActivationTime int64 `json:"activation_time"`
	// 机场空调工作状态信息
	AirConditioner struct {
		// {"0":"空闲模式（无制冷、制热、除湿等）","1":"制冷模式","2":"制热模式","3":"除湿模式","4":"制冷退出模式","5":"制热退出模式","6":"除湿退出模式","7":"制冷准备模式","8":"制热准备模式","9":"除湿准备模式"}
		State int32 `json:"air_conditioner_state"`
		// 剩余需要等待的可切换时间	秒
		SwitchTime int32 `json:"switch_time"`
	} `json:"air_conditioner"`
	// 电池保养(存储)模式	{"1":"电池计划存储策略","2":"电池应急存储策略"}
	BatteryStoreMode int32 `json:"battery_store_mode"`
	// {"0":"声光报警关闭","1":"声光报警开启"}
	AlarmState                  int32                          `json:"alarm_state"`
	DroneBatteryMaintenanceInfo DJIDroneBatteryMaintenanceInfo `json:"drone_battery_maintenance_info"`
	BackupBattery               DJIDockBackupBattery           `json:"backup_battery"`
	DroneChargeState            DJIDroneChargeState            `json:"drone_charge_state"`
	// 紧急停止按钮状态 {"0":"关闭","1":"开启"}
	EmergencyStopState int32            `json:"emergency_stop_state"`
	PositionState      DJIPositionState `json:"position_state"`
	MaintainStatus     struct {
		Statuses []DJIMaintainStatus `json:"maintain_status_array"`
	} `json:"maintain_status"`
	// 椭球高度 = 网关设备的海拔  米
	Height float32 `json:"height"`
	// 备降点
	AlternateLandPoint *DJILandPoint `json:"alternate_land_point"`
}

type DJIDockStateMessageData struct {
	FirmwareVersion string `json:"firmware_version"`
	// 网关当前整体直播状态推送
	LiveStatus []DJILiveStatus `json:"live_status"`
	// 网关直播能力
	LiveCapacity *DJILiveCapacity `json:"live_capacity"`
	// 固件一致性	{"0":"一致","1":"不一致"}
	CompatibleStatus *int `json:"compatible_status"`
	// 机场的航线解析库版本号
	WpmzVersion string `json:"wpmz_version"`
}

type DJIDockDeviceTopoData struct {
	// 网关设备的物模型版本
	Version int32       `json:"version"`
	Domain  json.Number `json:"domain"`
	// 网关设备的产品类型 https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html
	Type int32 `json:"type"`
	// 网关子设备的产品子类型
	SubType      int32               `json:"sub_type"`
	DeviceSecret string              `json:"device_secret"`
	Nonce        string              `json:"nonce"`
	SubDevices   []*DJIDockSubDevice `json:"sub_devices"`
}

type DJIDockSubDevice struct {
	// 子设备的物模型版本
	Version int32  `json:"version"`
	Sn      string `json:"sn"`
	// 0 飞机， 1负载，2 遥控器，3 机场
	Domain json.Number `json:"domain"`
	// 子设备的产品类型
	Type int32 `json:"type"`
	// 子设备的产品子类型
	SubType      int32  `json:"sub_type"`
	Index        string `json:"index"`
	DeviceSecret string `json:"device_secret"`
	Nonce        string `json:"nonce"`
}

// 设备返航退出状态  method=device_exit_homing_notify
type DJIDockFlightTaskExitHomingState struct {
	// [{"1":"进入 返航退出状态"},{"0":"退出 返航退出状态"}]	进入“返航退出状态”，指当机场处于返航模式时，由于 reason 字段中展示的某个原因，退出了返航过程。相似的，退出“返航退出状态”，指机场停止了退出返航这一过程。
	Action int32 `json:"action"`
	// {"0":"操纵杆油门添加","1":"操纵杆间距添加","2":"行为树初始化失败","3":"被障碍物包围","4":"触发限飞限制","5":"障碍物距离太近","6":"无 GPS 信号","7":"GPS 和 VIO 位置输出标志为 false","8":"GPS 和 VIO 融合位置误差太大","9":"短距离回溯","10":"近距离触发返航"}
	Reason int32  `json:"reason"`
	Sn     string `json:"sn"`
}

// DJIDockFlightTaskProgress 航线任务进度
type DJIDockFlightTaskProgress struct {
	Ext struct {
		// 当前执行到的航点数
		CurrentWaypointIndex int32 `json:"current_waypoint_index"`
		// 航线任务状态
		WaylineMissionState int32 `json:"wayline_mission_state"`
		// 本次航线任务执行产生的媒体文件数量
		MediaCount int32 `json:"media_count"`
		// 航迹 ID
		TrackId string `json:"trackId"`
		// 任务 ID
		FlightId   string `json:"flight_id"`
		BreakPoint struct {
			// 断点序号
			Index int32 `json:"index"`
			State int32 `json:"state"`
			// 中断原因
			BreakReason int32   `json:"break_reason"`
			Latitude    float64 `json:"latitude"`
			Longitude   float64 `json:"longitude"`
			Height      float64 `json:"height"`
		} `json:"break_point"`
	} `json:"ext"`
	Progress struct {
		// {"0-4":"MissionCenter 启动, 检查及恢复","5-18":"等待状态","19, 20":"任务执行","21-29":"返航","30-39":"日志拉取","其他":"交互完成"}
		CurrentStep int32 `json:"current_step"`
		// 进度值	 0~100
		Percent int32 `json:"percent"`
	}
	// {"partially_done":"部分完成","sent":"已下发","in_progress":"执行中","ok":"执行成功","paused":"暂停","rejected":"拒绝","failed":"失败","canceled":"取消或终止","timeout":"超时"}
	Status string `json:"status"`
}

var djiDockFlightTaskProgressStatusMap = []string{
	"partially_done", "sent", "in_progress", "ok", "paused", "rejected", "failed", "canceled", "timeout",
}

type DJIDockFlightTaskResourceRequest struct {
	FlightId string `json:"flight_id" validate:"required"`
}

// DJIDockFlightTask 航线文件
type DJIFlightTaskFile struct {
	URL string `json:"url"`
	// 文件内容 MD5 签名 TODO hex or base64
	Fingerprint string `json:"fingerprint"`
}

// DJIDockFlightTask 是否在模拟器中执行任务
type SimulateMission struct {
	IsEnable  int     `json:"is_enable"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

type DJIFlightTaskReadyConditions struct {
	// TODO
}

// DJIFlightTaskExecutableConditions 任务执行条件, 可选。用于在设备端增加任务执行的前置检查条件，存在任一条件不满足时会执行失败
type DJIFlightTaskExecutableConditions struct {
	// 可执行任务的机场或无人机最低存储容量，单位MB，机场或无人机存储容量不满足 storage_capacity 时，任务执行失败
	StorageCapacity int32 `json:"storage_capacity"`
}

type DJIDockRebootProgress struct {
	// 进度百分比
	Percent int32 `json:"percent"`
	// {"get_bid":"获取 bid","check_work_mode":"检查是否进入远程调试模式","check_task_state":"检查机场是否空闲","land_mcu_reboot":"停机坪 MCU 重启","rain_mcu_reboot":"气象站 MCU 重启","core_mcu_reboot":"中心控制 MCU 重启","sdr_reboot":"SDR 重启","write_reboot_param_file":"写重启标志位","get_drone_power_state":"获取电池充电状态","close_putter":"合拢推杆","check_wired_connect_state":"获取飞机状态","open_drone":"打开飞机","open_alarm":"打开声光报警","check_scram_state":"检查急停开关是否按下","open_cover":"打开舱盖","check_drone_sdr_connect_state":"建立 SDR 无线连接","turn_on_drone":"打开飞机","drone_paddle_forward":"开启正转桨","close_cover":"关闭舱盖","drone_paddle_reverse":"开启反转桨","drone_paddle_stop":"停止转桨","free_putter":"展开推杆","stop_charge":"停止充电"}
	StepKey string `json:"step_key"`
	// 非 0 代表错误
	StepResult int32 `json:"step_result"`
}

func NewDockServiceIdentifier(method string) biz.DockServiceIdentifier {
	switch method {
	case DJIMethodFlightTaskPrepare, DJIMethodFlightTaskExecute:
		return biz.DockServiceIdentifierTakeoff
	case DJIMethodDockReboot:
		return biz.DockServiceIdentifierReboot
	case DJIMethodReturnHome:
		return biz.DockServiceIdentifierReturn
	case DJIMethodGrabFlightControl:
		return biz.DockServiceIdentifierControlAero
	case DJIMethodDRCMode:
		return biz.DockServiceIdentifierEnterDRCMode
	case DJIMethodGrabPayloadControl:
		return biz.DockServiceIdentifierControlLens
	case DJIMethodFlyToPoint:
		return biz.DockServiceIdentifierFlytoPoint
	case DJIMethodStopFlyToPoint:
		return biz.DockServiceIdentifierFlyoffPoint
	case DJIMethodLookAtPoint:
		return biz.DockServiceIdentifierLookatPoint
	case DJIMethodExitDRCMode:
		return biz.DockServiceIdentifierExitDRCMode
	case DJIMethodSwitchCameraMode:
		return biz.DockServiceIdentifierSwitchCMode
	case DJIMethodTakePhoto:
		return biz.DockServiceIdentifierTakePicture
	case DJIMethodStartRecording:
		return biz.DockServiceIdentifierStartVideo
	case DJIMethodStopRecording:
		return biz.DockServiceIdentifierStopVideo
	case DJIMethodSetFocalLength:
		return biz.DockServiceIdentifierSetFocalLength
	case DJIMethodSetLiveQuality:
		return biz.DockServiceIdentifierSwitchClarity
	case DJIMethodResetGimbal:
		return biz.DockServiceIdentifierResetGimbal
	case DJIMethodFlightTaskPause:
		return biz.DockServiceIdentifierPauseAirline
	case DJIMethodSetLiveLen:
		return biz.DockServiceIdentifierSwitchLens
	case DJIMethodFlightTaskRecover:
		return biz.DockServiceIdentifierBackAirline
	case DJIMethodSpeakerPlayTTS, DJIMethodSpeakerPlayAudio:
		return biz.DockServiceIdentifierStartSpeaker
	case DJIMethodSpeakerReplay:
		return biz.DockServiceIdentifierReplaySpeaker
	case DJIMethodSpeakerStop:
		return biz.DockServiceIdentifierStopSpeaker
	case DJIMethodSpeakerModeSet:
		return biz.DockServiceIdentifierSetSpeakerPlayMode
	case DJIMethodSpeakerVolumeSet:
		return biz.DockServiceIdentifierSetSpeakerVolume
	case DJIMethodTakeOffToPoint:
		return biz.DockServiceIdentifierLaunch
	case DJIMethodUpdateFlyToPoint:
		return biz.DockServiceIdentifierUpdatePoint
	case DJIMethodEnterPOIMode:
		return biz.DockServiceIdentifierEnterPOIMode
	case DJIMethodSetPOICircleSpeed:
		return biz.DockServiceIdentifierUpdatePOISpeed
	case DJIMethodExitPOIMode:
		return biz.DockServiceIdentifierExitPOIMode
	case DJIMethodCancelReturnHome:
		return biz.DockServiceIdentifierCancelReturn
	case DJIMethodDebugMode:
		return biz.DockServiceIdentifierOpenDebugMode
	case DJIMethodDebugModeClose:
		return biz.DockServiceIdentifierCloseDebugMode
	case DJIMethodOpenDockCover:
		return biz.DockServiceIdentifierOpenCover
	case DJIMethodCloseDockCover:
		return biz.DockServiceIdentifierCloseCover
	case DJIMethodOpenDrone:
		return biz.DockServiceIdentifierOpenDrone
	case DJIMethodCloseDrone:
		return biz.DockServiceIdentifierCloseDrone
	case DJIMethodClosePutter:
		return biz.DockServiceIdentifierClosePutter
	case DJIMethodOpenPutter:
		return biz.DockServiceIdentifierOpenPutter
	case DJIMethodForceCloseDockCover:
		return biz.DockServiceIdentifierForceCloseCover
	case DJIMethodLogUpload:
		return biz.DockServiceIdentifierStartFileupload
	case DJIMethodQueryLogs:
		return biz.DockServiceIdentifierListFileupload
	case DJIMethodChangeLiveCamera:
		return biz.DockServiceIdentifierChangeLiveCamera
	case DJIMethodOpenCharge:
		return biz.DockServiceIdentifierOpenCharge
	case DJIMethodCloseCharge:
		return biz.DockServiceIdentifierCloseCharge
	default:
		return biz.DockServiceIdentifierUnknown
	}
}
