package thing

import (
	"strconv"
	"strings"
	"time"

	"github.com/asmarques/geodist"
	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/pkg/types"
	"golang.org/x/exp/slices"
)

// DJIDockOsdMessage 机库属性消息
type DJIDockOsdMessage struct {
	DJIMessage
	Data *DJIDockOsdMessageData `json:"data"`
}

func (m *DJIDockOsdMessage) ToBiz(dev *biz.Device, dataKeys types.HashSet[string]) *biz.DockProperties {
	bm := &biz.DockProperties{
		Id:        m.Bid,
		Timestamp: time.UnixMilli(m.Timestamp),
		RxTime:    time.Now(),
	}
	if dev != nil {
		bm.DeviceId = dev.Id
		bm.Sn = dev.Sn
	}
	data := m.Data
	if dataKeys.Contains("mode_code") {
		bm.State = &biz.DockState{
			Mode: m.Data.ModeCode,
		}
		if dataKeys.Contains("longitude") && dataKeys.Contains("latitude") {
			bm.State.Latitude = data.Latitude
			bm.State.Longitude = data.Longitude
		}
		bm.State.CoverState = data.CoverState
		bm.State.PutterState = data.PutterState
		if m.Data.SubDevice != nil {
			bm.State.DroneOnlineState = data.SubDevice.DeviceOnlineStatus
		}
		bm.State.SupplementLightState = data.SupplementLightState
		bm.State.Temperature = data.Temperature
		bm.State.Humidity = data.Humidity
		bm.State.Height = data.Height
		if data.Storage.Total > 0 {
			bm.State.StorageTotal = data.Storage.Total
			bm.State.StorageUsed = data.Storage.Used
		}
		if dataKeys.Contains("air_conditioner") {
			bm.State.AirConditionerState = data.AirConditioner.State
		}
	}
	if dataKeys.Contains("position_state") {
		bm.PositionState = &biz.PositionState{
			IsFixed:       data.PositionState.IsFixed,
			IsCalibration: data.PositionState.IsCalibration,
			Quality:       data.PositionState.Quality,
			GpsNumber:     data.PositionState.GpsNumber,
			RtkNumber:     data.PositionState.RtkNumber,
		}
	}
	if dataKeys.Contains("network_state") {
		bm.NetworkState = &biz.NetworkState{
			Type:    data.NetworkState.Type,
			Quality: data.NetworkState.Quality,
		}
	}
	if dataKeys.Contains("drone_in_dock") {
		bm.FlightTaskState = &biz.FlightTaskState{
			DroneInDock: data.DroneInDock,
			// Count:       data.JobNumber,
		}
		if data.AlternateLandPoint != nil {
			bm.FlightTaskState.Longitude = data.AlternateLandPoint.Longitude
			bm.FlightTaskState.Latitude = data.AlternateLandPoint.Latitude
			bm.FlightTaskState.SafeLandHeight = data.AlternateLandPoint.SafeLandHeight
			bm.FlightTaskState.SafeLandConfigured = data.AlternateLandPoint.IsConfigured
		}
	}
	if dataKeys.Contains("wind_speed") {
		bm.EnvironmentState = &biz.EnvironmentState{
			Rainfall:    data.Rainfall,
			WindSpeed:   data.WindSpeed,
			Temperature: data.EnvironmentTemperature,
			// 大疆移除了环境湿度，使用舱内湿度
			Humidity: data.Humidity,
		}
	}
	if wl := data.WirelessLink; wl != nil {
		bm.WirelessLinkState = wl.ToBiz()
	}
	if dataKeys.Contains("drone_charge_state") {
		bm.BatteryChargeState = &biz.BatteryChargeState{
			DroneBatteryChargeState: data.DroneChargeState.State,
			DroneBatteryPercent:     data.DroneChargeState.CapacityPercent,
		}
	}
	if dataKeys.Contains("backup_battery") {
		bm.ElecPowerState = &biz.ElecPowerState{
			DockBackupBatterySwitch:  data.BackupBattery.Switch,
			DockBackupBatteryVoltage: data.BackupBattery.Voltage,
			DockBackupTemperature:    data.BackupBattery.Temperature,
			SupplyVoltage:            data.ElectricSupplyVoltage,
			WorkingVoltage:           data.WorkingVoltage,
			WorkingCurrent:           data.WorkingCurrent,
		}
	}

	return bm
}

type DJIDockStateMessage struct {
	DJIMessage
	Data *DJIDockStateMessageData `json:"data"`
}

func (m *DJIDockStateMessage) ToBiz(dev *biz.Device) *biz.DockProperties {
	bm := &biz.DockProperties{
		Id:        m.Bid,
		Sn:        dev.Sn,
		DeviceId:  dev.Id,
		RxTime:    time.Now(),
		Timestamp: time.UnixMilli(m.Timestamp),
		Other:     make(map[string]interface{}),
	}
	if m.Data.FirmwareVersion != "" {
		bm.VersionState = &biz.VersionState{Firmware: m.Data.FirmwareVersion}
	}
	if m.Data.WpmzVersion != "" {
		bm.Other["wpmz"] = m.Data.WpmzVersion
	}
	return bm
}

// DJIGatewayTopoMessage method=update_topo
type DJIGatewayTopoMessage struct {
	DJIMessage
	Data *DJIDockDeviceTopoData `json:"data"`
}

func (m *DJIGatewayTopoMessage) ToBiz(dev *biz.Device) *biz.DockTopoUpdateEvent {
	event := &biz.DockTopoUpdateEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeUpdateTopo,
		},
		DeviceModel: dev.Model.String(),
		Subdevices: lo.FilterMap(m.Data.SubDevices, func(d *DJIDockSubDevice, _ int) (*biz.DockSubdevice, bool) {
			domain, err := d.Domain.Int64()
			if err != nil {
				return &biz.DockSubdevice{}, false
			}
			if domain == 3 {
				return &biz.DockSubdevice{}, false
			}
			return &biz.DockSubdevice{
				Sn:     d.Sn,
				Type:   NewTypeForDJIDockSub(int32(domain), d.Type, d.SubType),
				Domain: biz.SubDeviceDomain(int32(domain)),
				Index:  d.Index,
				Extra: map[string]interface{}{
					"secret": d.DeviceSecret,
					"nonce":  d.Nonce,
				},
			}, true
		}),
	}
	return event
}

// DJIDockHMSEventMessage 健康告警 Method == hms
type DJIDockHMSEventMessage struct {
	DJIEventMessage
	Data struct {
		List []DJIHMSEvent `json:"list"`
	} `json:"data"`
}

func (m *DJIDockHMSEventMessage) ToBizs(dev *biz.Device) []*biz.DockHealMonitorEvent {
	return lo.Map(m.Data.List, func(it DJIHMSEvent, _ int) *biz.DockHealMonitorEvent {
		var source int32
		if !strings.HasPrefix(it.DeviceType, "3-") {
			source = 1
		}
		now := time.Now()
		event := &biz.DockHealMonitorEvent{
			ThingEvent: biz.ThingEvent{
				Id:           m.Bid,
				DeviceId:     dev.Id,
				Sn:           dev.Sn,
				OccurredTime: time.UnixMilli(m.Timestamp),
				// td 相同时间覆盖问题
				RxTime: now.Add(1 * time.Millisecond),
				Type:   biz.ThingModelEventTypeHMS,
			},
			Level:    it.Level,
			Module:   it.Module,
			InTheSky: it.InTheSky,
			Code:     it.Code,
			Source:   source,
			Extra:    map[string]interface{}{"args": it.Args},
		}
		return event
	})
}

// DJIDockExitHomingStateMessage 设备返航退出状态通知 method=device_exit_homing_notify
type DJIDockExitHomingStateMessage struct {
	DJIEventMessage
	Data *DJIDockFlightTaskExitHomingState `json:"data"`
}

// DJIDockFlightTaskProgressMessage 航线任务进度上报 method=flighttask_progress
type DJIDockFlightTaskProgressMessage struct {
	DJIEventMessage
	Data struct {
		Output *DJIDockFlightTaskProgress `json:"output"`
		Result int                        `json:"result"`
	} `json:"data"`
}

func (m *DJIDockFlightTaskProgressMessage) ToBiz(dev *biz.Device) *biz.AutoFlightTaskProgressEvent {
	event := &biz.AutoFlightTaskProgressEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeFlightTaskProgress,
		},
	}
	data := m.Data
	if data.Result != 0 {
		event.Extra = map[string]any{
			"errCode": data.Result,
		}
		//	event.Extra["errCode"] = data.Output
	}
	if detail := data.Output; detail != nil {
		event.FlightId = detail.Ext.FlightId
		event.CurrentWaypointIndex = detail.Ext.CurrentWaypointIndex
		event.Percent = detail.Progress.Percent
		event.Step = detail.Progress.CurrentStep
		event.Status = int32(slices.Index(djiDockFlightTaskProgressStatusMap, detail.Status) + 1)
		event.VoyageState = detail.Ext.WaylineMissionState
		event.BreakReason = detail.Ext.BreakPoint.BreakReason
		if detail.Ext.BreakPoint.BreakReason > 0 {
			if event.Extra == nil {
				event.Extra = map[string]any{
					"breakPoint": []any{detail.Ext.BreakPoint.Longitude, detail.Ext.BreakPoint.Latitude},
				}
			} else {
				event.Extra["breakPoint"] = []any{detail.Ext.BreakPoint.Longitude, detail.Ext.BreakPoint.Latitude}
			}
		}
	}
	return event
}

type DJIDockOrganizationRequest struct {
	DJIMessage
	Data struct {
		Devices []struct {
			Sn string `json:"sn"`
		} `json:"devices"`
	} `json:"data"`
}

func NewDJIDockOrganizationReply(req *DJIDockOrganizationRequest, dev *biz.Device, merchant *biz.Merchant) *DJIReplyMessage {
	status := make([]biz.AnyMap, len(req.Data.Devices))
	for i, d := range req.Data.Devices {
		status[i] = map[string]any{
			"sn":                          d.Sn,
			"is_device_bind_organization": true,
			"organization_id":             strconv.FormatInt(merchant.Id, 36),
			"organization_name":           merchant.Name,
			"device_callsign":             dev.Deployment.Name,
		}
	}
	r := &DJIReplyMessage{
		DJIMessage: req.DJIMessage,
		Data: DJIReplyData{
			Output: map[string]any{
				"bind_status": status,
			},
		},
	}
	r.DJIMessage.Timestamp = time.Now().UnixMilli()
	return r
}

type DJIDockOrganizationGetRequest struct {
	DJIMessage
	Data struct {
		DeviceBindingCode string `json:"device_binding_code"`
		OrganizationId    string `json:"organization_id"`
	} `json:"data"`
}

func NewDJIDockOrganizationInfoReply(req *DJIDockOrganizationGetRequest, merchant *biz.Merchant) *DJIReplyMessage {
	r := &DJIReplyMessage{
		DJIMessage: req.DJIMessage,
		Data: DJIReplyData{
			Output: map[string]any{
				"organization_name": merchant.Name,
			},
		},
	}
	r.DJIMessage.Timestamp = time.Now().UnixMilli()
	return r
}

type DJIDockOrganizationBindRequest struct {
	DJIMessage
	Data struct {
		BindDevices []struct {
			DeviceBindingCode string `json:"device_binding_code"`
			OrganizationId    string `json:"organization_id"`
			DeviceCallsign    string `json:"device_callsign"`
			Sn                string `json:"sn"`
			DeviceModelKey    string `json:"device_model_key"`
		} `json:"bind_devices"`
	} `json:"data"`
}

func DJIDockOrganizationBindReply(req *DJIDockOrganizationBindRequest, merchant *biz.Merchant) *DJIReplyMessage {
	bindRets := make([]map[string]any, len(req.Data.BindDevices))
	for i, d := range req.Data.BindDevices {
		bindRets[i] = map[string]any{
			"sn":       d.Sn,
			"err_code": 0,
		}
	}
	r := &DJIReplyMessage{
		DJIMessage: req.DJIMessage,
		Data: DJIReplyData{
			Output: map[string]any{
				"err_infos": merchant.Name,
			},
		},
	}
	r.DJIMessage.Timestamp = time.Now().UnixMilli()
	return r
}

// DJIDockFlightTaskResourceRequestMessage 任务资源获取 method=flighttask_resource_get
type DJIDockFlightTaskResourceRequestMessage struct {
	DJIMessage
	Data *DJIDockFlightTaskResourceRequest `json:"data"`
}

func (m *DJIDockFlightTaskResourceRequestMessage) ToBiz(dev *biz.Device) *biz.DockFlightTaskResourceRequestEvent {
	return &biz.DockFlightTaskResourceRequestEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeFlightTaskResourceGet,
		},
		FlightId: m.Data.FlightId,
	}
}

// DJIDockRebootStatusMessage 机库重启进度  method=device_reboot
type DJIDockRebootStatusMessage struct {
	DJIMessage
	Data *struct {
		Result int32 `json:"result"`
		// {"sent":"已下发","in_progress":"执行中","ok":"执行成功","paused":"暂停","rejected":"拒绝","failed":"失败","canceled":"取消或终止","timeout":"超时"}
		Status   string                `json:"status"`
		Progress DJIDockRebootProgress `json:"progress"`
	} `json:"data"`
}

// DJIDockMediaUploadResultMessage 媒体文件上传结果上报 method=file_upload_callback
type DJIDockMediaUploadResultMessage struct {
	DJIEventMessage
	Data struct {
		Result int32 `json:"result"`
		File   *struct {
			ObjectKey string `json:"object_key"`
			Path      string `json:"path"`
			Name      string `json:"name"`
			Ext       struct {
				FlightId        string `json:"flight_id"`
				DroneModelKey   string `json:"drone_model_key"`
				PayloadModelKey string `json:"payload_model_key"`
				IsOriginal      bool   `json:"is_original"`
			} `json:"ext"`
			Metadata struct {
				GimbalYawdegree  float64 `json:"gimbal_yaw_degree"`
				AbsoluteAltitude float64 `json:"absolute_altitude"`
				RelativeAltitude float64 `json:"relative_altitude"`
				CreatedTime      string  `json:"created_time"`
				ShootPosition    struct {
					Lat float64 `json:"lat"`
					Lng float64 `json:"lng"`
				} `json:"shoot_position"`
			} `json:"metadata"`
		} `json:"file"`
		FlightTask struct {
			// 该飞行架次当前已上传媒体数量
			ExpectedFileCount int32 `json:"expected_file_count"`
			// 该飞行架次拍摄媒体总数量
			UploadedFileCount int32 `json:"uploaded_file_count"`
			// {"0":"航线任务","1":"一键起飞任务"}
			FlightType int32 `json:"flight_type"`
		} `json:"flight_task"`
	} `json:"data"`
}

func (m *DJIDockMediaUploadResultMessage) getMediaType() biz.MediaType {
	if m.Data.File == nil {
		return biz.MediaTypeOther
	}
	fileName := strings.Split(m.Data.File.Name, ".")
	if l := len(fileName); l > 1 {
		extName := strings.ToLower(fileName[l-1])
		if lo.Contains([]string{"jpg", "png", "jpeg", "webp", "heif", "avif"}, extName) {
			return biz.MediaTypePhoto
		}
	}
	return biz.MediaTypeVideo
}

func (m *DJIDockMediaUploadResultMessage) getSubDevice(dev *biz.Device) (*biz.DockSubdevice, bool) {
	payloadIndexPrefix := m.Data.File.Ext.PayloadModelKey[strings.Index(m.Data.File.Ext.PayloadModelKey, "-")+1:]
	return lo.Find(dev.Subdevices, func(it *biz.DockSubdevice) bool {
		return it.Domain == biz.SubDeviceDomainCamera && strings.HasPrefix(it.Index, payloadIndexPrefix)
	})
}

func (m *DJIDockMediaUploadResultMessage) ToMedia(dev *biz.Device, voyage *biz.Voyage, ps []*biz.Waypoint) *biz.Media {
	if m.Data.File == nil {
		return nil
	}
	sd, ok := m.getSubDevice(dev)
	if !ok {
		sd = &biz.DockSubdevice{}
	}
	f := m.Data.File
	ts, _ := now.Parse(f.Metadata.CreatedTime)
	name, st := biz.NewFileNameForDJIFileName(f.Name, dev, voyage)
	r := &biz.Media{
		Id:             utilities.MustNextID(),
		Type:           m.getMediaType(),
		SubType:        st,
		DeviceId:       dev.Id,
		SubDeviceIndex: sd.Index,
		VoyageId:       voyage.Id,
		AirlineId:      voyage.AirlineId,
		MerchantId:     dev.MerchantId,
		WaypointId:     -1,
		Meta: &biz.MediaMeta{
			Yaw:            f.Metadata.GimbalYawdegree,
			Altitude:       f.Metadata.AbsoluteAltitude,
			RelativeHeight: f.Metadata.RelativeAltitude,
			IsOriginal:     f.Ext.IsOriginal,
			ShootLnglat:    []float64{f.Metadata.ShootPosition.Lng, f.Metadata.ShootPosition.Lat},
			ShootTime:      ts,
		},
		Key:         f.ObjectKey,
		URL:         f.Path,
		Name:        name,
		CreatedTime: lo.Ternary(ts.IsZero(), time.Now(), ts),
		UpdatedTime: time.Now(),
	}
	dists := make([]float64, len(ps))
	shootPosition := geodist.Point{
		Lat:  f.Metadata.ShootPosition.Lat,
		Long: f.Metadata.ShootPosition.Lng,
	}
	const max = 1 << 30
	for i, p := range ps {
		if len(p.Lnglat) == 2 {
			dists[i] = geodist.HaversineDistance(shootPosition, geodist.Point{Long: p.Lnglat[0], Lat: p.Lnglat[1]})
		} else {
			dists[i] = max
		}
	}
	min := lo.Min(dists)
	if min < 0.02 {
		pIndex := lo.IndexOf(dists, min)
		if pIndex >= 0 {
			r.WaypointId = ps[pIndex].Id
		}
	}
	return r
}

// DJIDockStorageConfigGetRequestMessage 获取上传临时凭证 method=storage_config_get
type DJIDockStorageConfigGetRequestMessage struct {
	DJIMessage
	Data struct {
		Module int32 `json:"module"`
	} `json:"data"`
}

type DJIDockDRCStatusNotifyMessage struct {
	DJIEventMessage
	Data struct {
		Result   int32 `json:"result"`
		DrcState int32 `json:"drc_state"`
	} `json:"data"`
}

func (m *DJIDockDRCStatusNotifyMessage) ToBiz(dev *biz.Device) *biz.DockDRCStatusEvent {
	return &biz.DockDRCStatusEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeDRCState,
		},
		ErrCode: m.Data.Result,
		State:   m.Data.DrcState,
	}
}

type DJIFlyToProgressEvent struct {
	FlyToId           string           `json:"fly_to_id"`
	Status            string           `json:"status"`
	Result            int32            `json:"result"`
	WayPointIndex     int32            `json:"way_point_index"`
	RemainingDistance float32          `json:"remaining_distance"`
	RemainingTime     float32          `json:"remaining_time"`
	PlannedPathPoints []map[string]any `json:"planned_path_points"`
}

type DJITakeoffPointProgressEvent struct {
	DJIFlyToProgressEvent
	FlightId string `json:"flight_id"`
	TrackId  string `json:"track_id"`
}

type DJIFlyToProgressMessage struct {
	DJIEventMessage
	Data DJIFlyToProgressEvent `json:"data"`
}

type DJITakeoffPointProgressMessage struct {
	DJIEventMessage
	Data DJITakeoffPointProgressEvent `json:"data"`
}

func (m *DJIFlyToProgressMessage) ToBiz(dev *biz.Device) *biz.FlytoPointProgressEvent {
	return &biz.FlytoPointProgressEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeFlytoPointProgress,
		},
		Result:               m.Data.Result,
		Status:               m.Data.Status,
		CurrentWaypointIndex: m.Data.WayPointIndex,
		FlytoId:              m.Data.FlyToId,
		RemainingTime:        m.Data.RemainingTime,
		Distance:             m.Data.RemainingDistance,
	}
}

func (m *DJITakeoffPointProgressMessage) ToBiz(dev *biz.Device) *biz.LaunchPointProgressEvent {
	return &biz.LaunchPointProgressEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeLaunchPointProgress,
		},
		Result:               m.Data.Result,
		Status:               m.Data.Status,
		CurrentWaypointIndex: m.Data.WayPointIndex,
		FlightId:             m.Data.FlightId,
		RemainingTime:        m.Data.RemainingTime,
		Distance:             m.Data.RemainingDistance,
	}
}

type DJIPOIStatusEvent struct {
	// {"failed":"执行失败","in_progress":"执行中","ok":"执行成功"}
	Status string `json:"status"`
	// {"0":"正常","1":"未适配负载","2":"不支持该相机模式","3":"非法命令","4":"定位失败","5":"无人机未起飞","6":"飞行模式错误","7":"该模式下不可用（返航、降落、姿态）","8":"丢失遥控器或图传信号"}
	Reason         int32   `json:"reason"`
	CircleRadius   float32 `json:"circle_radius"`
	CircleSpeed    float32 `json:"circle_speed"`
	MaxCircleSpeed float32 `json:"max_circle_speed"`
}

type DJIPOIStatusMessage struct {
	DJIEventMessage
	Data DJIPOIStatusEvent `json:"data"`
}

func (m *DJIPOIStatusMessage) ToBiz(dev *biz.Device) *biz.OrbitPointNotifyEvent {
	return &biz.OrbitPointNotifyEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeOrbitPointNotify,
		},
		Status:   m.Data.Status,
		Code:     m.Data.Reason,
		Radius:   m.Data.CircleRadius,
		Speed:    m.Data.CircleSpeed,
		MaxSpeed: m.Data.MaxCircleSpeed,
	}
}

type DJIJobProgressEvent struct {
	Result int32 `json:"result"`
	Output struct {
		Status   string `json:"status"`
		Progress struct {
			Percent float32 `json:"percent"`
			StepKey string  `json:"step_key"`
		}
	} `json:"output"`
}

type DJIJobProgressMessage struct {
	DJIEventMessage
	Data DJIJobProgressEvent `json:"data"`
}

func (m *DJIJobProgressMessage) ToBiz(dev *biz.Device) *biz.OperationProgressEvent {
	opId, _ := utilities.SEUUIDToInt64(m.Bid)
	be := &biz.OperationProgressEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeOperationProgress,
		},
		OperationId: opId,
		Status:      m.Data.Output.Status,
		ServiceType: NewDockServiceIdentifier(m.Method).String(),
		Progress:    int32(m.Data.Output.Progress.Percent),
		Code:        m.Data.Result,
	}

	if m.Data.Output.Status == "" {
		if m.NeedReply == 1 {
			be.Status = "ok"
			be.Progress = 100
		}
	}
	return be
}

type DJIFileUploadProgressEvent struct {
	Result int32 `json:"result"`
	Output struct {
		Status string `json:"status"`
		Ext    struct {
			Files []*struct {
				DeviceSN    string `json:"device_sn"`
				Fingerprint string `json:"fingerprint"`
				Key         string `json:"key"`
				Module      string `json:"module"`
				Progress    struct {
					CurrentStep int32  `json:"current_step"`
					FinishTime  int64  `json:"finish_time"`
					Progress    int32  `json:"progress"`
					Result      int32  `json:"result"`
					Status      string `json:"status"`
					TotalStep   int32  `json:"total_step"`
					UploadRate  int32  `json:"upload_rate"`
				} `json:"progress"`
				Size int64 `json:"size"`
			} `json:"files"`
		} `json:"ext"`
	} `json:"output"`
}

type DJIFileUploadProgressMessage struct {
	DJIEventMessage
	Data DJIFileUploadProgressEvent `json:"data"`
}

func (m *DJIFileUploadProgressMessage) ToBiz(dev *biz.Device) *biz.FileuploadProgressEvent {
	be := &biz.FileuploadProgressEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeFileuploadProgress,
		},
		Files: make([]*biz.FileuploadProgress, len(m.Data.Output.Ext.Files)),
	}
	for i, f := range m.Data.Output.Ext.Files {
		be.Files[i] = &biz.FileuploadProgress{
			Key:         f.Key,
			Size:        f.Size,
			Status:      lo.Switch[string, int32](f.Progress.Status).Case("ok", 1).Case("failed", 2).Case("timeout", 2).Case("canceled", 2).Default(0),
			Progress:    int32(f.Progress.Progress),
			UploadRate:  int32(f.Progress.UploadRate),
			FinishTime:  f.Progress.FinishTime,
			CurrentStep: f.Progress.CurrentStep,
			TotalStep:   f.Progress.TotalStep,
			Result:      f.Progress.Result,
		}
	}

	return be
}
