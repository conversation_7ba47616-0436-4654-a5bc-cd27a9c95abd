package thing

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/pkg/types"
)

type DJIDroneOsdMessageData struct {
	// 无人机状态 0	{"0":"待机","1":"起飞准备","2":"起飞准备完毕","3":"手动飞行","4":"自动起飞","5":"航线飞行","6":"全景拍照","7":"智能跟随","8":"ADS-B 躲避","9":"自动返航","10":"自动降落","11":"强制降落","12":"三桨叶降落","13":"升级中","14":"未连接"}
	ModeCode int32 `json:"mode_code" validate:"max=14"`
	// {"0":"A","1":"P","2":"NAV","3":"FPV","4":"FARM","5":"S","6":"F","7":"M","8":"G","9":"T"}
	Gear int32 `json:"gear" validate:"max=9"`
	// 水平速度 m/s
	HorizontalSpeed float32 `json:"horizontal_speed"`
	// 垂直速度 m/s
	VerticalSpeed float32 `json:"vertical_speed"`

	Longitude float64 `json:"longitude"`

	Latitude float64 `json:"latitude"`
	// 绝对高度	相对地球椭球面高度
	Height float32 `json:"height"`
	// 相对起飞点高度
	Elevation float32 `json:"elevation"`
	// 俯仰轴角度
	AttitudePitch float32 `json:"attitude_pitch"`
	// 横滚轴角度
	AttitudeRoll float32 `json:"attitude_roll"`
	// 机头朝向角度
	AttitudeHead float32 `json:"attitude_head"`

	// 风速估计，该风速是通过无人机姿态推测出的，有一定的误差，仅供参考，不能作为气象数据使用
	WindSpeed float32 `json:"wind_speed"`
	// {"1":"正北","2":"东北","3":"东","4":"东南","5":"南","6":"西南","7":"西","8":"西北"}
	WindDirection int32 `json:"wind_direction" validate:"max=8"`
	// 累计航行时长 秒
	TotalFlightTime float64 `json:"total_flight_time"`
	// 无人机累计飞行总架次
	TotalFlightSorties int32 `json:"total_flight_sorties"`
	// 无人机累计飞行总里程	 米
	TotalFlightDistance float64 `json:"total_flight_distance"`
	// 无人机激活时间(unix 时间戳)	秒
	ActivationTime int64 `json:"activation_time"`
	// 无人机夜航灯状态	{"0":"关闭","1":"打开"}
	NightLightsState int32 `json:"night_lights_state"`
	// 无人机限高	{"min":"20","max":"1500","unit":"m","unitName":"米","step":"1"}
	HeightLimit int32 `json:"height_limit"`
	// {"0":"未达到设定的限制高度","1":"接近设定的限制高度"}
	IsNearHeightLimit int32 `json:"is_near_height_limit"`
	// {"0":"未达到限飞区","1":"接近限飞区"}
	IsNearAreaLimit int32 `json:"is_near_area_limit"`
	// 无人机限远状态
	DistanceLimitStatus struct {
		// {"0":"未设置","1":"已设置"}
		State int32 `json:"state"`
		// 限远距离	 米
		DistanceLimit int32 `json:"distance_limit" validate:"max=8000"`
		// 是否接近设定的限制距离
		IsNearIDistanceLimit int32 `json:"is_near_distance_limit"`
	} `json:"distance_limit_status"`
	// 无人机避障状态
	ObstacleAvoidance DJIDroneObstacleAvoidance `json:"obstacle_avoidance"`
	Battery           DJIDroneBatteryState      `json:"battery"`
	Storage           DJIStorage                `json:"storage"`
	PositionState     DJIPositionState          `json:"position_state"`
	MaintainStatus    []DJIMaintainStatus       `json:"DJIMaintainStatus"`
	// 返航高度	{"unit":"米","min":20,"max":500}
	RthAltitude int32 `json:"rth_altitude"`
	// 遥控器失控行为	 {"0":"悬停","1":"着陆(降落)","2":"返航"}
	RCLostAction int32 `json:"rc_lost_action"`
	// 航线失控动作	{"0":"继续执行航线任务","1":"退出航线任务，执行遥控器失控行为"}
	ExitWaylineWhenRCLost int32                  `json:"exit_wayline_when_rc_lost"`
	Cameras               []*DJIDroneCameraState `json:"cameras"`
}

type DJIDroneCameraState struct {
	// 相机模式 {"0":"拍照","1":"录像","2":"智能低光","3":"全景拍照","-1":"不支持的模式"}
	CameraMode int32 `json:"camera_mode"`
	// 拍照状态	{"0":"空闲","1":"拍照中"}
	PhotoState int32 `json:"photo_state"`
	// 录像状态	{"0":"空闲","1":"录像中"}
	RecordingState int32 `json:"recording_state"`
	// 负载编号
	PayloadIndex string `json:"payload_index"`
	// 变焦系数
	ZoomFactor float32 `json:"zoom_factor"`
	// 红外变焦倍数
	IRZoomFactor float32 `json:"ir_zoom_factor"`
	// 对焦模式 {"0":"MF","1":"AFS","2":"AFC"}
	ZoomFocusMode int32 `json:"zoom_focus_mode"`
	// 当前对焦值
	ZoomFocusValue int32 `json:"zoom_focus_value"`
	// 对焦状态 {"0":"空闲","1":"对焦中","2":"对焦成功","3":"对焦失败"}
	ZoomFocusState int32 `json:"zoom_focus_state"`
	// 红外测温模式 {"0":"关闭","1":"打开"}
	IRMeteringMode int32 `json:"ir_metering_mode"`
	// 视场角（FOV）在 liveview 中的区域
	LiveViewWorldRegion struct {
		Left   float64 `json:"left"`
		Top    float64 `json:"top"`
		Right  float64 `json:"right"`
		Bottom float64 `json:"bottom"`
	} `json:"liveview_world_region"`
}

type DJIDroneCameraInfo struct {
	// 云台俯仰轴角度
	GimbalPitch float64 `json:"gimbal_pitch"`
	// 云台翻滚轴角度
	GimbalRoll float64 `json:"gimbal_roll"`
	// 云台偏航轴角度
	GimbalYaw float64 `json:"gimbal_yaw"`
	// 激光测距目标经度
	MeasureTargetLongitude float64 `json:"measure_target_longitude"`
	// 激光测距目标纬度
	MeasureTargetLatitude float64 `json:"measure_target_latitude"`
	// 激光测距目标海拔
	MeasureTargetAltitude float64 `json:"measure_target_altitude"`
	// 激光测距目标距离
	MeasureTargetDistance float64 `json:"measure_target_distance"`
	// 激光测距状态	{"0":"NORMAL","1":"TOO_CLOSE","2":"TOO_FAR","3":"NO_SIGNAL"}
	MeasureTargetErrorState int32  `json:"measure_target_error_state"`
	PayloadIndex            string `json:"payload_index"`
	//调色盘样式	 {"0":"白热","1":"黑热","2":"描红","3":"医疗","5":"彩虹 1","6":"铁红","8":"北极","11":"熔岩","12":"热铁","13":"彩虹 2"}
	ThermalCurrentPaletteStyle int32 `json:"thermal_current_palette_style"`
	// 支持的调色盘样式
	ThermalSupportedPaletteStyle []int32 `json:"thermal_supported_palette_styles"`
}

type DJIDronePSDKWidget struct {
	PSDKIndex int    `json:"psdk_index"`
	PSDKName  string `json:"psdk_name"`
	PSDKSn    string `json:"psdk_sn"`
	// 设备固件版本
	PSDKVersion    string `json:"psdk_version"`
	PSDKLibVersion string `json:"psdk_lib_version"`
	Speaker        *struct {
		PlayFileMD5  string `json:"play_file_md5"`
		PlayFileName string `json:"play_file_name"`
		// {"0":"单次播放","1":"循环播放(单曲)"}
		PlayMode    int8 `json:"play_mode"`
		WorkMode    int8 `json:"work_mode"`
		PlayVolume  int8 `json:"play_volume"`
		SystemState int8 `json:"system_state"`
	} `json:"speaker,omitempty"`
	Values []struct {
		Index json.Number `json:"index"`
		Value json.Number `json:"value"`
	} `json:"values"`
}

type DJIDroneStateMessageData struct {
	FirmwareVersion string `json:"firmware_version"`
	// 固件一致性	{"0":"不需要一致性升级","1":"需要一致性升级"}
	CompatibleStatus int32   `json:"compatible_status"`
	HomeLongitude    float32 `json:"home_longitude"`
	HomeLatitude     float32 `json:"home_latitude"`
	// Home 点绝对高度
	HomeHeight   float32 `json:"home_height"`
	HomeDistance float32 `json:"home_distance"`
	// 可以为设备，也可以为某个浏览器。设备使用 A/B 表示 A 控，B 控，浏览器以自生成的 uuid 作为标识符
	ControlSource string `json:"control_source"`
	// 飞行限制高度
	HeightRestriction int32 `json:"height_restriction"`
	// 飞行限制距离
	DistanceRestriction int32 `json:"distance_restriction"`
	// 用户设置的电池低电量告警百分比
	LowBatteryWarningThreshold int32 `json:"low_battery_warning_threshold"`
	// 用户设置的电池严重低电量告警百分比
	SeriousLowBatteryWarningThreshold int32 `json:"serious_low_battery_warning_threshold"`
	// {"0":"无意义","1":"电池电量不足（返航、降落）","2":"电池电压不足（返航、降落）","3":"电压严重过低（返航、降落）","4":"遥控器按键请求（起飞、返航、降落）","5":"App 请求（起飞、返航、降落）","6":"遥控信号丢失（返航、降落、悬停）","7":"导航、SDK 等外部设备触发（起飞、返航、降落）","8":"进入机场限飞区（降落）","9":"虽然触发了返航但是因为距离home点距离太近（降落）","10":"虽然触发了返航但是因为距离home点距离太远（降落）","11":"执行航点任务时请求（起飞）","12":"返航阶段到达 home 点上方后请求（降落）","13":"无人机高度下降，距地面 0.7m（二段降落限低）时，继续下降导致（降落）","14":"APP、SDK 等设备强制突破限低保护进行（降落）","15":"因为周围有航班经过而请求（返航、降落）","16":"因为高度控制失败请求（返航、降落）","17":"智能低电量返航后进入（降落）","18":"AP 控制飞行模式（手动飞行）","19":"硬件异常（返航、降落）","20":"防触地保护结束（降落）","21":"返航取消 (悬停)","22":"返航时遇到障碍物（降落）"}
	ModeCodeReason int32 `json:"mode_code_reason"`
	// 负载状态
	Payloads         []map[string]any     `json:"payloads"`
	PSDKWidgetValues []DJIDronePSDKWidget `json:"psdk_widget_values"`
	PSDKUIResource   []map[string]any     `json:"psdk_ui_resource"`
}

type DJIDroneOsdMessage struct {
	DJIMessage
	Data *DJIDroneOsdMessageData `json:"data"`
}

func (m *DJIDroneOsdMessage) ToDockDroneBiz(dock *biz.Device, sn string, dataKeys types.HashSet[string], raw string) *biz.DroneProperties {
	bm := &biz.DroneProperties{
		Sn:        dock.Sn,
		DeviceId:  dock.Id,
		DroneSn:   sn,
		Id:        m.Bid,
		Timestamp: time.UnixMilli(m.Timestamp),
		RxTime:    time.Now(),
		Mode:      -1,
	}
	if subDev, ok := lo.Find(dock.Subdevices, func(d *biz.DockSubdevice) bool {
		return d.Sn == sn
	}); ok {
		bm.DroneType = subDev.Type
	}
	data := m.Data
	if dataKeys.Contains("mode_code") {
		bm.Mode = data.ModeCode
	}
	if dataKeys.Contains("horizontal_speed") {
		bm.FlightState = &biz.DroneFlightState{
			HorizontalSpeed:           data.HorizontalSpeed,
			VerticalSpeed:             data.VerticalSpeed,
			Longitude:                 data.Longitude,
			Latitude:                  data.Latitude,
			Height:                    data.Height,
			Elevation:                 data.Elevation,
			AttitudePitch:             data.AttitudePitch,
			AttitudeRoll:              data.AttitudeRoll,
			AttitudeHead:              data.AttitudeHead,
			TotalFlightTime:           int64(data.TotalFlightTime),
			WindSpeed:                 data.WindSpeed,
			WindDirection:             data.WindDirection,
			IsNearAreaLimit:           data.IsNearAreaLimit,
			IsNearHeightLimit:         data.IsNearHeightLimit,
			IsNearDistanceLimit:       -1,
			NightLightsState:          data.NightLightsState,
			RCLostAction:              data.RCLostAction,
			ExitWaylineWhenRCLost:     data.ExitWaylineWhenRCLost,
			HorizonObstacleAvoidance:  data.ObstacleAvoidance.Horizon,
			UpsideObstacleAvoidance:   data.ObstacleAvoidance.Upside,
			DownsideObstacleAvoidance: data.ObstacleAvoidance.Downside,
		}
		if data.DistanceLimitStatus.State == 1 {
			bm.FlightState.IsNearDistanceLimit = data.DistanceLimitStatus.IsNearIDistanceLimit
		}
	}
	if dataKeys.Contains("position_state") {
		bm.PositionState = &biz.PositionState{
			IsFixed:       data.PositionState.IsFixed,
			IsCalibration: data.PositionState.IsCalibration,
			Quality:       data.PositionState.Quality,
			GpsNumber:     data.PositionState.GpsNumber,
			RtkNumber:     data.PositionState.RtkNumber,
		}
	}
	if b := data.Battery; dataKeys.Contains("battery") {
		bm.Battery = &biz.DroneBatteryState{
			CapacityPercent:  b.CapacityPercent,
			RemainFlightTime: int32(b.RemainFlightTime),
			ReturnHomePower:  b.ReturnHomePower,
			LandingPower:     b.LandingPower,
			Batteries: lo.Map(b.Batteries, func(it *DJIDroneBatteryDetail, _ int) *biz.DroneBattery {
				return &biz.DroneBattery{
					Index:           it.Index,
					CapacityPercent: it.CapacityPercent,
					Voltage:         it.Voltage,
					Temperature:     it.Temperature,
					Sn:              it.Sn,
					Loop:            it.LoopTimes,
				}
			}),
		}
	}
	if s := data.Storage; dataKeys.Contains("storage") {
		bm.Storage = &biz.Storage{
			Total: s.Total,
			Used:  s.Used,
		}
	}
	if s := data.Cameras; len(s) > 0 {
		bm.Cameras = lo.Map(s, func(it *DJIDroneCameraState, _ int) *biz.DroneCameraState {
			s := &biz.DroneCameraState{
				Index:          it.PayloadIndex,
				Mode:           it.CameraMode,
				PhotoState:     it.PhotoState,
				RecordingState: it.RecordingState,
				ZoomFactor:     it.ZoomFactor,
				IRZoomFactor:   it.IRZoomFactor,
				ZoomFocusMode:  it.ZoomFocusMode,
				ZoomFocusState: it.ZoomFocusState,
				ZoomFocusValue: it.ZoomFocusValue,
				IRMeteringMode: it.IRMeteringMode,
				LiveViewWorldRegion: biz.CameraLiveViewWorldRegion{
					Left:   it.LiveViewWorldRegion.Left,
					Top:    it.LiveViewWorldRegion.Top,
					Right:  it.LiveViewWorldRegion.Right,
					Bottom: it.LiveViewWorldRegion.Bottom,
				},
			}
			if r := gjson.Get(raw, it.PayloadIndex); r.Exists() {
				ci := &DJIDroneCameraInfo{}
				if err := json.Unmarshal([]byte(r.Raw), ci); err == nil {
					s.GimbalPitch = ci.GimbalPitch
					s.GimbalRoll = ci.GimbalRoll
					s.GimbalYaw = ci.GimbalYaw
					s.MeasureTargetLongitude = ci.MeasureTargetLongitude
					s.MeasureTargetLatitude = ci.MeasureTargetLatitude
					s.MeasureTargetAltitude = ci.MeasureTargetAltitude
					s.MeasureTargetDistance = ci.MeasureTargetDistance
					s.MeasureErrState = ci.MeasureTargetErrorState
				}
			}
			return s
		})
	}
	return bm
}

type DJIDroneStateMessage struct {
	DJIMessage
	Data *DJIDroneStateMessageData `json:"data"`
}

func (m *DJIDroneStateMessage) ToDockDroneBiz(dock *biz.Device, sn string) *biz.DroneProperties {
	bm := &biz.DroneProperties{
		Sn:        dock.Sn,
		DeviceId:  dock.Id,
		DroneSn:   sn,
		Id:        m.Bid,
		Timestamp: time.UnixMilli(m.Timestamp),
		RxTime:    time.Now(),
		Mode:      -1,
		Other:     make(map[string]interface{}),
	}
	if m.Data.FirmwareVersion != "" {
		bm.Other["firmware"] = m.Data.FirmwareVersion
	}
	if m.Data.ControlSource != "" {
		bm.Other["ctl"] = m.Data.ControlSource
	}
	if m.Data.ModeCodeReason != 0 {
		bm.Other["modeReason"] = m.Data.ModeCodeReason
	}
	if len(m.Data.PSDKWidgetValues) > 0 {
		if sd, ok := lo.Find(m.Data.PSDKWidgetValues, func(it DJIDronePSDKWidget) bool {
			return it.Speaker != nil
		}); ok {
			bm.Speaker = &biz.DroneSpeakerState{
				DroneExtendedWidget: biz.DroneExtendedWidget{
					Index:           strconv.Itoa(sd.PSDKIndex),
					Name:            sd.PSDKName,
					Sn:              sd.PSDKSn,
					FirmwareVersion: sd.PSDKVersion,
					LibVersion:      sd.PSDKLibVersion,
				},
				Type:              sd.PSDKName,
				WorkMode:          sd.Speaker.WorkMode,
				PlayMode:          sd.Speaker.PlayMode,
				SystemState:       sd.Speaker.SystemState,
				PlayVolume:        sd.Speaker.PlayVolume,
				PlayFileName:      sd.Speaker.PlayFileName,
				PlayFileSignature: sd.Speaker.PlayFileMD5,
			}
		}
	}
	return bm
}

type DJISpeakerPalyProgresEventData struct {
	Output struct {
		PSDKIndex int `json:"psdk_index"`
		// {"in_progress":"处理中","ok":"播放成功"}
		Status   string `json:"status"`
		Md5      string `json:"md5"`
		Progress struct {
			Percent int32  `json:"percent"`
			StepKey string `json:"step_key"`
		} `json:"progress"`
	} `json:"output"`
	Result int32 `json:"result"`
}

type DJISpeakerPalyProgresEvent struct {
	DJIEventMessage
	Data DJISpeakerPalyProgresEventData `json:"data"`
}

func (m *DJISpeakerPalyProgresEvent) ToBiz(dev *biz.Device, mode int32) *biz.SpeakerPlayStatusEvent {
	step := m.Data.Output.Progress.StepKey
	if m.Data.Output.Status == "ok" || m.Data.Output.Status == "success" {
		step = "ok"
	}
	return &biz.SpeakerPlayStatusEvent{
		ThingEvent: biz.ThingEvent{
			Id:           m.Bid,
			DeviceId:     dev.Id,
			Sn:           dev.Sn,
			OccurredTime: time.UnixMilli(m.Timestamp),
			RxTime:       time.Now(),
			Type:         biz.ThingModelEventTypeSpeakerStatus,
		},
		Index:             strconv.Itoa(m.Data.Output.PSDKIndex),
		Code:              m.Data.Result,
		Mode:              mode,
		Progress:          m.Data.Output.Progress.Percent,
		Step:              step,
		PlayFileSignature: m.Data.Output.Md5,
	}
}
