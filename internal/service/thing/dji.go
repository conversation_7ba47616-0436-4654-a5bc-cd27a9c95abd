package thing

import (
	"strconv"
	"strings"

	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type DJIMessage struct {
	// 事务（Transaction）的 UUID：表征一次简单的消息通信，如：增/删/改/查，云台控制等，可以是：
	// 1. 数据上报请求+数据上报响应
	// 2 握手认证请求+响应+ack
	// 3.报警事件单向通知等，解决事务多并发和消息匹配的问题
	Tid string `json:"tid" validate:"required,uuid"`
	// 业务（Business）的 UUID：有些功能不是一次通信就能完成的，包含持续一段时间内的所有交互。
	// 业务通常由多个原子事务组成，且持续时间较长;
	// 例如点播/下载/回放；解决业务多并发和重复请求的问题，便于所有模块的状态机管理。
	Bid       string `json:"bid" validate:"required,uuid"`
	Timestamp int64  `json:"timestamp" validate:"required"`
	Gateway   string `json:"gateway" validate:"gateway"`
	Method    string `json:"method"`
}

type DJIEventMessage struct {
	DJIMessage
	// 服务端收到设备的events事件上报消息后，跟进need_reply来判断是否给予收到答复;0代表不需要，1代表需要
	NeedReply int32 `json:"need_reply"`
}

type DJIReplyData struct {
	// 非0代表错误
	Result int32                  `json:"result"`
	Output map[string]interface{} `json:"output,omitempty"`
}

type DJIStatusReplyOutput struct {
	// {"sent":"已下发","in_progress":"执行中","ok":"执行成功","paused":"暂停","rejected":"拒绝","failed":"失败","canceled":"取消或终止","timeout":"超时"}
	Status string `json:"status"`
}

type DJIStatusReplyData struct {
	// 非0代表错误
	Result int32                `json:"result"`
	Output DJIStatusReplyOutput `json:"output"`
}
type DJIStatusReplyMessage struct {
	DJIMessage
	Data DJIStatusReplyData `json:"data"`
}

type DJIReplyMessage struct {
	DJIMessage
	Data DJIReplyData `json:"data"`
}

type DJINetworkState struct {
	// 网络类型 {"1":"4G","2":"以太网"}
	Type int32 `json:"type"`
	// 网络质量 {"0":"差","1":"中","2":"好"}
	Quality int32   `json:"quality"`
	Rate    float64 `json:"rate"`
}

type DJIWirelessLink struct {
	DongleNumber int32 `json:"dongle_number"`
	// 4G 链路连接状态	{"0":"断开","1":"连接"}
	LinkState4G int32 `json:"4g_link_state"`
	// SDR 链路连接状态	{"0":"断开","1":"连接"}
	LinkStateSDR int32 `json:"sdr_link_state"`
	// 图传链路模式	{"0":"SDR 模式","1":"4G 融合模式"}
	LinkWorkmode int32 `json:"link_workmode"`
	// {"min":"0","max":"5","step":"1"}
	QualitySDR  int32   `json:"sdr_quality"`
	FreqBandSDR float64 `json:"sdr_freq_band"`
	// 总体 4G 信号质量	{"min":"0","max":"5","step":"1"}
	Quality4G int32 `json:"4g_quality"`
	// 天端 4G 信号质量
	Quality4GUav int32 `json:"4g_uav_quality"`
	// 地端 4G 信号质量
	Quality4GGnd int32   `json:"4g_gnd_quality"`
	FreqBand4G   float64 `json:"4g_freq_band"`
}

func (wl *DJIWirelessLink) ToBiz() *biz.WirelessLinkState {
	q4G := wl.Quality4G
	q4GUav := wl.Quality4GUav
	q4GGnd := wl.Quality4GGnd
	qSDR := wl.QualitySDR
	if q4G == 65535 {
		q4G = 0
	}
	if q4GUav == 0xff {
		q4GUav = 0
	}
	if q4GGnd == 0xff {
		q4GGnd = 0
	}
	if qSDR == 0xff {
		qSDR = 0
	}
	return &biz.WirelessLinkState{
		Mode:         wl.LinkWorkmode,
		State4G:      wl.LinkState4G,
		StateSDR:     wl.LinkStateSDR,
		QualitySDR:   qSDR,
		Quality4G:    q4G,
		Quality4GUav: q4GUav,
		Quality4GGnd: q4GGnd,
	}
}

type DJILiveStatus struct {
	// {"0":"自动","1":"流畅","2":"高清","3":"超清"}
	VideoClarity int32 `json:"video_quality"`
	// {"0":"未直播","1":"在直播"}
	Status      int32  `json:"status"`
	ErrorStatus int32  `json:"error_status"`
	VideoId     string `json:"video_id"`
	VideoType   string `json:"video_type"`
}

type DJILiveCapacity struct {
	AvailableVideoNumber  int32            `json:"available_video_number"`
	CoexistVideoNumberMax int32            `json:"coexist_video_number_max"`
	DeviceList            []DJIVideoDevice `json:"device_list"`
}

type DJICamera struct {
	CameraIndex           string `json:"camera_index"`
	AvailableVideoNumber  int32  `json:"available_video_number"`
	CoexistVideoNumberMax int32  `json:"coexist_video_number_max"`
	VideoList             []struct {
		VideoIndex string `json:"video_index"`
		// normal/wide/zoom/ir
		VideoType            string   `json:"video_type"`
		SwitchableVideoTypes []string `json:"switchable_video_types"`
	} `json:"video_list"`
}

func (c *DJICamera) ToSubDevice(sn string) *biz.DockSubdevice {
	t, st := GetDJITypeAndSubTypeFromCameraIndex(c.CameraIndex)
	vs := make([]biz.CameraVideo, 0, len(c.VideoList))
	if c.CoexistVideoNumberMax == 1 && len(c.VideoList) > 0 {
		switchable := make([]biz.VideoType, 0, len(c.VideoList)*len(c.VideoList[0].SwitchableVideoTypes))
		for _, v := range c.VideoList {
			if len(v.SwitchableVideoTypes) == 0 {
				switchable = append(switchable, biz.NewVideoType(v.VideoType))
			} else {
				switchable = append(switchable, lo.Map(v.SwitchableVideoTypes, func(it string, index int) biz.VideoType {
					return biz.NewVideoType(it)
				})...)
			}
		}
		vs = append(vs, biz.CameraVideo{
			Index:                c.VideoList[0].VideoIndex,
			SwitchableVideoTypes: switchable,
		})
	} else {
		for _, v := range c.VideoList {
			vs = append(vs, biz.CameraVideo{
				Index: v.VideoIndex,
				// Type:  biz.NewVideoType(v.VideoType),
				SwitchableVideoTypes: lo.Map(v.SwitchableVideoTypes, func(it string, index int) biz.VideoType {
					return biz.NewVideoType(it)
				}),
			})
		}
	}

	return &biz.DockSubdevice{
		Sn:     sn,
		Domain: biz.SubDeviceDomainCamera,
		Type:   NewTypeForDJIDockSub(1, t, st),
		Index:  c.CameraIndex,
		Extra: &biz.SubCameraInfo{
			VideoNumber:        c.AvailableVideoNumber,
			CoexistVideoNumber: c.CoexistVideoNumberMax,
			Videos:             vs,
		},
	}
}

type DJIVideoDevice struct {
	AvailableVideoNumber  int32        `json:"available_video_number"`
	CoexistVideoNumberMax int32        `json:"coexist_video_number_max"`
	Sn                    string       `json:"sn"`
	CameraList            []*DJICamera `json:"camera_list"`
}

type DJIStorage struct {
	// 总容量 单位千字节
	Total int64 `json:"total"`
	// 已使用容量	单位千字节
	Used int64 `json:"used"`
}

type DJIDroneBatteryMaintenanceInfo struct {
	// {"0":"无需保养","1":"待保养","2":"正在保养"}
	MaintenanceState int32 `json:"maintenance_state"`
	// 电池保养剩余时间	 小时
	MaintenanceTimeLeft int32 `json:"maintenance_time_left"`
	// 电池加热保温状态 {"0":"电池未开启加热或保温","1":"电池在加热中","2":"电池在保温中"}
	HeatState int32             `json:"heat_state"`
	Batteries []DJIDroneBattery `json:"batteries"`
}

type DJIDroneBattery struct {
	// {"0":"左电池","1":"右电池"}
	Index int32
	// 电池剩余电量百分比
	CapacityPercent int32 `json:"capacity_percent"`
	// 毫伏
	Voltage int32 `json:"voltage"`
	// 摄氏度
	Temperature float32 `json:"temperature"`
}

type DJIDroneBatteryDetail struct {
	DJIDroneBattery
	// 电池类型
	Type            int32  `json:"type"`
	SubType         int32  `json:"sub_type"`
	FirmwareVersion string `json:"firmware_version"`
	LoopTimes       int32  `json:"loop_times"`
	// 高电压存储天数
	HighVoltageStorageDays int32  `json:"high_voltage_storage_days"`
	Sn                     string `json:"sn"`
}

type DJIDroneBatteryState struct {
	// 电池剩余总电量百分比
	CapacityPercent int32 `json:"capacity_percent"`
	// 剩余飞行时间	秒
	RemainFlightTime float64 `json:"remain_flight_time"`
	// 返航所需电量百分比
	ReturnHomePower int32 `json:"return_home_power"`
	// 强制降落电量百分比
	LandingPower int32                    `json:"landing_power"`
	Batteries    []*DJIDroneBatteryDetail `json:"batteries"`
}

type DJIDockBackupBattery struct {
	// {"0":"备用电池关闭","1":"备用电池开启"}
	Switch int32 `json:"switch"`
	// 毫伏
	Voltage int32 `json:"voltage"`
	// 摄氏度
	Temperature float32 `json:"temperature"`
}

type DJIDroneChargeState struct {
	// 电量百分比
	CapacityPercent int32 `json:"capacity_percent"`
	// {"0":"空闲","1":"充电中"}
	State int32 `json:"state"`
}

// DJIPositionState 搜星状态
type DJIPositionState struct {
	// 是否收敛 {"0":"非开始","1":"收敛中","2":"收敛成功","3":"收敛失败"}
	IsFixed       int32 `json:"is_fixed"`
	IsCalibration int32 `json:"is_calibration"`
	// {"1":"1档","2":"2档","3":"3档","4":"4档","5":"5档"}
	Quality   int32 `json:"quality"`
	GpsNumber int32 `json:"gps_number"`
	RtkNumber int32 `json:"rtk_number"`
}

type DJIMaintainStatus struct {
	// {"0":"无保养","1":"保养中"}
	State int32 `json:"state"`
	// 上一次保养类型	 {"0":"无保养","1":"飞机基础保养","2":"飞机常规保养","3":"飞机深度保养","17":"机场保养"}
	LastMaintainType int32 `json:"last_maintain_type"`
	// unix 秒
	LastMaintainTime int64 `json:"last_maintain_time"`
	// 上一次保养时工作架次(机库)
	LastMaintainWorkSorties int32 `json:"last_maintain_work_sorties"`
	// 上一次保养时飞行航时 小时 （无人机）
	LastMaintainFlightTime int32 `json:"last_maintain_flight_time"`
	// 上一次保养时飞行架次	(无人机)
	LastMaintainFlightSorties int32 `json:"last_maintain_flight_sorties"`
}

// DJILandPoint 备降点
type DJILandPoint struct {
	Longitude float64 `json:"longitude"`
	Latitude  float64 `json:"latitude"`
	// 安全高度(备降转移高)
	SafeLandHeight float32 `json:"safe_land_height"`
	// {"0":"未设置","1":"已设置"}
	IsConfigured int32 `json:"is_configured"`
}

// DJIDroneObstacleAvoidance 无人机避障状态 {"0":"关闭","1":"开启"}
type DJIDroneObstacleAvoidance struct {
	// 水平避障状态
	Horizon int32 `json:"horizon"`
	// 上视避障状态
	Upside int32 `json:"upside"`
	// 下视避障状态
	Downside int32 `json:"downside"`
}

type DJIHMSEvent struct {
	// 告警等级 {"0":"通知","1":"提醒","2":"警告"}
	Level int32 `json:"level"`
	// {"0":"飞行任务","1":"设备管理","2":"媒体","3":"hms"}
	Module int32 `json:"module"`
	// {"0":"在地上","1":"在天上"}
	InTheSky int32  `json:"in_the_sky"`
	Imminent int32  `json:"imminent"`
	Code     string `json:"code"`
	// 格式如3-1-0
	DeviceType string      `json:"device_type"`
	Args       interface{} `json:"args"`
}

// DJIRequestMessage  配置更新消息 method=config
type DJIRequestMessage struct {
	DJIEventMessage
	Data struct {
		ConfigType  string `json:"config_type"`
		ConfigScope string `json:"config_scope"`
	} `json:"data"`
}

type DJIRequestReply struct {
	DJIEventMessage
	Data map[string]any
}

const (
	// 拓扑更新
	DJIMethodUpdateTopo = "update_topo"
	// 健康预警
	DJIMethodHMS = "hms"
	// 设备返航退出状态通知
	DJIMethodExitHomingNotify = "device_exit_homing_notify"
	// 航线任务上报
	DJIMethodFlightTaskProgress = "flighttask_progress"
	// 飞行任务资源获取
	DJIMethodFlightTaskResourceGet = "flighttask_resource_get"
	// 配置更新
	DJIMethodConfig = "config"
	// 获取设备绑定信息
	DJIMethodOrganizationStatus = "airport_bind_status"
	DJIMethodOrganizationGet    = "airport_organization_get"
	DJIMethodOrganizationBind   = "airport_organization_bind"
	// 获取上传临时凭证
	DJIMethodStoregeConfigGet = "storage_config_get"
	// 媒体文件上传结果上报
	DJIMethodFilghtTaskFileUpload = "file_upload_callback"
	// DRC 链路状态通知
	DJIMethodDRCStatusNotify = "drc_status_notify"
	// flyto 执行结果事件通知
	DJIMethodFlyToPointProgress = "fly_to_point_progress"
	// 一键起飞结果事件通知
	DJIMethodTakeoffToPointProgress   = "takeoff_to_point_progress"
	DJIMethodSpeakerAudioPlayProgress = "speaker_audio_play_start_progress"
	DJIMethodSpeakerTTSPlayProgress   = "speaker_tts_play_start_progress"
	// POI 环绕状态信息通知
	DJIMethodPOIStatusNotify = "poi_status_notify"
	// 日志上传进度通知
	DJIMethodLogUploadProgress = "fileupload_progress"
)

const (
	// 下发航线任务
	DJIMethodFlightTaskPrepare = "flighttask_prepare"
	// 执行任务
	DJIMethodFlightTaskExecute = "flighttask_execute"
	// 取消任务
	DJIMethodFlightTaskUndo = "flighttask_undo"
	// 航线暂停
	DJIMethodFlightTaskPause = "flighttask_pause"
	// 航线恢复
	DJIMethodFlightTaskRecover = "flighttask_recovery"
	// 机场重启
	DJIMethodDockReboot = "device_reboot"
	// 一键返航
	DJIMethodReturnHome = "return_home"
	// 开启调试模式
	DJIMethodDebugMode      = "debug_mode_open"
	DJIMethodDebugModeClose = "debug_mode_close"
	// 开始直播
	DJIMethodStartLive = "live_start_push"
	// 直播相机切换
	DJIMethodChangeLiveCamera = "live_camera_change"
	// 停止直播
	DJIMethodStopLive = "live_stop_push"
	// 设置直播清晰度
	DJIMethodSetLiveQuality = "live_set_quality"
	// 设置直播镜头
	DJIMethodSetLiveLen = "live_lens_change"
	// 飞行控制权抢夺
	DJIMethodGrabFlightControl = "flight_authority_grab"
	// 负载控制权抢夺
	DJIMethodGrabPayloadControl = "payload_authority_grab"
	// 指令飞行控制模式
	DJIMethodDRCMode     = "drc_mode_enter"
	DJIMethodExitDRCMode = "drc_mode_exit"
	// 指点飞行
	DJIMethodFlyToPoint = "fly_to_point"
	// 结束 flyto 结束任务后的无人机行为是悬停
	DJIMethodStopFlyToPoint = "fly_to_point_stop"
	// 更新指点
	DJIMethodUpdateFlyToPoint = "fly_to_point_update"
	// 注视观测点
	DJIMethodLookAtPoint = "camera_look_at"
	// 负载控制命令均需要获取负载控制权
	// 负载控制-重置云台
	DJIMethodResetGimbal = "gimbal_reset"
	// 负载控制-切换相机模式 需要负载控制权
	DJIMethodSwitchCameraMode = "camera_mode_switch"
	// 负载控制-单拍
	DJIMethodTakePhoto = "camera_photo_take"
	// 负载控制-开始录像
	DJIMethodStartRecording = "camera_recording_start"
	// 负载控制-结束录像
	DJIMethodStopRecording = "camera_recording_stop"
	// 负载控制-变焦
	DJIMethodSetFocalLength = "camera_focal_length_set"
	// 喊话器-开始播放TTS文本
	DJIMethodSpeakerPlayTTS = "speaker_tts_play_start"
	// 喊话器-开始播放音频
	DJIMethodSpeakerPlayAudio = "speaker_audio_play_start"
	// 喊话器-重新播放
	DJIMethodSpeakerReplay = "speaker_replay"
	DJIMethodSpeakerStop   = "speaker_play_stop"
	// 喊话器-设置播放模式 {"0":"单次播放","1":"循环播放(单曲)"}
	DJIMethodSpeakerModeSet = "speaker_play_mode_set"
	// 喊话器-设置播放音量
	DJIMethodSpeakerVolumeSet = "speaker_play_volume_set"
	// 一键起飞
	DJIMethodTakeOffToPoint = "takeoff_to_point"
	// 进入 POI 环绕模式
	DJIMethodEnterPOIMode = "poi_mode_enter"
	DJIMethodExitPOIMode  = "poi_mode_exit"
	// POI 环绕速度设置
	DJIMethodSetPOICircleSpeed = "poi_circle_speed_set"
	// 取消返航
	DJIMethodCancelReturnHome = "return_home_cancel"
	// 开启机舱盖
	DJIMethodOpenDockCover       = "cover_open"
	DJIMethodCloseDockCover      = "cover_close"
	DJIMethodForceCloseDockCover = "cover_force_close"
	// 无人机开机
	DJIMethodOpenDrone  = "drone_open"
	DJIMethodCloseDrone = "drone_close"
	// 打开推杆
	DJIMethodOpenPutter  = "putter_open"
	DJIMethodClosePutter = "putter_close"

	DJIMethodQueryLogs       = "fileupload_list"
	DJIMethodLogUpload       = "fileupload_start"
	DJIMethodCancelLogUpload = "fileupload_update"
	// 充电
	DJIMethodOpenCharge  = "charge_open"
	DJIMethodCloseCharge = "charge_close"
)

// https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html
var djiDroneTypeMapper = map[int32]map[int32]string{
	89: {0: "M350RTK"},
	60: {0: "M300RTK"},
	67: {0: "M30", 1: "M30T"},
	77: {0: "M3E", 1: "M3T", 2: "M3多光谱版"},
	91: {0: "Matrice 3D", 1: "Matrice 3TD"},
}

var djiCameraTypeMapper = map[int32]map[int32]string{
	20:  {0: "Z30"},
	26:  {0: "XT2"},
	39:  {0: "FPV"},
	41:  {0: "XTS"},
	42:  {0: "H20"},
	43:  {0: "H20T"},
	50:  {65535: "P1"},
	52:  {0: "M30 Camera"},
	53:  {0: "M30T Camera"},
	61:  {0: "H20N"},
	165: {0: "DJI Dock Camera"},
	80:  {0: "Matrice 3D Camera"},
	81:  {0: "Matrice 3TD Camera"},
	176: {0: "Matrice 3 辅助影像"},
}

var djiControllerTypeMapper = map[int32]map[int32]string{
	56:  {0: "DJI RC"},
	119: {0: "DJI RC Plus"},
	114: {0: "DJI RC Pro 行业版"},
}

func NewTypeForDJIDockSubFromIndex(index string) string {
	v := lo.Map(strings.Split(index, "-"), func(it string, _ int) int32 {
		if i, err := strconv.ParseInt(it, 10, 64); err == nil {
			return int32(i)
		}
		return -1
	})
	if len(v) == 3 {
		return NewTypeForDJIDockSub(v[0], v[1], v[2])
	}
	return "未知类型"
}

func NewTypeForDJIDockSub(domain, t, subType int32) string {
	// 飞机
	if domain == 0 {
		m, ok := djiDroneTypeMapper[t]
		if ok {
			return m[subType]
		}
	}
	// 负载类
	if domain == 1 {
		m, ok := djiCameraTypeMapper[t]
		if ok {
			return m[subType]
		}
	}
	if domain == 2 {
		m, ok := djiControllerTypeMapper[t]
		if ok {
			return m[subType]
		}
	}
	if domain == 3 {
		return "DJI DOCK"
	}
	return ""
}

func GetDJITypeAndSubTypeFromCameraIndex(cameraIndex string) (int32, int32) {
	values := strings.Split(cameraIndex, "-")
	if len(values) == 3 {
		return int32(conv.Atoi(values[0])), int32(conv.Atoi(values[1]))
	}
	return 0, 0
}

// https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html
func FindDJICameraPosition(cameraIndex string) int32 {
	values := lo.Map(strings.Split(cameraIndex, "-"), func(it string, _ int) int32 {
		return int32(conv.Atoi(it))
	})
	if len(values) == 3 {
		// dji dock camera
		if values[0] == 165 {
			return biz.LivePositionDock
		}
		if values[0] == 176 {
			return biz.LivePositionFPV
		}
		return values[2]
	}
	return -1
}
