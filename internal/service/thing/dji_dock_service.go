package thing

import (
	"time"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/pkg/id"
)

// DJIDockFlightTask 航线任务
type DJIDockFlightTask struct {
	FlightId string `json:"flight_id" validate:"required"`
	// {"0":"立即任务","1":"定时任务","2":"条件任务"}
	TaskType int32 `json:"task_type" validate:"max=2"`
	// 任务开始执行时间毫秒时间戳。可选字段。当task_type 为 0 或 1 时必填，为 2 时非必填。
	ExecuteTime          int64                              `json:"execute_time"`
	WaylineType          int32                              `json:"wayline_type"`
	File                 DJIFlightTaskFile                  `json:"file"`
	SimulateMission      *SimulateMission                   `json:"simulate_mission"`
	ReadyConditions      *DJIFlightTaskReadyConditions      `json:"ready_conditions,omitempty"`
	ExecutableConditions *DJIFlightTaskExecutableConditions `json:"executable_conditions,omitempty"`
	// 返航高度	 {"unit":"米","min":20,"max":500}
	RthAltitude float32 `json:"rth_altitude"`
	// {"0":"智能高度","1":"设定高度"}
	RthMode int32 `json:"rth_mode"`
	// {"0":"返航","1":"悬停","2":"降落"}
	OutOfControlAction    int32 `json:"out_of_control_action"`
	ExitWaylineWhenRcLost int32 `json:"exit_wayline_when_rc_lost"`
}

type DJIDockTakeoffToPointData struct {
	FlightId              string  `json:"flight_id"`
	TargetLatitude        float64 `json:"target_latitude"`
	TargetLongitude       float64 `json:"target_longitude"`
	TargetHeight          float32 `json:"target_height"`
	SecurityTakeoffHeight float32 `json:"security_takeoff_height"`
	RthAltitude           float32 `json:"rth_altitude"`
	// {"0":"悬停","1":"着陆(降落)","2":"返航"}
	RCLostAction int32 `json:"rc_lost_action"`
	// 	{"0":"智能高度","1":"设定高度"}
	RTHMode int32 `json:"rth_mode"`
	// {"0":"智能高度飞行","1":"设定高度飞行"}
	CommanderFlightMode     int32   `json:"commander_flight_mode"`
	CommanderModeLostAction int32   `json:"commander_mode_lost_action"`
	CommanderFlightHeight   float32 `json:"commander_flight_height"`
	//{"max":15,"min":1,"unit_name":"米每秒 / m/s"}
	MaxSpeed        float32          `json:"max_speed"`
	SimulateMission *SimulateMission `json:"simulate_mission"`
}

func NewDJIDockTakeoffToPointData(fightId string, lp *biz.LaunchPayload) *DJIDockTakeoffToPointData {
	data := &DJIDockTakeoffToPointData{
		FlightId:                fightId,
		TargetLatitude:          lp.Lnglat[1],
		TargetLongitude:         lp.Lnglat[0],
		TargetHeight:            lp.TargetHeight,
		SecurityTakeoffHeight:   lp.SecurityHeight,
		RthAltitude:             lp.ReturnAltitude,
		RCLostAction:            lp.RCLostAction,
		RTHMode:                 lp.ReturnMode,
		CommanderFlightMode:     int32(lp.CmderMode),
		CommanderModeLostAction: int32(lp.CmderModeLost),
		CommanderFlightHeight:   lp.CmderHeight,
		MaxSpeed:                lp.Speed,
	}
	if lp.SimulateMission != nil {
		data.SimulateMission = &SimulateMission{
			IsEnable:  lp.SimulateMission.IsEnable,
			Longitude: lp.SimulateMission.Lnglat[0],
			Latitude:  lp.SimulateMission.Lnglat[1],
		}
	}
	return data
}

type DJIDockTakeoffToPointService struct {
	DJIMessage
	Data *DJIDockTakeoffToPointData `json:"data"`
}

// DJIDockFlightTaskPrepareService 下发航线任务 method=flighttask_prepare
type DJIDockFlightTaskPrepareService struct {
	DJIMessage
	Data *DJIDockFlightTask `json:"data"`
}

type DJIDockFlightTaskExecuteCmd struct {
	FlightId string `json:"flight_id" validate:"required"`
}

type DJIDockFlightUndoCmd struct {
	FlightIds []string `json:"flight_ids" validate:"required,dive,requrire"`
}

// DJIDockFlightTaskExecuteService 执行任务 method=flighttask_execute
type DJIDockFlightTaskExecuteService struct {
	DJIMessage
	Data *DJIDockFlightTaskExecuteCmd `json:"data"`
}

// DJIDockFlightTaskUndoService 取消任务 method=flighttask_undo
type DJIDockFlightTaskUndoService struct {
	DJIMessage
	Data *DJIDockFlightUndoCmd `json:"data"`
}

type DJIDockStartLiveCmd struct {
	// {"0":"声网Agora","1":"RTMP","2":"RTSP","3":"GB28181"}
	UrlType      int32  `json:"url_type"`
	Url          string `json:"url"`
	VideoId      string `json:"video_id"`
	VideoQuality int32  `json:"video_quality"`
}

// DJIDockStartLiveService 开始直播 method=live_start_push
type DJIDockStartLiveService struct {
	DJIMessage
	Data *DJIDockStartLiveCmd `json:"data"`
}

func NewStartLiveService(dev *biz.Device, live *biz.Media, pc *biz.LivePushConfig, quality int32) *DJIDockStartLiveService {
	videoId := live.Key
	return &DJIDockStartLiveService{
		DJIMessage: DJIMessage{
			Tid:       id.NewUUIDV1(),
			Bid:       id.NewUUIDV1(),
			Timestamp: time.Now().UnixMilli(),
			Gateway:   dev.SourceSn,
			Method:    DJIMethodStartLive,
		},
		Data: &DJIDockStartLiveCmd{
			UrlType:      1,
			Url:          pc.Url,
			VideoId:      videoId,
			VideoQuality: quality,
		},
	}
}

type DJIDockStopLivePushCmd struct {
	VideoId string `json:"video_id"`
}

type DJIDockStopLiveService struct {
	DJIMessage
	Data DJIDockStopLivePushCmd `json:"data"`
}

func NewStopLiveService(dev *biz.Device, live *biz.Media) *DJIDockStopLiveService {
	return &DJIDockStopLiveService{
		DJIMessage: DJIMessage{
			Tid:       id.NewUUIDV1(),
			Bid:       id.NewUUIDV1(),
			Timestamp: time.Now().UnixMilli(),
			Gateway:   dev.SourceSn,
			Method:    DJIMethodStopLive,
		},
		Data: DJIDockStopLivePushCmd{
			VideoId: live.Key,
		},
	}
}

func NewDJIStopLiveService(dev *biz.Device, live *biz.Media) *DJIDockStopLiveService {
	return &DJIDockStopLiveService{
		DJIMessage: DJIMessage{
			Tid:       id.NewUUIDV1(),
			Bid:       id.NewUUIDV1(),
			Timestamp: time.Now().UnixMilli(),
			Gateway:   dev.SourceSn,
			Method:    DJIMethodStopLive,
		},
		Data: DJIDockStopLivePushCmd{
			VideoId: live.Key,
		},
	}
}

type DJISwithDockCameraCmd struct {
	VideoId        string `json:"video_id"`
	CameraPosition int32  `json:"camera_position"`
}

type DJIDockDRCMQTTBroker struct {
	Address    string `json:"address"`
	ClientId   string `json:"client_id"`
	Uername    string `json:"username"`
	Password   string `json:"password"`
	ExpireTime int32  `json:"expire_time"`
	EnableTLS  bool   `json:"enable_tls"`
}

type DJIDockDRCModeCmd struct {
	MQTTBroker *DJIDockDRCMQTTBroker `json:"mqtt_broker"`
	// OSD上报频率 1—~30
	OSDFrequency int32 `json:"osd_frequency"`
	// HSI 上报频率 1~30
	HSIFrequency int32 `json:"hsi_frequency"`
}

type DJIDockDRCModeService struct {
	DJIMessage
	Data *DJIDockDRCModeCmd `json:"data"`
}

type DJIDockLiveQualitySetCmd struct {
	VideoId      string `json:"video_id"`
	VideoQuality int32  `json:"video_quality"`
}

// DJIDockLiveQualitySetService 设置直播清晰度 method=live_set_quality
type DJIDockLiveQualitySetService struct {
	DJIMessage
	Data *DJIDockLiveQualitySetCmd `json:"data"`
}

type DJIDockLiveLensChangeCmd struct {
	VideoId   string `json:"video_id"`
	VideoType string `json:"video_type"`
}

type DJIDockLiveLensChangeService struct {
	DJIMessage
	Data *DJIDockLiveLensChangeCmd `json:"data"`
}

type DJIDockResetGimbalCmd struct {
	PayloadIndex string `json:"payload_index"`
	ResetMode    int32  `json:"reset_mode"`
}

type DJIDockResetGimbalService struct {
	DJIMessage
	Data *DJIDockResetGimbalCmd `json:"data"`
}

type DJIDockFlyToPoint struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	// 目标点高度，海拔高的绝对值，WGS84 0~1500 step":0.1
	Height float32 `json:"height"`
}

type DJIDockFlyToPointCmd struct {
	FlyToId string `json:"fly_to_id"`
	// flyto 的飞行过程中能达到的最大速度
	MaxSpeed int32               `json:"max_speed"`
	Points   []DJIDockFlyToPoint `json:"points"`
}

type DJIDockFlyToPointService struct {
	DJIMessage
	Data *DJIDockFlyToPointCmd `json:"data"`
}

type DJIDockLookAtPointCmd struct {
	// {"false":"仅云台转，机身不转","true":"锁定机头，云台和机身一起转"}
	Locked       bool    `json:"locked"`
	PayloadIndex string  `json:"payload_index"`
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
	Height       float32 `json:"height"`
}

type DJIDockLookAtPointService struct {
	DJIMessage
	Data *DJIDockLookAtPointCmd `json:"data"`
}

type DJIDockPayloadIndexData struct {
	PayloadIndex string `json:"payload_index"`
}

// type DJIDockGrabPaylaodControlService struct {
// 	DJIMessage
// 	Data *DJIDockPayloadIndexData `json:"data"`
// }

type DJIDockSwitchCameraModeCmd struct {
	PayloadIndex string `json:"payload_index"`
	// {"0":"拍照","1":"录像"}
	CameraMode int32 `json:"camera_mode"`
}

type DJIDockSwitchCameraModeService struct {
	DJIMessage
	Data *DJIDockSwitchCameraModeCmd `json:"data"`
}

// 获取负载控制权（payload_authority_grab
// 拍照（camera_photo_take）
// 开始录像(camera_recording_start)
// 结束录像（camera_recording_stop）
type DJIDockCameraCmdService struct {
	DJIMessage
	Data *DJIDockPayloadIndexData `json:"data"`
}

type DJIDockSetFocalLengthCmd struct {
	PayloadIndex string `json:"payload_index"`
	// {"zoom":"变焦","ir":"红外"}
	CameraType string `json:"camera_type"`
	// 可见光是 2～200，红外是 2～20
	ZoomFactor float64 `json:"zoom_factor"`
}

type DJIDockSetFocalLengthService struct {
	DJIMessage
	Data *DJIDockSetFocalLengthCmd `json:"data"`
}

type DJIDockEnterPOIModeCmd struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Height    float32 `json:"height"`
}

type DJIDockEnterPOIModeService struct {
	DJIMessage
	Data DJIDockEnterPOIModeCmd `json:"data"`
}

type DJIDockSetPOISpeedCmd struct {
	//环绕速度值，负数代表顺时针转，正数代表逆时针转
	CircleSpeed float32 `json:"circle_speed"`
}

type DJIDockSetPOISpeedService struct {
	DJIMessage
	Data DJIDockSetPOISpeedCmd `json:"data"`
}

type DJIDockLogListReplyData struct {
	Files []struct {
		DeviceSn string `json:"device_sn"`
		Module   string `json:"module"`
		Result   int32  `json:"result"`
		List     []struct {
			BootIndex int32 `json:"boot_index"`
			Size      int64 `json:"size"`
			StartTime int64 `json:"start_time"`
			EndTime   int64 `json:"end_time"`
		} `json:"list"`
	} `json:"files"`
}

func (s *DJIDockLogListReplyData) AsReplyData() biz.AnyMap {
	list := make([]any, 0)
	total := 0
	for _, mfs := range s.Files {
		total += len(mfs.List)
		for _, f := range mfs.List {
			list = append(list, biz.AnyMap{
				"module":    mfs.Module,
				"sn":        mfs.DeviceSn,
				"bootIndex": f.BootIndex,
				"size":      f.Size,
				"startTime": f.StartTime,
				"endTime":   f.EndTime,
			})
		}
	}
	return biz.AnyMap{
		"list":  list,
		"total": total,
	}
}
