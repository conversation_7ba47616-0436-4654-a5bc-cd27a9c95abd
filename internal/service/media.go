package service

import (
	"context"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	pb "gitlab.sensoro.com/skai/skai/api/media/v1"
	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type MediaService struct {
	pb.UnimplementedMediaServer
	log *log.Helper
	au  *biz.AuthUsecase
	mu  *biz.MediaUsecase
	lu  *biz.LiveUsecase
	du  *biz.DeviceUsecase
}

func NewMediaService(
	logger log.Logger,
	au *biz.AuthUsecase,
	mu *biz.MediaUsecase,
	lu *biz.LiveUsecase,
	du *biz.DeviceUsecase,
) *MediaService {
	return &MediaService{
		log: log.NewHelper(logger),
		au:  au,
		mu:  mu,
		lu:  lu,
		du:  du,
	}
}

func (s *MediaService) ListMedia(ctx context.Context, req *pb.ListMediaRequest) (*pb.ListMediaReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	t := []biz.MediaType{biz.MediaTypePhoto, biz.MediaTypeVideo}
	if req.WithVoyageRecord == 1 {
		t = t[:0]
	}
	query := &biz.MediaListQuery{
		BaseListQuery:    *biz.NewSimpleListQuery(req.Page, req.Size),
		ProjectInfo:      *biz.NewProjectInfo(av),
		Type:             t,
		WithVoyageRecord: req.WithVoyageRecord == 1,
	}
	if req.DeviceId > 0 {
		query.DeviceIds = append(query.DeviceIds, req.DeviceId)
	}
	if req.VoyageId > 0 {
		query.VoyageIds = append(query.VoyageIds, req.VoyageId)
	}
	if req.AirlineId > 0 {
		query.AirlineIds = append(query.AirlineIds, req.AirlineId)
	}
	if req.WaypointId > 0 {
		query.WaypointIds = append(query.WaypointIds, req.WaypointId)
	}
	if req.LenTypes != "" {
		query.LenTypes = lo.Filter(strings.Split(req.LenTypes, ","), func(it string, _ int) bool {
			_, ok := biz.MediaSubTypeValueMapper[it]
			return ok
		})
	}
	total, list, err := s.mu.ListProjectMedia(ctx, query)
	if err != nil {
		return nil, err
	}
	return &pb.ListMediaReply{
		Message: SuccessMessage,
		Data: &pb.ListMediaReply_Data{
			Total: float64(total),
			Page:  query.Page,
			Size:  query.Size,
			List: lo.Map(list, func(it *biz.Media, _ int) *pb.MediaItem {
				return pb.NewMediaItem(it)
			}),
		},
	}, nil
}

func (s *MediaService) ListLive(ctx context.Context, req *pb.LiveListRequest) (*pb.LiveListReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	query := &biz.LiveListQuery{
		BaseListQuery: *biz.NewSimpleListQuery(req.Page, req.Size),
		ProjectInfo:   *biz.NewProjectInfo(av),

		Statuses: []int32{1},
	}
	if req.DronesOnly {
		// m30t m30 camera 过滤fpv镜头
		query.SubDeviceIndexes = []string{"53-0-0", "52-0-0"}
	}
	ret, err := s.mu.ListLive(ctx, query)
	if err != nil {
		return nil, err
	}
	return &pb.LiveListReply{
		Message: SuccessMessage,
		Data: &pb.LiveListReply_Data{
			Total:   ret.Online,
			Offline: ret.Offline,
			Page:    req.Page,
			Size:    req.Size,
			List:    lo.Map(ret.List, func(it *biz.Live, _ int) *pb.Live { return pb.NewLive(it) }),
		},
	}, nil
}

func (s *MediaService) StartLiveCallback(ctx context.Context, req *pb.StartLiveCallbackRequest) (*pb.StartLiveCallbackReply, error) {
	live, err := s.lu.GetLive(ctx, req.LiveId)
	if err != nil {
		return nil, err
	}
	if err := s.lu.StartDockPushLive(ctx, live); err != nil {
		s.log.Errorf("StartLiveCallback.startFaild %d %+v", live.Id, err)
		dev, e := s.du.GetDevice(ctx, &biz.DetailQuery{Id: live.DeviceId})
		if e != nil {
			s.log.Errorf("StartLiveCallback.getDeviceFaild %d %+v", live.Id, err)
			return nil, e
		}
		if dev.Status == biz.StatusOffline {
			err = s.lu.CreateStartLiveDelayTask(ctx, dev, live, 120*time.Second)
		}
		return nil, err
	}
	return &pb.StartLiveCallbackReply{
		Message: SuccessMessage,
		Data: &pb.StartLiveCallbackReply_Data{
			Status: true,
		},
	}, nil
}

func (s *MediaService) GetChannelDetail(ctx context.Context, req *pb.ChannelDetailRequest) (*pb.ChannelDetailReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	ch, err := s.lu.GetChannelDetail(ctx, &biz.CameraChannelDetailQuery{
		ProjectInfo: *biz.NewProjectInfo(av),
		ChannelId:   req.Id,
	})
	if err != nil {
		if errors.IsNotFound(err) {
			return &pb.ChannelDetailReply{
				Code:    40003003,
				Message: "关联的通道不存在",
			}, nil
		}
		return nil, err
	}
	return &pb.ChannelDetailReply{
		Message: SuccessMessage,
		Data:    pb.NewVideoChannel(ch),
	}, nil
}

func (s *MediaService) ListChannels(ctx context.Context, req *pb.ChannelListRequest) (*pb.ChannelListReply, error) {
	av, err := s.au.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	query := &biz.CameraChannelListQuery{
		ProjectInfo: *biz.NewProjectInfo(av),
		BaseListQuery: biz.BaseListQuery{
			Page: req.Page,
			Size: req.Size,
		},
		Search: req.Search,
	}
	total, list, err := s.lu.ListChannels(ctx, query)
	if err != nil {
		return nil, err
	}
	return &pb.ChannelListReply{
		Message: SuccessMessage,
		Data: &pb.ChannelListReply_Data{
			Total: int32(total),
			Page:  req.Page,
			Size:  req.Size,
			List:  lo.Map(list, func(it *biz.CameraChannel, _ int) *pb.VideoChannel { return pb.NewVideoChannel(it) }),
		},
	}, nil
}
