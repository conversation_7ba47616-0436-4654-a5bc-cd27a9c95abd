package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	json "github.com/goccy/go-json"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	pb "gitlab.sensoro.com/skai/skai/api/devices/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
)

type DeviceService struct {
	pb.UnimplementedDeviceServer
	log *log.Helper
	au  *biz.AirlineUsecase
	cu  *biz.ConnectUsecase
	du  *biz.DeviceUsecase
	mu  *biz.MissionUsecase
	tu  *biz.ThingUsecase
	lu  *biz.LiveUsecase
}

func NewDeviceService(logger log.Logger, au *biz.AirlineUsecase, cu *biz.ConnectUsecase, du *biz.DeviceUsecase, mu *biz.MissionUsecase, tu *biz.ThingUsecase, lu *biz.LiveUsecase) *DeviceService {
	return &DeviceService{log: log.NewHelper(logger), au: au, cu: cu, du: du, mu: mu, tu: tu, lu: lu}
}

func (s *DeviceService) AddDevice(ctx context.Context, req *pb.AddRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	model := biz.DeviceModel(req.Model)
	device, err := s.du.CheckDevice(ctx, &biz.DeviceCheck{SourceSn: req.Sn, Model: &model})
	if err != nil {
		return nil, errors.BadRequest("40003004", "设备不存在")
	}
	if device.IsDeployed() {
		return nil, errors.BadRequest("40003004", "设备已被添加")
	}
	owner := device.Assign(projectInfo.TenantId, projectInfo.MerchantId)
	deployment := device.Deploy(s.pbToBizDeployment(req.Deployment, lo.ToPtr(time.Now())))
	if err := s.du.UpdateDevice(ctx, device.Id, lo.Assign(owner, deployment)); err != nil {
		return nil, err
	}
	// 创建操作记录入库
	payload := &biz.EmptyPayload{OperationTime: time.Now().UnixMilli()}
	if _, err := s.du.CreateOperation(ctx, &biz.Operation{
		AvatarId:   projectInfo.AvatarId,
		TenantId:   projectInfo.TenantId,
		MerchantId: projectInfo.MerchantId,
		SourceId:   device.Id,
		Type:       biz.TypeDeploy,
		Source:     biz.SourceDevice,
		Content:    utilities.StructToMap(payload),
		From:       "WEB",
		Message:    "success",
		Status:     biz.OperationStatusSuccess,
	}); err != nil {
		return nil, err
	}
	// 商户变更事件
	merchantMsg := &biz.MerchantChangedMsg{}
	merchantMsg.PaddingData(device, projectInfo.AvatarId)
	s.du.IntegrateDevice(ctx, merchantMsg)
	return &pb.CommonReply{
		Code:    SuccessCode,
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *DeviceService) ListDevice(ctx context.Context, req *pb.ListRequest) (*pb.ListReply, error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	total, devices, err := s.du.ListDevice(ctx, &biz.DeviceListQuery{
		ProjectInfo:   *projectInfo,
		Page:          int(req.Page),
		Size:          int(req.Size),
		Search:        req.Search,
		Type:          req.Type,
		Category:      (*biz.DeviceCategory)(req.Category),
		NetworkStatus: req.NetworkStatus,
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(devices, func(d *biz.Device, _ int) *pb.DeviceItem {
		return pb.BuildDeviceItem(d)
	})
	return &pb.ListReply{
		Message: SuccessMessage,
		Data: &pb.ListReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			List:  list,
			Total: total,
		},
	}, nil
}

func (s *DeviceService) GetDevice(ctx context.Context, req *pb.CommonRequest) (*pb.DeviceReply, error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	vo := pb.BuildDeviceItem(device)
	if vo.Deployment != nil && len(vo.Deployment.RelatedChIds) > 0 {
		_, chlist, err := s.lu.ListChannels(ctx, &biz.CameraChannelListQuery{
			ProjectInfo: *projectInfo,
			BaseListQuery: biz.BaseListQuery{
				Page: 1, Size: int32(len(vo.Deployment.RelatedChIds)),
			},
			Ids: vo.Deployment.RelatedChIds,
		})
		if err == nil {
			vo.Deployment.RelatedChs = lo.Map(chlist, func(ch *biz.CameraChannel, _ int) *pb.DeploymentRelatedCh {
				return &pb.DeploymentRelatedCh{
					Id:   ch.Id,
					Name: ch.Name,
				}
			})
			// 通道已删除
			if len(vo.Deployment.RelatedChs) == 0 {
				vo.Deployment.RelatedChIds = nil
			}
		}
	}
	return &pb.DeviceReply{
		Message: SuccessMessage,
		Data:    vo,
	}, nil
}

func (s *DeviceService) UpdateDevice(ctx context.Context, req *pb.UpdateRequest) (ret *pb.CommonReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	deployment := device.Deploy(s.pbToBizDeployment(req.Deployment, device.Deployment.Time))
	if err := s.du.UpdateDevice(ctx, device.Id, deployment); err != nil {
		return nil, err
	}
	// 部署变更事件
	deploymentMsg := &biz.DeploymentChangedMsg{}
	deploymentMsg.PaddingData(device, projectInfo.AvatarId)
	s.du.IntegrateDevice(ctx, deploymentMsg)
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *DeviceService) RemoveDevice(ctx context.Context, req *pb.CommonRequest) (*pb.CommonReply, error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	if err := s.du.UpdateDevice(ctx, device.Id, device.Recycle()); err != nil {
		return nil, err
	}
	// 商户变更事件
	merchantMsg := &biz.MerchantChangedMsg{}
	merchantMsg.PaddingData(device, projectInfo.AvatarId)
	s.du.IntegrateDevice(ctx, merchantMsg)
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *DeviceService) PropertyDevice(ctx context.Context, req *pb.PropertyRequest) (ret *pb.OperationReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 创建操作记录
	payload := &biz.SetPropertyPayload{Value: req.Value}
	operation, err := s.du.CreateOperation(ctx, &biz.Operation{
		MerchantId: device.MerchantId,
		AvatarId:   projectInfo.AvatarId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    biz.ServiceTimeout,
		Type:       biz.OperationType(req.Name),
		Source:     biz.SourceDevice,
		From:       "WEB",
		Status:     biz.OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return nil, err
	}
	// 下行设置属性命令
	// if err := s.tu.SendDockService(ctx, &biz.DockService{
	// 	Sn:         device.SourceSn,
	// 	DeviceId:   device.Id,
	// 	Identifier: biz.DockServiceIdentifierSetProperty,
	// 	ServiceId:  operation.Id,
	// 	Timeout:    biz.ServiceTimeout,
	// 	Payload:    payload,
	// }); err != nil {
	// 	return nil, err
	// }
	return &pb.OperationReply{
		Message: SuccessMessage,
		Data:    pb.BuildDeviceOperation(operation),
	}, nil
}

func (s *DeviceService) AlgflowDevice(ctx context.Context, req *pb.AlgflowRequest) (ret *pb.CommonReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	// 构造用户信息，操作人权限判断
	avatar := &biz.Avatar{Id: projectInfo.AvatarId, RoleType: 2}
	if !device.IsAllowed(avatar) {
		return nil, errors.BadRequest("40000003", "当前用户没有权限，请勿操作")
	}
	// 操作前提条件：开启人车流算法
	if device.PropData.Algorithm == nil || *device.PropData.Algorithm != biz.AlgNameAnalyser.String() {
		return nil, errors.BadRequest("40000003", "本次巡航未开启人车流分析算法，请勿操作")
	}
	// 更新人车流算法状态
	if err := s.du.UpdateDevice(ctx, device.Id, device.ExtraData.Algflow(req.AlgflowStatus)); err != nil {
		return nil, err
	}
	// 延迟1秒推送设备变更
	go func() {
		gctx, cancel := context.WithTimeout(context.Background(), biz.ServiceTimeout)
		defer cancel()
		pushData, _ := json.Marshal(device)
		s.du.PublishDevice(gctx, fmt.Sprintf("skai/merchant/%d/device/%d/change", device.MerchantId, device.Id), pushData, 1*time.Second)
	}()
	return &pb.CommonReply{
		Message: SuccessMessage,
		Data:    &pb.CommonReplyCommonData{Status: true},
	}, nil
}

func (s *DeviceService) DeviceEvents(ctx context.Context, req *pb.EventsRequest) (ret *pb.EventsReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	deployTime := time.Now()
	if device.Deployment.Time != nil {
		deployTime = *device.Deployment.Time
	}
	startTime, endTime := deployTime, deployTime
	if req.StartTime > deployTime.UnixMilli() {
		startTime = time.UnixMilli(req.StartTime)
	}
	if req.EndTime > deployTime.UnixMilli() {
		endTime = time.UnixMilli(req.EndTime)
	}
	types := make([]string, 0)
	if req.Types != "" {
		types = strings.Split(req.Types, ",")
	}
	levels := make([]int32, 0)
	if req.Levels != "" {
		levels = lo.Map(strings.Split(req.Levels, ","), func(level string, _ int) int32 {
			return int32(conv.Atoi(level))
		})
	}
	total, events, err := s.du.ListEvent(ctx, &biz.EventListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.Size),
		Sn:          device.Sn,
		StartTime:   startTime,
		EndTime:     endTime,
		Types:       types,
		Levels:      levels,
	})
	if err != nil {
		return nil, errors.BadRequest("40000002", "预警事件不存在")
	}
	list := lo.Map(events, func(a *biz.Event, _ int) *pb.EventsReplyListDataEvent {
		return pb.BuildDeviceEvent(a)
	})
	return &pb.EventsReply{
		Message: SuccessMessage,
		Data: &pb.EventsReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			Total: int32(total),
			List:  list,
		},
	}, nil
}

func (s *DeviceService) DeviceDatalogs(ctx context.Context, req *pb.DatalogsRequest) (ret *pb.DatalogsReply, err error) {
	var startTime, endTime time.Time
	if req.StartTime > 0 && req.EndTime > 0 {
		startTime = time.UnixMilli(req.StartTime)
		endTime = time.UnixMilli(req.EndTime)
	} else {
		endTime = time.Now()
		startTime = endTime.Add(-24 * time.Hour)
	}
	page := req.Page
	size := req.Size
	if page == 0 {
		page = 1
	}
	if size == 0 {
		size = 20
	}
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	total, datalogs, e := s.du.ListDatalog(ctx, &biz.DeviceDatalogListQuery{
		Id:     req.Id,
		Start:  startTime,
		End:    endTime,
		Page:   page,
		Size:   size,
		Device: device,
		Sort:   biz.Sort{Order: biz.ListSortOrderDesc},
	})
	if e != nil {
		err = e
		return
	}
	var list []*pb.DeviceDatalog
	if datalogs != nil {
		switch device.Model {
		case biz.ModelDJIDock, biz.ModelDJILite:
			list = lo.Map(datalogs.([]*biz.DockProperties), func(it *biz.DockProperties, _ int) *pb.DeviceDatalog {
				return pb.BuildDockDeviceDatalog(it)
			})
		case biz.ModelDJIPilot:
			list = lo.Map(datalogs.([]any), func(it any, _ int) *pb.DeviceDatalog {
				return pb.BuildControllerDeviceDatalog(it)
			})
		}
	}
	ret = &pb.DatalogsReply{
		Message: SuccessMessage,
		Data: &pb.DatalogsReplyListData{
			Page:  page,
			Size:  size,
			Total: int32(total),
			List:  list,
		},
	}
	return
}

func (s *DeviceService) DeviceSchedules(ctx context.Context, req *pb.SchedulesRequest) (ret *pb.SchedulesReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	mtype := biz.MissionType(req.Type)
	startTime := time.UnixMilli(req.StartTime)
	if req.StartTime < device.Deployment.Time.UnixMilli() {
		startTime = *device.Deployment.Time
	}
	endTime := time.UnixMilli(biz.MaxTimestamp)
	if mtype == biz.MissionTypeCustom && req.EndTime > 0 {
		endTime = time.UnixMilli(int64(req.EndTime))
	}
	excludeId := int64(0)
	if req.MissionId != nil {
		excludeId = *req.MissionId
	}
	schedules, err := s.mu.ScheduleMissions(ctx, &biz.MissionScheduleQuery{
		ProjectInfo: *projectInfo,
		DeviceId:    req.Id,
		StartTime:   startTime,
		EndTime:     endTime,
		ExcludeId:   excludeId,
	})
	if err != nil {
		return nil, err
	}
	list := lo.Map(schedules, func(i *biz.InverseTime, _ int) *pb.SchedulesReplyTimer {
		return &pb.SchedulesReplyTimer{Runday: int32(i.Runday), Moments: i.Moments}
	})
	return &pb.SchedulesReply{
		Message: SuccessMessage,
		Data: &pb.SchedulesReplyListData{
			Total: int32(len(list)),
			List:  list,
		},
	}, nil
}

func (s *DeviceService) DeviceOperations(ctx context.Context, req *pb.OperationsRequest) (ret *pb.OperationsReply, err error) {
	projectInfo, err := s.du.GetProjectInfo(ctx)
	if err != nil {
		return nil, errors.BadRequest("40000002", err.Error())
	}
	device, err := s.du.GetDevice(ctx, &biz.DetailQuery{Id: req.Id, ProjectInfo: *projectInfo})
	if err != nil {
		return nil, errors.BadRequest("40000002", "设备不存在")
	}
	endTime := time.Now()
	startTime := endTime
	if device.Deployment.Time != nil {
		startTime = *device.Deployment.Time
	}
	if req.StartTime > startTime.UnixMilli() {
		startTime = time.UnixMilli(req.StartTime)
	}
	if req.EndTime > startTime.UnixMilli() {
		endTime = time.UnixMilli(req.EndTime)
	}
	total, operations, err := s.du.ListOperation(ctx, &biz.OperationListQuery{
		ProjectInfo: *projectInfo,
		Page:        int(req.Page),
		Size:        int(req.Size),
		SourceId:    req.Id,
		Type:        (*biz.OperationType)(req.Type),
		StartTime:   startTime,
		EndTime:     endTime,
	})
	if err != nil {
		return nil, errors.BadRequest("40000002", "操作记录不存在")
	}
	list := lo.Map(operations, func(o *biz.Operation, _ int) *pb.Operation {
		return pb.BuildDeviceOperation(o)
	})
	return &pb.OperationsReply{
		Message: SuccessMessage,
		Data: &pb.OperationsReplyListData{
			Page:  req.Page,
			Size:  req.Size,
			Total: int32(total),
			List:  list,
		},
	}, nil
}

func (s *DeviceService) pbToBizDeployment(deployment *pb.Deployment, deployedTime *time.Time) *biz.Deployment {
	// 标签数组处理
	tags := lo.Ternary(len(deployment.Tags) > 0, deployment.Tags, []string{})
	// 联系人格式转换
	contacts := lo.Map(deployment.Contacts, func(v *pb.DeploymentContact, _ int) biz.Contact {
		return biz.Contact{Name: v.Name, Contact: v.Contact}
	})
	imageList := []string{deployment.Images.DeviceImg, deployment.Images.EnvImg}
	if deployment.Images.ShopImg != nil {
		imageList = append(imageList, *(deployment.Images.ShopImg))
	}
	// 构造部署信息
	return &biz.Deployment{
		Tags:      tags,
		Contacts:  contacts,
		Status:    true,
		ImageList: imageList,
		Time:      deployedTime,
		Name:      deployment.Name,
		Lnglat:    deployment.Lnglat,
		Altitude:  deployment.Altitude,
		Location:  deployment.Location,
		Images: biz.Images{
			DeviceImg: deployment.Images.DeviceImg,
			EnvImg:    deployment.Images.EnvImg,
			ShopImg:   deployment.Images.ShopImg,
		},
		RelatedChIds: deployment.RelatedChIds,
	}
}
