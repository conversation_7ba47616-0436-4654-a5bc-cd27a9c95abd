package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type Analyser struct {
	Id          int64  `json:"id,string,omitempty"`
	Name        string `json:"name,omitempty"`
	Status      int32  `json:"status,omitempty"`
	SourceUrl   string `json:"sourceUrl,omitempty"`
	TargetUrl   string `json:"targetUrl,omitempty"`
	CreatedTime int64  `json:"createdTime,omitempty"`
	Options     AnyMap `json:"options,omitempty"`
}

type AnalyserRepo interface {
	CreateAnalyser(ctx context.Context, body *Analyser) (*Analyser, error)
	GetAnalyser(ctx context.Context, id int64) (*Analyser, error)
	DeleteAnalyser(ctx context.Context, id int64) error
}

type AnalyserUsecase struct {
	repo AnalyserRepo
	log  *log.Helper
}

func NewAnalyserUsecase(logger log.Logger, repo AnalyserRepo) *AnalyserUsecase {
	return &AnalyserUsecase{repo: repo, log: log.NewHelper(logger)}
}

func (uc *AnalyserUsecase) CreateAnalyser(ctx context.Context, body *Analyser) (*Analyser, error) {
	return uc.repo.CreateAnalyser(ctx, body)
}

func (uc *AnalyserUsecase) GetAnalyser(ctx context.Context, id int64) (*Analyser, error) {
	return uc.repo.GetAnalyser(ctx, id)
}

func (uc *AnalyserUsecase) DeleteAnalyser(ctx context.Context, id int64) error {
	return uc.repo.DeleteAnalyser(ctx, id)
}
