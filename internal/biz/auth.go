package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
)

type Tenant = client.Tenant

type Project = client.Project

type Merchant = client.Merchant

type Avatar = client.Avatar

type Profile = client.Profile

type Permission = client.Permission

type SkaiAuthValue struct {
	AccountId       int64
	Nickname        string
	Username        string
	AvatarId        int64
	TenantId        int64
	ProjectId       int64
	Token           string
	Merchants       []*Merchant
	DefaultMerchant *Merchant
	// 仅 DJI Pilot 调用接口存在
	SN string
}

func (v *SkaiAuthValue) HasMerchantAccess(mId int64) bool {
	if len(v.Merchants) == 0 {
		return false
	}
	return lo.ContainsBy(v.Merchants, func(m *Merchant) bool {
		return m.Id == mId
	})
}

type ProjectResourcePair = client.ProjectResourcePair

type ProjectResourcePairRequest = client.ProjectResourcePairListRequest

type AuthorizeResourceOperation = client.ProjectResourceOperation

func (v *SkaiAuthValue) GetToken() string {
	return v.Token
}

type ProjectAuthorizedResorce struct {
	ProjectId   int64
	ResourceIds []int64
}

type ResourceAuthorizedProjects struct {
	ResourceId int64
	ProjectIds []int64
}

type SimpleProject struct {
	Id         int64
	Name       string
	AvatartIds []int64
}

type ResourceAuthorizedProjectsAndUsers struct {
	ResourceId int64
	Projects   []SimpleProject
}

type AvatarInProject = client.AvatarWithProject

type AuthRepo interface {
	GetCurrentAuthValue(ctx context.Context) (*SkaiAuthValue, error)
	GetAvatar(ctx context.Context, id int64) (*Avatar, error)
	GetProjectMerchants(ctx context.Context, id int64) ([]*Merchant, error)
	GetTenantAvatars(ctx context.Context, tenantId int64, avatartIds []int64) ([]*Avatar, error)
	GetTenantMerchants(ctx context.Context, tenantId int64) ([]*Merchant, error)
}

type AuthUsecase struct {
	repo AuthRepo
	log  *log.Helper
}

func NewAuthUsecase(logger log.Logger, repo AuthRepo) *AuthUsecase {
	return &AuthUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (uc *AuthUsecase) GetCurrentAuthValue(ctx context.Context) (*SkaiAuthValue, error) {
	return uc.repo.GetCurrentAuthValue(ctx)
}

func (uc *AuthUsecase) GetAvatar(ctx context.Context, id int64) (*Avatar, error) {
	return uc.repo.GetAvatar(ctx, id)
}

func (uc *AuthUsecase) GetProjectMerchants(ctx context.Context, id int64) ([]*Merchant, error) {
	return uc.repo.GetProjectMerchants(ctx, id)
}

func (uc *AuthUsecase) GetTenantAvatars(ctx context.Context, tenantId int64, avatarIds []int64) ([]*Avatar, error) {
	return uc.repo.GetTenantAvatars(ctx, tenantId, avatarIds)
}
