package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"golang.org/x/exp/slices"
)

type MissionType int

const (
	MissionTypeLong MissionType = iota + 1
	MissionTypeCustom
)

type MissionStatus int

const (
	StatusAwait MissionStatus = iota
	StatusDoing
	StatusPause
	StatusFinish
	StatusRemove
)

type AlgConfig struct {
	Type      AlgType   `json:"type,omitempty"`      // 算法类型
	Name      AlgName   `json:"name,omitempty"`      // 算法名称
	Interval  int32     `json:"interval,omitempty"`  // 抽帧间隔
	LensType  VideoType `json:"lensType,omitempty"`  // 镜头类型
	Threshold int32     `json:"threshold,omitempty"` // 算法阈值
}

type NoticeRule struct {
	Time        int32     `json:"time,omitempty"`
	Cascade     bool      `json:"cascade,omitempty"`
	Multilevel  int32     `json:"multilevel,omitempty"`
	NoticeTypes []string  `json:"noticeTypes,omitempty"`
	Conditions  []string  `json:"conditions,omitempty"`
	Contacts    []Contact `json:"contacts,omitempty"`
}

type ExecuteTime struct {
	Id      int64 `json:"-"`
	Moment  int32 `json:"moment,omitempty"`
	Rundays []int `json:"rundays,omitempty"`
}

type InverseTime struct {
	Runday  int     `json:"runday,omitempty"`
	Moments []int32 `json:"moments,omitempty"`
}

func (et *ExecuteTime) IsValid(its []*InverseTime) bool {
	timeMapper := lo.KeyBy(its, func(it *InverseTime) int { return it.Runday })
	moments := []int32{}
	for _, day := range et.Rundays {
		moments = lo.Uniq(append(moments, timeMapper[day].Moments...))
		slices.SortFunc(moments, func(a, b int32) int { return int(a - b) })
	}
	length := len(moments)
	// 1.时刻表为空，任意插入不冲突
	if length == 0 {
		return true
	}
	// 有序时刻表里找到moment所处位置(即首个大于moment元素之前)
	position, isHit := 0, 0
	for index, moment := range moments {
		if moment >= et.Moment {
			isHit = 1
			position = index
			break
		}
	}
	if position == 0 {
		// 2.时刻表First元素即比moment大，则moment必须小于First元素90min以上
		if isHit == 1 {
			return et.Moment+90*60 < moments[position]
		} else {
			// 3.时刻表Tail元素也比moment小，则moment必须大于Tail元素90min以上
			return et.Moment > moments[length-1]+90*60
		}
	} else {
		// 4.moment在时刻表中间位置，则必须小于Next元素90min以上，且大于Last元素90min以上
		return et.Moment+90*60 < moments[position] && et.Moment-90*60 > moments[position-1]
	}
}

type MissionListQuery struct {
	ProjectInfo
	Page      int
	Size      int
	Search    *string
	Ids       []int64
	AirlineId *int64
	DeviceIds []int64
	Status    *MissionStatus
	StartTime *time.Time
	EndTime   *time.Time
}

type MissionScheduleQuery struct {
	ProjectInfo
	DeviceId  int64
	ExcludeId int64
	StartTime time.Time
	EndTime   time.Time
}

type Mission struct {
	Id            int64
	Name          string
	Type          MissionType
	Status        MissionStatus
	TenantId      int64
	MerchantId    int64
	AvatarId      int64
	EditorId      int64
	DeviceId      int64
	AirlineId     int64
	Avatar        *Avatar
	Editor        *Avatar
	Device        *Device
	Airline       *Airline
	IsDeleted     bool
	StartTime     time.Time
	EndTime       time.Time
	Description   string
	AlarmCount    int32
	ExecuteCount  int32
	AlgConfigs    []*AlgConfig
	NoticeRules   []*NoticeRule
	ExecuteTimes  []*ExecuteTime
	LastExecution *Execution
	EditedTime    time.Time
	CreatedTime   time.Time
	UpdatedTime   time.Time
}

func (m *Mission) Flowing() MissionStatus {
	now := time.Now()
	status := m.Status
	// 开始时间比当前早，则重置为进行中
	if m.StartTime.Before(now) {
		status = StatusDoing
	}
	// 开始时间比当前晚，则重置为未开始
	if m.StartTime.After(now) {
		status = StatusAwait
	}
	// 结束时间比当前早，则重置为已结束
	if m.EndTime.Before(now) {
		status = StatusFinish
	}
	m.Status = status
	return status
}

func (m *Mission) Shift(status MissionStatus) AnyMap {
	m.Status = status
	return AnyMap{"status": status}
}

func (m *Mission) Release() AnyMap {
	m.ExecuteTimes = []*ExecuteTime{}
	times, _ := json.Marshal(m.ExecuteTimes)
	return AnyMap{"execute_times": times}
}

func (m *Mission) Change(body *Mission) AnyMap {
	m.Name = body.Name
	m.Type = body.Type
	m.EditorId = body.EditorId
	m.DeviceId = body.DeviceId
	m.AirlineId = body.AirlineId
	m.StartTime = body.StartTime
	m.EndTime = body.EndTime
	m.EditedTime = time.Now()
	m.Description = body.Description
	m.AlgConfigs = body.AlgConfigs
	m.NoticeRules = body.NoticeRules
	m.ExecuteTimes = body.ExecuteTimes
	m.Status = m.Flowing()
	algConfigs, _ := json.Marshal(m.AlgConfigs)
	noticeRules, _ := json.Marshal(m.NoticeRules)
	executeTimes, _ := json.Marshal(m.ExecuteTimes)
	data := AnyMap{
		"name":          m.Name,
		"type":          m.Type,
		"status":        m.Status,
		"editor_id":     m.EditorId,
		"device_id":     m.DeviceId,
		"airline_id":    m.AirlineId,
		"start_time":    m.StartTime,
		"end_time":      m.EndTime,
		"edited_time":   m.EditedTime,
		"alg_configs":   algConfigs,
		"notice_rules":  noticeRules,
		"execute_times": executeTimes,
		"description":   m.Description,
	}
	return data
}

type MissionRepo interface {
	CreateMission(ctx context.Context, body *Mission) (*Mission, error)
	UpdateMission(ctx context.Context, id int64, body AnyMap) error
	ListMissions(ctx context.Context, query *MissionListQuery) (int32, []*Mission, error)
	ScheduleMissions(ctx context.Context, query *MissionScheduleQuery) ([]*Mission, error)
	GetMission(ctx context.Context, query *DetailQuery) (*Mission, error)
	CountMission(ctx context.Context, query *CountQuery) ([]*CountRet, error)
	DeleteMission(ctx context.Context, id int64) error
	GetMissionByVoyageId(ctx context.Context, voyageId int64) (*Mission, error)
}

type MissionUsecase struct {
	aRepo AuthRepo
	dRepo DelayRepo
	mRepo MissionRepo
	xRepo AiEventRepo
	eRepo ExecutionRepo
	log   *log.Helper
}

func NewMissionUsecase(logger log.Logger, aRepo AuthRepo, dRepo DelayRepo, eRepo ExecutionRepo, xRepo AiEventRepo, mRepo MissionRepo) *MissionUsecase {
	return &MissionUsecase{aRepo: aRepo, dRepo: dRepo, eRepo: eRepo, xRepo: xRepo, mRepo: mRepo, log: log.NewHelper(logger)}
}

// 创建任务
func (uc *MissionUsecase) CreateMission(ctx context.Context, body *Mission) (*Mission, error) {
	mission, err := uc.mRepo.CreateMission(ctx, body)
	if err != nil {
		return nil, err
	}
	if err := uc.TriggerMission(ctx, mission); err != nil {
		return nil, err
	}
	if err := uc.ResetNextSchedule(ctx, body.Device); err != nil {
		return nil, err
	}
	return mission, nil
}

// 统计任务
func (uc *MissionUsecase) CountMission(ctx context.Context, query *CountQuery) ([]*CountRet, error) {
	return uc.mRepo.CountMission(ctx, query)
}

// 统计告警
func (uc *MissionUsecase) AlarmsMission(ctx context.Context, query *ExecutionTopNQuery) ([]*Mission, error) {
	tops, err := uc.eRepo.TopNAlarmCount(ctx, query)
	if err != nil {
		return nil, err
	}
	countMap := lo.KeyBy(lo.Filter(tops, func(cr *CountRet, _ int) bool { return cr.Count > 0 }), func(cr *CountRet) int64 { return cr.Radix })
	if len(countMap) == 0 {
		return make([]*Mission, 0), nil
	}
	missionIds := lo.Keys(countMap)
	_, list, err := uc.mRepo.ListMissions(ctx, &MissionListQuery{
		ProjectInfo: query.ProjectInfo,
		Page:        1,
		Size:        query.Size,
		Ids:         missionIds,
	})
	if err != nil {
		return nil, err
	}
	for _, mission := range list {
		mission.AlarmCount = countMap[mission.Id].Count
	}
	slices.SortFunc(list, func(a, b *Mission) int { return int(b.AlarmCount - a.AlarmCount) })
	return list, nil
}

// 查询任务
func (uc *MissionUsecase) ListMission(ctx context.Context, query *MissionListQuery) (int32, []*Mission, error) {
	total, list, err := uc.mRepo.ListMissions(ctx, query)
	if err != nil {
		return 0, nil, err
	}
	missionIds := lo.Map(list, func(m *Mission, _ int) int64 { return m.Id })
	if groupList, err := uc.eRepo.GroupExecution(ctx, &GroupQuery{
		ProjectInfo: query.ProjectInfo,
		ReferIds:    missionIds,
		GroupBy:     "mission_id",
		SumBy:       lo.ToPtr("alarm_count"),
		StartTime:   time.Now().Truncate(24 * time.Hour).Add(-6 * 24 * time.Hour),
	}); err == nil {
		groupMap := lo.KeyBy(groupList, func(c *GroupRet) int64 { return c.Radix })
		for _, mission := range list {
			if groupMap[mission.Id] != nil {
				mission.AlarmCount = groupMap[mission.Id].Total
				mission.ExecuteCount = groupMap[mission.Id].Count
			}
		}
	}
	return total, list, nil
}

// 任务详情
func (uc *MissionUsecase) GetMission(ctx context.Context, query *DetailQuery) (*Mission, error) {
	return uc.mRepo.GetMission(ctx, query)
}

// 更新任务
func (uc *MissionUsecase) UpdateMission(ctx context.Context, id int64, body AnyMap, mission *Mission) error {
	if err := uc.mRepo.UpdateMission(ctx, id, body); err != nil {
		return err
	}
	if mission == nil {
		return nil
	}
	if err := uc.TriggerMission(ctx, mission); err != nil {
		return err
	}
	if err := uc.ResetNextSchedule(ctx, mission.Device); err != nil {
		return err
	}
	return nil
}

// 删除任务
func (uc *MissionUsecase) DeleteMission(ctx context.Context, id int64) error {
	// 删除延时触发启动和结束(自定义类型)的任务
	uc.dRepo.DeleteTask(ctx, fmt.Sprintf("%s:MISSION:DOING:%d", SkaiDelayKeyPrefix, id))
	uc.dRepo.DeleteTask(ctx, fmt.Sprintf("%s:MISSION:FINISH:%d", SkaiDelayKeyPrefix, id))
	return uc.mRepo.DeleteMission(ctx, id)
}

// 设置任务启停触发器
func (uc *MissionUsecase) TriggerMission(ctx context.Context, mission *Mission) error {
	if mission == nil {
		return nil
	}
	// 如果任务未开始，则创建延时触发任务启动(浮动15秒)
	if mission.Status == StatusAwait {
		if err := uc.dRepo.CreateTask(ctx, &DelayTask{
			Times:    1,
			Source:   "SKAI-MISSION",
			Delay:    time.Until(mission.StartTime) + 15*time.Second,
			Key:      fmt.Sprintf("%s:MISSION:DOING:%d", SkaiDelayKeyPrefix, mission.Id),
			Callback: fmt.Sprintf("/internal/v1/missions/%d/status", mission.Id),
			Payload:  AnyMap{"action": "doing"},
		}); err != nil {
			return err
		}
	}
	// Redis的Expire值用32位表示，最大值是2^31-1，大约68年
	// 如果自定义任务，则创建延时任务触发结束(浮动15秒)，结束时间超过50年不处理
	if mission.Type == MissionTypeCustom && time.Until(mission.EndTime) < 50*366*24*time.Hour {
		if err := uc.dRepo.CreateTask(ctx, &DelayTask{
			Times:    1,
			Source:   "SKAI-MISSION",
			Delay:    time.Until(mission.EndTime) + 15*time.Second,
			Key:      fmt.Sprintf("%s:MISSION:FINISH:%d", SkaiDelayKeyPrefix, mission.Id),
			Callback: fmt.Sprintf("/internal/v1/missions/%d/status", mission.Id),
			Payload:  AnyMap{"action": "finish"},
		}); err != nil {
			return err
		}
	}
	return nil
}

// 重置设备下次执行计时
func (uc *MissionUsecase) ResetNextSchedule(ctx context.Context, device *Device) error {
	// 现在时间和当天秒数
	now := time.Now().In(TimeLocation)
	seconds := int32(now.Hour()*3600 + now.Minute()*60 + now.Second())
	// 设置默认5min后重试，任务为空
	moment := int32(300)
	missionId := int64(0)
	// 查询设备所有执行计划
	if list, err := uc.mRepo.ScheduleMissions(ctx, &MissionScheduleQuery{
		DeviceId:  device.Id,
		StartTime: *device.Deployment.Time,
		EndTime:   time.UnixMilli(MaxTimestamp),
	}); err == nil {
		// 当天周几，周日是0需要二次赋值
		weekday := now.Weekday()
		weekday = lo.Ternary(weekday == 0, 7, weekday)
		// 如果当天无调度计划，下次默认执行时间为后一天0点(自带5min浮动值)
		moment += 24*60*60 - seconds
		// 合并所有执行计划并排序
		executeTimes := make([]*ExecuteTime, 0)
		for _, mission := range list {
			for _, etime := range mission.ExecuteTimes {
				etime.Id = mission.Id
				executeTimes = append(executeTimes, etime)
			}
		}
		slices.SortFunc(executeTimes, func(a, b *ExecuteTime) int { return int(a.Moment - b.Moment) })
		// 如果当天有下次调度计划(即首个大于now的元素)，则重置时间
		for _, etime := range executeTimes {
			for _, runday := range etime.Rundays {
				if runday == int(weekday) && etime.Moment > seconds {
					missionId = etime.Id
					moment = etime.Moment - seconds
					break
				}
			}
			// 已找到符合条件数据，则跳出循环
			if missionId > 0 {
				break
			}
		}
	}
	if err := uc.dRepo.CreateTask(ctx, &DelayTask{
		Times:    1,
		Source:   "SKAI-EXECUTION",
		Delay:    time.Duration(moment) * time.Second,
		Key:      fmt.Sprintf("%s:DEVICE:%d:EXECUTION", SkaiDelayKeyPrefix, device.Id),
		Callback: fmt.Sprintf("/internal/v1/devices/%d/executions", device.Id),
		Payload:  AnyMap{"sourceId": conv.Itoa(missionId)},
	}); err != nil {
		return err
	}
	return nil
}

// 获取设备执行计划
func (uc *MissionUsecase) ScheduleMissions(ctx context.Context, query *MissionScheduleQuery) ([]*InverseTime, error) {
	list, err := uc.mRepo.ScheduleMissions(ctx, query)
	if err != nil {
		return nil, err
	}
	executeTimes := make([]*ExecuteTime, 0)
	for _, mission := range list {
		executeTimes = append(executeTimes, mission.ExecuteTimes...)
	}
	timer := map[int][]int32{}
	for _, et := range executeTimes {
		for _, rd := range et.Rundays {
			moments := lo.Uniq(append(timer[rd], et.Moment))
			slices.SortFunc(moments, func(a, b int32) int { return int(a - b) })
			timer[rd] = moments
		}
	}
	inverseTimes := make([]*InverseTime, 0)
	for day := 1; day <= 7; day++ {
		moments := make([]int32, 0)
		if list, ok := timer[day]; ok {
			moments = list
		}
		inverseTimes = append(inverseTimes, &InverseTime{Runday: day, Moments: moments})
	}
	return inverseTimes, nil
}

// 查询项目信息
func (uc *MissionUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}

// 查询用户
func (uc *MissionUsecase) GetAvatar(ctx context.Context, id int64) (*Avatar, error) {
	return uc.aRepo.GetAvatar(ctx, id)
}

// 创建执行记录
func (uc *MissionUsecase) CreateExecution(ctx context.Context, op *Execution) (*Execution, error) {
	return uc.eRepo.CreateExecution(ctx, op)
}

// 获取执行记录
func (uc *MissionUsecase) GetExecution(ctx context.Context, query *ExecutionQuery) (*Execution, error) {
	return uc.eRepo.GetExecution(ctx, query)
}

// 更新执行记录
func (uc *MissionUsecase) UpdateExecution(ctx context.Context, opId int64, body AnyMap) error {
	return uc.eRepo.UpdateExecution(ctx, opId, body)
}

// 执行记录列表
func (uc *MissionUsecase) ListExecution(ctx context.Context, query *ExecutionListQuery) (int32, []*Execution, error) {
	return uc.eRepo.ListExecutions(ctx, query)
}
