package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
)

type Annotation struct {
	Id          int64
	CreatedTime time.Time
	UpdatedTime time.Time
	TenantId    int64
	MerchantId  int64
	// 标注人id
	AvatarId    int64
	Media       []MediaWithArea
	VoyageId    int64
	SubjectId   int64
	Type        AiEventType
	Description string
	Subject     *Subject
}

func (a *Annotation) Clone() *Annotation {
	return &Annotation{
		Id:          a.Id,
		CreatedTime: a.CreatedTime,
		UpdatedTime: a.UpdatedTime,
		TenantId:    a.TenantId,
		MerchantId:  a.MerchantId,
		AvatarId:    a.AvatarId,
		Media:       a.Media,
		VoyageId:    a.VoyageId,
		SubjectId:   a.SubjectId,
		Type:        a.Type,
		Subject:     a.Subject,
		Description: a.Description,
	}
}

func (a *Annotation) makeEventCapture(sr SimpleStorageRepo) []*client.EventCaptureInfo {
	return lo.Map(a.Media, func(m MediaWithArea, _ int) *client.EventCaptureInfo {
		u, _ := sr.GetSignedObjectAddr(context.TODO(), &StorageObject{
			Key:    m.Item.Key,
			Bucket: m.Item.GetStorageBucket(),
		}, 23*time.Hour)
		tu := u
		if tumb, ok := m.Item.GetThumbnail(); ok {
			tu, _ = sr.GetSignedObjectAddr(context.TODO(), &StorageObject{
				Key:    tumb,
				Bucket: m.Item.GetStorageBucket(),
			}, 23*time.Hour)
		}
		return &client.EventCaptureInfo{
			CaptureTime: m.Item.CreatedTime.UnixMilli(),
			ImageUrl:    u.ObjectUrl,
			SceneUrl:    tu.ObjectUrl,
			Objects: lo.Map(m.ObjectAreas, func(oa ObjectArea, _ int) *client.EventCaptureObject {
				x, y, w, h := oa.AsRectangle()
				return &client.EventCaptureObject{
					Score:  100,
					X:      x,
					Y:      y,
					Width:  w,
					Height: h,
				}
			}),
		}
	})
}

func (a *Annotation) GenerateEvent(sr SimpleStorageRepo, dev *Device, voyage *Voyage, mission *Mission) *LinsMetaEvent {
	cps := a.makeEventCapture(sr)
	lnglat := dev.Deployment.Lnglat
	if m := a.Media[0]; m.Item != nil && m.Item.Meta != nil {
		lnglat = m.Item.Meta.ShootLnglat
	}
	le := &LinsMetaEvent{
		Type: a.Type.String(),
		Entry: client.Entry{
			CustomId:        conv.Itoa(a.Id) + ":" + conv.Itoa(a.SubjectId),
			AlarmName:       a.Type.Name() + "-" + dev.Deployment.Name,
			AlarmLnglat:     lo.Map(lnglat, func(p float64, _ int) float32 { return float32(p) }),
			AlarmLocation:   dev.Deployment.Location,
			AlarmMerchantId: conv.Itoa(dev.MerchantId),
			AlarmTenantId:   conv.Itoa(dev.TenantId),
			OccurredTime: lo.TernaryF(a.CreatedTime.IsZero(), func() int64 {
				return time.Now().UnixMilli()
			}, func() int64 {
				return a.CreatedTime.UnixMilli()
			}),
			Device: &client.EventDeviceInfo{
				SourceId:   conv.Itoa(dev.Id),
				MerchantId: conv.Itoa(dev.MerchantId),
				TenantId:   conv.Itoa(dev.TenantId),
				Subsystem:  "SKAI",
			},
			DroneAlgoEvent: map[string]interface{}{
				"subjectId": conv.Itoa(a.SubjectId),
				"captures":  cps,
				"eventDesc": a.Description,
			},
			Caputre: cps[0],
		},
	}
	if mission != nil {
		if len(mission.AlgConfigs) > 0 {
			le.Entry.DroneAlgoEvent["threshold"] = mission.AlgConfigs[0].Threshold
		}
		le.Entry.Task = &client.EventTaskInfo{
			Id:   conv.Itoa(mission.Id),
			Name: mission.Name,
			NoticeRules: lo.Map(a.Subject.NoticeRules, func(nr *NoticeRule, _ int) *client.NoticeRule {
				return &client.NoticeRule{
					Time:        nr.Time,
					Cascade:     nr.Cascade,
					Multilevel:  nr.Multilevel,
					NoticeTypes: nr.NoticeTypes,
					Conditions:  nr.Conditions,
					Contacts: lo.Map(nr.Contacts, func(bc Contact, _ int) client.Contact {
						return client.Contact{Name: bc.Name, Contact: bc.Contact}
					}),
				}
			}),
		}
	} else {
		le.Entry.Task = &client.EventTaskInfo{
			Id:   conv.Itoa(voyage.Id),
			Name: voyage.Name,
			NoticeRules: lo.Map(a.Subject.NoticeRules, func(nr *NoticeRule, _ int) *client.NoticeRule {
				return &client.NoticeRule{
					Time:        nr.Time,
					Cascade:     nr.Cascade,
					Multilevel:  nr.Multilevel,
					NoticeTypes: nr.NoticeTypes,
					Conditions:  nr.Conditions,
					Contacts: lo.Map(nr.Contacts, func(bc Contact, _ int) client.Contact {
						return client.Contact{Name: bc.Name, Contact: bc.Contact}
					}),
				}
			}),
		}
	}
	return le
}

type MediaWithArea struct {
	Id          int64        `json:"id,string"`
	ObjectAreas []ObjectArea `json:"objectArea"`
	Item        *Media       `json:"-"`
}
type ObjectArea struct {
	Points []Point `json:"points"`
}

func (a *ObjectArea) AsRectangle() (x, y, width, height int32) {
	if len(a.Points) != 4 {
		return 0, 0, 0, 0
	}
	return a.Points[0].X, a.Points[0].Y, a.Points[2].X - a.Points[0].X, a.Points[2].Y - a.Points[0].Y
}

type Point struct {
	X int32 `json:"x"`
	Y int32 `json:"y"`
}

type AnnotationListQuery struct {
	BaseListQuery
	ProjectInfo
	Types []AiEventType
	// 0:未标注，1：已标注
	States []int32
}

var EmtpyAnnotation = &Annotation{
	Id:   1,
	Type: AiEventTypeNone,
}

type MediaWithAnnotation struct {
	Media
	ObjectAreas []ObjectArea
	Annotation  *Annotation
	Voyage      *Voyage
	Airline     *Airline
}

type AnnotationRepo interface {
	Create(ctx context.Context, a *Annotation) error
	GetOne(ctx context.Context, id int64) (*Annotation, error)
	GetByMediaId(ctx context.Context, mediaId int64) (*Annotation, error)
	ListMediaAnnotation(ctx context.Context, query *AnnotationListQuery) (int64, []*MediaWithAnnotation, error)
}

type AnnotationUsecase struct {
	log         *log.Helper
	annRepo     AnnotationRepo
	subjectRepo SubjectRepo
	aeRepo      AiEventRepo
	mRepo       MediaRepo
	devRepo     DeviceRepo
	vygRepo     VoyageRepo
	msRepo      MissionRepo
	propRepo    PropertyRepo
	stRepo      SimpleStorageRepo
}

func NewAnnotationUsecase(logger log.Logger,
	annRepo AnnotationRepo, subjectRepo SubjectRepo,
	aeRepo AiEventRepo, mRepo MediaRepo,
	devRepo DeviceRepo,
	vygRepo VoyageRepo,
	msRepo MissionRepo,
	stRepo SimpleStorageRepo,
	propRepo PropertyRepo,
) *AnnotationUsecase {
	return &AnnotationUsecase{
		log:         log.NewHelper(logger),
		annRepo:     annRepo,
		subjectRepo: subjectRepo,
		aeRepo:      aeRepo,
		mRepo:       mRepo,
		devRepo:     devRepo,
		vygRepo:     vygRepo,
		msRepo:      msRepo,
		stRepo:      stRepo,
		propRepo:    propRepo,
	}
}

type AnnotationCreateReq struct {
	ProjectInfo
	Av          *SkaiAuthValue
	SubjectId   int64
	Media       []MediaWithArea
	Description string
}

func (uc *AnnotationUsecase) ListMediaAnnotation(ctx context.Context, query *AnnotationListQuery) (int64, []*MediaWithAnnotation, error) {
	total, list, err := uc.annRepo.ListMediaAnnotation(ctx, query)
	if err != nil {
		return 0, nil, err
	}
	const signTTL = 30 * time.Minute
	signedList := lo.Map(list, func(mwa *MediaWithAnnotation, _ int) *MediaWithAnnotation {
		signMediaURL(uc.stRepo, &mwa.Media, signTTL)
		return mwa
	})
	return total, signedList, nil
}

func (uc *AnnotationUsecase) GetByMediaId(ctx context.Context, id int64) (*Annotation, error) {
	an, err := uc.annRepo.GetByMediaId(ctx, id)
	if err != nil {
		return nil, err
	}
	if an.SubjectId > 1 {
		subject, err := uc.subjectRepo.GetSubject(ctx, &DetailQuery{Id: an.SubjectId, Unscoped: true})
		if err != nil {
			return nil, err
		}
		an.Subject = subject
	}
	const signTTL = 30 * time.Minute
	an.Media = lo.Map(an.Media, func(it MediaWithArea, _ int) MediaWithArea {
		me := it.Item
		if me != nil {
			signMediaURL(uc.stRepo, me, signTTL)
		}
		return MediaWithArea{
			Id:          it.Id,
			ObjectAreas: it.ObjectAreas,
			Item:        me,
		}
	})

	return an, nil
}

func (uc *AnnotationUsecase) Create(ctx context.Context, cr *AnnotationCreateReq) (*Annotation, error) {
	mIds := lo.Map(cr.Media, func(m MediaWithArea, _ int) int64 {
		return m.Id
	})
	_, media, err := uc.mRepo.List(ctx, &MediaListQuery{
		BaseListQuery: *NewSimpleListQuery(1, 20),
		ProjectInfo:   cr.ProjectInfo,
		Ids:           mIds,
	})
	if err != nil {
		return nil, err
	}
	if len(media) == 0 || len(media) != len(mIds) {
		return nil, NewBadRequestError("media not found", nil)
	}
	voyageId := media[0].VoyageId
	devId := media[0].DeviceId
	for i, m := range media {
		if m.Type != MediaTypePhoto {
			return nil, NewBadRequestError("media type is not photo", nil)
		}
		if m.AnnotationId > 0 {
			return nil, errors.BadRequest("media already has annotation", "图片已审核").WithMetadata(map[string]string{"mediaId": conv.Itoa(m.Id)})
		}
		if i > 0 {
			if voyageId != m.VoyageId {
				return nil, NewBadRequestError("media voyageId is not same", nil)
			}
			if devId != m.DeviceId {
				return nil, NewBadRequestError("media deviceId is not same", nil)
			}
		}
	}
	// 标记图片无事件
	if cr.SubjectId == 0 {
		now := time.Now()
		for _, m := range media {
			m.AnnotationId = EmtpyAnnotation.Id
			if m.Extra == nil {
				m.Extra = make(map[string]interface{})
			}
			m.Extra["avatarId"] = cr.Av.AvatarId
			m.Extra["annTime"] = now.Unix()
			if err := uc.mRepo.Update(ctx, m); err != nil {
				return nil, err
			}
		}
		ann := EmtpyAnnotation.Clone()
		ann.CreatedTime = now
		ann.MerchantId = media[0].MerchantId
		ann.Media = lo.Map(cr.Media, func(it MediaWithArea, _ int) MediaWithArea {
			mItem, _ := lo.Find(media, func(m *Media) bool { return m.Id == it.Id })
			return MediaWithArea{
				Id:          it.Id,
				ObjectAreas: it.ObjectAreas,
				Item:        mItem,
			}
		})
		return ann, nil
	}
	subject, err := uc.subjectRepo.GetSubject(ctx, &DetailQuery{
		ProjectInfo: cr.ProjectInfo,
		Id:          cr.SubjectId,
	})
	if err != nil {
		return nil, err
	}
	if len(subject.NoticeRules) == 0 {
		return nil, errors.BadRequest("createAnnotation.subject.NoticeRules", "生成事件失败，请先至预警主题配置通知规则")
	}
	ann := &Annotation{
		TenantId:    cr.Av.TenantId,
		MerchantId:  media[0].MerchantId,
		AvatarId:    cr.Av.AvatarId,
		Media:       cr.Media,
		VoyageId:    media[0].VoyageId,
		SubjectId:   cr.SubjectId,
		Type:        subject.EventType,
		Subject:     subject,
		Description: cr.Description,
	}
	dev, err := uc.devRepo.GetDevice(ctx, &DetailQuery{
		ProjectInfo: cr.ProjectInfo,
		Id:          devId,
	})
	if err != nil {
		return nil, err
	}
	voyage, err := uc.vygRepo.GetVoyage(ctx, &DetailQuery{
		ProjectInfo: cr.ProjectInfo,
		Id:          voyageId,
	})
	if err != nil {
		return nil, err
	}
	var mission *Mission
	mission, err = uc.msRepo.GetMissionByVoyageId(ctx, voyageId)
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	if err := uc.annRepo.Create(ctx, ann); err != nil {
		return nil, err
	}
	ann.Media = lo.Map(cr.Media, func(it MediaWithArea, i int) MediaWithArea {
		if m, ok := lo.Find(media, func(m *Media) bool { return m.Id == it.Id }); ok {
			m.AnnotationId = ann.Id
			uc.mRepo.Update(ctx, m)
			return MediaWithArea{
				Id:          m.Id,
				Item:        m,
				ObjectAreas: it.ObjectAreas,
			}
		}
		return it
	})
	ri := subject.ReportIncr(1)
	pi := subject.PendingIncr(1)
	uc.subjectRepo.UpdateSubject(ctx, subject.Id, lo.Assign(ri, pi))
	ae := ann.GenerateEvent(uc.stRepo, dev, voyage, mission)
	camera := dev.GetGimbal()
	if camera == nil {
		camera = &DockSubdevice{
			Index: "53-0-0",
		}
	}
	if _, list, err := uc.propRepo.ListCameraProperties(ctx, &CameraPropListQuery{
		PropListQuery: PropListQuery{
			Sn:           dev.SourceSn,
			Start:        media[0].CreatedTime.Add(-5 * time.Second),
			End:          media[0].CreatedTime.Add(5 * time.Second),
			Page:         1,
			Size:         10,
			disableCount: true,
		},
		CameraIndex: camera.Index,
	}); err == nil && len(list) > 0 {
		// 取时间差最小的一条数据
		diffs := lo.Map(list, func(it *DrondCameraProperty, _ int) time.Duration {
			d := it.Timestamp.Sub(media[0].CreatedTime)
			if d < 0 {
				return -d
			}
			return d
		})
		p := list[lo.IndexOf(diffs, lo.Min(diffs))]
		if p.State != nil && p.State.MeasureErrState == 0 {
			if p.State.MeasureTargetLongitude != 0 && p.State.MeasureTargetLatitude != 0 {
				ae.Entry.AlarmLnglat = []float32{float32(p.State.MeasureTargetLongitude), float32(p.State.MeasureTargetLatitude)}
			}
		}
	}
	if err := uc.aeRepo.PushAIEvent(ctx, ae); err != nil {
		uc.log.WithContext(ctx).Errorf("annotation.PushAIEvent %+v failed %v", ann, err)
		return nil, err
	}
	return ann, nil
}
