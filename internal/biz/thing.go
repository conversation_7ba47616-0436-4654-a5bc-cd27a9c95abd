package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"path"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gorm.io/datatypes"

	"golang.org/x/sync/errgroup"
)

type PacketPublisher interface {
	Publish(ctx context.Context, topic string, qos byte, data []byte) (PublishResult, error)
}

type ThingUsecase struct {
	log          *log.Helper
	authRepo     AuthRepo
	devRepo      DeviceRepo
	eventRepo    DockEventRepo
	propRepo     PropertyRepo
	connRepo     ConnectRepo
	mqttRepo     MQTTRepo
	voyageRepo   VoyageRepo
	airlineRepo  AirlineRepo
	waypointRepo WaypointRepo
	s3Repo       SimpleStorageRepo
}

func NewThingUsecase(
	logger log.Logger,
	authRepo AuthRepo,
	devRepo <PERSON>ceRepo,
	propRepo PropertyRepo,
	connRepo ConnectRepo,
	mqttRepo MQTTRepo,
	eventRepo DockEventRepo,
	vRepo VoyageRepo,
	s3Repo SimpleStorageRepo,
	aRepo AirlineRepo,
	wRepo WaypointRepo,
) *ThingUsecase {
	return &ThingUsecase{
		log:          log.NewHelper(logger),
		authRepo:     authRepo,
		devRepo:      devRepo,
		eventRepo:    eventRepo,
		propRepo:     propRepo,
		connRepo:     connRepo,
		mqttRepo:     mqttRepo,
		voyageRepo:   vRepo,
		airlineRepo:  aRepo,
		s3Repo:       s3Repo,
		waypointRepo: wRepo,
	}
}

func (u *ThingUsecase) GetDeviceAccessConfig(ctx context.Context, sourceSn string) (AnyMap, error) {
	device, err := u.devRepo.CheckDevice(ctx, &DeviceCheck{
		SourceSn: sourceSn,
	})
	if err != nil {
		return nil, err
	}
	return u.devRepo.GetDeviceAccessConfig(ctx, device)
}

func (u *ThingUsecase) GetDevice(ctx context.Context, sourceSn string) (*Device, error) {
	return u.devRepo.CheckDevice(ctx, &DeviceCheck{
		SourceSn: sourceSn,
	})
}

func (u *ThingUsecase) PushDockProperties(ctx context.Context, dp *DockProperties) error {
	eg := &errgroup.Group{}
	eg.Go(utilities.NewRecovedGOFunc(func() error {
		return u.propRepo.RecordDockProperties(ctx, dp)
	}))
	eg.Go(utilities.NewRecovedGOFunc(func() error {
		return u.connRepo.PushDockProperties(ctx, dp)
	}))
	return eg.Wait()
}

func (u *ThingUsecase) PushControllerProperties(ctx context.Context, cp *RemoteControllerProperties) error {
	eg := &errgroup.Group{}
	eg.Go(utilities.NewRecovedGOFunc(func() error {
		return u.propRepo.RecordControllerProperties(ctx, cp)
	}))
	eg.Go(utilities.NewRecovedGOFunc(func() error {
		return u.connRepo.PushControllerProperties(ctx, cp)
	}))
	return eg.Wait()
}
func (u *ThingUsecase) PushGatewayDroneProperties(ctx context.Context, dp *DroneProperties) error {
	eg := &errgroup.Group{}
	eg.Go(utilities.NewRecovedGOFunc(func() error {
		if err := u.propRepo.RecordDockDroneProperties(ctx, dp); err != nil {
			return err
		}
		return u.propRepo.RecordDroneCameraProperties(ctx, dp)
	}))
	eg.Go(utilities.NewRecovedGOFunc(func() error {
		return u.connRepo.PushDroneProperties(ctx, dp)
	}))
	return eg.Wait()
}

func (u *ThingUsecase) PushEvent(ctx context.Context, et ThingEvent, event any) (*EventUpReply, error) {
	go func() {
		f := utilities.NewRecovedGOFunc(func() error {
			return u.eventRepo.RecordDockEvent(ctx, et, event)
		})
		if err := f(); err != nil {
			u.log.Error("PushEvent.recordEvent failed: ", err)
		}
	}()
	if et.Type == ThingModelEventTypeOther {
		return nil, nil
	}
	return u.connRepo.PushEvent(ctx, et.Type, event)
}

func (u *ThingUsecase) PushServiceReply(ctx context.Context, dev *Device, r *DockServiceReply) error {
	ok, err := u.connRepo.RunDownlinkNextStep(ctx, dev, u.mqttRepo, r)
	if err != nil {
		return err
	}
	// 确实有下部动作执行，不返回service reply给上层
	if ok {
		u.log.Infof("device %s sevice %s id %s runing next step ", dev.Sn, r.Identifier, r.Id)
		return nil
	}
	return u.connRepo.PushDockServiceReply(ctx, r)
}

func (u *ThingUsecase) SendDockService(ctx context.Context, s *DockService) error {
	dev, err := u.devRepo.GetDevice(ctx, &DetailQuery{
		Id: s.DeviceId,
	})
	if err != nil {
		return err
	}
	return u.connRepo.SendDownlink(ctx, dev, u.mqttRepo, s.Identifier.String(), s)
}

func (u *ThingUsecase) GetDeviceMerchant(ctx context.Context, dev *Device) (*Merchant, error) {
	ms, err := u.authRepo.GetTenantMerchants(ctx, dev.TenantId)
	if err != nil {
		return nil, err
	}
	m, ok := lo.Find(ms, func(it *Merchant) bool { return it.Id == dev.MerchantId })
	if !ok {
		return nil, errors.NotFound("GetDeviceMerchant.merchantNotFount", "资源不存在")
	}
	return m, nil
}

type VoyageMediaUplinkConfig struct {
	Credentials StorageCredential
	KeyPrefix   string
}

func (u *ThingUsecase) GetVoyageMediaUploadConfig(ctx context.Context, dev *Device) (*VoyageMediaUplinkConfig, error) {
	// v, err := u.voyageRepo.LastVoyage(ctx, &VoyageLastQuery{DeviceId: dev.Id, IsFlown: lo.ToPtr(false)})
	// if err != nil {
	// 	u.log.Errorf("GetVoyageMediaUploadConfig.GetDeviceLastVoyage for dev %d failed: %v", dev.Id, err)
	// 	return nil, err
	// }
	sc, err := u.s3Repo.GetSessionToken(ctx, SessionTokenOption{
		TTL: 2 * time.Hour,
	})
	if err != nil {
		u.log.Errorf("GetVoyageMediaUploadConfig.GetSessionToken for dev %d failed: %v", dev.Id, err)
		return nil, err
	}
	return &VoyageMediaUplinkConfig{
		Credentials: sc,
		KeyPrefix:   u.newObjectKeyPrefixForVoyage(dev),
	}, nil
}

func (u *ThingUsecase) GetDeviceLastVoyage(ctx context.Context, dev *Device) (*Voyage, error) {
	return u.voyageRepo.LastVoyage(ctx, &VoyageLastQuery{DeviceId: dev.Id})
}

func (u *ThingUsecase) newObjectKeyPrefixForVoyage(dev *Device) string {
	return fmt.Sprintf("voyages/%s/%s", dev.Sn, time.Now().Format("06-01"))
}

func (u *ThingUsecase) CreateLiveKeyForSubDevice(dev *Device, sub DockSubdevice, videoIndex string) string {
	// if sub.Sn == dev.SourceSn {
	// 	return fmt.Sprintf("%s/0", sub.Sn)
	// }
	return strings.Join([]string{sub.Sn, sub.Index, videoIndex}, "/")
}

func (u *ThingUsecase) GetVoyageByFlight(ctx context.Context, flightId string, withAirlineInfo bool) (*Voyage, error) {
	v, err := u.voyageRepo.GetFlight(ctx, flightId)
	if err != nil {
		return nil, err
	}
	if withAirlineInfo {
		if a, err := u.airlineRepo.GetAirline(ctx, &AirlineQuery{Id: v.AirlineId}); err == nil {
			v.Airline = a
		} else {
			return nil, err
		}
	}
	return v, nil
}

func (u *ThingUsecase) GetAirlineWaypoints(ctx context.Context, airlineId int64) ([]*Waypoint, error) {
	return u.waypointRepo.AirlineWaypoints(ctx, airlineId)
}

func (u *ThingUsecase) GetDownlinkState(ctx context.Context, model DeviceModel, downId string) (any, error) {
	return u.connRepo.GetDownlinkState(ctx, model, downId)
}

func (u *ThingUsecase) UpdateVoyageMediaTotal(ctx context.Context, id int64, total int32) error {
	return u.voyageRepo.UpdateVoyage(ctx, id, map[string]any{"drone_media_total": total})
}

func (u *ThingUsecase) SaveVoyagePPKOrRTCM(ctx context.Context, v *Voyage, fileObjectKey string) error {
	if len(v.Extra) == 0 {
		v.Extra = make(map[string]any)
	}
	ext := path.Ext(fileObjectKey)
	if ext == "" {
		return errors.BadRequest("SaveVoyagePPKOrRTCM.invalidFileObjectKey", "invalid file object key")
	}
	v.Extra[ext[1:]] = fileObjectKey
	extra, _ := json.Marshal(v.Extra)
	return u.voyageRepo.UpdateVoyage(ctx, v.Id, map[string]any{"extra": datatypes.JSON(extra)})
}
