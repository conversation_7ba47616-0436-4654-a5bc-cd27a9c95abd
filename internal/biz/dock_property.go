package biz

import (
	"context"
	"time"
)

// Storage 存储容量单位千字节
type Storage struct {
	// 总容量
	Total int64
	Used  int64
}

type VersionState struct {
	Firmware string
}

// EnvironmentState 机库环境感知数据
type EnvironmentState struct {
	// 降雨量 {"0":"无雨","1":"小雨","2":"中雨","3":"大雨"}
	Rainfall int32
	// 风速 米每秒
	WindSpeed float32
	// 环境温度	摄氏度
	Temperature float32
	// 环境湿度	%RH
	Humidity float32
}

type FlightTaskState struct {
	// 机场累计作业次数
	// Count int32
	// 备降点经纬度 空表示未设置
	Longitude float64
	Latitude  float64
	// 安全高度(备降转移高度米)
	SafeLandHeight float32
	// 是否设置备降点	{"0":"未设置","1":"已设置"}
	SafeLandConfigured int32
	// 飞机是否在舱	 {"0":"舱外","1":"舱内", "-1":"未知应该使用上次状态"}
	DroneInDock int32
}

type BatteryChargeState struct {
	DroneBatteryPercent int32
	// {"0":"空闲","1":"充电中"}
	DroneBatteryChargeState int32
}

// ElecPowerState 电源电池状态 电压：毫伏 电流：毫安
type ElecPowerState struct {
	// {"0":"备用电池关闭","1":"备用电池开启"}
	DockBackupBatterySwitch int32
	// 备用电池
	DockBackupBatteryVoltage int32
	// 摄氏度
	DockBackupTemperature float32
	// 市电电压
	SupplyVoltage  int32
	WorkingVoltage int32
	WorkingCurrent int32
	// DroneChargeState   int32
	// DroneChargePercent int32
}

type NetworkState struct {
	// 网络类型 {"1":"4G","2":"以太网"}
	Type int32
	// 网络质量 {"0":"差","1":"中","2":"好", "-1": 无}
	Quality int32
}

// WirelessLinkState 图传状态
type WirelessLinkState struct {
	// 图传链路模式	{"0":"SDR 模式","1":"4G 融合模式"}
	Mode int32
	// {"0":"断开","1":"连接"}
	State4G  int32
	StateSDR int32
	// {"min":"0","max":"5","step":"1"}
	QualitySDR int32
	// FreqBandSDR float64
	// 总体 4G 信号质量	{"min":"0","max":"5","step":"1"}
	Quality4G    int32
	Quality4GUav int32
	Quality4GGnd int32
	//FreqBand4G   float64
}

type PositionState struct {
	// 是否收敛 {"0":"非开始","1":"收敛中","2":"收敛成功","3":"收敛失败"}
	IsFixed int32
	// 是否标定 	{"0":"未标定","1":"已标定"}
	IsCalibration int32
	// {"1":"1档","2":"2档","3":"3档","4":"4档","5":"5档"}
	Quality   int32
	GpsNumber int32
	RtkNumber int32
}

type DockState struct {
	// 机场状态	 {"0":"空闲中","1":"现场调试","2":"远程调试","3":"固件升级中","4":"作业中"}
	Mode int32
	// 位置 经度，纬度
	Longitude float64
	Latitude  float64

	// 舱盖状态	 {"0":"关闭","1":"打开","2":"半开","3":"舱盖状态异常"}
	CoverState int32
	// 推杆状态	 {"0":"关闭","1":"打开","2":"半开","3":"推杆状态异常"}
	PutterState int32
	// 补光灯状态	 {"0":"关闭","1":"打开", "-1":"无补光灯"}
	SupplementLightState int32
	// 舱内温度	 摄氏度
	Temperature float32
	// 舱内湿度
	Humidity float32
	// 舱体海拔	 米
	Height float32
	// 存储容量单位千字节
	StorageTotal int64
	StorageUsed  int64
	// {"-1": "无空调","0":"空闲模式（无制冷、制热、除湿等）","1":"制冷模式","2":"制热模式","3":"除湿模式","4":"制冷退出模式","5":"制热退出模式","6":"除湿退出模式","7":"制冷准备模式","8":"制热准备模式","9":"除湿准备模式"}
	AirConditionerState int32
	// 0 关机 1 开机
	DroneOnlineState int32
}

type DockProperties struct {
	Id                 string
	RxTime             time.Time
	Timestamp          time.Time
	DeviceId           int64
	Sn                 string
	State              *DockState
	PositionState      *PositionState
	NetworkState       *NetworkState
	FlightTaskState    *FlightTaskState
	EnvironmentState   *EnvironmentState
	WirelessLinkState  *WirelessLinkState
	ElecPowerState     *ElecPowerState
	BatteryChargeState *BatteryChargeState
	VersionState       *VersionState
	Other              map[string]interface{}
}

type PropListQuery struct {
	Sn    string
	Start time.Time
	End   time.Time
	Page  int32
	Size  int32
	// Sort         Sort
	disableCount bool
	pageMark     *rcDatalogPageMark
}

func (q *PropListQuery) DisableCount() bool {
	return q.disableCount
}

type CameraPropListQuery struct {
	PropListQuery
	CameraIndex string
}

type DrondCameraProperty struct {
	DockSn    string
	DroneSn   string
	RxTime    time.Time
	Timestamp time.Time
	State     *DroneCameraState
}

type PropertyRepo interface {
	RecordDockProperties(ctx context.Context, data *DockProperties) error
	RecordControllerProperties(ctx context.Context, data *RemoteControllerProperties) error
	RecordDockDroneProperties(ctx context.Context, data *DroneProperties) error
	RecordDroneCameraProperties(ctx context.Context, data *DroneProperties) error
	ListDockProperties(ctx context.Context, query *PropListQuery) (int64, []*DockProperties, error)
	ListDockDroneProperties(ctx context.Context, query *PropListQuery) (int64, []*DroneProperties, error)
	ListControllerProperties(ctx context.Context, query *PropListQuery) (int64, []*RemoteControllerProperties, error)
	ListCameraProperties(ctx context.Context, query *CameraPropListQuery) (int64, []*DrondCameraProperty, error)
}
