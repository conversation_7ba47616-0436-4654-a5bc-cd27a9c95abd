package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type AccessConfig struct {
	AppId      string
	AppKey     string
	AppLicense string
}

type MQTTConnect struct {
	Host     string
	Username string
	Password string
}

type APIConnect struct {
	Host  string
	Token string
}

type Workspace struct {
	Id   string
	Name string
}

type ThingConfig struct {
	Mqtt      MQTTConnect
	Api       APIConnect
	Workspace Workspace
}

type ConfigRepo interface {
	AccessConfig(ctx context.Context) (*AccessConfig, error)
	ThingConfig(ctx context.Context, sn, token string) (*ThingConfig, error)
}

type ConfigUsecase struct {
	aRepo AuthRepo
	cRepo ConfigRepo
	log   *log.Helper
}

func NewConfigUsecase(logger log.Logger, aRepo AuthRepo, cRepo ConfigRepo) *ConfigUsecase {
	return &ConfigUsecase{aRepo: aRepo, cRepo: cRepo, log: log.NewHelper(logger)}
}

func (uc *ConfigUsecase) GetAccessConfig(ctx context.Context) (*AccessConfig, error) {
	return uc.cRepo.AccessConfig(ctx)
}

func (uc *ConfigUsecase) GetThingConfig(ctx context.Context, sn string) (*ThingConfig, error) {
	av, _ := uc.aRepo.GetCurrentAuthValue(ctx)
	token := av.Token
	if len(token) > 7 && token[0:7] == "Bearer " {
		token = token[7:]
	}
	return uc.cRepo.ThingConfig(ctx, sn, token)
}

func (uc *ConfigUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}
