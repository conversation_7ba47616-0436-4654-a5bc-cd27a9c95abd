package biz

import (
	"bufio"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/grafov/m3u8"
	"github.com/jinzhu/copier"
	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/stream-sidecar/pkg/stream"
)

type MediaType int

const (
	MediaTypeOther MediaType = iota
	MediaTypeVideo
	MediaTypeLive
	MediaTypePhoto
)

func (t MediaType) String() string {
	switch t {
	case MediaTypeVideo:
		return "video"
	case MediaTypePhoto:
		return "image"
	case MediaTypeLive:
		return "live"
	default:
		return "other"
	}
}

type VideoType int

const (
	// VideoTypeNormal 普通可见光类型
	VideoTypeNormal VideoType = iota
	// VideoTypeWide 广角
	VideoTypeWide
	// VideoTypeZoom 变焦
	VideoTypeZoom
	// VideoTypeIR 红外
	VideoTypeIR
)

const DefaultVideoType = VideoTypeWide

func (v VideoType) String() string {
	switch v {
	case VideoTypeZoom:
		return "zoom"
	case VideoTypeWide:
		return "wide"
	case VideoTypeIR:
		return "ir"
	default:
		return "normal"
	}
}

func (v VideoType) Index() string {
	return v.String() + "-0"
}

func NewVideoType(t string) VideoType {
	switch strings.ToLower(t) {
	case "zoom":
		return VideoTypeZoom
	case "wide":
		return VideoTypeWide
	case "ir":
		return VideoTypeIR
	default:
		return VideoTypeNormal
	}
}

type MediaMeta struct {
	// 云台偏航角
	Yaw float64
	// 拍摄绝对高度
	Altitude       float64
	RelativeHeight float64
	ShootLnglat    []float64
	IsOriginal     bool
	ShootTime      time.Time
}

type Media struct {
	Id             int64
	Type           MediaType
	SubType        int32
	DeviceId       int64
	SubDeviceIndex string
	// 直播流为0
	VoyageId  int64
	AirlineId int64
	// 手动巡航抓拍-1
	WaypointId  int64
	MerchantId  int64
	CreatedTime time.Time
	UpdatedTime time.Time
	Meta        *MediaMeta
	// 直播流： 0 离线，1 推流中
	Status int32
	// 对象存储key或直播时的streamId
	Key string
	// 录像图片地址或直播流地址
	URL string
	// 对于live name is videoType.String()
	Name string
	// clarity 清晰度 {"0":"自适应","1":"流畅","2":"标清","3":"高清","4":"超清"}
	// position 0,1,2,7,100001, 100002
	Extra AnyMap
	// 图片时存在
	thumbnailUrl string
	AnnotationId int64
}

func (m *Media) GetSignature() (string, bool) {
	s, ok := m.Extra["sign"]
	if ok {
		return s.(string), ok
	}
	return "", ok
}

func (m *Media) SetSignature(s string) {
	if m.Extra == nil {
		m.Extra = make(map[string]any)
	}
	m.Extra["sign"] = s
}

func (m *Media) SetShortSignature(s string) {
	if m.Extra == nil {
		m.Extra = make(map[string]any)
	}
	m.Extra["sSign"] = s
}

func (m *Media) SetThumbnail(t *ImageThumbnailResult) {
	if m.Extra == nil {
		m.Extra = make(map[string]any)
	}
	m.Extra["thumbnail"] = t.Key
	m.Extra["tCost"] = int(t.Cost / time.Millisecond)
	m.Extra["tSize"] = t.Size
}

func (m *Media) GetThumbnail() (string, bool) {
	// if m.Type != MediaTypePhoto {
	// 	return "", false
	// }
	v, ok := m.Extra["thumbnail"]
	if ok {
		return v.(string), ok
	}
	return "", false
}

func (m *Media) GetThumbnailUrl() string {
	return m.thumbnailUrl
}

// GetIfPanorama 是否是全景
func (m *Media) GetIfPanorama() bool {
	if m.Type != MediaTypePhoto {
		return false
	}
	if v, ok := m.Extra["pType"].(float64); ok {
		return v == 1
	}
	return false
}

// SetPhotoType 0 普通， 1 全景
func (m *Media) SetPhotoType(v int) {
	if m.Extra == nil {
		m.Extra = make(map[string]any)
	}
	if i := lo.IndexOf([]int{0, 1}, v); i > -1 {
		m.Extra["pType"] = float64(v)
	}
}

func (m *Media) GetShortSignature() (string, bool) {
	s, ok := m.Extra["sSign"].(string)
	return s, ok
}

func (m *Media) GetLiveClarity() int32 {
	if m.Type != MediaTypeLive {
		return -1
	}
	if v, ok := m.Extra["clarity"].(float64); ok {
		return int32(v)
	}
	return 0
}

func (m *Media) IsAnalyserMedia() bool {
	return strings.HasPrefix(m.Key, "analyser/")
}

func (m *Media) AnalyserMedia() *Media {
	am := &Media{}
	copier.CopyWithOption(am, m, copier.Option{DeepCopy: true})
	am.Key = "analyser/" + m.Key
	am.Extra["position"] = float64(9999)
	return am
}

// 0，对于 M300 RTK 机型为视线随机头朝前，机身下方左云台。对于其他机型，对应主云台。
// 1，对于 M300 RTK 机型为视线随机头朝前，机身下方右云台。
// 2，对于 M300 RTK 机型为机身上方云台。
// 7，指 FPV 相机。
// 100001 指机场外
// 100002 机场内
func (m *Media) GetLivePosition() int32 {
	if m.Type != MediaTypeLive {
		return -1
	}
	if v, ok := m.Extra["position"].(float64); ok {
		return int32(v)
	}
	return 0
}

func (m *Media) SetLivePosition(p int32) {
	if m.Type != MediaTypeLive {
		return
	}
	if m.Extra == nil {
		m.Extra = make(map[string]any)
	}
	m.Extra["position"] = float64(p)
}

func (m *Media) GetVideoType(subs []*DockSubdevice) (VideoType, []VideoType) {
	index, ok := m.Extra["index"].(string)
	if !ok {
		return NewVideoType(m.Name), []VideoType{}
	}
	if d, ok := lo.Find(subs, func(it *DockSubdevice) bool {
		return it.Index == m.SubDeviceIndex
	}); ok {
		ci, valid := d.GetCameraInfo()
		if valid && len(ci.Videos) > 0 {
			vs, exist := lo.Find(ci.Videos, func(it CameraVideo) bool { return it.Index == index })
			switchable := lo.Ternary(exist, vs.SwitchableVideoTypes, []VideoType{})
			return NewVideoType(m.Name), switchable
		}
	}
	return NewVideoType(m.Name), []VideoType{}
}

// 是否是航次完整录像
func (m *Media) IsVoyageRecordVideo() bool {
	if m.WaypointId > 0 {
		return false
	}
	if v, ok := m.Extra["fullRecord"].(float64); ok {
		return v == 1
	}
	return false
}

func (m *Media) SetStorageBucket(b string) {
	if m.Extra == nil {
		m.Extra = make(map[string]any)
	}
	m.Extra["bucket"] = b
}

func (m *Media) GetStorageBucket() string {
	if b, ok := m.Extra["bucket"].(string); ok {
		return b
	}
	return ""
}

type MediaListQuery struct {
	BaseListQuery
	ProjectInfo
	Type                 []MediaType
	DeviceIds            []int64
	VoyageIds            []int64
	AirlineIds           []int64
	WaypointIds          []int64
	LenTypes             []string
	Ids                  []int64
	WithVoyageRecord     bool
	DisableProjectFilter bool
	FullVoyageRecord     bool
}

type DeviceLiveQuery struct {
	DeviceId       int64
	SubDeviceIndex string
	device         *Device
}

func (q *DeviceLiveQuery) GetDeviceInfo() *Device {
	return q.device
}

type LivePushConfig struct {
	StreamId string
	Url      string
	Options  AnyMap
}

type LiveStatus struct {
	Clarity int32
	Type    VideoType
	Status  int32
}

type RecordFileQuery struct {
	Live *Media
	Path string
}

type ExportVoyageVideoRecordData struct {
	Live   *Media
	Dev    *Device
	Voyage *Voyage
}

type VideoRecordMeta struct {
	ContentLength int64
	ContentType   string
}

type DeviceMediaListQuery struct {
	BaseListQuery
	Dev             *Device
	Type            []MediaType
	VoyageIds       []int64
	ShortSignatures []string
}

type LiveList struct {
	Offline int32
	Online  int32
	List    []*Live
}

type LiveListQuery struct {
	BaseListQuery
	ProjectInfo
	DeviceIds        []int64
	SubDeviceIndexes []string
	Statuses         []int32
}

var MediaSubTypeValueMapper = map[string]int32{
	"W": 1,
	"Z": 2,
	"T": 3,
	"V": 4,
	"S": 20004,
}

var MediaSubTypeKeyMapper = lo.Invert(MediaSubTypeValueMapper)

func NewFileNameForDJIFileName(name string, device *Device, voyage *Voyage) (fileName string, subType int32) {
	// 例如 DJI_20231010181733_0029_W.JPG
	i := strings.LastIndexByte(name, '.')
	ext := ""
	if i > 0 {
		ext = name[i:]
		name = name[:i]
	}
	data := strings.TrimPrefix(strings.ToUpper(name), "DJI_")
	seq := "0001"
	deviceName := device.Sn
	if parts := strings.Split(data, "_"); len(parts) >= 3 {
		seq = parts[1]
		if v, ok := MediaSubTypeValueMapper[parts[2]]; ok {
			subType = v
		}
		if device.Deployment != nil {
			deviceName = device.Deployment.Name
		}
	}
	if voyage != nil && voyage.Airline != nil {
		fileName = fmt.Sprintf("%s_%s%s", voyage.Airline.Name, seq, ext)
		return
	}
	fileName = fmt.Sprintf("%s(%s)_%s%s", deviceName, device.Type, seq, ext)
	return
}

func NewMediaUploadedMessage(m *Media, s3Repo SimpleStorageRepo, total, uploaded int32) (string, []byte) {
	const signTTL = 10 * time.Minute
	type msg struct {
		Id                  int64  `json:"id,string"`
		DeviceId            int64  `json:"deviceId,string"`
		VoyageId            int64  `json:"voyageId,string"`
		Type                string `json:"type"`
		URL                 string `json:"url"`
		Name                string `json:"name"`
		ShootTime           int64  `json:"shootTime"`
		VoyageMediaTotal    int32  `json:"voyageMediaTotal,omitempty"`
		VoyageMediaUploaded int32  `json:"voyageMediaUploaded,omitempty"`
	}
	url := ""
	r, err := s3Repo.GetSignedObjectAddr(context.Background(), &StorageObject{
		Key: m.Key,
	}, signTTL)
	if err == nil {
		url = r.ObjectUrl
	}
	if thumbnail, ok := m.GetThumbnail(); ok {
		tUrlRet, err := s3Repo.GetSignedObjectAddr(context.Background(), &StorageObject{Key: thumbnail}, signTTL)
		if err == nil {
			url = tUrlRet.ObjectUrl
		}
	}
	topic := fmt.Sprintf("skai/merchant/%d/media/%d", m.MerchantId, m.DeviceId)
	data, _ := json.Marshal(&msg{
		Id:                  m.Id,
		DeviceId:            m.DeviceId,
		VoyageId:            m.VoyageId,
		Type:                m.Type.String(),
		URL:                 url,
		Name:                m.Name,
		ShootTime:           m.CreatedTime.UnixMilli(),
		VoyageMediaTotal:    total,
		VoyageMediaUploaded: uploaded,
	})
	return topic, data
}

type GalleryMediaListQueryGroupBy int

const (
	GalleryMediaListQueryGroupByNone GalleryMediaListQueryGroupBy = iota
	GalleryMediaListQueryGroupByVoyage
)

type GalleryMediaListQuery struct {
	BaseListQuery
	ProjectInfo
	Search           string
	Types            []int32
	DeviceCategories []DeviceCategory
	DeviceTypes      []string
	DeviceIds        []int64
	AirlineIds       []int64
	// SubTypes         []int32
	// 以下is条件均与SubTypes取并集
	IsVideo      bool
	IsPanorama   bool
	IsFullRecord bool
	IsPhoto      bool
	Grouped      GalleryMediaListQueryGroupBy
	LenTypes     []string
}

type MediaWithRelatedDevice struct {
	Media   *Media
	Device  *Device
	Voyage  *Voyage
	Airline *Airline
}

type MediaRemovement struct {
	ProjectInfo
	Ids       []int64
	VoyageIds []int64
}

type TransportStream = stream.TransportStream

type StreamDescriptor = stream.StreamDescriptor

type MediaRepo interface {
	Create(ctx context.Context, m []*Media) error
	GetOne(ctx context.Context, id int64) (*Media, error)
	Update(ctx context.Context, m *Media) error
	List(ctx context.Context, query *MediaListQuery) (int64, []*Media, error)
	//TODO: move to LiveRepo
	GetDeviceLives(ctx context.Context, query *DeviceLiveQuery) ([]*Media, error)
	//TODO: move to LiveRepo
	GetLivePushConfig(ctx context.Context, dev *Device, live *Media) (*LivePushConfig, error)
	//TODO: move to LiveRepo
	UpdateLiveStatus(ctx context.Context, id int64, s LiveStatus) error
	UpdateMediaExtra(ctx context.Context, id int64, extra map[string]any) error
	// record 相关已弃用
	StartRecordLive(ctx context.Context, live *Media) error
	IfLiveRecording(ctx context.Context, live *Media) (bool, error)
	ListRecordFile(ctx context.Context, live *Media, date time.Time) ([]string, error)
	GetRecordFileStream(ctx context.Context, req *RecordFileQuery) (io.ReadCloser, *VideoRecordMeta, error)
	DelRecordFile(ctx context.Context, live *Media, date time.Time) error

	GetDeviceMedia(ctx context.Context, query *DeviceMediaListQuery) ([]*Media, error)
	ListLive(ctx context.Context, query *LiveListQuery) (LiveList, error)
	ListGalleryMedia(ctx context.Context, query *GalleryMediaListQuery) (int32, []MediaWithRelatedDevice, error)
	RemoveMedia(ctx context.Context, mr MediaRemovement) ([]*Media, error)
	MakeVideoSnap(ctx context.Context, videoURL string) (io.ReadCloser, error)
}

type MediaUsecase struct {
	log         *log.Helper
	mr          MediaRepo
	ar          AuthRepo
	sr          SimpleStorageRepo
	vr          VoyageRepo
	lr          LockRepo
	dr          DelayRepo
	mqRepo      MQTTRepo
	c           *conf.Data
	m3u8SignKey []byte
}

func NewMediaUsecase(
	logger log.Logger,
	mr MediaRepo,
	ar AuthRepo,
	sr SimpleStorageRepo,
	vr VoyageRepo,
	lr LockRepo,
	dr DelayRepo,
	mqRepo MQTTRepo,
	c *conf.Data,
) *MediaUsecase {
	h := sha256.New()
	h.Write([]byte(c.Media.AdminToken))
	return &MediaUsecase{
		log:         log.NewHelper(logger),
		mr:          mr,
		ar:          ar,
		sr:          sr,
		vr:          vr,
		lr:          lr,
		dr:          dr,
		mqRepo:      mqRepo,
		c:           c,
		m3u8SignKey: h.Sum(nil),
	}
}

func signMediaURL(sr SimpleStorageRepo, m *Media, signTTL time.Duration) {
	bkt := m.GetStorageBucket()
	if thumbnail, ok := m.GetThumbnail(); ok {
		tUrlRet, err := sr.GetSignedObjectAddr(context.Background(), &StorageObject{Key: thumbnail, Bucket: bkt}, signTTL)
		if err == nil {
			m.thumbnailUrl = tUrlRet.ObjectUrl
		}
	}
	if mUrlRet, err := sr.GetSignedObjectAddr(context.Background(), &StorageObject{Key: m.Key, Bucket: bkt}, signTTL); err == nil {
		m.URL = mUrlRet.ObjectUrl
	}
}

func (u *MediaUsecase) signMediaURL(_ context.Context, m *Media, signTTL time.Duration) {
	if m.Key == "" {
		return
	}
	if m.Type == MediaTypeVideo && m.IsVoyageRecordVideo() {
		m.URL = newM3U8ProxyURI(u.c.Skai.PublicUrl, m.Key, u.m3u8SignKey)
		if thumbnail, ok := m.GetThumbnail(); ok {
			tUrlRet, err := u.sr.GetSignedObjectAddr(context.Background(), &StorageObject{Key: thumbnail, Bucket: m.GetStorageBucket()}, signTTL)
			if err == nil {
				m.thumbnailUrl = tUrlRet.ObjectUrl
			}
		}
		return
	}
	signMediaURL(u.sr, m, signTTL)
	// if m.Type == MediaTypeVideo && m.IsVoyageRecordVideo() {
	// 	// update m3u8 content
	// 	if mcAt, ok := m.Extra["m3u8CreatedAt"].(float64); ok && int64(mcAt)+M3U8SegmentSignatureTTL.Milliseconds() < time.Now().UnixMilli()+300*1000 {
	// 		go u.updateM3u8ContentSignature(m.Id, m.Key, m.Extra)
	// 	}
	// }
}

// func (u *MediaUsecase) updateM3u8ContentSignature(mId int64, key string, extra map[string]any) {
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	_, md, err := u.sr.GetObject(ctx, &StorageObject{Key: key})
// 	if err != nil {
// 		u.log.Errorw("updateM3u8ContentSignature.getM3u8 %s failed %+v", key, err)
// 		return
// 	}
// 	defer md.Close()
// 	list, t, err := m3u8.DecodeFrom(md, false)
// 	if err != nil {
// 		u.log.Errorw("updateM3u8ContentSignature.ivalid %s failed %+v", key, err)
// 		return
// 	}
// 	if t == m3u8.MEDIA {
// 		mpl := list.(*m3u8.MediaPlaylist)
// 		segments := make([]*m3u8.MediaSegment, len(mpl.Segments))
// 		for i := 0; i < int(mpl.Count()); i++ {
// 			s := mpl.Segments[i]
// 			v, err := u.sr.RefreshSignedUrl(ctx, s.URI, M3U8SegmentSignatureTTL)
// 			if err == nil {
// 				s.URI = v
// 			}
// 			segments[i] = s
// 		}
// 		mpl.Segments = segments
// 		plData := mpl.Encode()
// 		if _, err := u.sr.PutObject(ctx, &StorageObject{
// 			Key:  key,
// 			Data: plData,
// 		}); err != nil {
// 			u.log.Errorw("updateM3u8ContentSignature.put %s failed %+v", key, err)
// 			return
// 		}
// 		e := make(map[string]any, len(extra))
// 		for k, v := range extra {
// 			e[k] = v
// 		}
// 		e["m3u8CreatedAt"] = time.Now().UnixMilli()
// 		u.mr.UpdateMediaExtra(ctx, mId, e)
// 	}
// }

func (u *MediaUsecase) GetDeviceAllLives(ctx context.Context, dev *Device) ([]*Media, error) {
	return u.mr.GetDeviceLives(ctx, &DeviceLiveQuery{
		device:   dev,
		DeviceId: dev.Id,
	})
}

func (u *MediaUsecase) CreateLives(ctx context.Context, lives []*Media) error {
	return u.mr.Create(ctx, lo.Filter(lives, func(item *Media, _ int) bool { return item.Type == MediaTypeLive }))
}

func (u *MediaUsecase) GetLivePushConfig(ctx context.Context, dev *Device, live *Media) (*LivePushConfig, error) {
	return u.mr.GetLivePushConfig(ctx, dev, live)
}

func (u *MediaUsecase) UpdateLiveStatus(ctx context.Context, id int64, s LiveStatus) error {
	return u.mr.UpdateLiveStatus(ctx, id, s)
}

func (u *MediaUsecase) Create(ctx context.Context, media *Media) error {
	if err := u.mr.Create(ctx, []*Media{media}); err != nil {
		return err
	}
	go utilities.NewRecovedGOFunc(func() error {
		m := &Media{}
		copier.CopyWithOption(m, media, copier.Option{IgnoreEmpty: true, DeepCopy: true})
		actx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
		defer cancel()
		if m.Type == MediaTypeVideo {
			if e := u.getVideoSnapshot(actx, m); e != nil {
				u.log.Errorf("CreateVideoSnapshot %+v failed %+v", m, e)
				return nil
			}
		} else if m.Type == MediaTypePhoto {
			if e := u.getPhotoThumbnail(actx, m); e != nil {
				u.log.Errorf("CreatePhotoThumbnail %+v failed %+v", m, e)
				return nil
			}
		}
		return nil
	})()
	if media.VoyageId > 0 {
		c := VoyageMediaCount{}
		switch media.Type {
		case MediaTypePhoto:
			c.Images = 1
		case MediaTypeVideo:
			c.Videos = 1
		}
		return u.vr.IncressVoyageMediaCount(ctx, media.VoyageId, c)
	}
	return nil
}

func (u *MediaUsecase) getPhotoThumbnail(ctx context.Context, m *Media) error {
	t, err := u.sr.GenImageThumbnail(ctx, &StorageObject{
		Key: m.Key,
	})
	if err != nil {
		u.log.Errorf("CreatePhotoThumbnail %d %s failed %+v", m.Id, m.Key, err)
		return err
	}
	m.SetThumbnail(t)
	u.log.Infof("CreatePhotoThumbnail %d %s", m.Id, t.Key)
	return u.mr.UpdateMediaExtra(ctx, m.Id, m.Extra)
}

func (u *MediaUsecase) getVideoSnapshot(ctx context.Context, m *Media) error {
	vu, err := u.sr.GetInternalSignedObjectAddr(ctx, &StorageObject{Key: m.Key}, 10*time.Minute)
	if err != nil {
		return err
	}
	u.log.Infof("getVideoSnapshot for %s with tmp url %s", m.Key, vu.ObjectUrl)
	vd, err := u.mr.MakeVideoSnap(ctx, vu.ObjectUrl)
	if err != nil {
		u.log.Errorf("getVideoSnapshot for %s failed %+v", m.Key, err)
		return err
	}
	defer vd.Close()
	r, err := u.sr.GenImageThumbnail(ctx, &StorageObject{
		Key:  "snapshot/" + m.Key,
		Data: vd,
	})
	if err != nil {
		return err
	}
	u.log.Infof("getVideoSnapshot for %s success %s", m.Key, r.Key)
	m.SetThumbnail(r)
	return u.mr.UpdateMediaExtra(ctx, m.Id, m.Extra)
}

func (u *MediaUsecase) StartRecordLive(ctx context.Context, live *Media) error {
	if err := u.mr.StartRecordLive(ctx, live); err != nil {
		if errors.IsNotFound(err) {
			waitForStreamHandler := utilities.NewRecovedGOFunc(func() error {
				for i := 0; i < 60; i++ {
					time.Sleep(500 * time.Millisecond)
					cctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
					err := u.mr.StartRecordLive(cctx, live)
					cancel()
					if err == nil {
						return nil
					} else {
						if !errors.IsNotFound(err) {
							u.log.Errorf("StartRecordLive %d for dev %d  err %v", live.Id, live.DeviceId, err)
							return nil
						}
						u.log.Errorf("StartRecordLive %d for dev %d find no live, retrying %d", live.Id, live.DeviceId, i)
					}
				}
				return nil
			})
			go waitForStreamHandler()
		}
	}
	return nil
}

func (u *MediaUsecase) ExportVideoRecord(ctx context.Context, data *ExportVoyageVideoRecordData) ([]*Media, error) {
	if s := data.Dev.PropData.DroneState; s != nil {
		// "3":"手动飞行","4":"自动起飞","5":"航线飞行",
		if lo.Contains([]int32{3, 4, 5}, *s) {
			return nil, nil
		}
	}
	l := NewLock("ExportVideoRecord:"+data.Live.Key, 60*time.Second)
	if err := u.lr.TryLock(ctx, l); err != nil {
		u.log.Warn("ExportVideoRecord trylock err: ", err, "live", data.Live.DeviceId)
		return nil, err
	}
	// TODO: check drone status
	defer u.lr.Release(ctx, l)
	records, err := u.mr.ListRecordFile(ctx, data.Live, data.Voyage.CreatedTime)
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		for i := 0; i < 30; i++ {
			time.Sleep(500 * time.Millisecond)
			u.log.Infof("ExportVideoRecord %s record not found retrying...", data.Live.Key)
			records, err = u.mr.ListRecordFile(ctx, data.Live, data.Voyage.CreatedTime)
			if err != nil {
				return nil, err
			}
			if len(records) > 0 {
				break
			}
		}
		if len(records) == 0 {
			return nil, NewNotFoundError("VoyageVideoRecord", "date", data.Voyage.CreatedTime.Format(time.RFC3339))
		}
	}
	defer func() {
		if err = u.mr.DelRecordFile(ctx, data.Live, data.Voyage.CreatedTime); err != nil {
			u.log.Errorf("ExportVideoRecord.DelRecordFile for live %d voyage %d failed: %v ", data.Live.Id, data.Voyage.Id, err)
		}
	}()
	ms := make([]*Media, 0, 1)
	for i := 0; i < len(records); i++ {
		stream, meta, err := u.mr.GetRecordFileStream(ctx, &RecordFileQuery{
			Live: data.Live,
			Path: records[i],
		})
		if err != nil {
			return nil, err
		}
		defer stream.Close()
		key := fmt.Sprintf("%s/voyage/%s-%d.mp4", data.Voyage.Sn, strconv.FormatInt(data.Voyage.Id, 36), i)
		_, err = u.sr.PutObject(ctx, &StorageObject{
			Key:  key,
			Data: stream,
			Meta: &StorageObjectMeta{ContentLength: meta.ContentLength, ContentType: meta.ContentType},
		})
		if err != nil {
			return nil, err
		}
		m := &Media{
			Type:           MediaTypeVideo,
			DeviceId:       data.Live.DeviceId,
			SubDeviceIndex: data.Live.SubDeviceIndex,
			AirlineId:      data.Voyage.AirlineId,
			MerchantId:     data.Live.MerchantId,
			VoyageId:       data.Voyage.Id,
			WaypointId:     -1,
			Key:            key,
			Name:           fmt.Sprintf("全程录像_%s（%s）.mp4", data.Dev.Deployment.Name, data.Dev.Type),
			Extra:          map[string]any{"fullRecord": 1, "i": i},
		}
		if err = u.Create(ctx, m); err != nil {
			return nil, err
		}
		ms = append(ms, m)
	}
	return ms, nil
}

func (u *MediaUsecase) ListProjectMedia(ctx context.Context, query *MediaListQuery) (int64, []*Media, error) {
	total, list, err := u.mr.List(ctx, query)
	if err != nil {
		return 0, nil, err
	}
	const signTTL = 30 * time.Minute
	lo.ForEach(list, func(item *Media, _ int) {
		u.signMediaURL(ctx, item, signTTL)
	})
	return total, list, nil
}

func (u *MediaUsecase) GetDeviceMedia(ctx context.Context, query *DeviceMediaListQuery) ([]*Media, error) {
	return u.mr.GetDeviceMedia(ctx, query)
}

func (u *MediaUsecase) ListLive(ctx context.Context, query *LiveListQuery) (LiveList, error) {
	return u.mr.ListLive(ctx, query)
}

func (u *MediaUsecase) PushMediaUploadedMessage(ctx context.Context, m *Media, total, uploaded int32) {
	t, d := NewMediaUploadedMessage(m, u.sr, total, uploaded)
	_, err := u.mqRepo.Publish(ctx, t, 0, d)
	if err != nil {
		u.log.Warnf("PushMediaUploadedMessage %s to %s failed %+v", d, t, err)
	}
}

func (u *MediaUsecase) GetSignMediaURL(ctx context.Context, m *Media, signTTL time.Duration) (string, string) {
	if m.Key == "" {
		return "", ""
	}
	signMediaURL(u.sr, m, signTTL)
	return m.URL, m.thumbnailUrl
}

func (u *MediaUsecase) ListGalleryMedia(ctx context.Context, query *GalleryMediaListQuery) (int32, []MediaWithRelatedDevice, error) {
	total, list, err := u.mr.ListGalleryMedia(ctx, query)
	if err != nil || total == 0 {
		return total, nil, err
	}
	const signTTL = 30 * time.Minute
	lo.ForEach(list, func(item MediaWithRelatedDevice, index int) {
		u.signMediaURL(ctx, item.Media, signTTL)
	})
	return total, list, err
}

func (u *MediaUsecase) CountGalleryMedia(ctx context.Context, pi ProjectInfo) (int64, error) {
	total, _, err := u.mr.ListGalleryMedia(ctx, &GalleryMediaListQuery{
		ProjectInfo: pi,
		BaseListQuery: BaseListQuery{
			// 开始时间固定位 2023年1月1日
			TimeScope: NewTimeScope(1672531200000, now.EndOfDay().UnixMilli()),
		},
	})

	return int64(total), err
}

func (u *MediaUsecase) RemoveMedia(ctx context.Context, mr MediaRemovement) ([]*Media, error) {
	return u.mr.RemoveMedia(ctx, mr)
}

type MediaDetailQuery struct {
	ProjectInfo
	MediaId int64
}

type MediaDownloadURL struct {
	URL string
}

func (u *MediaUsecase) createTMPFileCleanUpTask(ctx context.Context, key string) {
	u.dr.CreateTask(ctx, &DelayTask{
		Delay: 65 * time.Minute,
		Payload: map[string]interface{}{
			"action": "cleanup",
			"key":    key,
		},
		Key:      fmt.Sprintf("%s:CLEANUP:%s", SkaiDelayKeyPrefix, key),
		Callback: "/internal/v1/media/gallery/cleanUpTmpDownloadableFile",
		Source:   "SKAI-SERVER_MEDIA",
	})
	u.log.Info("createTMPFileCleanUpTask for file: ", key)
}

func (u *MediaUsecase) CleanTmpDownlodFile(ctx context.Context, key string) error {
	return u.sr.DeleteObject(ctx, &StorageObject{Key: key})
}

func (u *MediaUsecase) GetVideoMediaDownloadUrl(ctx context.Context, query MediaDetailQuery) (*MediaDownloadURL, error) {
	m, err := u.mr.GetOne(ctx, query.MediaId)
	if err != nil {
		return nil, err
	}
	if !lo.Contains(query.MerchantIds, m.MerchantId) {
		return nil, errors.NotFound("GetVideoMediaDownloadUrl.merchant", "对应资源不存在")
	}
	if !m.IsVoyageRecordVideo() {
		return nil, errors.BadRequest("GetVideoMediaDownloadUrl.type", "不是全程录像")
	}
	const ttl = 1 * time.Hour
	downloadFileKey := fmt.Sprintf("tmp/%s", strings.Replace(m.Key, ".m3u8", ".ts", -1))
	if ok, _ := u.sr.CheckObjectIfExists(ctx, &StorageObject{Key: downloadFileKey}); ok {
		addr, err := u.sr.GetSignedObjectAddr(ctx, &StorageObject{Key: downloadFileKey}, ttl)
		if err != nil {
			return nil, err
		}
		u.createTMPFileCleanUpTask(ctx, downloadFileKey)
		return &MediaDownloadURL{
			URL: addr.ObjectUrl,
		}, nil
	}
	// 兼容旧的mp4录像文件
	if _, ok := m.Extra["m3u8CreatedAt"]; !ok {
		du, err := u.sr.GetSignedObjectAddr(ctx, &StorageObject{Key: m.Key}, ttl)
		if err != nil {
			return nil, err
		}
		return &MediaDownloadURL{URL: du.ObjectUrl}, nil
	}
	_, data, err := u.sr.GetObject(ctx, &StorageObject{Key: m.Key})
	if err != nil {
		return nil, err
	}
	defer data.Close()
	pl, t, err := m3u8.DecodeFrom(data, false)
	if err != nil {
		return nil, errors.InternalServer("GetVideoMediaDownloadUrl.format", "不是m3u8文件")
	}
	if t != m3u8.MEDIA {
		return nil, errors.BadRequest("GetVideoMediaDownloadUrl.type", "不是m3u8文件")
	}
	r, w := io.Pipe()
	defer w.Close()
	rw := bufio.NewReadWriter(bufio.NewReader(r), bufio.NewWriter(w))
	go utilities.NewRecovedGOFunc(func() error {
		upCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
		defer cancel()
		u.sr.PutObject(upCtx, &StorageObject{
			Key:  downloadFileKey,
			Data: rw,
			Meta: &StorageObjectMeta{ContentLength: int64(m.Extra["vSize"].(float64))},
		})
		return nil
	})()
	l := pl.(*m3u8.MediaPlaylist)
	for i := 0; i < int(l.Count()); i++ {
		u.log.Debugf("GetVideoMediaDownloadUrl %s duration %.2f", l.Segments[i].URI, l.Segments[i].Duration)
		_, res, err := u.sr.GetObject(ctx, &StorageObject{URL: l.Segments[i].URI})
		if err != nil {
			return nil, err
		}
		io.Copy(rw, res)
		res.Close()
	}
	rw.Flush()
	addr, err := u.sr.GetSignedObjectAddr(ctx, &StorageObject{Key: downloadFileKey}, ttl)
	if err != nil {
		return nil, err
	}
	u.createTMPFileCleanUpTask(ctx, downloadFileKey)
	return &MediaDownloadURL{
		URL: addr.ObjectUrl,
	}, nil
}
