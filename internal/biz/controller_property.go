package biz

import "time"

// 遥控器物模型定义

type RemoteControllerState struct {
	// 电量百分比
	CapacityPercent int32
	// 位置 经度，纬度
	Longitude float64
	Latitude  float64
}

type RemoteControllerProperties struct {
	Id                string
	RxTime            time.Time
	Timestamp         time.Time
	DeviceId          int64
	Sn                string
	State             *RemoteControllerState
	WirelessLinkState *WirelessLinkState
	VersionState      *VersionState
	Other             AnyMap
}
