package biz

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type DelayTask struct {
	Key      string                 `json:"key"`
	Times    int                    `json:"times"`
	Source   string                 `json:"source"`
	Callback string                 `json:"callback,omitempty"`
	Delay    time.Duration          `json:"delay"`
	Payload  map[string]interface{} `json:"payload,omitempty"`
}

type DelayRepo interface {
	DeleteTask(ctx context.Context, id string) error
	CreateTask(ctx context.Context, task *DelayTask) error
}

type DelayUsecase struct {
	repo DelayRepo
	log  *log.Helper
}

func NewDelayUsecase(logger log.Logger, repo DelayRepo) *DelayUsecase {
	return &DelayUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (uc *DelayUsecase) CreateTask(ctx context.Context, task *DelayTask) error {
	return uc.repo.CreateTask(ctx, task)
}

func (uc *DelayUsecase) DeleteTask(ctx context.Context, key string) error {
	return uc.repo.DeleteTask(ctx, key)
}

func NewOperationTimeoutDelayTaskKey(op *Operation) string {
	return fmt.Sprintf("%s:SERVICE:%s:%d", SkaiThingKeyPrefix, op.Type.ToServiceIdentifier().ToUpper(), op.Id)
}
