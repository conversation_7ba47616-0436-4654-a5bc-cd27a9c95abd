package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
)

type Role = client.Role
type Permit = client.Permit
type Session = client.Session
type MobileLogin = client.MobileLogin
type LoginHeaders = client.LoginHeaders
type UsernameLogin = client.UsernameLogin
type MobileLoginDto = client.MobileLoginDto
type UsernameLoginDto = client.UsernameLoginDto

type SessionRepo interface {
	Logout(ctx context.Context) error
	LoginByMobile(ctx context.Context, dto MobileLoginDto) (*Session, error)
	LoginByUsername(ctx context.Context, dto UsernameLoginDto) (*Session, error)
}

type SessionUsecase struct {
	repo SessionRepo
	log  *log.Helper
}

func NewSessionUsecase(logger log.Logger, repo SessionRepo) *SessionUsecase {
	return &SessionUsecase{
		repo: repo,
		log:  log.New<PERSON>elper(logger),
	}
}

func (uc *SessionUsecase) Logout(ctx context.Context) error {
	return uc.repo.Logout(ctx)
}

func (uc *SessionUsecase) LoginByMobile(ctx context.Context, dto MobileLoginDto) (*Session, error) {
	return uc.repo.LoginByMobile(ctx, dto)
}

func (uc *SessionUsecase) LoginByUsername(ctx context.Context, dto UsernameLoginDto) (*Session, error) {
	return uc.repo.LoginByUsername(ctx, dto)
}
