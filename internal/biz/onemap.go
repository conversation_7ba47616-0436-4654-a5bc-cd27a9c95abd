package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
)

type StatQuery struct {
	ProjectInfo
	DeviceId *int64
}

type StatData struct {
	DockCount      int32
	OnlineDock     int32
	PilotCount     int32
	OnlinePilot    int32
	ImageCount     int32
	VideoCount     int32
	VoyageTimes    int32
	VoyageMileages int32
}

type OnemapUsecase struct {
	aRepo AuthRepo
	dRepo DeviceRepo
	lRepo LiveRepo
	mRepo MediaRepo
	vRepo VoyageRepo
	log   *log.Helper
}

func NewOnemapUsecase(logger log.Logger, aRepo AuthRepo, dRepo DeviceRepo, lRepo LiveRepo, mRepo MediaRepo, vRepo VoyageRepo) *OnemapUsecase {
	return &OnemapUsecase{aRepo: aRepo, dRepo: dRepo, lRepo: lRepo, mRepo: mRepo, vRepo: vRepo, log: log.NewHelper(logger)}
}

func (uc *OnemapUsecase) Statistics(ctx context.Context, query *StatQuery) (*StatData, error) {
	data := &StatData{}
	filter := "filter (where network_status=true) as kount"
	if counter, err := uc.dRepo.CountDevices(ctx, &DeviceCountQuery{ProjectInfo: query.ProjectInfo, Id: query.DeviceId, CountBy: "category", Filter: &filter}); err != nil {
		return nil, err
	} else {
		for _, item := range counter {
			if item.Name == CategoryDock.String() {
				data.DockCount = item.Count
				data.OnlineDock = item.Kount
			}
			if item.Name == CategoryPilot.String() {
				data.PilotCount = item.Count
				data.OnlinePilot = item.Kount
			}
		}
	}
	if overview, err := uc.vRepo.AggregateVoyages(ctx, &VoyageOverviewQuery{ProjectInfo: query.ProjectInfo, DeviceId: query.DeviceId}); err != nil {
		return data, err
	} else {
		data.ImageCount = overview.Images
		data.VideoCount = overview.Videos
		data.VoyageTimes = overview.Times
		data.VoyageMileages = overview.Mileages / 1000
	}
	return data, nil
}

func (uc *OnemapUsecase) ListDevice(ctx context.Context, query *DeviceListQuery) (int32, []*Device, error) {
	total, devices, err := uc.dRepo.ListDevices(ctx, query)
	if err != nil {
		return total, devices, err
	}
	overviews, err := uc.vRepo.RangeVoyages(ctx, &VoyageOverviewQuery{ProjectInfo: query.ProjectInfo})
	if err != nil {
		return total, devices, nil
	}
	for _, d := range devices {
		ov := lo.FindOrElse(overviews, &VoyageOverview{}, func(ov *VoyageOverview) bool { return ov.DeviceId == d.Id })
		d.VoyageTimes = ov.Times
	}
	return total, devices, err
}

func (uc *OnemapUsecase) GetDevice(ctx context.Context, query *DetailQuery) (*Device, error) {
	device, err := uc.dRepo.GetDevice(ctx, query)
	if err != nil {
		return nil, err
	}
	if lives, err := uc.mRepo.GetDeviceLives(ctx, &DeviceLiveQuery{DeviceId: device.Id, SubDeviceIndex: ""}); err == nil {
		device.LiveVideos = lives
	}
	if device.PropData.Algorithm != nil && *device.PropData.Algorithm == AlgNameAnalyser.String() {
		if live, ok := lo.Find(device.LiveVideos, func(live *Media) bool { return live.GetLivePosition() == 0 }); ok {
			amedia := live.AnalyserMedia()
			if amedia.URL, err = uc.lRepo.GetLiveProtocolPlayUrl(ctx, amedia, MediaProtocolHTTP, false); err == nil {
				device.LiveVideos = append(device.LiveVideos, amedia)
			}
		}
	}
	if voyage, err := uc.vRepo.LastVoyage(ctx, &VoyageLastQuery{DeviceId: device.Id}); err == nil {
		device.LastVoyage = voyage
	}
	return device, err
}

func (uc *OnemapUsecase) JointDevice(ctx context.Context, query *DeviceJointQuery) ([]*Device, error) {
	return uc.dRepo.JointDevices(ctx, query)
}

func (uc *OnemapUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}

func (uc *OnemapUsecase) GetAvatar(ctx context.Context, id int64) (*Avatar, error) {
	return uc.aRepo.GetAvatar(ctx, id)
}
