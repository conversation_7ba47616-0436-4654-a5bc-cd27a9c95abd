package biz

import (
	"context"
	"io"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/skai/skai/pkg/radar"
)

type LLMEngine = client.LLMEngine
type LinsMetaEvent = client.EventSyncDTO
type DectectTarget = client.EventCaptureObject

type AlgType int

const (
	AlgTypeUnknown  AlgType = iota
	AlgTypeSnapshot         // 实时视频抽帧
	AlgTypePathshot         // 巡航线路拍照
)

type AlgName string

const (
	AlgNameUnknown  AlgName = "unknown"
	AlgNamePerson   AlgName = "person"
	AlgNameSmokeye  AlgName = "smokeye"
	AlgNameFloater  AlgName = "floater"
	AlgNameAnalyser AlgName = "flowAnalyser"
)

func (an AlgName) String() string {
	return string(an)
}

func (an AlgName) Translate() string {
	switch an {
	case AlgNamePerson:
		return "人群聚集"
	case AlgNameSmokeye:
		return "烟火监测"
	case AlgNameFloater:
		return "河道漂浮物"
	case AlgNameAnalyser:
		return "人车流分析"
	}
	return "未知算法"
}

type AiEventType string

const (
	AiEventUnknown  AiEventType = "UNKNOWN"
	AiEventTypeNone AiEventType = "None"
	AiEventPerson   AiEventType = "drone:capture.person"
	AiEventSmokeye  AiEventType = "drone:capture.smokeye"
	AiEventFloater  AiEventType = "drone:capture.floater"
	AiEventFirework AiEventType = "drone:capture.firework"
)

func (et AiEventType) String() string {
	return string(et)
}

func (et AiEventType) Name() string {
	switch et {
	case AiEventTypeNone:
		return "常规事件"
	case AiEventPerson:
		return "人群聚集"
	case AiEventSmokeye:
		return "烟火监测"
	case AiEventFloater:
		return "河道漂浮物"
	}
	return "未知事件"
}

func NewAiEventType(name AlgName) AiEventType {
	switch name {
	case AlgNamePerson:
		return AiEventPerson
	case AlgNameSmokeye:
		return AiEventSmokeye
	case AlgNameFloater:
		return AiEventFloater
	default:
		return AiEventUnknown
	}
}

type AlgoDetectData struct {
	Type        string           `json:"type,omitempty"`
	Name        string           `json:"name,omitempty"`
	LLMEngine   *LLMEngine       `json:"llmEngine,omitempty"`
	ObjectInfo  []*DectectTarget `json:"objectInfo,omitempty"`
	ObjectCount int32            `json:"objectCount,omitempty"`
}

type AlgoEventDto struct {
	AlgoDetectData
	AlgType   AlgType   `json:"algType,omitempty"`
	ImageUrl  string    `json:"imageUrl,omitempty"`
	Timestamp int64     `json:"timestamp,omitempty"`
	Lnglat    []float32 `json:"lnglat,omitempty"`
	Location  string    `json:"location,omitempty"`
}

type SnapshotData struct {
	Id        string       `json:"id"`
	Timestamp int64        `json:"timestamp"`
	Key       string       `json:"key"`
	Url       string       `json:"url"`
	Task      SnapshotTask `json:"task"`
}

type SnapshotKafkaMessage struct {
	LinsKafkaMessage
	Data *SnapshotData `json:"data"`
}

type AlarmBase struct {
	Id       string `json:"id"`
	Type     string `json:"type"`
	Status   string `json:"status"`
	CustomId string `json:"customId"`
}

type AlarmApproval struct {
	Approval  bool   `json:"approval"`
	Status    string `json:"status"`
	Timestamp int64  `json:"time,omitempty"`
}

type AlarmEvent struct {
	AlarmBase
	Timestamp int64        `json:"time,omitempty"`
	Metadata  client.Entry `json:"metadata,omitempty"`
}

type AlarmDomainData struct {
	AlarmBase
	TaskId        string        `json:"taskId,omitempty"`
	MerchantId    string        `json:"merchantId,omitempty"`
	CurrentStatus string        `json:"currentStatus,omitempty"`
	Approval      AlarmApproval `json:"approval,omitempty"`
	AlarmEvents   []*AlarmEvent `json:"events,omitempty"`
}

type AlarmDomainKafkaMessage struct {
	LinsKafkaMessage
	Data *AlarmDomainData `json:"data"`
}

type AiEventRepo interface {
	PushAIEvent(ctx context.Context, event *LinsMetaEvent) error
	AggAIEvent(ctx context.Context, query *client.AlarmAggQuery) ([]*client.AggTaskItem, error)
	AnalyseSelfDetect(ctx context.Context, name AlgName, reader io.ReadCloser) (*AlgoDetectData, error)
	AnalyseGddiDetect(ctx context.Context, name AlgName, reader io.ReadCloser) (*AlgoDetectData, error)
	AnalyseChatDetect(ctx context.Context, name AlgName, reader io.ReadCloser) (*AlgoDetectData, error)
}

type AiEventUsecase struct {
	log *log.Helper
	er  AiEventRepo
	vr  VoyageRepo
	ar  AirlineRepo
	sr  SimpleStorageRepo
}

func NewAiEventUsecase(logger log.Logger, er AiEventRepo, vr VoyageRepo, ar AirlineRepo, sr SimpleStorageRepo) *AiEventUsecase {
	return &AiEventUsecase{
		log: log.NewHelper(logger),
		er:  er,
		vr:  vr,
		ar:  ar,
		sr:  sr,
	}
}

func (uc *AiEventUsecase) PushAIEvent(ctx context.Context, event *LinsMetaEvent) error {
	return uc.er.PushAIEvent(ctx, event)
}

func (uc *AiEventUsecase) SnapshotDetect(ctx context.Context, algname AlgName, data *SnapshotData) (*AlgoDetectData, error) {
	res, err := radar.NewRequest().SetContext(ctx).SetDoNotParseResponse(true).Get(data.Url)
	if err != nil {
		return nil, err
	}
	switch algname {
	case AlgNamePerson:
		// test url: "https://img95.699pic.com/xsj/2j/9w/w8.jpg%21/fw/700/watermark/url/L3hzai93YXRlcl9kZXRhaWwyLnBuZw/align/southeast"
		return uc.er.AnalyseSelfDetect(ctx, algname, res.RawBody())
	case AlgNameSmokeye, AlgNameFloater:
		return uc.er.AnalyseChatDetect(ctx, algname, res.RawBody())
	default:
		return nil, NewBadRequestError("暂未支持该算法", nil)
	}
}

func (uc *AiEventUsecase) PathshotDetect(ctx context.Context, algname AlgName, data *Media) (*AlgoDetectData, error) {
	_, reader, err := uc.sr.GetObject(ctx, &StorageObject{Key: data.Key})
	if err != nil {
		return nil, err
	}
	return uc.er.AnalyseChatDetect(ctx, algname, reader)
}

type AiEventVoyageQuery struct {
	Device    *Device
	TimeScope *TimeScope
}

func (uc *AiEventUsecase) GetEventVoyageByTime(ctx context.Context, query *AiEventVoyageQuery) (*Voyage, error) {
	v, err := uc.vr.LastVoyage(ctx, &VoyageLastQuery{
		DeviceId:  query.Device.Id,
		TimeScope: query.TimeScope,
	})
	if err != nil {
		return nil, err
	}
	if v.AirlineId > 0 {
		a, err := uc.ar.GetAirline(ctx, &AirlineQuery{Id: v.AirlineId})
		if err == nil {
			v.Airline = a
		}
	}
	return v, nil
}
