package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
)

type ArchiveTask struct {
	Id                  int64
	CreatedTime         time.Time
	UpdatedTime         time.Time
	TimeScope           TimeScope
	Cost                time.Duration
	ObjectCount         int64
	TotalSize           int64
	VoyageDownsizedTime time.Time
	Downsized           int64
}

type ArchiveTaskRepo interface {
	Create(ctx context.Context, t *ArchiveTask) error
	FindLast(ctx context.Context) (*ArchiveTask, error)
	NeedToRunNext(ctx context.Context, last *ArchiveTask) (bool, error)
	DownsizeVoyageVideoRecord(ctx context.Context, m *Media, or ObjectRemover) (int64, error)
	UpdateTask(ctx context.Context, t *ArchiveTask) error
}

type ArchiveTaskUsecase struct {
	log *log.Helper
	mr  MediaRepo
	sr  SimpleStorageRepo
	ar  ArchiveTaskRepo
	lr  LockRepo
}

func NewArchiveTaskUsecase(
	logger log.Logger,
	mr <PERSON>R<PERSON>o,
	sr <PERSON>StorageRepo,
	ar ArchiveTaskRepo,
	lr LockRepo,
) *ArchiveTaskUsecase {
	return &ArchiveTaskUsecase{
		log: log.<PERSON>(logger),
		mr:  mr,
		sr:  sr,
		ar:  ar,
		lr:  lr,
	}
}

func (u *ArchiveTaskUsecase) FindLast(ctx context.Context) (*ArchiveTask, error) {
	return u.ar.FindLast(ctx)
}

func (u *ArchiveTaskUsecase) GetTaskLock(ctx context.Context, timeout time.Duration) error {
	return u.lr.TryLock(ctx, NewLock("archiveTask", timeout))
}

func (u *ArchiveTaskUsecase) RunArchiveTask(ctx context.Context, t *ArchiveTask) error {
	u.log.Infof("start archive task %v", t)
	ab, ok := u.sr.GetSecondaryStorageBucket()
	if !ok {
		u.log.Infof("ArchiveTask %+v no secondary bucket", t)
		return nil
	}
	if t.TimeScope.StartTime.IsZero() || t.TimeScope.EndTime.IsZero() {
		u.log.Infof("ArchiveTask %+v no timeScope", t)
		return nil
	}
	if ok, _ := u.ar.NeedToRunNext(ctx, t); !ok {
		u.log.Infof("ArchiveTask %+v not need to run", t)
		return nil
	}
	start := time.Now()
	total, list, err := u.mr.List(ctx, &MediaListQuery{
		BaseListQuery: BaseListQuery{
			Page:      1,
			Size:      500,
			TimeScope: &t.TimeScope,
			Sort:      Sort{Field: lo.ToPtr("created_time"), Order: ListSortOrderAsc},
		},
		Type:                 []MediaType{MediaTypePhoto, MediaTypeVideo},
		DisableProjectFilter: true,
	})
	if err != nil {
		u.log.Errorf("RunArchiveTask %+v listMediaFailed %v", t, err)
		return err
	}
	if int(total) > len(list) {
		t.TimeScope.EndTime = list[len(list)-1].CreatedTime
	}
	for i := 0; i < len(list); i++ {
		if err := u.archiveMedia(ctx, t, list[i], ab); err != nil {
			u.log.Errorf("RunArchiveTask %+v  media %d key=%s archiveMediaFailed %v", t, list[i].Id, list[i].Key, err)
		}
	}
	t.Cost = time.Since(start)
	return u.ar.Create(ctx, t)
}

func (u *ArchiveTaskUsecase) archiveMedia(ctx context.Context, t *ArchiveTask, m *Media, archiveBucket string) error {
	if m.Type == MediaTypeVideo && m.IsVoyageRecordVideo() {
		return nil
	}
	meta, data, err := u.sr.GetObject(ctx, &StorageObject{Key: m.Key})
	if err != nil {
		u.log.Errorf("archiveMedia %+v getObjectFailed %v", m, err)
		return err
	}
	defer data.Close()
	_, err = u.sr.PutObject(ctx, &StorageObject{
		Bucket: archiveBucket,
		Key:    m.Key,
		Meta:   meta,
		Data:   data,
	})
	if err != nil {
		u.log.Errorf("archiveMedia %d object %s putObjectFailed %v", m.Id, m.Key, err)
		return err
	}
	t.ObjectCount++
	t.TotalSize += meta.ContentLength

	if tbk, ok := m.GetThumbnail(); ok {
		tm, td, err := u.sr.GetObject(ctx, &StorageObject{Key: tbk})
		if err != nil {
			u.sr.DeleteObject(ctx, &StorageObject{Key: m.Key, Bucket: archiveBucket})
			u.log.Errorf("archiveMedia %d object %s getThumbnailFailed %v", m.Id, m.Key, err)
			return err
		}
		defer td.Close()
		_, err = u.sr.PutObject(ctx, &StorageObject{
			Bucket: archiveBucket,
			Key:    tbk,
			Meta:   tm,
			Data:   td,
		})
		if err != nil {
			u.sr.DeleteObject(ctx, &StorageObject{Key: m.Key, Bucket: archiveBucket})
			u.log.Errorf("archiveMedia %d object %s putThumbnailFailed %v", m.Id, m.Key, err)
			return err
		}
		t.ObjectCount++
		t.TotalSize += tm.ContentLength
	}
	m.SetStorageBucket(archiveBucket)
	err = u.mr.Update(ctx, m)
	if err != nil {
		return err
	}
	u.log.Debugf("archiveMedia %d object %s success, size %d KB", m.Id, m.Key, meta.ContentLength>>10)
	if tbk, ok := m.GetThumbnail(); ok {
		u.sr.DeleteObject(ctx, &StorageObject{Key: tbk})
	}
	return u.sr.DeleteObject(ctx, &StorageObject{Key: m.Key})
}

func (u *ArchiveTaskUsecase) RunVoyageVideoRecordDownsizeTask(ctx context.Context, last, current *ArchiveTask) error {
	if current == nil || current.Id == 0 {
		return nil
	}
	u.log.Infof("start archive related downsize task %v", current)
	ts := &TimeScope{
		StartTime: current.TimeScope.StartTime,
		EndTime:   current.TimeScope.EndTime,
	}
	if last.VoyageDownsizedTime.IsZero() {
		ts.StartTime = time.Now().Add(-24 * 365 * time.Hour)
		ts.EndTime = ts.StartTime.Add(4 * time.Hour)
	} else {
		ts.StartTime = last.VoyageDownsizedTime
		ts.EndTime = ts.StartTime.Add(6 * time.Hour)
		// 夜里时间段增长
		if sh := ts.StartTime.In(TimeLocation).Hour(); sh > 20 || sh < 5 {
			ts.EndTime = ts.EndTime.Add(6 * time.Hour)
		}
		if now := time.Now(); ts.EndTime.After(now) {
			ts.EndTime = now
		}
	}
	defer func() {
		current.VoyageDownsizedTime = ts.EndTime
		if err := u.ar.UpdateTask(ctx, current); err != nil {
			u.log.Errorf("RunVoyageVideoRecordDownsizeTask %+v updateTaskFailed %v", current, err)
		}
	}()
	total, list, err := u.mr.List(ctx, &MediaListQuery{
		BaseListQuery: BaseListQuery{
			Page:      1,
			Size:      10,
			TimeScope: ts,
			Sort:      Sort{Field: lo.ToPtr("created_time"), Order: ListSortOrderAsc},
		},
		Type:                 []MediaType{MediaTypeVideo},
		DisableProjectFilter: true,
		FullVoyageRecord:     true,
	})
	if err != nil {
		u.log.Errorf("RunVoyageVideoRecordDownsizeTask %+v listMediaFailed %v", current, err)
		return err
	}
	if total > 10 {
		ts.EndTime = list[len(list)-1].CreatedTime
	}
	var s int64
	for i := 0; i < len(list); i++ {
		ds, err := u.ar.DownsizeVoyageVideoRecord(ctx, list[i], u.sr)
		if err != nil {
			u.log.Errorf("RunVoyageVideoRecordDownsizeTask %+v  media %d key=%s downsizeFailed %v", current, list[i].Id, list[i].Key, err)
		}
		s += ds
	}
	current.Downsized += s
	return nil
}
