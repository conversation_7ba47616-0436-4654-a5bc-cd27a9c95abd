package biz

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/tidwall/conv"
)

type EventType string

const (
	UnknownEvent EventType = "UNKNOWN" // 未知事件
	// 机场离线事件，来自平台业务逻辑，超过30秒未上报信息
	OfflineEvent EventType = "OFFLINE"
	// 设备低电事件，来自平台业务逻辑，飞机电量低于30%触发
	LowBatteryEvent EventType = "LOW_BATTERY"
	// 设备严重低电，来自平台业务逻辑，飞机电量低于10%触发
	MinBatteryEvent EventType = "MIN_BATTERY"
	// 机场检测事件，来自设备HMS健康告警上报
	DockDetectEvent EventType = "DOCK_DETECT"
	// 飞机检测事件，来自设备HMS健康告警上报，摘取部分告警码，详见HealthBreakCode
	// 1.记录设备事件(DeviceEvent)；[2.判断自动返航(DeviceAutoback)；3.提升等级启动倒计时(DelayerTask)；]4.推送设备告警(DeviceCaution)
	DroneDetectEvent EventType = "DRONE_DETECT"
	// 巡航中断原因，来自航线任务进度上报，摘取部分原因码，详见CruiseBreakCode
	// 1.记录设备事件(DeviceEvent)；2.推送设备告警(DeviceCaution)；3.更新设备状态(LockStatus等)；4.推送设备变更(DeviceChange)
	CruiseBreakReason EventType = "CRUISE_BREAK"
	// 指令响应异常，来自指令响应错误码，详见ServiceReplyCodeDescMap的ErrorCode，仅记录设备事件(DeviceEvent)
	ServiceReplyError EventType = "SERVICE_REPLY"
	// 高风险强制返航，来自平台业务逻辑，自由飞行且电量低于30%触发
	// 1.记录设备事件(DeviceEvent)；2.设备强制返航(ForceBack)；3.推送用户告警(AvatarCaution)
	HighRiskForceBack EventType = "HIGH_RISK_BACK"
)

func (s EventType) String() string {
	return string(s)
}

type Event struct {
	Id           int64
	Sn           string
	Type         EventType
	Code         string
	Level        int32
	Place        int32
	ExtraData    AnyMap
	OccurredTime time.Time
	CreatedTime  time.Time
	UpdatedTime  time.Time
}

// 航线中断巡航原因码
var CruiseBreakCode = []int32{2, 517, 518, 519, 769, 773, 775, 784, 1565}

// 航线中断巡航描述
var CruiseBreakCodeDescMap = StringMap{
	"2":    "不常见错误，建议联系技术支持",
	"517":  "无人机触发避障",
	"518":  "RTK信号差",
	"519":  "接近禁飞区边界",
	"769":  "GPS信号弱",
	"773":  "低电量返航导致航线中断",
	"775":  "遥控器与无人机失联",
	"784":  "大风返航导致航线中断",
	"1565": "航线避障紧急刹停",
}

// 健康告警中断巡航码
var HealthBreakCode = []string{
	"0x110B0013", "0x16010007", "0x16010010", "0x1601002F", "0x16020016", "0x16030013", "0x16040010",
	"0x16040013", "0x16040019", "0x1608005e", "0x16100001", "0x1610000C", "0x1610000D", "0x1610000E",
	"0x16100012", "0x16100018", "0x16100019", "0x1610001A", "0x1610004A", "0x1610004E", "0x16100051",
	"0x16100064", "0x16100065", "0x16100066", "0x16100069", "0x16100083", "0x16100090", "0x1610009A",
	"0x161000A6", "0x161000C7", "0x161000F4", "0x161000F7", "0x161000F8", "91000000",
}

// 强制返航码描述
var HighRiskForceCodeDescMap = StringMap{
	"99000000": "电量低于30%，开始强制返航",
}

// 故障事件码描述
var MaintenanceCodeDescMap = StringMap{
	"90000000": "设备断网或断电",
	"91000000": "设备电量低于30%",
	// 维保故障
	"92000000": "电量低于10%，且不在舱",
	"92000001": "电量低于10%，在舱但未充电",
}

var EventTypeMap = StringMap{
	"UNKNOWN":        "未知事件",
	"OFFLINE":        "设备离线",
	"LOW_BATTERY":    "设备低电",
	"MIN_BATTERY":    "设备严重低电",
	"DOCK_DETECT":    "机场检测异常",
	"DRONE_DETECT":   "飞机检测异常",
	"CRUISE_BREAK":   "巡航中断原因",
	"SERVICE_REPLY":  "指令响应异常",
	"HIGH_RISK_BACK": "高风险强制返航",
}

func (e *Event) Trans() string {
	return EventTypeMap[e.Type.String()]
}

func (e *Event) Describe() string {
	description := ""
	if e.Type == DockDetectEvent {
		tip := fmt.Sprintf("dock_tip_%s", e.Code)
		description = HMSEventDescMap[tip]
	} else if e.Type == DroneDetectEvent {
		tip := fmt.Sprintf("fpv_tip_%s", e.Code)
		description = HMSEventDescMap[tip+"_sky"]
		if description == "" {
			description = HMSEventDescMap[tip]
		}
	} else if e.Type == HighRiskForceBack {
		description = HighRiskForceCodeDescMap[e.Code]
	} else if e.Type == CruiseBreakReason {
		description = CruiseBreakCodeDescMap[e.Code]
	} else if e.Type == ServiceReplyError {
		description = ServiceReplyCodeDescMap[e.Code]
	} else {
		description = MaintenanceCodeDescMap[e.Code]
	}
	if description == "" {
		description = "暂未支持事件，无描述"
	}
	if args, ok := e.ExtraData["args"]; ok {
		if aid, ok := args.(map[string]any)["alarmid"]; ok {
			description = strings.Replace(description, "%alarmid", conv.Vtoa(aid.(int32)), -1)
		}
		if idx, ok := args.(map[string]any)["sensor_index"]; ok {
			id := conv.Vtoi(idx)
			description = strings.Replace(description, "%index", conv.Itoa(id+1), -1)
			description = strings.Replace(description, "%battery_index", lo.Ternary(id == 0, "左", "右"), -1)
		}
		if idx, ok := args.(map[string]any)["component_index"]; ok {
			id := conv.Vtoi(idx)
			description = strings.Replace(description, "%component_index", conv.Itoa(id+1), -1)
		}
	}
	return description
}

type EventListQuery struct {
	ProjectInfo
	Sort
	Page      int
	Size      int
	Sn        string
	Types     []string
	Levels    []int32
	StartTime time.Time
	EndTime   time.Time
}

type EventRepo interface {
	LockAutoback(ctx context.Context, id int64) bool
	UnlockAutoback(ctx context.Context, id int64) error
	SendManualSms(ctx context.Context, event *Event) error
	CreateEvent(ctx context.Context, body *Event) (*Event, error)
	UpdateEvent(ctx context.Context, id int64, body AnyMap) error
	ListEvents(ctx context.Context, query *EventListQuery) (int64, []*Event, error)
	ExportEvents(ctx context.Context, query *EventListQuery) (<-chan *Event, error)
}
