package biz

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"io"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/grafov/m3u8"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/pkg/id"
)

type StorageObjectMeta struct {
	ContentLength int64
	ContentType   string
	Signature     string
}

type StorageObject struct {
	URL    string
	Bucket string
	Key    string
	// Region     string
	// Endpoint   string
	// Expiration *time.Duration
	Tags []map[string]string
	Data io.Reader
	// 不指定ContentLength时，Data必须是io.ReadSeeker
	Meta *StorageObjectMeta
}

func (s *StorageObject) GetObjectName() string {
	if n := filepath.Base(s.Key); n != "" && n != "." {
		return n
	}
	return s.Key
}

type StorageObjectPutResult struct {
	Bucket string
	Key    string
	// The base64-encoded, 256-bit SHA-256 digest of the object.
	// ChecksumSHA256 string
}

type SessionTokenOption struct {
	Bucket string
	TTL    time.Duration
	// TODO
	Read bool
	// TODO
	Write bool
	Name  string
}

type StorageCredential struct {
	AccessKey    string
	AccessSecret string
	// 单位秒
	Expire       int64
	SessionToken string
	Bucket       string
	Endpoint     string
	// ali, aws, minio
	Provider string
	Region   string
}

type PreSignedObject struct {
	ObjectUrl string
	TTL       time.Duration
	// MD5       string
}

type ImageThumbnailResult struct {
	Key          string
	Size         int
	OriginKey    string
	OriginalSize int
	Cost         time.Duration
}

type ObjectLoader interface {
	GetObject(ctx context.Context, obj *StorageObject) (*StorageObjectMeta, io.ReadCloser, error)
}

type ObjectRemover interface {
	DeleteObject(ctx context.Context, obj *StorageObject) error
}

type SimpleStorageRepo interface {
	GetSessionToken(ctx context.Context, opt SessionTokenOption) (StorageCredential, error)
	PutObject(ctx context.Context, obj *StorageObject) (*StorageObjectPutResult, error)
	GetObject(ctx context.Context, obj *StorageObject) (*StorageObjectMeta, io.ReadCloser, error)
	GetSignedObjectAddr(ctx context.Context, obj *StorageObject, ttl time.Duration) (*PreSignedObject, error)
	// 使用内部请求地址计算签名
	GetInternalSignedObjectAddr(ctx context.Context, obj *StorageObject, ttl time.Duration) (*PreSignedObject, error)
	RefreshSignedUrl(ctx context.Context, u string, ttl time.Duration) (string, error)
	GenImageThumbnail(ctx context.Context, originalImage *StorageObject) (*ImageThumbnailResult, error)
	CheckObjectIfExists(ctx context.Context, obj *StorageObject) (bool, error)
	DeleteObject(ctx context.Context, obj *StorageObject) error
	GetSecondaryStorageBucket() (string, bool)
}

const publicFileKeyPrefix = "public"
const PublicFileURIPrefix = "/api/v1/storage"

const proxyFileURIPrefix = "/api/skai/v1/storage"

const M3U8FileProxyURIPrefix = "/api/v1/m3u8Storage"
const M3U8FileProxyURIPublicPrefix = "/api/skai/v1/m3u8Storage"

func newM3U8ProxyURI(pubUrl, objectKey string, sKey []byte) string {
	v := base64.URLEncoding.EncodeToString([]byte(objectKey))
	nonce := makeM3U8ProxyURINonce()
	exprAt := time.Now().Add(M3U8SegmentSignatureTTL)
	sig := signM3U8ProxyURI(objectKey, sKey, nonce, exprAt.UnixMilli())
	return fmt.Sprintf("%s%s/%s.m3u8?v=%s&n=%s&e=%d&s=%s",
		pubUrl, M3U8FileProxyURIPublicPrefix, id.NewXXHashId([]byte(objectKey)), v, nonce, exprAt.UnixMilli(), sig,
	)
}

func makeM3U8ProxyURINonce() string {
	nonce := make([]byte, 4)
	_, _ = rand.Read(nonce)
	return hex.EncodeToString(nonce)
}

func signM3U8ProxyURI(objectKey string, sKey []byte, nonce string, exprAt int64) string {
	s := hmac.New(sha256.New, sKey)
	s.Write([]byte(objectKey))
	s.Write([]byte(nonce))
	eb := make([]byte, 8)
	binary.BigEndian.PutUint64(eb, uint64(exprAt))
	s.Write(eb)
	return base64.URLEncoding.EncodeToString(s.Sum(nil)[:16])
}

func IsValidM3U8ProxyURI(objectKey string, sKey []byte, query url.Values) bool {

	var exprAt int64
	if v := query.Get("e"); v != "" {
		exprAt, _ = strconv.ParseInt(v, 10, 64)
	}
	if exprAt == 0 || time.Now().UnixMilli() > exprAt {
		return false
	}
	nonce := query.Get("n")
	sig := query.Get("s")
	return sig == signM3U8ProxyURI(objectKey, sKey, nonce, exprAt)
}

func getM3U8ObjectKeyFromQuery(query url.Values) (string, error) {
	if v, ok := query["v"]; ok && len(v) > 0 {
		key, err := base64.URLEncoding.DecodeString(v[0])
		if err != nil {
			return "", errors.BadRequest("invalid v query parameter", "文件不存在")
		}
		return string(key), nil
	} else {
		return "", errors.BadRequest("missing v query parameter", "非法请求地址")
	}
}

type StorageUsecase struct {
	log  *log.Helper
	c    *conf.Data
	sr   SimpleStorageRepo
	ar   AuthRepo
	sKey []byte
}

func NewStorageUsecase(
	logger log.Logger,
	c *conf.Data,
	sr SimpleStorageRepo,
	ar AuthRepo,
) *StorageUsecase {
	h := sha256.New()
	h.Write([]byte(c.Media.AdminToken))
	return &StorageUsecase{
		log:  log.NewHelper(logger),
		c:    c,
		sr:   sr,
		ar:   ar,
		sKey: h.Sum(nil),
	}
}

type PublicFile struct {
	Name string
	Data io.Reader
	Meta *StorageObjectMeta
}

func (f *PublicFile) GetObjectKey(av *SkaiAuthValue) string {
	name := f.Name
	if name == "" {
		name = f.Meta.Signature
	}
	now := time.Now().In(TimeLocation)
	return fmt.Sprintf("%s/avatar/%d/%s/%s-%s",
		publicFileKeyPrefix,
		av.AvatarId,
		now.Format(time.DateOnly),
		now.Format("15-04-05"),
		name,
	)
}

func newPublicFileURI(c *conf.Data, key string) string {
	return fmt.Sprintf("%s%s/%s", c.Skai.PublicUrl, proxyFileURIPrefix, key)
	// return proxyFileURIPrefix + "/" + key
}

func (u *StorageUsecase) getKeyFromURI(uri string) (string, error) {
	index := strings.Index(uri, PublicFileURIPrefix)
	if index < 0 {
		return "", NewBadRequestError("getKeyFromURI.invalidURI", map[string]string{"uri": uri})
	}
	if index >= len(publicFileKeyPrefix) {
		return "", NewNotFoundError("file", "uri", uri)
	}
	key := uri[index+len(PublicFileURIPrefix)+1:]
	if !strings.HasPrefix(key, publicFileKeyPrefix) {
		return "", NewNotFoundError("file", "key", key)
	}
	return key, nil
}

func (u *StorageUsecase) UploadPublicFile(ctx context.Context, f *PublicFile) (string, error) {
	av, err := u.ar.GetCurrentAuthValue(ctx)
	if err != nil {
		return "", err
	}
	key := f.GetObjectKey(av)
	ret, err := u.sr.PutObject(ctx, &StorageObject{
		Key:  key,
		Meta: f.Meta,
		Data: f.Data,
	})
	if err != nil {
		return "", err
	}
	return newPublicFileURI(u.c, ret.Key), nil
}

func (u *StorageUsecase) GetPublicFile(ctx context.Context, uri string) (*StorageObjectMeta, io.ReadCloser, error) {
	// av, err := u.ar.GetCurrentAuthValue(ctx)
	// if err != nil {
	// 	return nil, nil, err
	// }
	u.log.Debug("GetPublicFile file: ", uri)
	key, err := u.getKeyFromURI(uri)
	if err != nil {
		return nil, nil, err
	}
	return u.sr.GetObject(ctx, &StorageObject{Key: key})
}

func (u *StorageUsecase) PorxyM3U8File(ctx context.Context, query url.Values) (*StorageObjectMeta, io.ReadCloser, error) {
	key, err := getM3U8ObjectKeyFromQuery(query)
	if err != nil {
		return nil, nil, err
	}
	if !IsValidM3U8ProxyURI(key, u.sKey, query) {
		return nil, nil, errors.NotFound("invalid m3u8 proxy uri (1)", "无效的m3u8文件地址")
	}
	// TODO: 校验文件权限
	// name, err := lo.Last(strings.Split(key, "/"))
	m, data, err := u.sr.GetObject(ctx, &StorageObject{Key: key})
	if err != nil {
		return nil, nil, err
	}
	defer data.Close()
	l, _, err := m3u8.DecodeFrom(data, false)
	if err != nil {
		return nil, nil, errors.InternalServer("invalid m3u8 file", "错误的m3u8文件")
	}
	pl := l.(*m3u8.MediaPlaylist)
	now := time.Now()
	refreshSign := false
	sl := int(pl.Count())
	for i := 0; i < sl; i++ {
		v := pl.Segments[i]
		if u.ifSignedURLExpried(v.URI, now) {
			refreshSign = true
			break
		}
	}
	var fd *bytes.Buffer
	if refreshSign {
		segments := make([]*m3u8.MediaSegment, sl)
		for i := 0; i < sl; i++ {
			s := pl.Segments[i]
			v, err := u.sr.RefreshSignedUrl(ctx, s.URI, M3U8SegmentSignatureTTL)
			if err == nil {
				s.URI = v
			}
			segments[i] = s
		}
		pl.Segments = segments
		pld := pl.Encode().Bytes()
		fd = bytes.NewBuffer(pld)
		u.sr.PutObject(ctx, &StorageObject{
			Key:  key,
			Data: bytes.NewBuffer(pld),
		})
	} else {
		fd = pl.Encode()
	}
	m.ContentLength = int64(fd.Len())
	return m, io.NopCloser(fd), nil
}

func (u *StorageUsecase) ifSignedURLExpried(urlValue string, now time.Time) bool {
	pu, err := url.Parse(urlValue)
	if err != nil {
		return true
	}
	const amzDateLayout = "20060102T150405Z"
	amzDate, err := time.Parse(amzDateLayout, pu.Query().Get("X-Amz-Date"))
	if err != nil {
		return true
	}
	expires, err := strconv.ParseInt(pu.Query().Get("X-Amz-Expires"), 10, 64)
	if err != nil {
		return true
	}
	return amzDate.Add(time.Duration(expires) * time.Second).Before(now)
}
