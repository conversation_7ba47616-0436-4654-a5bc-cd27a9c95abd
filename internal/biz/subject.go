package biz

import (
	"context"
	"encoding/json"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type SubjectListQuery struct {
	ProjectInfo
	Page      int
	Size      int
	Unscoped  bool
	Search    *string
	EventType *string
}

type Subject struct {
	Id           int64
	Name         string
	Description  string
	EventType    AiEventType
	NoticeRules  []*NoticeRule
	ReportCount  int32
	PendingCount int32
	FinishCount  int32
	TenantId     int64
	ProjectId    int64
	AvatarId     int64
	EditorId     int64
	IsDeleted    bool
	Avatar       *Avatar
	Editor       *Avatar
	CreatedTime  time.Time
	EditedTime   time.Time
	UpdatedTime  time.Time
}

func (s *Subject) ReportIncr(count int32) AnyMap {
	s.ReportCount += count
	return AnyMap{"report_count": s.ReportCount}
}

func (s *Subject) PendingIncr(count int32) AnyMap {
	s.PendingCount += count
	return AnyMap{"pending_count": s.PendingCount}
}

func (s *Subject) FinishIncr(count int32) AnyMap {
	s.FinishCount += count
	return AnyMap{"finish_count": s.FinishCount}
}

func (s *Subject) Change(body *Subject) AnyMap {
	s.Name = body.Name
	// s.EventType = body.EventType
	s.Description = body.Description
	s.EditorId = body.EditorId
	s.EditedTime = time.Now()
	s.NoticeRules = body.NoticeRules
	noticeRules, _ := json.Marshal(s.NoticeRules)
	data := AnyMap{
		"name": s.Name,
		// "event_type":   s.EventType,
		"description":  s.Description,
		"editor_id":    s.EditorId,
		"edited_time":  s.EditedTime,
		"notice_rules": noticeRules,
	}
	return data
}

type SubjectRepo interface {
	CreateSubject(ctx context.Context, body *Subject) (*Subject, error)
	UpdateSubject(ctx context.Context, id int64, body AnyMap) error
	ListSubjects(ctx context.Context, query *SubjectListQuery) (int32, []*Subject, error)
	GetSubject(ctx context.Context, query *DetailQuery) (*Subject, error)
	DeleteSubject(ctx context.Context, id int64) error
}

type SubjectUsecase struct {
	aRepo AuthRepo
	sRepo SubjectRepo
	log   *log.Helper
}

func NewSubjectUsecase(logger log.Logger, aRepo AuthRepo, sRepo SubjectRepo) *SubjectUsecase {
	return &SubjectUsecase{aRepo: aRepo, sRepo: sRepo, log: log.NewHelper(logger)}
}

func (uc *SubjectUsecase) CreateSubject(ctx context.Context, body *Subject) (*Subject, error) {
	return uc.sRepo.CreateSubject(ctx, body)
}

func (uc *SubjectUsecase) ListSubject(ctx context.Context, query *SubjectListQuery) (int32, []*Subject, error) {
	return uc.sRepo.ListSubjects(ctx, query)
}

func (uc *SubjectUsecase) GetSubject(ctx context.Context, query *DetailQuery) (*Subject, error) {
	subject, err := uc.sRepo.GetSubject(ctx, query)
	if err != nil {
		return nil, err
	}
	subject.Avatar, _ = uc.aRepo.GetAvatar(ctx, subject.AvatarId)
	subject.Editor, _ = uc.aRepo.GetAvatar(ctx, subject.EditorId)
	return subject, nil
}

func (uc *SubjectUsecase) UpdateSubject(ctx context.Context, id int64, body AnyMap) error {
	return uc.sRepo.UpdateSubject(ctx, id, body)
}

func (uc *SubjectUsecase) DeleteSubject(ctx context.Context, id int64) error {
	return uc.sRepo.DeleteSubject(ctx, id)
}

func (uc *SubjectUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}
