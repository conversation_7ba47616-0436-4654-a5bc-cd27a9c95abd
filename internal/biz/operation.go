package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/pkg/types"
)

type OperationType string

const (
	TypeUnknown  OperationType = "UNKNOWN"  // 未知操作
	TypeDeploy   OperationType = "DEPLOY"   // 添加设备
	TypeDelete   OperationType = "DELETE"   // 删除设备
	TypeTakeoff  OperationType = "TAKEOFF"  // 启动巡航
	TypeCruise   OperationType = "CRUISE"   // 自动巡航
	TypeLaunch   OperationType = "LAUNCH"   // 一键起飞
	TypeReturn   OperationType = "RETURN"   // 一键返航
	TypeBacknow  OperationType = "BACKNOW"  // 强制返航
	TypeAutoback OperationType = "AUTOBACK" // 自动返航
	TypeCancel   OperationType = "CANCEL"   // 取消返航
	TypeReboot   OperationType = "REBOOT"   // 重启设备

	TypePauseAirline OperationType = "PAUSE_AIRLINE" // 暂停航线
	TypeBackAirline  OperationType = "BACK_AIRLINE"  // 返回航线

	TypeFlytoPoint   OperationType = "FLYTO_POINT"   // 飞向目标点
	TypeFlyoffPoint  OperationType = "FLYOFF_POINT"  // 终止目标点
	TypeFlywardPoint OperationType = "FLYWARD_POINT" // 更换目标点
	TypeLookatPoint  OperationType = "LOOKAT_POINT"  // 注视目标点

	TypeControlLens   OperationType = "CONTROL_LENS"   // 开启镜头控制
	TypeReleaseLens   OperationType = "RELEASE_LENS"   // 关闭镜头控制
	TypeSwitchLens    OperationType = "SWITCH_LENS"    // 切换镜头
	TypeSwitchZoom    OperationType = "SWITCH_ZOOM"    // 变焦镜头
	TypeResetGimbal   OperationType = "RESET_GIMBAL"   // 重置云台
	TypeSwitchClairty OperationType = "SWITCH_CLARITY" // 切换清晰度
	TypeTakePicture   OperationType = "TAKE_PICTURE"   // 开始拍照
	TypeStartVideo    OperationType = "START_VIDEO"    // 开始录像
	TypeStopVideo     OperationType = "STOP_VIDEO"     // 停止录像

	TypeControlAero OperationType = "CONTROL_AERO" // 开启飞行控制
	TypeReleaseAero OperationType = "RELEASE_AERO" // 关闭飞行控制
	TypeEnterFree   OperationType = "ENTER_FREE"   // 进入自由模式
	TypeExitFree    OperationType = "EXIT_FREE"    // 退出自由模式
	TypeExecuteFree OperationType = "EXECUTE_FREE" // 执行自由飞行
	TypeEnterOrbit  OperationType = "ENTER_ORBIT"  // 进入环绕模式
	TypeExitOrbit   OperationType = "EXIT_ORBIT"   // 退出环绕模式
	TypeSpeedOrbit  OperationType = "SPEED_ORBIT"  // 更新环绕速度

	TypeControlSpeaker OperationType = "CONTROL_SPEAKER" // 开启喊话控制
	TypeReleaseSpeaker OperationType = "RELEASE_SPEAKER" // 关闭喊话控制
	TypeTTSSpeaker     OperationType = "TTS_SPEAKER"     // TTS喊话
	TypeAudioSpeaker   OperationType = "AUDIO_SPEAKER"   // 语音喊话
	TypeStopSpeaker    OperationType = "STOP_SPEAKER"    // 停止喊话
	TypeAdjustVolume   OperationType = "ADJUST_VOLUME"   // 喊话音量调节
	TypeSetPlaymode    OperationType = "SET_PLAYMODE"    // 切换播放模式

	TypeRCLostAction  OperationType = "RC_LOST_ACTION" // 设置遥控失控行为
	TypeWLLostAction  OperationType = "WL_LOST_ACTION" // 设置航线失控动作
	TypeLimitDistance OperationType = "LIMIT_DISTANCE" // 设置限远距离

	TypeRemoteLogfile OperationType = "REMOTE_LOGFILE" // 查询远程日志
	TypeUploadLogfile OperationType = "UPLOAD_LOGFILE" // 上传远程日志

	TypeOpenDebug        OperationType = "OPEN_DEBUG"         // 开始调试模式
	TypeCloseDebug       OperationType = "CLOSE_DEBUG"        // 停止调试模式
	TypeOpenCover        OperationType = "OPEN_COVER"         // 开启机舱盖
	TypeCloseCover       OperationType = "CLOSE_COVER"        // 关闭机舱盖
	TypeOpenDrone        OperationType = "OPEN_DRONE"         // 开启无人机
	TypeCloseDrone       OperationType = "CLOSE_DRONE"        // 关闭无人机
	TypeOpenPutter       OperationType = "OPEN_PUTTER"        // 开启推杆
	TypeClosePutter      OperationType = "CLOSE_PUTTER"       // 关闭推杆
	TypeForceCloseCover  OperationType = "FORCE_CLOSE_COVER"  // 强制关闭机舱盖
	TypeChangeLiveCamera OperationType = "CHANGE_LIVE_CAMERA" // 切换直播摄像头
	TypeOpenCharge       OperationType = "OPEN_CHARGE"
	TypeCloseCharge      OperationType = "CLOSE_CHARGE"
)

func (s OperationType) String() string {
	return string(s)
}

func (s OperationType) Translate() string {
	switch s {
	case TypeDeploy:
		return "添加设备"
	case TypeTakeoff:
		return "航线飞行"
	case TypeCruise:
		return "自动巡航"
	case TypeLaunch:
		return "一键起飞"
	case TypeReturn:
		return "一键返航"
	case TypeBacknow:
		return "强制返航"
	case TypeAutoback:
		return "自动返航"
	case TypeCancel:
		return "取消返航"
	case TypeReboot:
		return "重启设备"

	case TypePauseAirline:
		return "暂停航线"
	case TypeBackAirline:
		return "返回航线"

	case TypeControlLens:
		return "开启镜头控制"
	case TypeReleaseLens:
		return "关闭镜头控制"
	case TypeSwitchLens:
		return "切换镜头"
	case TypeSwitchZoom:
		return "变焦镜头"
	case TypeResetGimbal:
		return "重置云台"
	case TypeSwitchClairty:
		return "切换清晰度"
	case TypeTakePicture:
		return "开始拍照"
	case TypeStartVideo:
		return "开始录像"
	case TypeStopVideo:
		return "停止录像"

	case TypeFlytoPoint:
		return "飞向目标点"
	case TypeFlyoffPoint:
		return "终止目标点"
	case TypeFlywardPoint:
		return "更换目标点"
	case TypeLookatPoint:
		return "注视目标点"

	case TypeControlAero:
		return "开启飞行控制"
	case TypeReleaseAero:
		return "关闭飞行控制"
	case TypeEnterFree:
		return "开启自由飞行"
	case TypeExitFree:
		return "关闭自由飞行"
	case TypeExecuteFree:
		return "飞行方位控制"
	case TypeEnterOrbit:
		return "开启环绕飞行"
	case TypeExitOrbit:
		return "关闭环绕飞行"
	case TypeSpeedOrbit:
		return "更新环绕速度"

	case TypeControlSpeaker:
		return "开启喊话控制"
	case TypeReleaseSpeaker:
		return "关闭喊话控制"
	case TypeTTSSpeaker:
		return "TTS喊话"
	case TypeAudioSpeaker:
		return "语音喊话"
	case TypeStopSpeaker:
		return "停止喊话"
	case TypeAdjustVolume:
		return "喊话音量调节"
	case TypeSetPlaymode:
		return "切换播放模式"

	case TypeRCLostAction:
		return "设置遥控失控行为"
	case TypeWLLostAction:
		return "设置航线失控动作"
	case TypeLimitDistance:
		return "设置限远距离"

	case TypeRemoteLogfile:
		return "查询远程日志"
	case TypeUploadLogfile:
		return "上传远程日志"

	case TypeOpenDebug:
		return "开启调试模式"
	case TypeCloseDebug:
		return "关闭调试模式"
	case TypeOpenCover:
		return "打开机舱盖"
	case TypeCloseCover:
		return "关闭机舱盖"
	case TypeOpenDrone:
		return "飞行器开机"
	case TypeCloseDrone:
		return "飞行器关机"
	case TypeOpenPutter:
		return "推杆展开"
	case TypeClosePutter:
		return "推杆闭合"
	case TypeForceCloseCover:
		return "强制关闭机舱盖"
	case TypeOpenCharge:
		return "打开充电"
	case TypeCloseCharge:
		return "关闭充电"
	default:
		return "未知操作"
	}
}

func (s OperationType) ToControlAction() ActionControl {
	action := NoneControl
	switch s {
	case TypeTakeoff, TypeCruise, TypeLaunch, TypeReturn, TypeBacknow, TypeAutoback, TypeCancel:
		action = FlyControl
	case TypePauseAirline, TypeBackAirline:
		action = HoverControl
	case TypeControlLens, TypeReleaseLens:
		action = LensControl
	case TypeControlAero, TypeReleaseAero:
		action = AeroControl
	case TypeControlSpeaker, TypeReleaseSpeaker:
		action = SpeakerControl
	}
	return action
}

func (s OperationType) ToServiceIdentifier() DockServiceIdentifier {
	return operationServiceRelations[s]
}

// 需要执行过程的操作
var JobOperations = types.NewHashSetFromSlice([]OperationType{
	TypeOpenCover, TypeCloseCover, TypeForceCloseCover, TypeOpenDrone, TypeCloseDrone,
	TypeOpenPutter, TypeClosePutter, TypeOpenCharge, TypeCloseCharge,
})

var operationServiceRelations = map[OperationType]DockServiceIdentifier{
	TypeUnknown: DockServiceIdentifierUnknown,

	TypeTakeoff:  DockServiceIdentifierTakeoff,
	TypeCruise:   DockServiceIdentifierTakeoff,
	TypeLaunch:   DockServiceIdentifierLaunch,
	TypeReturn:   DockServiceIdentifierReturn,
	TypeBacknow:  DockServiceIdentifierReturn,
	TypeAutoback: DockServiceIdentifierReturn,
	TypeReboot:   DockServiceIdentifierReboot,
	TypeCancel:   DockServiceIdentifierCancelReturn,

	TypePauseAirline: DockServiceIdentifierPauseAirline,
	TypeBackAirline:  DockServiceIdentifierBackAirline,

	TypeFlytoPoint:   DockServiceIdentifierFlytoPoint,
	TypeFlyoffPoint:  DockServiceIdentifierFlyoffPoint,
	TypeFlywardPoint: DockServiceIdentifierUpdatePoint,
	TypeLookatPoint:  DockServiceIdentifierLookatPoint,

	TypeControlLens:   DockServiceIdentifierControlLens,
	TypeSwitchLens:    DockServiceIdentifierSwitchLens,
	TypeSwitchZoom:    DockServiceIdentifierSetFocalLength,
	TypeResetGimbal:   DockServiceIdentifierResetGimbal,
	TypeSwitchClairty: DockServiceIdentifierSwitchClarity,
	TypeTakePicture:   DockServiceIdentifierTakePicture,
	TypeStartVideo:    DockServiceIdentifierStartVideo,
	TypeStopVideo:     DockServiceIdentifierStopVideo,

	TypeControlAero: DockServiceIdentifierControlAero,
	TypeEnterFree:   DockServiceIdentifierEnterDRCMode,
	TypeExitFree:    DockServiceIdentifierExitDRCMode,
	TypeEnterOrbit:  DockServiceIdentifierEnterPOIMode,
	TypeExitOrbit:   DockServiceIdentifierExitPOIMode,
	TypeSpeedOrbit:  DockServiceIdentifierUpdatePOISpeed,

	TypeTTSSpeaker:   DockServiceIdentifierStartSpeaker,
	TypeAudioSpeaker: DockServiceIdentifierStartSpeaker,
	TypeStopSpeaker:  DockServiceIdentifierStopSpeaker,
	TypeAdjustVolume: DockServiceIdentifierSetSpeakerVolume,
	TypeSetPlaymode:  DockServiceIdentifierSetSpeakerPlayMode,

	TypeRCLostAction:  DockServiceIdentifierSetProperty,
	TypeWLLostAction:  DockServiceIdentifierSetProperty,
	TypeLimitDistance: DockServiceIdentifierSetProperty,

	TypeRemoteLogfile: DockServiceIdentifierListFileupload,
	TypeUploadLogfile: DockServiceIdentifierStartFileupload,

	TypeOpenDebug:        DockServiceIdentifierOpenDebugMode,
	TypeCloseDebug:       DockServiceIdentifierCloseDebugMode,
	TypeOpenCover:        DockServiceIdentifierOpenCover,
	TypeCloseCover:       DockServiceIdentifierCloseCover,
	TypeOpenDrone:        DockServiceIdentifierOpenDrone,
	TypeCloseDrone:       DockServiceIdentifierCloseDrone,
	TypeOpenPutter:       DockServiceIdentifierOpenPutter,
	TypeClosePutter:      DockServiceIdentifierClosePutter,
	TypeForceCloseCover:  DockServiceIdentifierForceCloseCover,
	TypeChangeLiveCamera: DockServiceIdentifierChangeLiveCamera,
	TypeOpenCharge:       DockServiceIdentifierOpenCharge,
	TypeCloseCharge:      DockServiceIdentifierCloseCharge,
}

func NewOperationType(v string) OperationType {
	switch strings.ToUpper(v) {
	case TypeRCLostAction.String():
		return TypeRCLostAction
	case TypeWLLostAction.String():
		return TypeWLLostAction
	case TypeLimitDistance.String():
		return TypeLimitDistance
	default:
		return TypeUnknown
	}
}

type OperationSource string

const (
	SourceDevice  OperationSource = "DEVICE"
	SourceAirline OperationSource = "AIRLINE"
	SourceMission OperationSource = "MISSION"
)

func (c OperationSource) String() string {
	return string(c)
}

type OperationStatus string

const (
	OperationStatusPending OperationStatus = "PENDING"
	OperationStatusExecute OperationStatus = "EXECUTE"
	OperationStatusSuspend OperationStatus = "SUSPEND"
	OperationStatusCancel  OperationStatus = "CANCAL"
	OperationStatusReject  OperationStatus = "REJECT"
	OperationStatusTimeout OperationStatus = "TIMEOUT"
	OperationStatusSuccess OperationStatus = "SUCCESS"
	OperationStatusFailure OperationStatus = "FAILURE"
)

func (c OperationStatus) String() string {
	return string(c)
}

func (c OperationStatus) Simplify() string {
	switch c {
	case OperationStatusPending:
		return c.String()
	case OperationStatusSuccess:
		return c.String()
	default:
		return OperationStatusFailure.String()
	}
}

type Operation struct {
	Id          int64           `json:"id,string,omitempty"`
	Sn          string          `json:"sn,omitempty"`
	MerchantId  int64           `json:"merchantId,string,omitempty"`
	TenantId    int64           `json:"tenantId,string,omitempty"`
	AvatarId    int64           `json:"avatarId,string,omitempty"`
	From        string          `json:"from,omitempty"`
	Avatar      *Avatar         `json:"-"`
	Timeout     time.Duration   `json:"-"`
	Type        OperationType   `json:"type,omitempty"`
	Status      OperationStatus `json:"status,omitempty"`
	Source      OperationSource `json:"source,omitempty"`
	SourceId    int64           `json:"sourceId,string,omitempty"`
	Message     string          `json:"message,omitempty"`
	Content     AnyMap          `json:"content,omitempty"`
	CreatedTime time.Time       `json:"createdTime,omitempty"`
	UpdatedTime time.Time       `json:"updatedTime,omitempty"`
}

func (o *Operation) Belong(avatar *Avatar) {
	o.Avatar = avatar
}

func (o *Operation) Update(status OperationStatus, message string) AnyMap {
	o.Status = status
	o.Message = message
	return AnyMap{"status": o.Status.String(), "message": o.Message}
}

func (o *Operation) GetContent(contenPayload any) error {
	if len(o.Content) == 0 {
		return nil
	}
	v, err := json.Marshal(o.Content)
	if err != nil {
		return err
	}
	return json.Unmarshal(v, contenPayload)
}

type OperationListQuery struct {
	ProjectInfo
	Sort
	Page      int
	Size      int
	SourceId  int64
	Type      *OperationType
	Types     []OperationType
	StartTime time.Time
	EndTime   time.Time
}

type OperationRepo interface {
	CreateOperation(ctx context.Context, body *Operation) (*Operation, error)
	UpdateOperation(ctx context.Context, id int64, body AnyMap) error
	ListOperation(ctx context.Context, query *OperationListQuery) (int64, []*Operation, error)
	GetOperation(ctx context.Context, id int64) (*Operation, error)
}

type OperationUsecase struct {
	log   *log.Helper
	or    OperationRepo
	dr    DeviceRepo
	cr    ConnectRepo
	mr    MQTTRepo
	delay DelayRepo
}

func NewOperationUsecase(logger log.Logger, or OperationRepo, dr DeviceRepo, cr ConnectRepo, mr MQTTRepo, delay DelayRepo) *OperationUsecase {
	return &OperationUsecase{log: log.NewHelper(logger), or: or, dr: dr, cr: cr, mr: mr, delay: delay}
}

func (u *OperationUsecase) ExecServiceOperationWithPayload(ctx context.Context, dev *Device, o *Operation, payload any) (*Operation, error) {
	op, err := u.or.CreateOperation(ctx, o)
	if err != nil {
		return nil, err
	}
	sid := op.Type.ToServiceIdentifier()
	if err := u.delay.CreateTask(ctx, &DelayTask{
		Times:    1,
		Delay:    JobServiceTimeout,
		Source:   "SKAI-CONNECT",
		Key:      NewOperationTimeoutDelayTaskKey(op),
		Callback: fmt.Sprintf("/internal/v1/devices/%d/callback", dev.Id),
		Payload:  AnyMap{"type": "service", "action": "timeout", "sourceId": conv.Itoa(op.Id)},
	}); err != nil {
		u.log.WithContext(ctx).Errorf("ExecServiceOperation create delay task for op %+v error %v", op, err)
		return nil, err
	}
	if err := u.cr.SendDownlink(ctx, dev, u.mr, sid.String(), &DockService{
		Sn:         dev.Sn,
		DeviceId:   dev.Id,
		ServiceId:  op.Id,
		Identifier: sid,
		Timeout:    JobServiceTimeout,
		Payload:    payload,
	}); err != nil {
		return nil, err
	}
	return op, nil
}

func (u *OperationUsecase) ExecServiceOperation(ctx context.Context, dev *Device, o *Operation) (*Operation, error) {
	return u.ExecServiceOperationWithPayload(ctx, dev, o, nil)
}
