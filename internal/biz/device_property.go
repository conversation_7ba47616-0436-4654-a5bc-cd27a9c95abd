package biz

import (
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gitlab.sensoro.com/skai/skai/pkg/types"
)

var DevicePropertyMap = map[string]struct {
	Name       string
	Unit       string
	Dictionary StringMap
}{
	"MsgType":              {Name: "消息类型", Unit: "", Dictionary: StringMap{"0": "空包", "1": "机场", "2": "遥控", "3": "无人机", "4": "喊话器"}},
	"UppedTime":            {Name: "上行时间", Unit: ""},
	"TakeoffTime":          {Name: "起飞时间", Unit: ""},
	"DockState":            {Name: "机场状态", Unit: "", Dictionary: StringMap{"0": "空闲中", "1": "现场调试", "2": "远程调试", "3": "固件升级中", "4": "作业中"}},
	"NetworkType":          {Name: "网络类型", Unit: "", Dictionary: StringMap{"0": "未知", "1": "4G", "2": "以太网", "3": "Wi-Fi"}},
	"SignalQuality":        {Name: "网络质量", Unit: "", Dictionary: StringMap{"NONE": "无", "BAD": "差", "NORMAL": "良", "GOOD": "优"}},
	"FirmwareVersion":      {Name: "固件版本", Unit: ""},
	"Humidity":             {Name: "舱内湿度", Unit: "%"},
	"Tmperature":           {Name: "舱内温度", Unit: "℃"},
	"UsedStorage":          {Name: "存储容量", Unit: "G"},
	"TotalStorage":         {Name: "总容量", Unit: "G"},
	"CoverState":           {Name: "舱盖状态", Unit: "", Dictionary: StringMap{"0": "关闭", "1": "打开", "2": "半开", "3": "舱盖状态异常"}},
	"DroneOnlineState":     {Name: "飞机开关机", Unit: "", Dictionary: StringMap{"0": "关机", "1": "开机"}},
	"PutterState":          {Name: "推杆状态", Unit: "", Dictionary: StringMap{"0": "关闭", "1": "打开", "2": "半开", "3": "推杆状态异常"}},
	"AirCondState":         {Name: "空调状态", Unit: "", Dictionary: StringMap{"0": "空闲模式", "1": "制冷模式", "2": "制热模式", "3": "除湿模式", "4": "制冷退出模式", "5": "制热退出模式", "6": "除湿退出模式", "7": "制冷准备模式", "8": "制热准备模式", "9": "除湿准备模式"}},
	"SupplyVoltage":        {Name: "市电电压", Unit: "V"},
	"WorkingVoltage":       {Name: "工作电压", Unit: "V"},
	"WorkingCurrent":       {Name: "工作电流", Unit: "A"},
	"BackupBatteryVoltage": {Name: "备用电池", Unit: "V"},
	"Capacity":             {Name: "设备电量", Unit: "%"},
	"Status":               {Name: "设备状态", Unit: ""},
	"IsRain":               {Name: "是否有雨", Unit: ""},
	"Rainfall":             {Name: "天气", Unit: "", Dictionary: StringMap{"0": "无雨", "1": "小雨", "2": "中雨", "3": "大雨"}},
	"WindSpeed":            {Name: "风速", Unit: "m/s"},
	"WindDirection":        {Name: "风向", Unit: "风", Dictionary: StringMap{"1": "正北", "2": "东北", "3": "东", "4": "东南", "5": "南", "6": "西南", "7": "西", "8": "西北"}},
	"EnvHumidity":          {Name: "环境湿度", Unit: "%"},
	"EnvTemperature":       {Name: "环境温度", Unit: "℃"},
	"WirelessMode":         {Name: "图传链路模式", Unit: "", Dictionary: StringMap{"0": "SDR模式", "1": "4G融合模式"}},
	"QualityIT":            {Name: "图传质量", Unit: "", Dictionary: StringMap{"0": "无", "1": "差", "2": "差", "3": "良", "4": "优", "5": "优"}},
	"Quality4G":            {Name: "4G信号质量", Unit: "", Dictionary: StringMap{"0": "无", "1": "差", "2": "差", "3": "良", "4": "优", "5": "优"}},
	"QualitySDR":           {Name: "SDR信号质量", Unit: "", Dictionary: StringMap{"0": "无", "1": "差", "2": "差", "3": "良", "4": "优", "5": "优"}},
	"IsFixed":              {Name: "是否收敛", Unit: "", Dictionary: StringMap{"0": "非开始", "1": "收敛中", "2": "收敛成功", "3": "收敛失败"}},
	"GPSNumber":            {Name: "GPS搜星数", Unit: ""},
	"RTKNumber":            {Name: "RTK搜星数", Unit: ""},
	"CabinStatus":          {Name: "在舱状态", Unit: "", Dictionary: StringMap{"0": "离舱", "1": "在舱"}},
	"DroneState":           {Name: "飞机状态", Unit: "", Dictionary: StringMap{"0": "待机", "1": "起飞准备", "2": "起飞准备完毕", "3": "手动飞行", "4": "自动起飞", "5": "航线飞行", "6": "全景拍照", "7": "智能跟随", "8": "ADS-B 躲避", "9": "自动返航", "10": "自动降落", "11": "强制降落", "12": "三桨叶降落", "13": "升级中", "14": "未连接", "15": "辅助飞行功能 - APAS", "16": "虚拟摇杆模式", "17": "指令飞行", "18": "空中RTK收敛模式", "19": "机场选址中", "20": "POI环绕"}},
	"BatteryCapacity":      {Name: "飞机电量", Unit: "%"},
	"ChildrenCapacity":     {Name: "飞机电池组电量", Unit: "%"},
	"RemainTime":           {Name: "剩余续航时间", Unit: "ms"},
	"ReturnCapacity":       {Name: "返航所需电量", Unit: "%"},
	"ChargeState":          {Name: "充电状态", Unit: "", Dictionary: StringMap{"0": "空闲", "1": "充电中"}},
	"ChargePercent":        {Name: "充电电量", Unit: "%"},
	"PilotLongitude":       {Name: "遥控经度", Unit: ""},
	"PilotLatitude":        {Name: "遥控纬度", Unit: ""},
	"CurrentWaypoint":      {Name: "当前执行航点", Unit: ""},
	"CourseWaypoints":      {Name: "行进航点列表", Unit: ""},
	"VerticalSpeed":        {Name: "垂直速度", Unit: "m/s"},
	"HorizontalSpeed":      {Name: "水平速度", Unit: "m/s"},
	"Longitude":            {Name: "经度", Unit: ""},
	"Latitude":             {Name: "纬度", Unit: ""},
	"Height":               {Name: "海拔高度", Unit: "m"},
	"Elevation":            {Name: "距起飞点高度", Unit: "m"},
	"Yaw":                  {Name: "偏航角", Unit: "°"},
	"Roll":                 {Name: "横滚角", Unit: "°"},
	"Pitch":                {Name: "俯仰角", Unit: "°"},
	"TotalRuntime":         {Name: "累积巡航时长", Unit: "s"},
}

type DeviceProperty struct {
	MsgType              int            `json:"msgType,omitempty"`              // 消息类型
	UppedTime            int64          `json:"uppedTime,omitempty"`            // 上行时间
	ShieldTime           int64          `json:"shieldTime,omitempty"`           // 屏障时间
	DockState            *int32         `json:"dockState,omitempty"`            // 机场状态
	DockHeight           *float32       `json:"dockHeight,omitempty"`           // 机舱海拔
	NetworkType          int32          `json:"networkType,omitempty"`          // 网络类型
	SignalQuality        string         `json:"signalQuality,omitempty"`        // 信号质量
	FirmwareVersion      *string        `json:"firmwareVersion,omitempty"`      // 固件版本
	Humidity             *float32       `json:"humidity,omitempty"`             // 舱内湿度
	Tmperature           *float32       `json:"tmperature,omitempty"`           // 舱内温度
	UsedStorage          *int64         `json:"usedStorage,omitempty"`          // 存储容量
	TotalStorage         *int64         `json:"totalStorage,omitempty"`         // 总容量
	CoverState           *int32         `json:"coverState,omitempty"`           // 舱盖状态
	DroneOnlineState     *int32         `json:"droneOnlineState,omitempty"`     // 无人机在线状态
	PutterState          *int32         `json:"putterState,omitempty"`          // 推杆状态
	AirCondState         *int32         `json:"airCondState,omitempty"`         // 空调状态
	SupplyVoltage        *int32         `json:"supplyVoltage,omitempty"`        // 市电电压
	WorkingVoltage       *int32         `json:"workingVoltage,omitempty"`       // 工作电压
	WorkingCurrent       *int32         `json:"workingCurrent,omitempty"`       // 工作电流
	BackupBatteryVoltage *int32         `json:"backupBatteryVoltage,omitempty"` // 备用电池
	Model                string         `json:"model,omitempty"`                // 设备型号
	Status               string         `json:"status,omitempty"`               // 设备状态
	Capacity             int32          `json:"capacity,omitempty"`             // 设备电量
	IsRain               bool           `json:"isRain,omitempty"`               // 是否有雨
	Rainfall             *int32         `json:"rainfall,omitempty"`             // 下雨等级
	WindSpeed            *float32       `json:"windSpeed,omitempty"`            // 风速
	WindDirection        *int32         `json:"windDirection,omitempty"`        // 风向
	EnvHumidity          *float32       `json:"envHumidity,omitempty"`          // 湿度
	EnvTemperature       *float32       `json:"envTemperature,omitempty"`       // 温度
	WirelessMode         *int32         `json:"wirelessMode,omitempty"`         // 图传链路模式
	Quality4G            *int32         `json:"quality4G,omitempty"`            // 4G信号质量
	QualityIT            *int32         `json:"qualityIT,omitempty"`            // 图传质量
	QualitySDR           *int32         `json:"qualitySDR,omitempty"`           // SDR信号质量
	IsFixed              *int32         `json:"isFixed,omitempty"`              // 是否收敛
	GPSNumber            *int32         `json:"gpsNumber,omitempty"`            // GPS搜星数
	RTKNumber            *int32         `json:"rtkNumber,omitempty"`            // RTK搜星数
	CabinStatus          *int32         `json:"cabinStatus,omitempty"`          // 在舱状态
	Algorithm            *string        `json:"algorithm,omitempty"`            // 加载算法
	TakeoffTime          int64          `json:"takeoffTime,omitempty"`          // 起飞时间
	PilotLongitude       *float64       `json:"pilotLongitude,omitempty"`       // 遥控经度
	PilotLatitude        *float64       `json:"pilotLatitude,omitempty"`        // 遥控纬度
	DroneState           *int32         `json:"droneState,omitempty"`           // 飞机状态
	LastDroneState       *int32         `json:"lastDroneState,omitempty"`       // 上次状态
	BatteryCapacity      *int32         `json:"batteryCapacity,omitempty"`      // 总剩余电量
	ChildrenCapacity     []any          `json:"childrenCapacity,omitempty"`     // 子组剩余电量
	RemainTime           *int64         `json:"remainTime,omitempty"`           // 剩余续航时间
	ReturnCapacity       *int32         `json:"returnCapacity,omitempty"`       // 返航所需电量
	ChargeState          *int32         `json:"chargeState,omitempty"`          // 充电状态
	ChargePercent        *int32         `json:"chargePercent,omitempty"`        // 充电电量
	CurrentWaypoint      *int32         `json:"currentWaypoint,omitempty"`      // 当前执行航点
	CourseWaypoints      []any          `json:"courseWaypoints,omitempty"`      // 行进航点列表
	VerticalSpeed        *float32       `json:"verticalSpeed,omitempty"`        // 垂直速度
	HorizontalSpeed      *float32       `json:"horizontalSpeed,omitempty"`      // 水平速度
	Longitude            *float64       `json:"longitude,omitempty"`            // 飞机经度
	Latitude             *float64       `json:"latitude,omitempty"`             // 飞机纬度
	Height               *float32       `json:"height,omitempty"`               // 海拔高度
	Elevation            *float32       `json:"elevation,omitempty"`            // 相对高度
	Yaw                  *float32       `json:"yaw,omitempty"`                  // 偏航角
	Roll                 *float32       `json:"roll,omitempty"`                 // 横滚角
	Pitch                *float32       `json:"pitch,omitempty"`                // 俯仰角
	Mileage              int32          `json:"mileage,omitempty"`              // 飞行距离
	TotalRuntime         *int64         `json:"totalRuntime,omitempty"`         // 累积巡航时长
	AeroCameras          []*AeroCamera  `json:"-"`                              // 飞行相机信息
	Speaker              *SpeakerWidget `json:"-"`                              // 喊话器子装置
}

func (dp *DeviceProperty) Dislink() *DeviceProperty {
	return &DeviceProperty{Algorithm: dp.Algorithm, TakeoffTime: dp.TakeoffTime, ChargePercent: dp.ChargePercent}
}

func (dp *DeviceProperty) AnalyzeStatus(status Status) (Status, Gradient) {
	switch dp.MsgType {
	case 1:
		return dp.analyzeDockStatus(status) // 机场设备
	case 2:
		return dp.analyzePilotStatus(status) // 遥控设备
	case 3:
		return dp.analyzeDroneStatus(status) // 飞机设备
	default:
	}
	return status, GradientNone
}

func (dp *DeviceProperty) analyzeDockStatus(status Status) (Status, Gradient) {
	gradient := GradientNone
	if dp.DockState == nil {
		return status, gradient
	}
	// 设备离线时上行，重置为在线
	if status == StatusOffline {
		status = StatusStandby
		gradient = GradientOnline
	}
	if status == StatusStandby && *dp.DockState == 1 {
		// 上次设备待机中且本次机场状态为1，表示开启测试
		status = StatusIntest
		gradient = GradientOnTest
	} else if status == StatusIntest && *dp.DockState == 0 {
		// 上次设备测试中且本次机场状态为0，表示关闭测试
		status = StatusStandby
		gradient = GradientOffTest
	} else if status == StatusStandby && *dp.DockState == 2 {
		// 上次设备待机中且本次机场状态为2，表示开启调试
		status = StatusDebug
		gradient = GradientOnDebug
	} else if status == StatusDebug && *dp.DockState == 0 {
		// 上次设备调试中且本次机场状态为0，表示关闭调试
		status = StatusStandby
		gradient = GradientOffDebug
	} else if status == StatusStandby && *dp.DockState == 3 {
		// 上次设备待机中且本次机场状态为3，表示开始升级
		status = StatusUpgrade
		gradient = GradientOnUpgrade
	} else if status == StatusUpgrade && *dp.DockState == 0 {
		// 上次设备升级中且本次机场状态为0，表示结束升级
		status = StatusStandby
		gradient = GradientOffUpgrade
	} else if status == StatusStandby && *dp.DockState == 4 {
		// 上次设备待机中且本次机场状态为4，表示开始飞行
		status = StatusExecution
		gradient = GradientOnFlight
	} else if status == StatusExecution && *dp.DockState == 0 {
		// 上次设备执行中且本次机场状态为0，表示结束飞行
		status = StatusStandby
		gradient = GradientOffFlight
	}
	return status, gradient
}

func (dp *DeviceProperty) analyzePilotStatus(status Status) (Status, Gradient) {
	gradient := GradientNone
	// 设备离线时上行，重置为在线
	if status == StatusOffline {
		status = StatusStandby
		gradient = GradientOnline
	}
	return status, gradient
}

func (dp *DeviceProperty) analyzeDroneStatus(status Status) (Status, Gradient) {
	gradient := GradientNone
	// 设备离线或飞机空状态，上行忽略
	if status == StatusOffline || dp.DroneState == nil {
		return status, gradient
	}
	// 机场无需飞机状态分析
	if dp.Model != ModelDJIPilot.String() {
		return status, gradient
	}
	// 遥控设备状态需要根据飞机状态分析
	if status == StatusStandby && lo.Contains([]int32{3, 4, 5}, *dp.DroneState) {
		// 上次设备待机中且本次飞机状态为3/4/5，表示开始飞行
		status = StatusExecution
		gradient = GradientOnFlight
	} else if status == StatusExecution && *dp.DroneState == 0 {
		// 上次设备执行中且本次飞机状态为0，表示结束飞行
		status = StatusStandby
		gradient = GradientOffFlight
	}
	return status, gradient
}

func (dp *DeviceProperty) Convert() AnyMap {
	data := make(AnyMap, 0)
	for k, v := range utilities.StructToMap(dp) {
		property := AnyMap{}
		if p, ok := DevicePropertyMap[k]; ok {
			property["name"] = p.Name
			property["unit"] = p.Unit
			if types.IsNil(v) {
				v = "-"
			} else {
				if types.IsPtr(v) {
					v = types.GetElem(v)
				}
				property["raw"] = v
				if p.Dictionary != nil {
					if t, ok := p.Dictionary[conv.Vtoa(v)]; ok {
						v = t
					} else {
						v = "未知"
					}
				}
			}
			property["value"] = v
		}
		data[k] = property
	}
	return data
}
