package biz

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/grafov/m3u8"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gitlab.sensoro.com/skai/skai/pkg/id"
)

type ConnectRepo interface {
	PushDockProperties(ctx context.Context, dp *DockProperties) error
	PushControllerProperties(ctx context.Context, cp *RemoteControllerProperties) error
	PushDroneProperties(ctx context.Context, dp *DroneProperties) error
	PushEvent(ctx context.Context, eventType ThingModelEventType, event any) (*EventUpReply, error)
	SendDownlink(ctx context.Context, dev *Device, puber PacketPublisher, serviceId string, serviceData any) error
	PushDockServiceReply(ctx context.Context, r *DockServiceReply) error
	RunDownlinkNextStep(ctx context.Context, dev *Device, puber PacketPublisher, reply any) (bool, error)
	GetDownlinkState(ctx context.Context, model DeviceModel, downId string) (any, error)
}

type ConnectUsecase struct {
	aRepo AirlineRepo
	tRepo DelayRepo
	eRepo EventRepo
	dRepo DeviceRepo
	cRepo ConnectRepo
	qRepo MQTTRepo
	oRepo OperationRepo
	sRepo SimpleStorageRepo
	vRepo VoyageRepo
	iRepo MediaRepo
	lRepo LiveRepo
	mRepo MissionRepo
	xRepo ExecutionRepo
	pRepo SnapshotRepo
	zRepo AnalyserRepo
	fRepo LogfileRepo
	log   *log.Helper
}

func NewConnectUsecase(logger log.Logger, aRepo AirlineRepo, tRepo DelayRepo, eRepo EventRepo, dRepo DeviceRepo, cRepo ConnectRepo, qRepo MQTTRepo, oRepo OperationRepo, sRepo SimpleStorageRepo, vRepo VoyageRepo,
	iRepo MediaRepo,
	lRepo LiveRepo,
	mRepo MissionRepo,
	xRepo ExecutionRepo,
	pRepo SnapshotRepo,
	zRepo AnalyserRepo,
	fRepo LogfileRepo,
) *ConnectUsecase {
	return &ConnectUsecase{
		log:   log.NewHelper(logger),
		aRepo: aRepo, dRepo: dRepo, cRepo: cRepo, tRepo: tRepo, eRepo: eRepo, qRepo: qRepo,
		oRepo: oRepo, sRepo: sRepo, vRepo: vRepo, iRepo: iRepo, lRepo: lRepo, mRepo: mRepo,
		xRepo: xRepo, pRepo: pRepo, zRepo: zRepo, fRepo: fRepo,
	}
}

func (uc *ConnectUsecase) GetDevice(ctx context.Context, query *DetailQuery) (*Device, error) {
	return uc.dRepo.GetDevice(ctx, query)
}

func (uc *ConnectUsecase) PropertyUp(ctx context.Context, device *Device, body *DeviceProperty, deviceState *DeviceProperty) bool {
	// speical uplink for speaker
	if body.MsgType == 4 {
		uc.dRepo.UpdateDevice(ctx, device.Id, device.Buzzing(body.Speaker))
		return true
	}
	// analyze device offline delay
	if time.UnixMilli(deviceState.ShieldTime).Add(10 * time.Second).Before(time.Now()) {
		if err := uc.SetOffline(ctx, device.Id); err == nil {
			body.ShieldTime = time.Now().UnixMilli()
		}
	}
	// analyze device status
	device.AnalyzeStatus(body)
	// analyze device hms events
	for _, event := range device.DetectHMSEvents(body) {
		uc.log.Infof("PropertyUp detect hms event: %s", event.Code)
		uc.EventUp(ctx, device, event.Type, event)
	}
	// analyze pilot takeoff event
	if atoEvent := device.AnalyzeTakeoffEvent(body); atoEvent != nil {
		uc.log.Infof("PropertyUp analyze pilot takeoff event: %d", atoEvent.DeviceId)
		uc.EventUp(ctx, device, atoEvent.Type, atoEvent)
	}
	// analyze live start event
	if aslEvent := device.AnalyzeStartLiveEvent(body); aslEvent != nil {
		uc.log.Infof("PropertyUp analyze live start event: %t", aslEvent.Status)
		uc.EventUp(ctx, device, aslEvent.Type, aslEvent)
	}
	// analyze force back event
	if afbEvent := device.AnalyzeForceBackEvent(body); afbEvent != nil {
		uc.log.Infof("PropertyUp analyze force back event: %t", afbEvent.DeviceId)
		uc.EventUp(ctx, device, afbEvent.Type, afbEvent)
	}
	// analyze device return event
	if retEvent := device.AnalyzeReturnEvent(body); retEvent != nil {
		uc.log.Infof("PropertyUp analyze device return event: %d", retEvent.Mileage)
		uc.EventUp(ctx, device, retEvent.Type, retEvent)
	}
	if device.Status == StatusDebug {
		if ok, ot, coEvent := device.AnalyzeCoverOperationEvent(body, deviceState); ok {
			now := time.Now()
			coQuery := &OperationListQuery{
				ProjectInfo: ProjectInfo{TenantId: device.TenantId, AvatarId: device.AvatarId},
				Page:        1,
				Size:        10,
				SourceId:    device.Id,
				Types:       []OperationType{ot},
				StartTime:   now.Add(-5 * time.Minute),
				EndTime:     now,
			}
			if ot == TypeCloseCover {
				coQuery.Types = []OperationType{ot, TypeForceCloseCover}
			}
			if c, ops, err := uc.oRepo.ListOperation(ctx, coQuery); err == nil && c > 0 {
				uc.log.Infof("PropertyUp analyse cover operation event: %s", coEvent.Status)
				coEvent.OperationId = ops[0].Id
				defer uc.EventUp(ctx, device, coEvent.Type, coEvent)
			}
		}

		if ok, ot, doEvent := device.AnalyzeDroneOnOffOperationEvent(body, deviceState); ok {
			now := time.Now()
			if c, ops, err := uc.oRepo.ListOperation(ctx, &OperationListQuery{
				ProjectInfo: ProjectInfo{TenantId: device.TenantId, AvatarId: device.AvatarId},
				Page:        1,
				Size:        10,
				SourceId:    device.Id,
				Type:        lo.ToPtr(ot),
				StartTime:   now.Add(-5 * time.Minute),
				EndTime:     now,
			}); err == nil && c > 0 {
				uc.log.Infof("PropertyUp analyse drone on off operation event: %s", doEvent.Status)
				doEvent.OperationId = ops[0].Id
				defer uc.EventUp(ctx, device, doEvent.Type, doEvent)
			}
		}
		if ok, ot, poEvent := device.AnalyzePutterStateOperationEvent(body, deviceState); ok {
			now := time.Now()
			if c, ops, err := uc.oRepo.ListOperation(ctx, &OperationListQuery{
				ProjectInfo: ProjectInfo{TenantId: device.TenantId, AvatarId: device.AvatarId},
				Page:        1,
				Size:        10,
				SourceId:    device.Id,
				Type:        lo.ToPtr(ot),
				StartTime:   now.Add(-5 * time.Minute),
				EndTime:     now,
			}); err == nil && c > 0 {
				uc.log.Infof("PropertyUp analyse putter state operation event: %s", poEvent.Status)
				poEvent.OperationId = ops[0].Id
				defer uc.EventUp(ctx, device, poEvent.Type, poEvent)
			}
		}
		if ok, ot, cgEvent := device.AnalyseChargeStateOperationEvent(body, deviceState); ok {
			now := time.Now()
			if c, ops, err := uc.oRepo.ListOperation(ctx, &OperationListQuery{
				ProjectInfo: ProjectInfo{TenantId: device.TenantId, AvatarId: device.AvatarId},
				Page:        1,
				Size:        10,
				SourceId:    device.Id,
				Type:        lo.ToPtr(ot),
				StartTime:   now.Add(-5 * time.Minute),
				EndTime:     now,
			}); err == nil && c > 0 {
				uc.log.Infof("PropertyUp analyse charge state operation event: %s", cgEvent.Status)
				cgEvent.OperationId = ops[0].Id
				defer uc.EventUp(ctx, device, cgEvent.Type, cgEvent)
			}
		}
	}

	// save uplink data
	uc.dRepo.UpdateDevice(ctx, device.Id, device.PropertyUp(body))
	// push uplink mqtt
	body.Status = device.Status.String()
	pushData, _ := json.Marshal(body.Convert())
	uc.qRepo.Publish(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/property", device.MerchantId, device.Id), 0, pushData)
	// send integrate message
	if lo.Contains([]Gradient{GradientOnline, GradientOnFlight}, device.Gradient) {
		// 状态变更事件
		statusMsg := &StatusChangedMsg{}
		statusMsg.PaddingData(device, 0)
		uc.dRepo.IntegrateDevice(ctx, statusMsg)
	}
	return true
}

func (uc *ConnectUsecase) SetOffline(ctx context.Context, deviceId int64) error {
	task := &DelayTask{
		Times:    1,
		Delay:    ServiceTimeout,
		Source:   "SKAI-DEVICE",
		Key:      fmt.Sprintf("%s:DEVICE:%d:OFFLINE", SkaiDelayKeyPrefix, deviceId),
		Callback: fmt.Sprintf("/internal/v1/devices/%d/callback", deviceId),
		Payload:  map[string]interface{}{"type": "device", "action": "offline", "sourceId": conv.Vtoa(deviceId)},
	}
	return uc.tRepo.CreateTask(ctx, task)
}

func (uc *ConnectUsecase) EventUp(ctx context.Context, device *Device, etype ThingModelEventType, event interface{}) *EventUpReply {
	reply := &EventUpReply{DeviceId: device.Id, Sn: device.Sn}
	projectInfo := ProjectInfo{TenantId: device.TenantId, AvatarId: device.AvatarId, MerchantIds: []int64{device.MerchantId}}
	switch etype {
	case ThingModelEventTypeHMS:
		data := event.(*DockHealMonitorEvent)
		reply.Id = data.Id
		// save device event to pg, frontend-showable
		var eventType EventType
		if data.Source == 0 {
			eventType = DockDetectEvent
		} else if data.Source == 1 {
			eventType = DroneDetectEvent
		} else if data.Source == 2 {
			eventType = data.Extra["customType"].(EventType)
		}
		event := &Event{
			Sn:           device.Sn,
			Type:         eventType,
			Code:         data.Code,
			Level:        data.Level,
			Place:        data.InTheSky,
			ExtraData:    data.Extra,
			OccurredTime: data.OccurredTime,
		}
		if _, err := uc.eRepo.CreateEvent(ctx, event); err == nil {
			// 如果[1.飞机未返航;2.飞机在空中;3.有操作人;4.事件码影响巡航;5.自动返航加锁成功]，则提升等级为3-fatal，同时创建自动返航倒计时任务
			if device.IsControlled(FlyControl) && event.Place == 1 && device.AvatarId > 0 && lo.Contains(HealthBreakCode, event.Code) && uc.eRepo.LockAutoback(ctx, device.Id) {
				event.Level = 3
				// 自动返航倒计时任务
				uc.tRepo.CreateTask(ctx, &DelayTask{
					Times:    1,
					Delay:    11 * time.Second,
					Source:   "SKAI-DEVICE",
					Key:      fmt.Sprintf("%s:DEVICE:AUTOBACK:%d", SkaiDelayKeyPrefix, device.Id),
					Callback: fmt.Sprintf("/internal/v1/devices/%d/callback", device.Id),
					Payload:  AnyMap{"type": "device", "action": "autoback", "sourceId": conv.Vtoa(device.Id)},
				})
			}
			// 如果设备维保，则短信通知
			if event.Type == MinBatteryEvent {
				uc.eRepo.SendManualSms(ctx, event)
			}
			pushData, _ := json.Marshal(data.Caution(event))
			uc.qRepo.Publish(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/caution", device.MerchantId, device.Id), 0, pushData)
		}
	case ThingModelEventTypeUpdateTopo:
		data := event.(*DockTopoUpdateEvent)
		reply.Id = data.Id
		if len(data.Subdevices) == 0 {
			return reply
		}
		// save subdevices data
		uc.dRepo.UpdateDevice(ctx, device.Id, device.Children(data.Subdevices))
	case ThingModelEventTypePilotTakeoff:
		data := event.(*ThingEvent)
		reply.Id = data.Id
		// 遥控设备根据上行分析起飞事件，需创建航次记录
		offer := &Takeoffer{FlightId: lo.ToPtr(id.NewUUIDV1())}
		// 生成航次记录，并绑定flightId
		if voyage, err := uc.CreateVoyageWithFlight(ctx, offer, device); err == nil {
			offer.VoyageId = &voyage.Id
		}
		// 保存飞行相关信息
		uc.dRepo.UpdateDevice(ctx, device.Id, device.Takeoff(offer))
	case ThingModelEventTypeFlightReturn:
		data := event.(*FlightReturnEvent)
		reply.Id = data.Id
		if device.VoyageId == nil {
			return reply
		}
		voyageId := *device.VoyageId
		voyage, err := uc.vRepo.GetVoyage(ctx, &DetailQuery{Id: voyageId})
		if err != nil {
			return reply
		}
		// 失连恢复在线，仅更新航次状态；正常返航更新所有信息
		body := AnyMap{"status": "return"}
		if data.Status == GradientOffFlight {
			voyage.Mileage += data.Mileage
			voyage.EndTime = data.OccurredTime
			runtime := int32(voyage.EndTime.Sub(voyage.StartTime).Seconds())
			body = lo.Assign(body, AnyMap{"is_flown": false, "is_success": true, "runtime": runtime, "end_time": voyage.EndTime, "mileage": voyage.Mileage})
		}
		// 更新航次信息
		uc.vRepo.UpdateVoyage(ctx, voyageId, body)
		// 如果任务巡航，更新执行状态
		if execution, err := uc.xRepo.GetExecution(ctx, &ExecutionQuery{ProjectInfo: projectInfo, VoyageId: voyageId}); err == nil {
			uc.xRepo.UpdateExecution(ctx, execution.Id, AnyMap{"status": OperationStatusSuccess})
		}
		// 如果有算法，停止服务
		if device.PropData != nil && device.PropData.Algorithm != nil {
			// 人车流视频，关闭人解析服务
			if *device.PropData.Algorithm == AlgNameAnalyser.String() {
				uc.zRepo.DeleteAnalyser(ctx, voyageId)
			} else {
				// 人群聚集/烟雾明火/河道漂浮物等，关闭抽帧服务
				uc.pRepo.Stop(ctx, voyageId)
			}
		}
		// 如果有喊话，调用停止
		if device.IsSpeaking() {
			uc.cRepo.SendDownlink(ctx, device, uc.qRepo, DockServiceIdentifierStopSpeaker.String(), &DockService{
				Sn:         device.SourceSn,
				DeviceId:   device.Id,
				Identifier: DockServiceIdentifierStopSpeaker,
				ServiceId:  id.NextID(),
				Timeout:    time.Second * 30,
				Payload:    &SpeakerOperationPayload{Index: device.Speaker.Index},
			})
		}
		// 清理飞行相关信息
		uc.dRepo.UpdateDevice(ctx, device.Id, device.Return())
		// 保存录像
		snapFunc := func(et time.Time) func() error {
			di := &Device{}
			vi := &Voyage{}
			copier.CopyWithOption(di, device, copier.Option{DeepCopy: true, IgnoreEmpty: true})
			copier.CopyWithOption(vi, voyage, copier.Option{DeepCopy: true, IgnoreEmpty: true})
			return func() error {
				actx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
				defer cancel()
				e := uc.SaveFlightRecordVideo(actx, di, vi, et)
				if e != nil {
					uc.log.Errorf("SaveFlightRecordVideo %s %d failed %+v", di.Sn, vi.Id, e)
				}
				return nil
			}
		}(voyage.EndTime)
		go utilities.NewRecovedGOFunc(snapFunc)()
	case ThingModelEventTypeStartLive:
		data := event.(*StartLiveEvent)
		reply.Id = data.Id
		// 切换相机模式为录像，画面比例默认保持为16:9
		// 查找无人机云台子设备
		if gimbal := device.GetGimbal(); gimbal != nil {
			// 查找云台相机，并判断当前是否为录像模式
			if camera := device.GetCamera(gimbal); camera.GetMode() != VideoMode {
				// 下行切换录像模式命令
				identifier := DockServiceIdentifierSwitchCMode
				if err := uc.cRepo.SendDownlink(ctx, device, uc.qRepo, identifier.String(), &DockService{
					Sn:         device.SourceSn,
					DeviceId:   device.Id,
					Identifier: identifier,
					ServiceId:  id.NextID(),
					Timeout:    time.Second * 10,
					Payload:    &CameraModePaylaod{Subdevice: *gimbal, Mode: VideoMode},
				}); err != nil {
					uc.log.Errorf("Start live success then switch cmode to video failed")
				}
			}
		}
		// 算法不为空，则开启相应服务
		if device.PropData.Algorithm != nil {
			algoName := *device.PropData.Algorithm
			// 如果手动巡航，人车流视频解析
			if device.MissionId == nil {
				switch algoName {
				case AlgNameAnalyser.String():
					lives, err := uc.iRepo.GetDeviceLives(ctx, &DeviceLiveQuery{DeviceId: device.Id, SubDeviceIndex: ""})
					if err != nil {
						return reply
					}
					live, ok := lo.Find(lives, func(live *Media) bool { return live.GetLivePosition() == 0 })
					if !ok {
						return reply
					}
					sourceUrl, err := uc.lRepo.GetLiveProtocolPlayUrl(ctx, live, MediaProtocolRTSP, true)
					if err != nil {
						return reply
					}
					targetUrl, err := uc.lRepo.GetLiveProtocolPlayUrl(ctx, live.AnalyserMedia(), MediaProtocolRTSP, true)
					if err != nil {
						return reply
					}
					uc.zRepo.CreateAnalyser(ctx, &Analyser{Id: data.TaskId, Name: "一张图人车流", SourceUrl: sourceUrl, TargetUrl: targetUrl})
				}
			} else {
				// 如果任务巡航，校验任务并开启抽帧服务
				mission, err := uc.mRepo.GetMission(ctx, &DetailQuery{Id: *device.MissionId})
				if err != nil {
					uc.log.Warnf("Start live success then with invalid mission")
					return reply
				}
				if err := uc.swithLiveLenToZoom(ctx, device); err != nil {
					uc.log.Warnf("Start live success then switch live len to zoom failed", err)
				}
				// 查找实时视频抽帧算法
				config, ok := lo.Find(mission.AlgConfigs, func(item *AlgConfig) bool {
					return item.Type == AlgTypeSnapshot
				})
				if !ok || config == nil {
					uc.log.Warnf("Start live success then without snapshot algorithm")
					return reply
				}
				switch config.Name {
				case AlgNamePerson, AlgNameSmokeye, AlgNameFloater:
					lives, err := uc.iRepo.GetDeviceLives(ctx, &DeviceLiveQuery{DeviceId: device.Id, SubDeviceIndex: ""})
					if err != nil {
						return reply
					}
					live, ok := lo.Find(lives, func(live *Media) bool { return live.GetLivePosition() == 0 })
					if !ok {
						return reply
					}
					url, err := uc.lRepo.GetLiveProtocolPlayUrl(ctx, live, MediaProtocolRTMP, true)
					if err != nil {
						return reply
					}
					uc.pRepo.Start(ctx, &SnapshotTask{Id: data.TaskId, Stream: Stream{Id: live.Id, Url: url, Source: "SKAI"}, Interval: float64(config.Interval)})
				}
			}
		}
	case ThingModelEventTypeFlightTaskProgress:
		data := event.(*AutoFlightTaskProgressEvent)
		reply.Id = data.Id
		// 航点不为0，更新设备经过航点信息
		if data.CurrentWaypointIndex > 0 {
			uc.dRepo.UpdateDevice(ctx, device.Id, device.Xross(data))
		}
		// 巡航进度，若中断则记录对应原因
		if data.VoyageState == 7 && lo.Contains(CruiseBreakCode, data.BreakReason) {
			if event, err := uc.eRepo.CreateEvent(ctx, &Event{
				Sn:           device.Sn,
				Type:         CruiseBreakReason,
				Code:         conv.Vtoa(data.BreakReason),
				Level:        2,
				Place:        1,
				ExtraData:    AnyMap{"customType": CruiseBreakReason.String()},
				OccurredTime: data.OccurredTime,
			}); err == nil {
				pushData, _ := json.Marshal(data.Caution(event))
				uc.qRepo.Publish(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/caution", device.MerchantId, device.Id), 0, pushData)
			}
			// 巡航中断后，设备主动返航，需同步更新设备状态联动前端
			updateInfo := lo.Assign(device.Aviate(NullMode), device.ResetLock(0), device.ExtraData.ResetAero())
			if err := uc.dRepo.UpdateDevice(ctx, device.Id, updateInfo); err == nil {
				pushData, _ := json.Marshal(device)
				uc.qRepo.Publish(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/change", device.MerchantId, device.Id), 0, pushData)
			}
		}
		pushData, _ := json.Marshal(data.BuildEvent())
		uc.qRepo.Publish(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/event", device.MerchantId, device.Id), 0, pushData)
	case ThingModelEventTypeLaunchPointProgress:
		data := event.(*LaunchPointProgressEvent)
		reply.Id = data.Id
		// 一键起飞任务结果上报
		if lo.Contains([]string{"wayline_ok", "wayline_cancel", "wayline_failed"}, data.Status) {
			// web端推送
			uc.log.Debugf("TakeoffPointProgress: clear takeoff cache info and push result")
			guidePoint := device.ExtraData.GuidePoint()
			uc.dRepo.UpdateDevice(ctx, device.Id, device.ExtraData.Copter(nil))
			pushData, _ := json.Marshal(data.BuildEvent())
			uc.qRepo.Publish(ctx, fmt.Sprintf("skai/avatar/%d/device/%d/flyto", device.AvatarId, device.Id), 0, pushData)
			// 成功后保存目标点
			if data.Status == "wayline_ok" && device.VoyageId != nil {
				voyage, _ := uc.vRepo.GetVoyage(ctx, &DetailQuery{Id: *device.VoyageId})
				if voyage == nil {
					return reply
				}
				uc.vRepo.UpdateVoyage(ctx, voyage.Id, voyage.Course(guidePoint))
			}
		}
	case ThingModelEventTypeFlytoPointProgress:
		data := event.(*FlytoPointProgressEvent)
		reply.Id = data.Id
		// 指点飞行任务结果上报
		if lo.Contains([]string{"wayline_ok", "wayline_cancel", "wayline_failed"}, data.Status) {
			// web端推送
			uc.log.Debugf("FlytoPointProgress: clear flyto cache info and push result")
			guidePoint := device.ExtraData.GuidePoint()
			uc.dRepo.UpdateDevice(ctx, device.Id, device.ExtraData.Point(nil))
			pushData, _ := json.Marshal(data.BuildEvent())
			uc.qRepo.Publish(ctx, fmt.Sprintf("skai/avatar/%d/device/%d/flyto", device.AvatarId, device.Id), 0, pushData)
			// 成功后保存目标点
			if data.Status == "wayline_ok" && device.VoyageId != nil {
				voyage, _ := uc.vRepo.GetVoyage(ctx, &DetailQuery{Id: *device.VoyageId})
				if voyage == nil {
					return reply
				}
				uc.vRepo.UpdateVoyage(ctx, voyage.Id, voyage.Course(guidePoint))
			}
		}
	case ThingModelEventTypeOrbitPointNotify:
		data := event.(*OrbitPointNotifyEvent)
		reply.Id = data.Id
		// 环绕飞行信息状态通知
		if lo.Contains([]string{"in_progress", "ok", "failed"}, data.Status) {
			// web端推送
			uc.log.Debugf("OrbitPointNotify: clear orbit cache info and push result")
			// 执行中即保存目标点
			if data.Status == "in_progress" && device.VoyageId != nil {
				voyage, _ := uc.vRepo.GetVoyage(ctx, &DetailQuery{Id: *device.VoyageId})
				if voyage == nil {
					return reply
				}
				guidePoint := device.ExtraData.GuidePoint()
				uc.vRepo.UpdateVoyage(ctx, voyage.Id, voyage.Course(guidePoint))
			}
			// 失败后清除中间数据
			if data.Status == "failed" {
				uc.dRepo.UpdateDevice(ctx, device.Id, device.ExtraData.Orbit(nil))
			}
			pushData, _ := json.Marshal(data.BuildEvent())
			uc.qRepo.Publish(ctx, fmt.Sprintf("skai/avatar/%d/device/%d/orbit", device.AvatarId, device.Id), 0, pushData)
		}
	case ThingModelEventTypeFlightForceBack:
		data := event.(*ThingEvent)
		reply.Id = data.Id
		// 强制返航并推送前端告警
		if err := uc.ForceBackFlight(ctx, device); err != nil {
			uc.log.Warnf("ForceBackFlightEvent execute within error: %+v", err)
		}
		pushData, _ := json.Marshal(data.Caution(&Event{
			Sn:           device.Sn,
			Type:         HighRiskForceBack,
			Code:         "99000000",
			Level:        2,
			Place:        1,
			ExtraData:    AnyMap{"customType": "forceBack"},
			OccurredTime: data.OccurredTime,
		}))
		uc.qRepo.Publish(ctx, fmt.Sprintf("skai/avatar/%d/device/%d/caution", device.AvatarId, device.Id), 0, pushData)
	case ThingModelEventTypeSpeakerStatus:
		data := event.(*SpeakerPlayStatusEvent)
		reply.Id = data.Id
		// 喊话结果
		if data.Code != 0 {
			uc.log.Errorf("SpeakerPlayStatusEvent error code: %d", data.Code)
			return reply
		}
		// 喊话步骤不为play和ok
		if !lo.Contains([]string{"play", "ok"}, data.Step) {
			uc.log.Infof("SpeakerPlayStatusEvent step: %s", data.Step)
			return reply
		}
		// 喊话步骤进度为100才处理
		if data.Progress < 100 {
			uc.log.Infof("SpeakerPlayStatusEvent progress: %d", data.Progress)
			return reply
		}
		uc.log.Debugf("SpeakerPlayStatusEvent: push event result")
		// 喊话成功且单播模式清空IsSpoke
		if data.Step == "ok" && device.Speaker.Playmode == 0 {
			uc.dRepo.UpdateDevice(ctx, device.Id, device.ExtraData.Speak(-1, ""))
		}
		pushData, _ := json.Marshal(data.BuildEvent())
		uc.qRepo.Publish(ctx, fmt.Sprintf("skai/avatar/%d/device/%d/speaker", device.AvatarId, device.Id), 0, pushData)
		// TODO: 暂时将消息推送至设备event，便于前端展示，后续移除
		uc.qRepo.Publish(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/event", device.MerchantId, device.Id), 0, pushData)
	case ThingModelEventTypeFlightTaskResourceGet:
		data := event.(*DockFlightTaskResourceRequestEvent)
		reply.Id = data.Id
		if device.AirlineId != nil {
			reply.AirlineId = *device.AirlineId
		}
	case ThingModelEventTypeOperationProgress:
		data := event.(*OperationProgressEvent)
		reply.Id = data.Id
		op, err := uc.oRepo.GetOperation(ctx, data.OperationId)
		if err != nil {
			uc.log.Warnf("Get operation %d error: %v", data.OperationId, err)
			return reply
		}
		uc.tRepo.DeleteTask(ctx, NewOperationTimeoutDelayTaskKey(op))
		pd, _ := json.Marshal(data.BuildEvent(op))
		uc.qRepo.Publish(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/opProgress", op.MerchantId, device.Id), 0, pd)
	case ThingModelEventTypeFileuploadProgress:
		data := event.(*FileuploadProgressEvent)
		reply.Id = data.Id
		for _, file := range data.Files {
			if logfile, err := uc.fRepo.SearchLogfile(ctx, &LogfileSearchQuery{DeviceId: data.DeviceId, Module: file.Module, Url: file.Key}); err == nil {
				uc.fRepo.UpdateLogfile(ctx, logfile.Id, logfile.Progress(file))
			}
		}
	}
	return reply
}

func (uc *ConnectUsecase) swithLiveLenToZoom(ctx context.Context, device *Device) error {
	lives, err := uc.iRepo.GetDeviceLives(ctx, &DeviceLiveQuery{
		DeviceId: device.Id,
		device:   device,
	})
	if err != nil {
		return err
	}
	ml, ok := lo.Find(lives, func(it *Media) bool { return it.GetLivePosition() == LivePositionMainPTZ })
	if !ok {
		return NewInternalError("swithLiveLen.mainLiveNotFound", nil)
	}
	payload := &SwithLensPayload{
		Lens: VideoTypeZoom,
		Live: ml,
	}
	operation, err := uc.oRepo.CreateOperation(ctx, &Operation{
		MerchantId: device.MerchantId,
		AvatarId:   0,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Type:       TypeSwitchLens,
		Source:     SourceDevice,
		From:       "SYSTEM",
		Status:     OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return err
	}
	return uc.cRepo.SendDownlink(ctx, device, uc.qRepo, string(DockServiceIdentifierSwitchLens), &DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: DockServiceIdentifierSwitchLens,
		ServiceId:  operation.Id,
		Timeout:    10 * time.Second,
		Payload:    payload,
	})
}

func (uc *ConnectUsecase) ForceBackFlight(ctx context.Context, device *Device) error {
	// 强制返航事件：1.如果自由飞行则退出；2.创建操作和超时任务；3.下行返航指令
	var updateInfo AnyMap
	payload := &EmptyPayload{OperationTime: time.Now().UnixMilli()}
	if device.IsFreed() {
		identifier := DockServiceIdentifierExitDRCMode
		if err := uc.cRepo.SendDownlink(ctx, device, uc.qRepo, identifier.String(), &DockService{
			Sn:         device.SourceSn,
			DeviceId:   device.Id,
			Identifier: identifier,
			ServiceId:  id.NextID(),
			Timeout:    ServiceTimeout,
			Payload:    payload,
		}); err != nil {
			uc.log.Errorf("ForceBackFlightEvent implicit downlink exitDRCMode failed")
		}
		updateInfo = device.ExtraData.Free(false)
	}
	opType := TypeBacknow
	identifier := opType.ToServiceIdentifier()
	operation, err := uc.oRepo.CreateOperation(ctx, &Operation{
		MerchantId: device.MerchantId,
		TenantId:   device.TenantId,
		SourceId:   device.Id,
		Timeout:    ServiceTimeout,
		Type:       opType,
		Source:     SourceDevice,
		From:       "SYSTEM",
		Status:     OperationStatusPending,
		Content:    utilities.StructToMap(payload),
	})
	if err != nil {
		return err
	}
	// 设置命令超时任务
	uc.tRepo.CreateTask(ctx, &DelayTask{
		Times:    1,
		Delay:    ServiceTimeout,
		Source:   "SKAI-CONNECT",
		Key:      fmt.Sprintf("%s:SERVICE:%s:%d", SkaiThingKeyPrefix, identifier.ToUpper(), operation.Id),
		Callback: fmt.Sprintf("/internal/v1/devices/%d/callback", device.Id),
		Payload:  AnyMap{"type": "service", "action": "timeout", "sourceId": conv.Vtoa(operation.Id)},
	})
	// 锁定设备状态，防止多次控制
	updateInfo = lo.Assign(updateInfo, device.Control(opType.ToControlAction(), true))
	if err := uc.dRepo.UpdateDevice(ctx, device.Id, updateInfo); err != nil {
		return err
	}
	return uc.cRepo.SendDownlink(ctx, device, uc.qRepo, identifier.String(), &DockService{
		Sn:         device.SourceSn,
		DeviceId:   device.Id,
		Identifier: identifier,
		ServiceId:  operation.Id,
		Timeout:    ServiceTimeout,
		Payload:    payload,
	})
}

func (uc *ConnectUsecase) ServiceReply(ctx context.Context, device *Device, body *DockServiceReply) bool {
	ro := body.AsOperation()
	flag := ro.Status == OperationStatusSuccess
	// remove serviceDown timeout task
	// get operation by serviceId
	operation, err := uc.oRepo.GetOperation(ctx, ro.Id)
	// operation not found, return true
	if err != nil {
		if body.Identifier == DockServiceIdentifierStartPushLive.String() && body.Data != nil {
			if liveId, ok := body.Data["liveId"].(string); ok {
				if live, err := uc.lRepo.GetLive(ctx, conv.Atoi(liveId)); err == nil {
					uc.lRepo.UpdateLiveCamera(ctx, live.Id, int32(body.Data["position"].(float64)))
				} else {
					uc.log.Errorf("ServiceReply.AutoStartPushLive get live %s failed %v", liveId, err)
				}
			}
		}
		return true
	}
	if !JobOperations.Contains(operation.Type) {
		uc.tRepo.DeleteTask(ctx, fmt.Sprintf("%s:SERVICE:%s:%d", SkaiThingKeyPrefix, strings.ToUpper(body.Identifier), ro.Id))
	}
	// update operation status
	uc.oRepo.UpdateOperation(ctx, ro.Id, operation.Update(ro.Status, ro.Message))
	waitFor := make(chan struct{})
	go func() {
		<-waitFor
		pushData, _ := json.Marshal(operation)
		pctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		// 推送用户级别操作变更，如果是返航操作则升级为设备级别
		if lo.Contains([]OperationType{TypeReturn, TypeBacknow, TypeAutoback}, operation.Type) {
			uc.qRepo.Publish(pctx, fmt.Sprintf("skai/merchant/%d/device/%d/return", device.MerchantId, device.Id), 0, pushData)
		} else {
			uc.qRepo.Publish(pctx, fmt.Sprintf("skai/avatar/%d/operation/%d", operation.AvatarId, operation.Id), 0, pushData)
		}
	}()
	// enalbe and disable operation need to update device lockStatus by action; update device avatarId
	action := operation.Type.ToControlAction()
	if action != NoneControl {
		uc.dRepo.UpdateDevice(ctx, device.Id, lo.Assign(device.Control(action, flag), device.Operate(operation.AvatarId)))
	}
	if !flag {
		close(waitFor)
		// create service reply error event
		uc.eRepo.CreateEvent(ctx, &Event{
			Sn:           device.Sn,
			Type:         ServiceReplyError,
			Code:         conv.Vtoa(body.Code),
			Level:        2,
			Place:        lo.Ternary[int32](device.CabinStatus, 0, 1),
			ExtraData:    AnyMap{"customType": operation.Type},
			OccurredTime: body.Timestamp,
		})
		// update execution error status if mission cruise
		projectInfo := ProjectInfo{TenantId: operation.TenantId, AvatarId: operation.AvatarId, MerchantIds: []int64{operation.MerchantId}}
		if execution, err := uc.xRepo.GetExecution(ctx, &ExecutionQuery{ProjectInfo: projectInfo, OperationId: operation.Id}); err == nil {
			uc.xRepo.UpdateExecution(ctx, execution.Id, execution.Update(ro.Status, body.TransReason()))
		}
		return true
	}
	// set takeoff avatarId, guide point, picture time, video time/status when service success
	var updateInfo AnyMap
	switch operation.Type {
	// 航线和任务起飞成功，绑定avatarId并设置flightId或airlineId/missionId
	case TypeTakeoff, TypeCruise:
		// 本次飞行相关信息
		offer := &Takeoffer{FlyerId: operation.AvatarId, FlightId: lo.ToPtr(id.NewUUIDV1())}
		// 航线起飞使用上报的flightId
		if id, ok := ro.Content["flightId"]; ok {
			offer.FlightId = lo.ToPtr(conv.Vtoa(id))
		}
		payload := &TakeoffPayload{}
		if err := operation.GetContent(payload); err == nil {
			offer.AirlineId = lo.Ternary(payload.AirlineId == 0, nil, &payload.AirlineId)
			offer.MissionId = lo.Ternary(payload.MissionId == 0, nil, &payload.MissionId)
			offer.Algorithm = lo.Ternary(payload.Algorithm == "", nil, &payload.Algorithm)
			offer.AirlineName = lo.Ternary(payload.AirlineName == "", nil, &payload.AirlineName)
		}
		// 生成航次记录，并绑定flightId
		if voyage, err := uc.CreateVoyageWithFlight(ctx, offer, device); err == nil {
			offer.VoyageId = &voyage.Id
		}
		updateInfo = device.Takeoff(offer)
		// 如果是巡航任务，根据航次ID设置关联设备信息，1小时自动失效，不再主动清理
		if offer.VoyageId != nil && offer.MissionId != nil {
			jointDevice := &JointDevice{}
			jointDevice.Copy(device)
			uc.vRepo.CacheJointDevice(ctx, *offer.VoyageId, jointDevice)
		}
	// 一键起飞成功，绑定avatarId并设置flightId和guidePoint
	case TypeLaunch:
		// 本次飞行相关信息
		offer := &Takeoffer{FlyerId: operation.AvatarId, FlightId: lo.ToPtr(id.NewUUIDV1()), AirlineId: nil}
		// 一键起飞使用上报的flightId
		if id, ok := ro.Content["flightId"]; ok {
			offer.FlightId = lo.ToPtr(conv.Vtoa(id))
		}
		payload := &LaunchPayload{}
		if err := operation.GetContent(payload); err == nil {
			offer.Algorithm = lo.Ternary(payload.Algorithm == "", nil, &payload.Algorithm)
		}
		// 生成航次记录，并绑定flightId
		if voyage, err := uc.CreateVoyageWithFlight(ctx, offer, device); err == nil {
			offer.VoyageId = &voyage.Id
		}
		updateInfo = device.Takeoff(offer)
		// 保存本次起飞设置信息
		updateInfo = lo.Assign(updateInfo, device.Launch(payload))
		// 悬停控制默认开启，1.进入中间过度状态；2.直接更新为成功
		device.Control(HoverControl, true)
		updateInfo = lo.Assign(updateInfo, device.Control(HoverControl, true))
		// 飞行控制默认开启，1.进入中间过度状态；2.直接更新为成功
		device.Control(AeroControl, true)
		updateInfo = lo.Assign(updateInfo, device.Control(AeroControl, true))
		// 默认为指点飞行模式，1.设置aeroMode，2.设置isPointed为true，互斥其他任务
		updateInfo = lo.Assign(updateInfo, device.Aviate(PointMode))
		if lnglat, ok := operation.Content["Lnglat"].([]any); ok {
			point := lo.Map(lnglat, func(coord any, _ int) float64 { return conv.Vtof(coord) })
			updateInfo = lo.Assign(updateInfo, device.ExtraData.Copter(point))
		}
	// 返航成功，重置飞行模式+数据/控制状态，所有控制均关闭
	case TypeReturn, TypeBacknow, TypeAutoback:
		updateInfo = lo.Assign(device.Aviate(NullMode), device.ResetLock(0), device.ExtraData.ResetAero())
	// 取消返航成功，补充设置悬停控制
	case TypeCancel:
		device.Control(HoverControl, true)
		updateInfo = lo.Assign(updateInfo, device.Control(HoverControl, true))
	// 恢复航线成功，重置飞行模式+数据/控制状态，除起飞控制均关闭
	case TypeBackAirline:
		updateInfo = lo.Assign(device.Aviate(NullMode), device.ResetLock(2), device.ExtraData.ResetAero())
	// 进入自由飞行成功，设置isFreed
	case TypeEnterFree:
		updateInfo = device.ExtraData.Free(true)
	// 退出自由飞行成功，清空isFreed
	case TypeExitFree:
		// 若当前飞行模式为自由模式，则置空aeroMode
		if device.AeroMode == FreeMode {
			updateInfo = device.Aviate(NullMode)
		}
		// 若当前为自由飞行中，则清空isPointed，guidePoint
		if device.IsFreed() {
			updateInfo = lo.Assign(updateInfo, device.ExtraData.Free(false))
		}
	// 飞向/更换目标成功，设置isPointed，guidePoint
	case TypeFlytoPoint, TypeFlywardPoint:
		if lnglat, ok := operation.Content["Lnglat"].([]any); ok {
			point := lo.Map(lnglat, func(coord any, _ int) float64 { return conv.Vtof(coord) })
			updateInfo = device.ExtraData.Point(point)
		}
	// 终止目标成功，清空isPointed，guidePoint
	case TypeFlyoffPoint:
		// 若当前飞行模式为指点模式，则置空aeroMode
		if device.AeroMode == PointMode {
			updateInfo = device.Aviate(NullMode)
		}
		// 若当前为指点飞行中，则清空isPointed，guidePoint
		if device.IsPointed() {
			updateInfo = lo.Assign(updateInfo, device.ExtraData.Point(nil))
		}
	// 进入环绕成功，设置isOrbited，guidePoint
	case TypeEnterOrbit:
		payload := &StartOrbitPayload{}
		if err := operation.GetContent(payload); err == nil {
			updateInfo = device.ExtraData.Orbit(payload)
			// 隐式设置环绕速度
			identifier := DockServiceIdentifierUpdatePOISpeed
			if err := uc.cRepo.SendDownlink(ctx, device, uc.qRepo, identifier.String(), &DockService{
				Sn:         device.SourceSn,
				DeviceId:   device.Id,
				Identifier: identifier,
				ServiceId:  id.NextID(),
				Timeout:    time.Second * 10,
				Payload:    &StartOrbitPayload{Speed: payload.Speed},
			}); err != nil {
				uc.log.Warnf("Enter orbit success then set speed failed")
			}
		}
	// 退出环绕成功，清空aeroMode，isOrbited，guidePoint
	case TypeExitOrbit:
		// 若当前飞行模式为环绕模式，则置空aeroMode
		if device.AeroMode == OrbitMode {
			updateInfo = device.Aviate(NullMode)
		}
		// 若当前为环绕飞行中，则清空isOrbited，guidePoint
		if device.IsOrbited() {
			updateInfo = lo.Assign(updateInfo, device.ExtraData.Orbit(nil))
		}
	// 更新环绕速度成功，设置orbitSpeed
	case TypeSpeedOrbit:
		if cs, ok := operation.Content["Speed"]; ok {
			speed := float32(conv.Vtof(cs))
			updateInfo = device.ExtraData.Orbeed(speed)
		}
	// 拍照成功，设置pictureTime，表示拍照时间
	case TypeTakePicture:
		updateInfo = device.ExtraData.Picture(ro.UpdatedTime.UnixMilli())
	// 录像成功，设置videoTime，表示录像时间
	case TypeStartVideo:
		updateInfo = device.ExtraData.Video(ro.UpdatedTime.UnixMilli())
	// 主动停止录像，清零videoTime，表示关闭录像
	case TypeStopVideo:
		updateInfo = device.ExtraData.Video(0)
	// 变倍成功，延迟5秒推送，等待相机切换完成
	case TypeSwitchZoom:
		go func() {
			gctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()
			time.Sleep(5 * time.Second)
			pushData, _ := json.Marshal(device)
			uc.qRepo.Publish(gctx, fmt.Sprintf("skai/merchant/%d/device/%d/change", device.MerchantId, device.Id), 0, pushData)
		}()
	// 开始喊话成功，保存喊话内容
	case TypeTTSSpeaker, TypeAudioSpeaker:
		if cm, ok := operation.Content["Mode"]; ok {
			mode := int32(conv.Vtoi(cm))
			if data, ok := operation.Content["Data"]; ok {
				updateInfo = device.ExtraData.Speak(mode, conv.Vtoa(data))
			}
		}
	// 停止喊话成功，清理喊话标识状态
	case TypeStopSpeaker:
		updateInfo = device.ExtraData.Speak(-1, "")
	// 查询日志成功，推送原始数据
	case TypeRemoteLogfile:
		pushData, _ := json.Marshal(ro.Content)
		uc.qRepo.Publish(ctx, fmt.Sprintf("skai/avatar/%d/device/%d/logfiles", operation.AvatarId, device.Id), 0, pushData)
	// 切换镜头类型
	case TypeSwitchLens:
		if lensType, ok := operation.Content["Lens"].(json.Number); ok {
			updateInfo = device.ExtraData.Lens(int(conv.Vtoi(lensType)))
			go func() {
				gctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
				defer cancel()
				err := utilities.NewRecovedGOFunc(uc.newRecordCurrentLenOp(gctx, operation))()
				if err != nil {
					uc.log.Errorf("newRecordCurrentLenOp %s %d failed %+v", device.Sn, operation.Id, err)
				}
			}()
		}
	// 切换清晰度成功，更新媒体数据
	case TypeSwitchClairty:
		sp := &SwitchClarityPayload{}
		if err := operation.GetContent(sp); err == nil {
			live, err := uc.iRepo.GetOne(ctx, sp.Live.Id)
			if err == nil {
				vt, _ := live.GetVideoType(device.Subdevices)
				uc.iRepo.UpdateLiveStatus(ctx, sp.Live.Id, LiveStatus{
					Clarity: sp.Clarity,
					Status:  live.Status,
					Type:    vt,
				})
			} else {
				uc.log.Errorf("SWITCH_CLARITY get live for %d %v err %v", operation.Id, sp.Live, err)
			}
		} else {
			uc.log.Errorf("SWITCH_CLARITY content tranform for %d %v err %v", operation.Id, operation.Content, err)
		}
	case TypeChangeLiveCamera:
		sp := &ChangeLiveCameraPayload{}
		if err := operation.GetContent(sp); err == nil && sp.Live != nil {
			uc.lRepo.UpdateLiveCamera(ctx, sp.Live.Id, sp.Position)
		}
	}
	if updateInfo != nil {
		uc.dRepo.UpdateDevice(ctx, device.Id, updateInfo)
	}
	if operation.Type == TypeOpenDebug {
		go func() {
			wctx, cancel := context.WithCancel(context.Background())
			defer cancel()
			count := 0
			ticker := time.NewTicker(500 * time.Millisecond)
			defer ticker.Stop()
			for {
				<-ticker.C
				count++
				d, err := uc.dRepo.GetDevice(wctx, &DetailQuery{Id: device.Id})
				if err != nil {
					uc.log.Errorf("GetDevice %d err %v", device.Id, err)
					return
				}
				if d.InDebugMode() || count > 10 {
					close(waitFor)
					return
				}
			}
		}()
	} else {
		close(waitFor)
		// push device change message
		pushData, _ := json.Marshal(device)
		uc.qRepo.Publish(ctx, fmt.Sprintf("skai/merchant/%d/device/%d/change", device.MerchantId, device.Id), 0, pushData)
	}
	return true
}

func (uc *ConnectUsecase) CreateVoyageWithFlight(ctx context.Context, offer *Takeoffer, device *Device) (*Voyage, error) {
	now := time.Now()
	airlineId := int64(0)
	if offer.AirlineId != nil {
		airlineId = *offer.AirlineId
	}
	airlineName := lo.Ternary(device.Category == CategoryDock, "一键飞行巡航", "未按航线飞行")
	if offer.AirlineName != nil {
		airlineName = *offer.AirlineName
	}
	voyageName := fmt.Sprintf("%s_%s%s", airlineName, device.Deployment.Name, now.In(TimeLocation).Format("06010215"))
	voyage, err := uc.vRepo.CreateVoyage(ctx, &Voyage{
		Sn:         device.Sn,
		Name:       voyageName,
		DeviceId:   device.Id,
		IsFlown:    true,
		AirlineId:  airlineId,
		FlightId:   *offer.FlightId,
		TenantId:   device.TenantId,
		MerchantId: device.MerchantId,
		StartTime:  now,
	})
	if err != nil {
		uc.log.Errorf("CreateVoyage device: %s, with flightId: %s failed %+v", device.Sn, *offer.FlightId, err)
		return nil, err
	}
	// 任务起飞，更新执行记录VoyageId
	if offer.MissionId != nil {
		projectInfo := ProjectInfo{TenantId: device.TenantId, MerchantIds: []int64{device.MerchantId}}
		if execution, err := uc.xRepo.GetExecution(ctx, &ExecutionQuery{ProjectInfo: projectInfo, MissionId: *offer.MissionId}); err == nil {
			uc.xRepo.UpdateExecution(ctx, execution.Id, execution.Voyage(voyage.Id))
		}
	}
	return voyage, nil
}

func (uc *ConnectUsecase) SaveFlightRecordVideo(ctx context.Context, dev *Device, voyage *Voyage, endTime time.Time) error {
	lives, err := uc.iRepo.GetDeviceLives(ctx, &DeviceLiveQuery{
		DeviceId: dev.Id,
		device:   dev,
	})
	if err != nil {
		return err
	}
	for _, l := range lives {
		if !strings.HasPrefix(l.SubDeviceIndex, "165-0") && l.SubDeviceIndex != "39-0-7" && l.SubDeviceIndex != "176-0-0" {
			pl, err := uc.lRepo.GetLiveHistoryVideos(ctx, &LiveHistoryQuery{
				Live:      l,
				StartTime: voyage.StartTime.Add(0 - M3U8SegmentMaxDuration),
				EndTime:   endTime.Add(M3U8SegmentMaxDuration),
			})
			if err != nil {
				return err
			}
			name := dev.Sn
			if dev.Deployment != nil {
				name = dev.Deployment.Name
			}
			vSize := lo.SumBy(pl.TSs, func(it *TransportStream) int64 {
				return it.FileSize
			})
			vTime := lo.SumBy(pl.TSs, func(it *TransportStream) float64 {
				return it.Duration
			})
			m := &Media{
				Id:          utilities.MustNextID(),
				CreatedTime: voyage.StartTime,
				UpdatedTime: time.Now(),
				Type:        MediaTypeVideo,
				Meta: &MediaMeta{
					ShootTime: voyage.StartTime,
				},
				DeviceId:       dev.Id,
				SubDeviceIndex: l.SubDeviceIndex,
				AirlineId:      voyage.AirlineId,
				MerchantId:     dev.MerchantId,
				VoyageId:       voyage.Id,
				WaypointId:     -1,
				Key:            fmt.Sprintf("voyage/%s/%s/%d/%s", dev.Sn, voyage.StartTime.Format("06-01"), voyage.Id, pl.Name),
				Name:           fmt.Sprintf("全程录像_%s（%s）_0001", name, dev.Type),
				Extra:          map[string]any{"fullRecord": 1, "vSize": float64(vSize), "vTime": vTime, "m3u8CreatedAt": time.Now().UnixMilli()},
			}
			if err = uc.iRepo.Create(ctx, []*Media{m}); err != nil {
				return err
			}
			uc.sRepo.PutObject(ctx, &StorageObject{
				Key:  m.Key,
				Data: bytes.NewReader(pl.Data),
			})
			uc.vRepo.IncressVoyageMediaCount(ctx, voyage.Id, VoyageMediaCount{Videos: 1})
			list, _, err := m3u8.DecodeFrom(bytes.NewReader(pl.Data), false)
			if err != nil {
				return err
			}
			if p, ok := list.(*m3u8.MediaPlaylist); ok && p.Count() > 0 {
				s := p.Segments
				vu, err := uc.sRepo.GetInternalSignedObjectAddr(ctx, &StorageObject{URL: s[0].URI}, 10*time.Minute)
				if err != nil {
					return err
				}
				vd, err := uc.iRepo.MakeVideoSnap(ctx, vu.ObjectUrl)
				if err != nil {
					return err
				}
				defer vd.Close()
				r, err := uc.sRepo.GenImageThumbnail(ctx, &StorageObject{
					Key:  "snapshot/voyage/" + strconv.FormatInt(voyage.Id, 36) + ".jpg",
					Data: vd,
				})
				if err != nil {
					return err
				}
				uc.log.Infof("getRecordVideoSnapshot for %s out success %s", m.Key, r.Key)
				m.SetThumbnail(r)
				uc.iRepo.UpdateMediaExtra(ctx, m.Id, m.Extra)
			}
			// 恢复默认镜头类型
			uc.iRepo.UpdateLiveStatus(ctx, l.Id, LiveStatus{
				Clarity: l.GetLiveClarity(),
				Status:  l.Status,
				Type:    DefaultVideoType,
			})
		}
	}

	return nil
}

func (uc *ConnectUsecase) newRecordCurrentLenOp(ctx context.Context, operation *Operation) func() error {
	return func() error {
		p := &SwithLensPayload{}
		if err := operation.GetContent(p); err == nil {
			if p.Live != nil {
				live, err := uc.iRepo.GetOne(ctx, p.Live.Id)
				if err == nil {
					uc.iRepo.UpdateLiveStatus(ctx, live.Id, LiveStatus{
						Clarity: live.GetLiveClarity(),
						Status:  live.Status,
						Type:    p.Lens,
					})
				}
			}
		} else {
			return err
		}
		return nil
	}
}
