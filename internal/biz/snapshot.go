package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type Stream struct {
	Id     int64  `json:"id,string"`
	Source string `json:"source"`
	Url    string `json:"url"`
}

type SnapshotTask struct {
	Id        int64   `json:"id,string"`
	Stream    Stream  `json:"stream"`
	Interval  float64 `json:"interval"`
	SnapPodIp string  `json:"snapPodIp"`
}

type SnapshotRepo interface {
	Start(ctx context.Context, task *SnapshotTask) error
	Stop(ctx context.Context, taskId int64) error
}

type SnapshotUsecase struct {
	repo SnapshotRepo
	log  *log.Helper
}

func NewSnapshotUsecase(logger log.Logger, repo SnapshotRepo) *SnapshotUsecase {
	return &SnapshotUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (uc *SnapshotUsecase) Start(ctx context.Context, task *SnapshotTask) error {
	return uc.repo.Start(ctx, task)
}

func (uc *SnapshotUsecase) Stop(ctx context.Context, taskId int64) error {
	return uc.repo.Stop(ctx, taskId)
}
