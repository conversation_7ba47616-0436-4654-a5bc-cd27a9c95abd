package biz

import (
	"context"
	"time"
)

type LogfileSearchQuery struct {
	ProjectInfo
	Url      string
	Module   string
	DeviceId int64
}

type LogfileListQuery struct {
	ProjectInfo
	Sort
	Page      int
	Size      int
	DeviceId  int64
	Status    []int32
	Modules   []string
	StartTime time.Time
	EndTime   time.Time
}

type Logfile struct {
	Id          int64
	Url         string
	Module      string
	Status      int32
	BootIndex   int32
	DeviceId    int64
	TenantId    int64
	MerchantId  int64
	OperationId int64
	Size        int64
	StartTime   time.Time
	EndTime     time.Time
	CreatedTime time.Time
	UpdatedTime time.Time
}

func (l *Logfile) Progress(file *FileuploadProgress) AnyMap {
	l.Status = file.Status
	return AnyMap{"status": l.Status}
}

type LogfileRepo interface {
	CreateLogfiles(ctx context.Context, body []*Logfile) error
	UpdateLogfile(ctx context.Context, id int64, body AnyMap) error
	GetLogfile(ctx context.Context, query *DetailQuery) (*Logfile, error)
	SearchLogfile(ctx context.Context, query *LogfileSearchQuery) (*Logfile, error)
	ListLogfiles(ctx context.Context, query *LogfileListQuery) (int32, []*Logfile, error)
}
