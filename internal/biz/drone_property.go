package biz

import "time"

type DroneFlightState struct {
	// 水平速度 m/s
	HorizontalSpeed float32
	// 垂直速度 m/s
	VerticalSpeed float32
	Longitude     float64
	Latitude      float64
	// 绝对高度	相对地球椭球面高度
	Height float32
	// 相对起飞点高度
	Elevation float32
	// 俯仰轴角度
	AttitudePitch float32
	// 横滚轴角度
	AttitudeRoll float32
	// 机头朝向角度
	AttitudeHead float32
	// 累计航行时长 秒
	TotalFlightTime int64
	WindSpeed       float32
	// {"1":"正北","2":"东北","3":"东","4":"东南","5":"南","6":"西南","7":"西","8":"西北"}
	WindDirection int32
	// {"0":"未达到设定的限制高度","1":"接近设定的限制高度"}
	IsNearHeightLimit int32
	// {"0":"未达到限飞区","1":"接近限飞区"}
	IsNearAreaLimit int32
	// {"0":"未达到设定的限制距离","1":"接近设定的限制距离", "-1": "未开启限制"}
	IsNearDistanceLimit int32
	// 无人机夜航灯状态	{"0":"关闭","1":"打开"}
	NightLightsState int32
	// 遥控器失控行为	 {"0":"悬停","1":"着陆(降落)","2":"返航"}
	RCLostAction int32
	// 航线失控动作	{"0":"继续执行航线任务","1":"退出航线任务，执行遥控器失控行为"}
	ExitWaylineWhenRCLost int32
	// 水平避障状态	{"0":"关闭","1":"开启"}
	HorizonObstacleAvoidance int32
	// 上视避障状态
	UpsideObstacleAvoidance int32
	// 下视避障状态
	DownsideObstacleAvoidance int32
}

type DroneBatteryState struct {
	// 电池剩余总电量百分比
	CapacityPercent int32
	// 剩余飞行时间	秒
	RemainFlightTime int32
	// 返航所需电量百分比
	ReturnHomePower int32
	// 强制降落电量百分比
	LandingPower int32
	Batteries    []*DroneBattery
}

type DroneBattery struct {
	Index int32
	// 电池剩余电量百分比
	CapacityPercent int32
	// 毫伏
	Voltage int32
	// 摄氏度
	Temperature float32
	Loop        int32
	Sn          string
}

type DroneBatteryChargeState struct {
	// 电量百分比
	CapacityPercent int32
	// {"0":"空闲","1":"充电中"}
	State int32
}

type DroneProperties struct {
	Id        string
	RxTime    time.Time
	Timestamp time.Time
	// 所属网关id
	DeviceId int64
	// 所属网关sn
	Sn        string
	DroneSn   string
	DroneType string
	// 无人机状态 -1无效	{"0":"待机","1":"起飞准备","2":"起飞准备完毕","3":"手动飞行","4":"自动起飞","5":"航线飞行","6":"全景拍照","7":"智能跟随","8":"ADS-B 躲避","9":"自动返航","10":"自动降落","11":"强制降落","12":"三桨叶降落","13":"升级中","14":"未连接","15":"APAS","16":"虚拟摇杆状态","17":"指令飞行","18":"空中 RTK 收敛模式","19":"机场选址中","20":"POI环绕"}
	Mode          int32
	FlightState   *DroneFlightState
	PositionState *PositionState
	Battery       *DroneBatteryState
	Storage       *Storage
	// 喊话器状态
	Speaker *DroneSpeakerState
	// 相机状态
	Cameras []*DroneCameraState
	Other   map[string]interface{}
}

type DroneExtendedWidget struct {
	Index           string
	Name            string
	Sn              string
	FirmwareVersion string
	LibVersion      string
}

type DroneSpeakerState struct {
	DroneExtendedWidget
	// 喊话器型号
	Type string
	// {"0":"TTS 负载模式","1":"录音喊话"}
	WorkMode int8
	// {"0":"空闲中","1":"传输中(机场到无人机)","2":"播放中","3":"异常中","4":"TTS 文本转换中","99":"下载中(机场从云端下载)"}
	SystemState int8
	// {"0":"单次播放","1":"循环播放(单曲)"}
	PlayMode int8
	// 0~100 音量
	PlayVolume int8
	// 最近播放文件名称
	PlayFileName string
	// 最近播放文件md5哈希
	PlayFileSignature string
}

type DroneCameraState struct {
	// 相机编号，对应subdevice index
	Index string
	// {"0":"拍照","1":"录像","2":"智能低光","3":"全景拍照","-1":"不支持的模式"}
	Mode int32
	// 拍照状态 0空闲 1拍照中
	PhotoState int32
	// 录像状态 0空闲 1录像中
	RecordingState int32
	// 变焦系数
	ZoomFactor float32
	// 红外变焦系数
	IRZoomFactor float32
	// 变焦镜头对焦模式	{"0":"MF","1":"AFS","2":"AFC"}
	ZoomFocusMode  int32
	ZoomFocusValue int32
	// 对焦状态 {"0":"空闲","1":"对焦中","2":"对焦成功","3":"对焦失败"}
	ZoomFocusState int32
	// 红外测温模式 {"0":"关闭","1":"打开"}
	IRMeteringMode int32
	// 云台俯仰轴角度
	GimbalPitch float64
	// 云台翻滚轴角度
	GimbalRoll float64
	// 云台偏航轴角度
	GimbalYaw float64
	// 激光测距目标点经度
	MeasureTargetLongitude float64
	// 激光测距目标点纬度
	MeasureTargetLatitude float64
	// 激光测距目标点高度
	MeasureTargetAltitude float64
	// 激光测距距离
	MeasureTargetDistance float64
	// 激光测距状态 	{"0":"NORMAL","1":"TOO_CLOSE","2":"TOO_FAR","3":"NO_SIGNAL"}
	MeasureErrState int32
	// 视场角（FOV）在 liveview 中的区域
	LiveViewWorldRegion CameraLiveViewWorldRegion
}

type CameraLiveViewWorldRegion struct {
	Left   float64 `json:"l"`
	Top    float64 `json:"t"`
	Right  float64 `json:"r"`
	Bottom float64 `json:"b"`
}
