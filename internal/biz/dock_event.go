package biz

import (
	"context"
	"encoding/base64"
	"time"

	"github.com/goccy/go-json"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/skai/skai/pkg/types"
)

type ThingModelEventType int

const (
	ThingModelEventTypeOther ThingModelEventType = iota
	// 拓扑更新
	ThingModelEventTypeUpdateTopo
	// 遥控起飞
	ThingModelEventTypePilotTakeoff
	// 飞行返航
	ThingModelEventTypeFlightReturn
	// 直播流打开
	ThingModelEventTypeStartLive
	// 航线任务进度上报
	ThingModelEventTypeFlightTaskProgress
	// 指点飞行进度上报
	ThingModelEventTypeFlytoPointProgress
	// POI环绕状态通知
	ThingModelEventTypeOrbitPointNotify
	// 一键起飞进度上报
	ThingModelEventTypeLaunchPointProgress
	// 健康预警
	ThingModelEventTypeHMS
	// 飞行任务资源获取
	ThingModelEventTypeFlightTaskResourceGet
	// 手动控制状态事件
	ThingModelEventTypeDRCState
	// 喊话器播放状态
	ThingModelEventTypeSpeakerStatus
	// 长操作状态更新
	ThingModelEventTypeOperationProgress
	// 高风险强制返航
	ThingModelEventTypeFlightForceBack
	// 远程日志进度更新
	ThingModelEventTypeFileuploadProgress
)

// ThingEvent 基于物模型的事件不同于设备事件
// 设备事件可能来自于与物模型事件或上行属性，service的回复等
type ThingEvent struct {
	Id           string              `json:"id,omitempty"`
	DeviceId     int64               `json:"deviceId,omitempty"`
	Sn           string              `json:"sn,omitempty"`
	OccurredTime time.Time           `json:"ocurredTime,omitempty"`
	RxTime       time.Time           `json:"rxTime,omitempty"`
	Type         ThingModelEventType `json:"type,omitempty"`
}

func (e *ThingEvent) Caution(event *Event) AnyMap {
	return AnyMap{
		"id":           e.Id,
		"sn":           event.Sn,
		"type":         event.Trans(),
		"level":        event.Level,
		"message":      event.Describe(),
		"occurredTime": event.OccurredTime,
		"deviceId":     conv.Itoa(e.DeviceId),
	}
}

type EventUpReply struct {
	Id        string `json:"id,omitempty"`
	Sn        string `json:"sn,omitempty"`
	DeviceId  int64  `json:"deviceId,omitempty"`
	AirlineId int64  `json:"airlineId,omitempty"`
	Data      AnyMap `json:"data,omitempty"`
}

type FlightReturnEvent struct {
	ThingEvent
	Mileage int32
	Status  Gradient
	Extra   AnyMap
}

type StartLiveEvent struct {
	ThingEvent
	TaskId int64
	Status bool
	Extra  AnyMap
}

type AutoFlightTaskProgressEvent struct {
	ThingEvent
	FlightId             string
	CurrentWaypointIndex int32
	// { 1 "partially_done":"部分完成",2"sent":"已下发",3"in_progress":"执行中",4"ok":"执行成功",5"paused":"暂停",6"rejected":"拒绝",7"failed":"失败",8"canceled":"取消或终止",9"timeout":"超时"}
	Status int32
	//参见：dictionary.go -> ProgressStepDict
	Step int32
	// 针对step的进度0~100
	Percent int32
	// {"0":"断连","1":"不支持该航点","2":"航线准备状态,可上传文件,可执行已有文件","3":"航线文件上传中","4":"触发开始命令，无人机触发读航线等逻辑，还未开始任务，处于准备状态","5":"进入航线,到第一个航点","6":"航线执行","7":"航线中断，触发条件：1.用户主动暂停 2.飞控异常","8":"航线恢复","9":"航线结束"}
	VoyageState int32
	// 任务中断原因 0 无异常
	BreakReason int32
	Extra       AnyMap
}

func (e *AutoFlightTaskProgressEvent) BuildEvent() AnyMap {
	return AnyMap{
		"extra":           e.Extra,
		"status":          e.Status,
		"percent":         e.Percent,
		"occurredTime":    e.OccurredTime,
		"deviceId":        conv.Itoa(e.DeviceId),
		"currentWaypoint": e.CurrentWaypointIndex,
		"step":            FlightProgressMap[conv.Vtoa(e.Step)],
	}
}

type FlytoPointProgressEvent struct {
	ThingEvent
	Result               int32
	FlytoId              string
	CurrentWaypointIndex int32
	// {"wayline_progress":"执行中","wayline_failed":"执行失败","wayline_ok":"执行成功，已飞向目标点","wayline_cancel":"取消飞向目标点"}
	Status string
	// 剩余距离
	Distance float32
	// 秒
	RemainingTime float32
}

func (e *FlytoPointProgressEvent) BuildEvent() AnyMap {
	return AnyMap{
		"status":          e.Status,
		"result":          e.Result,
		"flytoId":         e.FlytoId,
		"occurredTime":    e.OccurredTime,
		"deviceId":        conv.Itoa(e.DeviceId),
		"currentWaypoint": e.CurrentWaypointIndex,
	}
}

type OrbitPointNotifyEvent struct {
	ThingEvent
	//{"failed":"执行失败","in_progress":"执行中","ok":"执行成功"}
	Status string `json:"status"`
	// {"0":"正常","1":"未适配负载","2":"不支持该相机模式","3":"非法命令","4":"定位失败","5":"无人机未起飞","6":"飞行模式错误","7":"该模式下不可用（返航、降落、姿态）","8":"丢失遥控器或图传信号"}
	Code int32 `json:"code"`
	// 环绕半径
	Radius   float32 `json:"radius"`
	Speed    float32 `json:"speed"`
	MaxSpeed float32 `json:"maxSpeed"`
}

func (e *OrbitPointNotifyEvent) BuildEvent() AnyMap {
	return AnyMap{
		"code":         e.Code,
		"speed":        e.Speed,
		"status":       e.Status,
		"radius":       e.Radius,
		"occurredTime": e.OccurredTime,
		"deviceId":     conv.Itoa(e.DeviceId),
	}
}

type LaunchPointProgressEvent struct {
	ThingEvent
	Result               int32
	FlightId             string
	CurrentWaypointIndex int32
	// {"task_finish":"一键起飞任务完成","task_ready":"准备起飞","wayline_cancel":"取消飞向目标点","wayline_failed":"执行失败","wayline_ok":"执行成功，已飞向目标点","wayline_progress":"执行中"}
	Status string
	// 剩余距离
	Distance float32
	// 秒
	RemainingTime float32
}

func (e *LaunchPointProgressEvent) BuildEvent() AnyMap {
	return AnyMap{
		"status":          e.Status,
		"result":          e.Result,
		"flightId":        e.FlightId,
		"occurredTime":    e.OccurredTime,
		"deviceId":        conv.Itoa(e.DeviceId),
		"currentWaypoint": e.CurrentWaypointIndex,
	}
}

type FileuploadProgress struct {
	Sn     string
	Key    string
	Size   int64
	Module string
	Result int32
	// 0正在上传;1上传完成;2上传失败
	Status      int32
	Progress    int32
	UploadRate  int32
	FinishTime  int64
	CurrentStep int32
	TotalStep   int32
}

type FileuploadProgressEvent struct {
	ThingEvent
	Files []*FileuploadProgress
}

type DockHealMonitorEvent struct {
	ThingEvent
	// 告警等级 {"0":"通知","1":"提醒","2":"警告","3":"紧急"}
	Level int32
	// {"0":"飞行任务","1":"设备管理","2":"媒体","3":"hms"}
	Module   int32
	InTheSky int32
	// 是否为及时性的
	Imminent int32
	Code     string
	// 0 机库 1 无人机 2 云端分析
	Source int32
	Extra  AnyMap
}

type DockTopoUpdateEvent struct {
	ThingEvent
	DeviceModel string
	Subdevices  []*DockSubdevice
}

type SubDeviceDomain int

const (
	SubDeviceDomainDrone SubDeviceDomain = iota
	SubDeviceDomainCamera
	SubDeviceDomainController
	_
)

// DockSubdevice 机库子设备包含无人机与摄像机
// sn等于机库SourceSn的 Domain=1 表示机库监控摄像机
type DockSubdevice struct {
	Domain SubDeviceDomain
	Sn     string
	Type   string
	Index  string
	Status string
	Extra  any
}

func (s *DockSubdevice) GetCameraInfo() (*SubCameraInfo, bool) {
	if s.Extra != nil {
		if s.Domain == SubDeviceDomainCamera {
			switch v := s.Extra.(type) {
			case *SubCameraInfo:
				return v, true
			case []byte:
				ci := &SubCameraInfo{}
				json.UnmarshalNoEscape(v, ci)
				s.Extra = ci
				return ci, true
			case string:
				ci := &SubCameraInfo{}
				if ed, err := base64.StdEncoding.DecodeString(v); err == nil {
					json.UnmarshalNoEscape(ed, ci)
					s.Extra = ci
					return ci, true
				} else {
					return nil, false
				}
			default:
				raw, _ := json.Marshal(v)
				ci := &SubCameraInfo{}
				json.UnmarshalNoEscape(raw, ci)
				s.Extra = ci
				return ci, true
			}
		}
	}
	return nil, false
}

type SubCameraInfo struct {
	CoexistVideoNumber int32         `json:"cvn"`
	VideoNumber        int32         `json:"vn"`
	Videos             []CameraVideo `json:"vs"`
}

func (c *SubCameraInfo) GetCameraSwitchable() []VideoType {
	vts := types.NewHashSet[VideoType]()
	for _, v := range c.Videos {
		for _, vt := range v.SwitchableVideoTypes {
			vts.Add(vt)
		}
	}
	return vts.ToSlice()
}

type CameraVideo struct {
	Index string `json:"index"`
	// Type                 VideoType   `json:"type"`
	SwitchableVideoTypes []VideoType `json:"switchable"`
}

type DockFlightTaskResourceRequestEvent struct {
	ThingEvent
	FlightId string
}

// DRC 链路状态事件
type DockDRCStatusEvent struct {
	ThingEvent
	ErrCode int32 `json:"errCode,omitempty"`
	// {"0":"未连接","1":"连接中","2":"已连接"}
	State int32 `json:"state"`
}

type SpeakerPlayStatusEvent struct {
	ThingEvent
	Index string `json:"index"`
	// 非0表示播放出错
	Code int32 `json:"code"`
	// {"0":"TTS 负载模式","1":"录音喊话"}
	Mode int32 `json:"mode"`
	// 0~100
	Progress int32 `json:"progress"`
	// Mode=1 {"change_work_mode":"切换工作模式","download":"从云端下载音频文件到机场","encoding":"编码pcm为opus","play":"开始播放","upload":"机场上传音频到psdk"}
	// Mode=0 {"change_work_mode":"切换工作模式","play":"开始播放","upload":"机场上传音频到psdk"}
	// 成功 ok
	Step string `json:"step"`
	// 播放文件md5哈希
	PlayFileSignature string `json:"playFileSignature"`
}

func (e *SpeakerPlayStatusEvent) BuildEvent() AnyMap {
	return AnyMap{
		"code":         e.Code,
		"mode":         e.Mode,
		"index":        e.Index,
		"status":       e.Step,
		"progress":     e.Progress,
		"occurredTime": e.OccurredTime,
		"deviceId":     conv.Itoa(e.DeviceId),
		"step":         SpeakerProgressMap[e.Step],
	}
}

type OperationProgressEvent struct {
	ThingEvent
	OperationId int64  `json:"operationId"`
	Status      string `json:"status"`
	ServiceType string `json:"operationType"`
	// 0~100
	Progress int32 `json:"progress"`
	// 0 成功
	Code  int32  `json:"code"`
	Extra AnyMap `json:"extra"`
}

func (e *OperationProgressEvent) BuildEvent(op *Operation) AnyMap {
	return AnyMap{
		"code":          e.Code,
		"status":        e.Status,
		"progress":      e.Progress,
		"operationType": op.Type.String(),
		"operationId":   conv.Itoa(e.OperationId),
		"deviceId":      conv.Itoa(e.DeviceId),
		"timestamp":     e.OccurredTime,
	}
}

type DockEventRepo interface {
	RecordDockEvent(ctx context.Context, et ThingEvent, detail any) error
}
