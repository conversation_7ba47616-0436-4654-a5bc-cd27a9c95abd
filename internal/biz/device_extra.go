package biz

import (
	"encoding/json"
	"time"

	"github.com/samber/lo"
	"github.com/tidwall/conv"
)

// 扩展数据
type ExtraData AnyMap

// 原地垂直起飞 or 起飞失败 or 起飞完成
func (extra ExtraData) Copter(point []float64) AnyMap {
	if point != nil {
		extra["isPointed"] = true
	} else {
		extra["isPointed"] = false
	}
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 进入自由 or 退出自由
func (extra ExtraData) Free(flag bool) AnyMap {
	extra["isFreed"] = flag
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 开始指点 or 指点失败 or 指点完成 or 退出指点
func (extra ExtraData) Point(point []float64) AnyMap {
	if point != nil {
		extra["isPointed"] = true
		extra["guidePoint"] = point
	} else {
		extra["isPointed"] = false
		delete(extra, "guidePoint")
	}
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 开始环绕 or 环绕失败 or 退出环绕
func (extra ExtraData) Orbit(payload *StartOrbitPayload) AnyMap {
	if payload != nil {
		extra["isOrbited"] = true
		extra["orbitSpeed"] = payload.Speed
		extra["orbitRadius"] = payload.Radius
		extra["guidePoint"] = payload.Point.Point
		extra["orbitHeight"] = payload.Point.Height
	} else {
		extra["isOrbited"] = false
		delete(extra, "orbitSpeed")
		delete(extra, "guidePoint")
		delete(extra, "orbitHeight")
		delete(extra, "orbitRadius")
	}
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 更新环绕速度
func (extra ExtraData) Orbeed(speed float32) AnyMap {
	extra["orbitSpeed"] = speed
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 关闭飞行控制重置数据
func (extra ExtraData) ResetAero() AnyMap {
	extra["isFreed"] = false
	extra["isPointed"] = false
	extra["isOrbited"] = false
	delete(extra, "orbitSpeed")
	delete(extra, "guidePoint")
	delete(extra, "orbitHeight")
	delete(extra, "orbitRadius")
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 获取当前指点坐标
func (extra ExtraData) GuidePoint() *GuidePoint {
	var point []float64
	if gp, ok := extra["guidePoint"].([]any); ok {
		point = lo.Map(gp, func(coord any, _ int) float64 { return conv.Vtof(coord) })
	}
	return &GuidePoint{Point: point, Height: 0, Timestamp: time.Now().UnixMilli()}
}

// 切换镜头类型
func (extra ExtraData) Lens(vtype int) AnyMap {
	extra["lens"] = VideoType(vtype).String()
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 设置拍照时间
func (extra ExtraData) Picture(timestamp int64) AnyMap {
	extra["pictureTime"] = timestamp
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 设置录像时间
func (extra ExtraData) Video(timestamp int64) AnyMap {
	if timestamp > 0 {
		extra["isVideoed"] = true
		extra["videoTime"] = timestamp
	} else {
		extra["isVideoed"] = false
	}
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 开始喊话 or 停止喊话 or 喊话完成
func (extra ExtraData) Speak(mode int32, content string) AnyMap {
	if mode < 0 {
		extra["isSpoke"] = false
	} else {
		extra["isSpoke"] = true
		extra["speakMode"] = mode
		extra["speakContent"] = content
	}
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 设置解析流状态
func (extra ExtraData) Algflow(status bool) AnyMap {
	extra["algflowStatus"] = status
	extraData, _ := json.Marshal(extra)
	return AnyMap{"extra_data": extraData}
}

// 返航重置数据，镜头类型、飞行控制暂存信息、录像状态、相机模式、喊话信息等
func (extra ExtraData) ReturnReset() {
	delete(extra, "lens")
	delete(extra, "isVideoed")
	delete(extra, "isFreed")
	delete(extra, "isPointed")
	delete(extra, "guidePoint")
	delete(extra, "isOrbited")
	delete(extra, "orbitSpeed")
	delete(extra, "orbitHeight")
	delete(extra, "orbitRadius")
	delete(extra, "isSpoke")
	delete(extra, "speakMode")
	delete(extra, "speakContent")
	delete(extra, "algflowStatus")
}
