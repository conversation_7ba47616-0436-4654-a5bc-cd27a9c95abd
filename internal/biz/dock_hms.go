package biz

var HMSEventDescMap = StringMap{
	"dock_tip_0x12040000":            "机场RTK设备断连",
	"dock_tip_0x12040001":            "机场位置被移动",
	"dock_tip_0x12040002":            "机场RTK工作异常",
	"dock_tip_0x12040003":            "机场RTK标定位置距离机场位置过远",
	"dock_tip_0x12040004":            "机场RTK初始化收敛失败",
	"dock_tip_0x16070045":            "检测到机场发生移动，请谨慎操作",
	"dock_tip_0x160900BF":            "风速过大（≥9 m/s），无法执行飞行任务",
	"dock_tip_0x17000001":            "无图传信号",
	"dock_tip_0x17000004":            "图传信号弱",
	"dock_tip_0x17000005":            "机场所在位置信号干扰较强，请注意飞行安全",
	"dock_tip_0x17110041":            "机场工作状态异常，无人机可能执行航线失联行为",
	"dock_tip_0x1910F003":            "机场舱外监控摄像头未安装或断连",
	"dock_tip_0x19110000":            "%1$s舱盖电机堵转",
	"dock_tip_0x19110002":            "%1$s舱盖位置误差过大",
	"dock_tip_0x19110003":            "%1$s舱盖电机运动超速",
	"dock_tip_0x19110004":            "%1$s舱盖电机无法控制",
	"dock_tip_0x19110005":            "%1$s舱盖电机电压过低",
	"dock_tip_0x19110006":            "%1$s舱盖电机电压过高",
	"dock_tip_0x1911000F":            "%1$s舱盖驱动器通讯异常",
	"dock_tip_0x19110011":            "%1$s舱盖运行未知错误",
	"dock_tip_0x19110012":            "%1$s舱盖位置校准数据错误",
	"dock_tip_0x19110013":            "%1$s舱盖开启或关闭时位置不到位",
	"dock_tip_0x19110014":            "%1$s舱盖开启超时",
	"dock_tip_0x19110015":            "%1$s舱盖关闭超时",
	"dock_tip_0x19110016":            "%1$s舱盖关闭时触发防夹，未成功关闭",
	"dock_tip_0x19110017":            "%1$s舱盖开启时触发防夹，未成功开启",
	"dock_tip_0x19110020":            "%s舱盖霍尔传感器故障",
	"dock_tip_0x19110021":            "%s舱盖开启或关闭超时",
	"dock_tip_0x19110022":            "%s舱盖打开或关闭时触发防夹",
	"dock_tip_0x19110023":            "%s舱盖推杆电压过低",
	"dock_tip_0x19110024":            "%s舱盖推杆断线",
	"dock_tip_0x19110025":            "%s推杆短路",
	"dock_tip_0x19110026":            "%s推杆温度过高",
	"dock_tip_0x19110027":            "%s舱盖异常",
	"dock_tip_0x19110028":            "%s急停按钮故障",
	"dock_tip_0x19110029":            "%s舱盖命令执行失败",
	"dock_tip_0x1911002A":            "%s舱盖未处于密封状态",
	"dock_tip_0x1911002D":            "%dock_cover_index位置功能异常",
	"dock_tip_0x19110400":            "%1$s推杆位置标定数据错误",
	"dock_tip_0x19110401":            "%1$s推杆舵机堵转",
	"dock_tip_0x19110402":            "%1$s推杆舵机阻力过大",
	"dock_tip_0x19110403":            "%1$s推杆舵机温度过高",
	"dock_tip_0x19110404":            "%1$s推杆舵机通讯异常",
	"dock_tip_0x19110405":            "%1$s推杆舵机内部错误",
	"dock_tip_0x19110406":            "%1$s推杆内侧限位开关工作异常",
	"dock_tip_0x19110407":            "%1$s推杆内侧限位开关工作异常",
	"dock_tip_0x19110408":            "%1$s推杆外侧限位开关工作异常",
	"dock_tip_0x19110409":            "%1$s推杆外侧限位开关工作异常",
	"dock_tip_0x1911040B":            "%1$s推杆闭合超时",
	"dock_tip_0x1911040C":            "%1$s推杆展开超时",
	"dock_tip_0x19110800":            "机场蓄电池无法充电",
	"dock_tip_0x19110801":            "机场蓄电池电量低",
	"dock_tip_0x19110802":            "机场蓄电池温度过高",
	"dock_tip_0x19110803":            "机场蓄电池温度过低",
	"dock_tip_0x19110804":            "机场蓄电池开关未开启",
	"dock_tip_0x19110805":            "机场蓄电池过度放电，已无法正常工作",
	"dock_tip_0x19110806":            "机场蓄电池充电功能异常",
	"dock_tip_0x19110807":            "机场蓄电池充电功能异常",
	"dock_tip_0x19110808":            "机场蓄电池功能异常",
	"dock_tip_0x19110809":            "机场蓄电池功能异常",
	"dock_tip_0x19110C00":            "机场监控摄像头未安装或断连",
	"dock_tip_0x19111400":            "机场配电柜柜门被打开",
	"dock_tip_0x19111401":            "机场配电柜柜门传感器异常",
	"dock_tip_0x19111402":            "机场配电柜柜门传感器异常",
	"dock_tip_0x19111800":            "急停按钮被按下",
	"dock_tip_0x19111D01":            "舱内无人机未对频，已对频无人机在舱外",
	"dock_tip_0x19112C01":            "机场主控模块温度传感器损坏",
	"dock_tip_0x19112C02":            "机场主控模块温度传感器损坏",
	"dock_tip_0x19112C03":            "机场环境温度传感器损坏",
	"dock_tip_0x19112C04":            "机场主控模块温度传感器损坏",
	"dock_tip_0x19112C05":            "机场主控模块温度传感器损坏",
	"dock_tip_0x19112C06":            "机场左侧充电连接器温度传感器损坏",
	"dock_tip_0x19112C07":            "机场右侧充电连接器温度传感器损坏",
	"dock_tip_0x19112C08":            "停机坪接口板温度传感器损坏",
	"dock_tip_0x19112C09":            "停机坪接口板温度传感器损坏",
	"dock_tip_0x19112C0A":            "停机坪接口板温度传感器损坏",
	"dock_tip_0x19112C0B":            "机场主控模块温度传感器损坏",
	"dock_tip_0x19112C0C":            "机场主控模块温度传感器未连接",
	"dock_tip_0x19112C0D":            "机场充电电源模块温度传感器损坏",
	"dock_tip_0x19112C0E":            "机场充电电源模块温度传感器未连接",
	"dock_tip_0x19112C0F":            "机场电源模块温度传感器损坏",
	"dock_tip_0x19112C10":            "机场电源模块温度传感器未连接",
	"dock_tip_0x19112C11":            "机场电源模块连接器温度传感器损坏",
	"dock_tip_0x19112C12":            "机场电源模块连接器温度传感器未连接",
	"dock_tip_0x19112C13":            "机场充电模块连接器温度传感器损坏",
	"dock_tip_0x19112C14":            "机场充电模块连接器温度传感器未连接",
	"dock_tip_0x19112C15":            "机场空调电源模块温度传感器损坏",
	"dock_tip_0x19112C16":            "机场空调电源模块温度传感器未连接",
	"dock_tip_0x19112C17":            "机场空调电源模块连接器温度传感器损坏",
	"dock_tip_0x19112C18":            "机场空调电源模块连接器温度传感器未连接",
	"dock_tip_0x19112C19":            "机场蓄电池温度传感器损坏",
	"dock_tip_0x19112C1A":            "机场蓄电池温度传感器未连接",
	"dock_tip_0x19113000":            "机场振动传感器工作异常",
	"dock_tip_0x19113001":            "机场产生振动，可能被移动或遭到破坏",
	"dock_tip_0x19113401":            "无人机电池充电电压异常",
	"dock_tip_0x19113403":            "无人机电池不在位，或电量极低已进入休眠模式",
	"dock_tip_0x19113404":            "无人机电池充电失败",
	"dock_tip_0x19113406":            "无人机电池异常",
	"dock_tip_0x19113407":            "机场供电电源模块异常，无法给无人机电池充电",
	"dock_tip_0x19113414":            "电池温度过高（≥44℃）无法开始充电",
	"dock_tip_0x19113800":            "机场内部电源模块工作异常",
	"dock_tip_0x19113802":            "机场弱电模块供电异常",
	"dock_tip_0x19113803":            "机场供电电源断开",
	"dock_tip_0x19113806":            "机场电源模块故障",
	"dock_tip_0x19113807":            "机场电源模块输出电压过高",
	"dock_tip_0x19113808":            "机场电源模块输出电压过低",
	"dock_tip_0x19113809":            "机场电源模块温度过高",
	"dock_tip_0x1911380A":            "机场电源模块温度过低",
	"dock_tip_0x1911380B":            "机场电源模块连接器温度过高",
	"dock_tip_0x1911380C":            "机场电源模块连接器温度过低",
	"dock_tip_0x19113901":            "机场系统电源电压过高",
	"dock_tip_0x19113902":            "机场系统电源电压过低",
	"dock_tip_0x19113903":            "机场主控模块异常掉电",
	"dock_tip_0x19113C00":            "防雷器后备保护器开关未开启，或防雷器损坏",
	"dock_tip_0x19113C01":            "停机坪连接异常",
	"dock_tip_0x19113C02":            "雨量计连接异常",
	"dock_tip_0x19113C03":            "空调连接异常",
	"dock_tip_0x19113C04":            "机场充电模块连接异常",
	"dock_tip_0x19113D00":            "空调制冷片损坏",
	"dock_tip_0x19113D01":            "空调制冷片损坏",
	"dock_tip_0x19113D02":            "空调制冷片损坏",
	"dock_tip_0x19113D03":            "空调加热器损坏",
	"dock_tip_0x19113D04":            "空调加热器损坏",
	"dock_tip_0x19113D05":            "空调内循环风扇损坏",
	"dock_tip_0x19113D06":            "空调外循环风扇损坏",
	"dock_tip_0x19113D07":            "空调外循环出风口温度传感器损坏",
	"dock_tip_0x19113D08":            "空调内循环进风口温度传感器损坏",
	"dock_tip_0x19113D09":            "空调内循环出风口右侧温度传感器损坏",
	"dock_tip_0x19113D0A":            "空调内循环出风口左侧温度传感器损坏",
	"dock_tip_0x19113D0B":            "空调制冷片温度传感器损坏",
	"dock_tip_0x19113D0C":            "空调内循环风道温度传感器损坏",
	"dock_tip_0x19113D0D":            "舱盖破损或无法完全关闭",
	"dock_tip_0x19113D0E":            "空调内循环进风口堵塞",
	"dock_tip_0x19113D0F":            "空调内循环出风口堵塞",
	"dock_tip_0x19113D10":            "空调外循环出风口与进风口温差过大（≥20℃）",
	"dock_tip_0x19113D11":            "空调制冷片供电异常",
	"dock_tip_0x19113D12":            "空调加热器供电异常",
	"dock_tip_0x19113D13":            "空调外循环风扇供电异常",
	"dock_tip_0x19113D14":            "空调内循环风扇供电异常",
	"dock_tip_0x19113D15":            "空调供电异常",
	"dock_tip_0x19113D16":            "空调外循环进风口堵塞",
	"dock_tip_0x19113D20":            "空调外循环进风口温度传感器损坏",
	"dock_tip_0x19113D21":            "空调内循环出风口温度传感器损坏",
	"dock_tip_0x19113D22":            "空调TEC冷端温度传感器损坏",
	"dock_tip_0x19113D23":            "空调外循环出风口与进风口温差过大（≥10℃）",
	"dock_tip_0x19113D24":            "左推杆温感NTC温度传感器损坏",
	"dock_tip_0x19113D25":            "右推杆温感NTC温度传感器损坏",
	"dock_tip_0x19113D26":            "高温预警（≥45℃）",
	"dock_tip_0x19113D27":            "低温预警（≤-20℃）",
	"dock_tip_0x19113D28":            "空调外循环出风口温度传感器短路",
	"dock_tip_0x19113D29":            "空调内循环进风口温度传感器短路",
	"dock_tip_0x19113D2A":            "空调制冷片温度传感器短路",
	"dock_tip_0x19113D2B":            "空调TEC冷端温度传感器短路",
	"dock_tip_0x19113D2C":            "空调外循环进风口温度传感器短路",
	"dock_tip_0x19113D2D":            "空调内循环出风口温度传感器短路",
	"dock_tip_0x19113D2E":            "左推杆温感NTC温度传感器短路",
	"dock_tip_0x19113D2F":            "右推杆温感NTC温度传感器短路",
	"dock_tip_0x19113D60":            "空调制冷功能异常",
	"dock_tip_0x19113D61":            "空调制冷功能异常",
	"dock_tip_0x19113D62":            "空调外循环风扇损坏",
	"dock_tip_0x19113D63":            "空调外循环风扇风速异常",
	"dock_tip_0x19113D64":            "空调内循环风扇损坏",
	"dock_tip_0x19113D65":            "空调内循环风扇风速异常",
	"dock_tip_0x19114000":            "风速过大（≥9 m/s），无法执行飞行任务",
	"dock_tip_0x19114800":            "雨量过大，无法执行飞行任务",
	"dock_tip_0x19114801":            "机场监控摄像头供电异常",
	"dock_tip_0x19114802":            "DJI Cellular 模块供电异常",
	"dock_tip_0x19114803":            "舱盖告警灯供电异常",
	"dock_tip_0x19114804":            "风速计加热供电异常",
	"dock_tip_0x19114805":            "舱内温湿度传感器连接异常",
	"dock_tip_0x19114806":            "雨量计连接异常",
	"dock_tip_0x19114807":            "雨量计加热模块供电异常",
	"dock_tip_0x19114808":            "雨量计加热模块温度传感器连接异常",
	"dock_tip_0x19114809":            "雨量计加热模块温度传感器温度过高",
	"dock_tip_0x1911480A":            "风速计加热模块温度传感器损坏",
	"dock_tip_0x1911480B":            "风速计加热模块温度传感器温度过高",
	"dock_tip_0x1911480C":            "舱盖除冰加热温度传感器损坏",
	"dock_tip_0x1911480D":            "舱盖除冰加热温度传感器温度过高",
	"dock_tip_0x1911480E":            "雨量计连接异常",
	"dock_tip_0x1911480F":            "雨量计短路",
	"dock_tip_0x19114810":            "风速计模块连接异常",
	"dock_tip_0x19114811":            "风速计霍尔温度传感器损坏",
	"dock_tip_0x19114812":            "风速计霍尔温度传感器损坏",
	"dock_tip_0x19114813":            "温湿度传感器自加热时间过久",
	"dock_tip_0x19114814":            "雨量计检测部件损坏",
	"dock_tip_0x19114815":            "雨量计检测部件损坏",
	"dock_tip_0x1911481A":            "机场舱盖除冰功能异常",
	"dock_tip_0x1911481B":            "风速计供电异常",
	"dock_tip_0x19114C00":            "机场浸水",
	"dock_tip_0x19115000":            "机场异常重启",
	"dock_tip_0x19116000":            "机场充电模块电源故障",
	"dock_tip_0x19116001":            "机场充电模块电源电压过高",
	"dock_tip_0x19116002":            "机场充电模块电源电压过低",
	"dock_tip_0x19116003":            "机场充电模块电源电流过大",
	"dock_tip_0x19116004":            "机场充电模块电源电流过小",
	"dock_tip_0x19116005":            "机场充电模块电源MOS温度过高",
	"dock_tip_0x19116006":            "机场充电模块电源MOS温度过低",
	"dock_tip_0x19116007":            "机场充电模块控制器电源异常",
	"dock_tip_0x19116008":            "机场充电模块连接器温度过高",
	"dock_tip_0x19116009":            "机场充电模块连接器温度过低",
	"dock_tip_0x1911600A":            "机场空调电源模块供电异常",
	"dock_tip_0x1911600B":            "机场空调电源模块电压过高",
	"dock_tip_0x1911600C":            "机场空调电源模块电压过低",
	"dock_tip_0x1911600D":            "机场空调电源模块电流过大",
	"dock_tip_0x1911600E":            "机场空调电源模块电流过小",
	"dock_tip_0x1911600F":            "机场空调控制器模块电源故障",
	"dock_tip_0x19116010":            "机场空调控制器模块电流过大",
	"dock_tip_0x19116011":            "机场空调控制器模块电流过小",
	"dock_tip_0x19116012":            "机场空调模块电源MOS温度过高",
	"dock_tip_0x19116013":            "机场空调模块电源MOS温度过低",
	"dock_tip_0x19116014":            "空调模块连接器温度过高",
	"dock_tip_0x19116015":            "空调模块连接器温度过低",
	"dock_tip_0x19116016":            "气象站模块电源故障",
	"dock_tip_0x19116017":            "气象站模块电源电流过大",
	"dock_tip_0x19116018":            "气象站模块电源电流过小",
	"dock_tip_0x1911601B":            "机场图传模块电源异常",
	"dock_tip_0x1911601F":            "机场舱内监控摄像头电压过高",
	"dock_tip_0x19116020":            "机场舱内监控摄像头电压过低",
	"dock_tip_0x19116021":            "右舱盖电源电压过高",
	"dock_tip_0x19116022":            "右舱盖电源电压过低",
	"dock_tip_0x19116023":            "机场主控模块温度过高",
	"dock_tip_0x19116024":            "机场主控模块温度过低",
	"dock_tip_0x19117000":            "无人机电池充电失败",
	"dock_tip_0x19117021":            "舱盖运动中，无人机电池充电失败",
	"dock_tip_0x19117022":            "机场升级中，无人机电池充电失败",
	"dock_tip_0x19117023":            "未检测到无人机",
	"dock_tip_0x19117024":            "机场充电模块检测到金属异物，无人机电池充电失败",
	"dock_tip_0x19117025":            "检测到机场舱盖已打开，无人机充电失败",
	"dock_tip_0x19117041":            "无人机未连接",
	"dock_tip_0x19117043":            "无人机严重低电量，无法开机",
	"dock_tip_0x19117045":            "无人机电池故障，无法充电或开机",
	"dock_tip_0x19117046":            "无人机电池通信异常",
	"dock_tip_0x19117047":            "无人机电量较高，无需充电",
	"dock_tip_0x19117048":            "无人机无线充电模块异常",
	"dock_tip_0x19117061":            "无人机充电模块唤醒失败",
	"dock_tip_0x19117202":            "无人机开机超时",
	"dock_tip_0x19117221":            "机场连接无人机蓝牙失败",
	"dock_tip_0x19117222":            "机场蓝牙模块链路故障",
	"dock_tip_0x19117223":            "无人机蓝牙模块信号弱",
	"dock_tip_0x19117321":            "无人机图传链路异常，关机失败",
	"dock_tip_0x19117322":            "无人机关机超时",
	"dock_tip_0x19117460":            "机场充电电源模块电压过低",
	"dock_tip_0x19117461":            "机场充电电源模块电流过大",
	"dock_tip_0x19117462":            "无人机充电模块线圈电流过大",
	"dock_tip_0x19117463":            "无人机充电电源故障",
	"dock_tip_0x19117464":            "无人机充电电源过压",
	"dock_tip_0x19117465":            "机场充电电源过压",
	"dock_tip_0x19117470":            "无人机充电电源故障",
	"dock_tip_0x19117480":            "机场充电模块温度过高",
	"dock_tip_0x19117481":            "机场充电模块充电电流过大",
	"dock_tip_0x191174A0":            "机场充电模块线圈温度过高",
	"dock_tip_0x191174B0":            "机场无线充电模块温度检测异常",
	"dock_tip_0x191174B1":            "机场充电模块线圈温度传感器未连接",
	"dock_tip_0x191174B2":            "机场充电模块线圈温度传感器异常",
	"dock_tip_0x191174C0":            "无人机充电模块线圈温度过高",
	"dock_tip_0x191174D0":            "无人机无线充电模块温度检测异常",
	"dock_tip_0x191174D1":            "无人机充电模块线圈温度传感器未连接",
	"dock_tip_0x191174D2":            "无人机充电模块线圈温度传感器异常",
	"fpv_tip_0x11000002":             "无人机分电板1温度过高，请尽快返航或降落",
	"fpv_tip_0x11000003":             "无人机分电板2温度过高，请尽快返航或降落",
	"fpv_tip_0x11000020":             "%index号云台位负载电压过高",
	"fpv_tip_0x11000021":             "%index号云台位负载电压过低",
	"fpv_tip_0x11000022":             "%index号云台位电源温度过高",
	"fpv_tip_0x11000023":             "%index号云台位电源温度过低",
	"fpv_tip_0x11000024":             "负载总功率过载",
	"fpv_tip_0x11000025":             "%index号云台负载链路异常",
	"fpv_tip_0x11000026":             "中心板异常重启",
	"fpv_tip_0x11000027":             "%index号负载接口异常，请检查负载接口是否破损、进水或者存在异物",
	"fpv_tip_0x11000029":             "PSDK 设备电压过高",
	"fpv_tip_0x1100002A":             "PSDK 设备电压过低",
	"fpv_tip_0x1100002B":             "PSDK电源温度过高",
	"fpv_tip_0x1100002C":             "PSDK电源温度过低",
	"fpv_tip_0x1100002D":             "PSDK 设备功率过载，重启中",
	"fpv_tip_0x1100002E":             "PSDK 设备功率过载，重启失败",
	"fpv_tip_0x11000034":             "起落架舵机供电异常，请尝试重启无人机",
	"fpv_tip_0x11000035":             "雷达供电异常，请尝试重启雷达",
	"fpv_tip_0x11000036":             "4G 模块供电异常，请尝试拔插",
	"fpv_tip_0x11000037":             "云台供电异常，请尝试拔插云台",
	"fpv_tip_0x11000038":             "左电池连接器温度过高，请尽快返航或降落",
	"fpv_tip_0x11000039":             "右电池连接器温度过高，请尽快返航或降落",
	"fpv_tip_0x1100003A":             "左电池安装不到位，请重新安装电池",
	"fpv_tip_0x1100003B":             "右电池安装不到位，请重新安装电池",
	"fpv_tip_0x1100003C":             "云台安装不到位，请重新安装云台",
	"fpv_tip_0x1100003D":             "无人机中心板温度过高，请尽快返航或降落",
	"fpv_tip_0x1100003E":             "雷达连接断开，请谨慎飞行并尽快返航或降落",
	"fpv_tip_0x1100003F":             "无人机TOF模块连接异常，请尝试重启无人机",
	"fpv_tip_0x11000040":             "起落架舵机通信异常，请尝试重启无人机",
	"fpv_tip_0x11000041":             "起落架舵机堵转，请检查是否有异物并重启无人机",
	"fpv_tip_0x11000042":             "电池电流差异大，请保持平稳飞行并尽快降落",
	"fpv_tip_0x11000043":             "舵机温度高，请避免频繁变形",
	"fpv_tip_0x11000044":             "舵机温度过高，已经停止变形，请等待舵机温度降低",
	"fpv_tip_0x11000045":             "温度过低起落架运动受阻",
	"fpv_tip_0x11000046":             "无人机丝杆即将达到设计使用寿命，为确保飞行安全，请您及时联系 DJI 技术支持并寄回处理",
	"fpv_tip_0x11000047":             "无人机丝杆已超出设计使用寿命，为确保飞行安全，请您立刻联系 DJI 技术支持并寄回处理，继续使用导致的飞行事故，需要您承担维修费用",
	"fpv_tip_0x11000048":             "起落架异常，避障性能降低，请谨慎飞行，注意降落镜头保护，请联系售后",
	"fpv_tip_0x11020030":             "%battery_index电池上盖温度异常，转为单电池飞行，请尽快返航或降落",
	"fpv_tip_0x11020031":             "%battery_index电池连接器温度异常，转为单电池飞行，请尽快返航或降落",
	"fpv_tip_0x11020032":             "%battery_index电池温度异常，转为单电池飞行，请尽快返航或降落",
	"fpv_tip_0x11020033":             "%battery_index电池异常，转为单电池飞行，请尽快返航或降落",
	"fpv_tip_0x110B0001":             "%battery_index电池放电过流，请保持平稳飞行并检查是否有负重",
	"fpv_tip_0x110B0002":             "%battery_index电池温度过高，请尽快返航，待电池冷却到常温再使用",
	"fpv_tip_0x110B0003":             "%battery_index电池温度过低，请预热电池至5摄氏度以上",
	"fpv_tip_0x110B0004":             "%battery_index电池放电出现短路，请更换电池",
	"fpv_tip_0x110B0005":             "电芯%index欠压，请更换电池",
	"fpv_tip_0x110B0006":             "%battery_index电池电芯损坏，请停止使用该电池，并联系售后服务",
	"fpv_tip_0x110B0007":             "%battery_index电池自检失败，请更换电池，并联系售后服务",
	"fpv_tip_0x110B0008":             "%battery_index电池自检失败，请更换电池，并联系售后服务",
	"fpv_tip_0x110B0009":             "%battery_index电池自检失败，请更换电池，并联系售后服务",
	"fpv_tip_0x110B000A":             "%battery_index电池自检失败，请更换电池，并联系售后服务",
	"fpv_tip_0x110B000B":             "%battery_index电池已损坏，请停止使用该电池，并联系售后服务",
	"fpv_tip_0x110B000C":             "%battery_index电池需要保养",
	"fpv_tip_0x110B000D":             "%battery_index电池已损坏，请停止使用该电池，并联系售后服务",
	"fpv_tip_0x110B000E":             "%index电池发生存储自放电",
	"fpv_tip_0x110B000F":             "%battery_index电池性能严重下降，继续使用有安全风险",
	"fpv_tip_0x110B0010":             "超出安全条件使用，请报废%battery_index电池",
	"fpv_tip_0x110B0011":             "%battery_index电池数据通信异常，请重新安装电池，如果未解决请更换电池",
	"fpv_tip_0x110B0012":             "%battery_index电池需要保养",
	"fpv_tip_0x110B0013":             "%battery_index电池槽没有电池，请插入电池或者更换电池",
	"fpv_tip_0x110B0013_sky":         "%battery_index电池槽检测不到电池，请尽快返航或降落",
	"fpv_tip_0x110B0014":             "%battery_index电池在自加热",
	"fpv_tip_0x110B0015":             "%battery_index电池需要保养才能正常飞行",
	"fpv_tip_0x110B0016":             "%battery_index电池需要保养才能正常飞行",
	"fpv_tip_0x110B0017":             "%battery_index电池需要保养才能正常飞行",
	"fpv_tip_0x110B0018":             "%battery_index电池需要保养才能正常飞行",
	"fpv_tip_0x110B0019":             "%battery_index电池需要保养才能正常飞行",
	"fpv_tip_0x110B001A":             "%battery_index电池需要保养才能正常飞行",
	"fpv_tip_0x110B001B":             "%battery_index电池需要保养才能正常飞行",
	"fpv_tip_0x110B001C":             "电池不匹配，建议更换匹配电池",
	"fpv_tip_0x110B001D":             "电池槽%battery_index电池放电异常，请尽快返航或降落",
	"fpv_tip_0x110B001E":             "电池槽%battery_index电池接触不良，请尽快返航或降落",
	"fpv_tip_0x110B007":              "电池%d%自检失败，请更换电池，并联系售后服务",
	"fpv_tip_0x110B0403":             "双电池版本不一致，请更新电池固件版本",
	"fpv_tip_0x110b001e":             "电池槽%battery_index电池接触不良，请尽快返航或降落",
	"fpv_tip_0x12000000":             "电池箱未激活",
	"fpv_tip_0x12000001":             "电池箱自检异常",
	"fpv_tip_0x12000002":             "电池箱拓展板通信异常",
	"fpv_tip_0x12010000":             "电源模块无法识别",
	"fpv_tip_0x12010001":             "电源模块输入电压过高",
	"fpv_tip_0x12010002":             "电源模块输入电压过低",
	"fpv_tip_0x12010003":             "电源模块输出电压异常",
	"fpv_tip_0x12010004":             "电源模块输出电流异常",
	"fpv_tip_0x12010005":             "电源模块发生短路",
	"fpv_tip_0x12010006":             "电源模块通信异常",
	"fpv_tip_0x12010007":             "电源模块风扇故障",
	"fpv_tip_0x12010008":             "电源模块温度过高",
	"fpv_tip_0x12020000":             "%index槽位电池通讯异常",
	"fpv_tip_0x12020001":             "%index槽位电池温度过低",
	"fpv_tip_0x12020002":             "%index槽位电池温度过高",
	"fpv_tip_0x12020003":             "%index槽位电池电芯过压",
	"fpv_tip_0x12020004":             "%index槽位电池系统异常",
	"fpv_tip_0x12020005":             "%index槽位电池充电过流",
	"fpv_tip_0x12020006":             "%index槽位电池电压过高",
	"fpv_tip_0x12020007":             "%index槽位电池充电过压",
	"fpv_tip_0x12020008":             "%index槽位电池充电欠压",
	"fpv_tip_0x12020009":             "%index槽位电池永久损坏",
	"fpv_tip_0x1202000A":             "%index槽位电池短路",
	"fpv_tip_0x1202000B":             "%index槽位电池充电时间过长",
	"fpv_tip_0x1202000C":             "%index槽位电池无充电电流",
	"fpv_tip_0x1202000D":             "%index槽位电池类型无法识别",
	"fpv_tip_0x12030000":             "电池箱主板温度过高",
	"fpv_tip_0x12030001":             "电池箱主板电压过高",
	"fpv_tip_0x12030002":             "电池箱主板电压过低",
	"fpv_tip_0x12030003":             "电池箱%index槽位充电通道异常",
	"fpv_tip_0x12030004":             "交流输入压降过大",
	"fpv_tip_0x12030005":             "机油保养提示",
	"fpv_tip_0x12030006":             "管家板输出电压异常",
	"fpv_tip_0x12030007":             "管家板输入电压异常",
	"fpv_tip_0x12120000":             "%index槽位遥控器电池通信异常",
	"fpv_tip_0x12120001":             "%index槽位遥控器电池温度异常",
	"fpv_tip_0x12120002":             "%index槽位遥控器电池充电异常",
	"fpv_tip_0x14010031":             "负载融合数据异常，请重启负载",
	"fpv_tip_0x14010032":             "负载融合数据未收敛",
	"fpv_tip_0x14010033":             "负载系统时间异常，请重启无人机",
	"fpv_tip_0x14010034":             "负载角速度计数据非法，请重启负载",
	"fpv_tip_0x14010035":             "负载角速度计数据卡死，请重启负载",
	"fpv_tip_0x14010036":             "负载无角速度计数据，请重启负载",
	"fpv_tip_0x14010037":             "负载角速度计数据超限，请重启负载",
	"fpv_tip_0x14010038":             "负载角速度计数据异常，请重启负载",
	"fpv_tip_0x14010039":             "负载加速度计数据非法，请重启负载",
	"fpv_tip_0x1401003A":             "负载加速度计数据卡死，请重启负载",
	"fpv_tip_0x1401003B":             "负载无加速度计数据，请重启负载",
	"fpv_tip_0x1401003C":             "负载加速度计数据超限，请重启负载",
	"fpv_tip_0x1401003D":             "负载加速度计数据异常，请重启负载",
	"fpv_tip_0x1401003E":             "负载RTK数据解算异常，请重启无人机",
	"fpv_tip_0x1401003F":             "负载测向数据异常，请确认飞机搜星质量",
	"fpv_tip_0x14010040":             "负载RTK数据异常，请重启无人机",
	"fpv_tip_0x14010041":             "负载RTK时间异常，请重启无人机",
	"fpv_tip_0x14010042":             "负载RTK数据无效，请重启无人机",
	"fpv_tip_0x14010042_sky":         "负载RTK数据无效，请确认搜星情况良好，等待恢复",
	"fpv_tip_0x14010043":             "请等待负载惯导预热完成",
	"fpv_tip_0x14010044":             "负载惯导温控异常，请重启负载",
	"fpv_tip_0x14010045":             "负载惯导温度过高，请重启负载",
	"fpv_tip_0x14010046":             "负载惯导温度过低，请重启负载",
	"fpv_tip_0x14010047":             "负载芯片温度过高，请尽快返航或降落，移除负载等待冷却后重启使用",
	"fpv_tip_0x14010047_sky":         "负载芯片温度过高，请尽快返航或降落，移除负载等待冷却后重启使用",
	"fpv_tip_0x14010048":             "负载风扇异常，请检查负载风扇是否有堵转",
	"fpv_tip_0x14010048_sky":         "负载风扇异常，请返航或降落，检查负载风扇是否有堵转",
	"fpv_tip_0x14010049":             "负载PPS数据异常",
	"fpv_tip_0x1401004A":             "负载UTC时间异常",
	"fpv_tip_0x14020000":             "%lidar_index主控芯片温度异常",
	"fpv_tip_0x14020001":             "主控芯片温度异常",
	"fpv_tip_0x14020002":             "%lidar_index看门狗超时",
	"fpv_tip_0x14020003":             "ASIC芯片错误",
	"fpv_tip_0x14020004":             "%lidar_index栈溢出",
	"fpv_tip_0x14020005":             "MIPI故障",
	"fpv_tip_0x14020007":             "%lidar_index脏污，请擦拭雷达光窗",
	"fpv_tip_0x14020008":             "%lidar_index参数异常",
	"fpv_tip_0x14020031":             "负载融合数据异常，请重启负载",
	"fpv_tip_0x14020032":             "负载融合数据未收敛",
	"fpv_tip_0x14020033":             "负载系统时间异常，请重启无人机",
	"fpv_tip_0x14020034":             "负载角速度计数据非法，请重启负载",
	"fpv_tip_0x14020035":             "负载角速度计数据卡死，请重启负载",
	"fpv_tip_0x14020036":             "负载无角速度计数据，请重启负载",
	"fpv_tip_0x14020037":             "负载角速度计数据超限，请重启负载",
	"fpv_tip_0x14020038":             "负载角速度计数据异常，请重启负载",
	"fpv_tip_0x14020039":             "负载加速度计数据非法，请重启负载",
	"fpv_tip_0x1402003A":             "负载加速度计数据卡死，请重启负载",
	"fpv_tip_0x1402003B":             "负载无加速度计数据，请重启负载",
	"fpv_tip_0x1402003C":             "负载加速度计数据超限，请重启负载",
	"fpv_tip_0x1402003D":             "负载加速度计数据异常，请重启负载",
	"fpv_tip_0x1402003E":             "负载RTK数据解算异常，请重启无人机",
	"fpv_tip_0x14020040":             "负载RTK数据异常，请重启无人机",
	"fpv_tip_0x14020041":             "负载RTK时间异常，请重启无人机",
	"fpv_tip_0x14020042":             "负载RTK数据无效，请重启无人机",
	"fpv_tip_0x14020042_sky":         "负载RTK数据无效，请确认搜星情况良好，等待恢复",
	"fpv_tip_0x140200FF":             "%lidar_index模块异常，请联系售后服务",
	"fpv_tip_0x14030000":             "%lidar_index电调板温度异常保护",
	"fpv_tip_0x14030001":             "%lidar_index电机电流过高保护",
	"fpv_tip_0x14030002":             "%lidar_index电机转速过低",
	"fpv_tip_0x14030003":             "%lidar_index扫描模块自检异常",
	"fpv_tip_0x14030004":             "%lidar_index电机堵转",
	"fpv_tip_0x14030005":             "%lidar_index电机供电电压过低保护",
	"fpv_tip_0x14030006":             "%lidar_index电机供电电压过高保护",
	"fpv_tip_0x14030008":             "%lidar_index电机过载",
	"fpv_tip_0x1403000B":             "%lidar_index 校准中，无法起飞，请勿移动无人机，等待校准完毕",
	"fpv_tip_0x1403000C":             "%lidar_index扫描模块异常恢复中，若长时间未恢复请重启",
	"fpv_tip_0x1403000D":             "%lidar_index光通信误码",
	"fpv_tip_0x1403000E":             "%lidar_index光通信断连",
	"fpv_tip_0x1403000F":             "%lidar_index电机自检总线电压异常",
	"fpv_tip_0x14030010":             "%lidar_index电机自检驱动芯片异常",
	"fpv_tip_0x14030011":             "%lidar_index电机自检相电流异常",
	"fpv_tip_0x14030012":             "%lidar_index电机自检相电阻异常",
	"fpv_tip_0x14030013":             "%lidar_index电机自检码盘异常",
	"fpv_tip_0x14030014":             "%lidar_index电机自检超时",
	"fpv_tip_0x14030015":             "%lidar_index电机超速异常",
	"fpv_tip_0x14030016":             "%lidar_index振镜温度异常",
	"fpv_tip_0x14030017":             "%lidar_index无线供电电压异常",
	"fpv_tip_0x14030018":             "%lidar_index振镜功耗异常",
	"fpv_tip_0x14030019":             "%lidar_index振镜FOV异常",
	"fpv_tip_0x1403001A":             "%lidar_index 低温加热中，无法起飞，请等待加热完成",
	"fpv_tip_0x1403001B":             "%lidar_index电调断连",
	"fpv_tip_0x1403001C":             "%lidar_index振镜不对称异常",
	"fpv_tip_0x1403001D":             "%lidar_index需要校准，长时间使用后建议进行校准",
	"fpv_tip_0x14040000":             "%lidar_indexAPD高压异常",
	"fpv_tip_0x14040001":             "%lidar_index接收器件温度异常",
	"fpv_tip_0x14040002":             "%lidar_index激光器温度过异常",
	"fpv_tip_0x14040003":             "%lidar_indexTIA_DC阈值异常",
	"fpv_tip_0x14040004":             "%lidar_index温度偏高，请注意运行环境或移至阴凉处",
	"fpv_tip_0x14050000":             "%lidar_indexPPS信号异常",
	"fpv_tip_0x14050001":             "%lidar_indexPTP信号异常",
	"fpv_tip_0x14050002":             "PPS同步异常",
	"fpv_tip_0x14050003":             "%lidar_index时间同步精度低",
	"fpv_tip_0x14050005":             "%lidar_index无PPS脉冲异常",
	"fpv_tip_0x14050006":             "%lidar_index无PTP信号异常",
	"fpv_tip_0x14060000":             "近红外补光功率异常并关闭，请重启或联系售后客服",
	"fpv_tip_0x1466001":              "%lidar_index主控芯片温度异常",
	"fpv_tip_0x14810040":             "激光雷达温度过高/过低",
	"fpv_tip_0x14810080":             "激光雷达电压过高/过低",
	"fpv_tip_0x148100c0":             "激光雷达电机异常",
	"fpv_tip_0x14810100":             "激光雷达寿命预警",
	"fpv_tip_0x14810140":             "激光雷达系统异常",
	"fpv_tip_0x15000020":             "毫米波雷达温度过低，请确认工作环境温度是否过低",
	"fpv_tip_0x15000021":             "毫米波雷达温度过高，请确认工作环境温度是否过高",
	"fpv_tip_0x15010020":             "毫米波雷达内部供电异常，请重启雷达负载",
	"fpv_tip_0x15010021":             "毫米波雷达内部供电异常，请重启雷达负载",
	"fpv_tip_0x15010022":             "毫米波雷达内部供电异常，请重启雷达负载",
	"fpv_tip_0x15010023":             "毫米波雷达内部供电异常，请重启雷达负载",
	"fpv_tip_0x15020020":             "毫米波雷达电机异常，请检查雷达是否卡转",
	"fpv_tip_0x15020021":             "毫米波雷达电机异常，请检查雷达是否卡转",
	"fpv_tip_0x15020022":             "毫米波雷达电机异常，请检查雷达是否卡转",
	"fpv_tip_0x15020023":             "毫米波雷达感知功能异常，请检查固件版本",
	"fpv_tip_0x15030020":             "毫米波雷达链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15030021":             "毫米波雷达链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15030022":             "毫米波雷达链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15030023":             "毫米波雷达启动超时，请检查周边是否存在强力干扰",
	"fpv_tip_0x15030024":             "毫米波雷达通信链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15030025":             "毫米波雷达通信链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15030026":             "毫米波雷达通信链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15030028":             "%index雷达线材异常，避障定高功能失效",
	"fpv_tip_0x15040020":             "毫米波雷达电机异常，请检查雷达是否卡转",
	"fpv_tip_0x15040021":             "毫米波雷达电机异常，请检查雷达是否卡转",
	"fpv_tip_0x15060020":             "毫米波雷达电机异常，请检查雷达是否卡转",
	"fpv_tip_0x15070020":             "毫米波雷达通信链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15080020":             "毫米波雷达温度过高，请确认工作环境温度是否过高",
	"fpv_tip_0x15080021":             "毫米波雷达芯片温度过高，避障定高性能衰退",
	"fpv_tip_0x15080022":             "毫米波雷达芯片温度过高，避障定高性能衰退",
	"fpv_tip_0x15090020":             "毫米波雷达温度过高，请确认工作环境温度是否过高",
	"fpv_tip_0x15090021":             "毫米波雷达功能异常，避障功能失效",
	"fpv_tip_0x15100020":             "毫米波雷达通信链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15100021":             "毫米波雷达通信链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15100022":             "毫米波雷达通信链路不稳定，请检查周边是否存在强力干扰",
	"fpv_tip_0x15110020":             "毫米波雷达电机异常，请检查雷达是否卡转",
	"fpv_tip_0x15130021":             "毫米波雷达感知功能异常，请检查固件版本",
	"fpv_tip_0x15140020":             "毫米波雷达射频时钟异常，请立即返航并检查雷达负载",
	"fpv_tip_0x15200020":             "毫米波雷达温度异常，定高避障功能不稳定",
	"fpv_tip_0x15200021":             "毫米波雷达温度异常，定高避障功能不稳定",
	"fpv_tip_0x15200022":             "毫米波雷达温度异常，定高避障功能不稳定",
	"fpv_tip_0x15200023":             "毫米波雷达温度异常，定高避障功能不稳定",
	"fpv_tip_0x15210020":             "毫米波雷达供电异常，定高避障功能不稳定，请关机后检查雷达是否安装到位",
	"fpv_tip_0x15210021":             "毫米波雷达供电异常，定高避障功能不稳定，请关机后检查雷达是否安装到位",
	"fpv_tip_0x15210022":             "毫米波雷达通信异常，定高避障功能不稳定，请关机后检查雷达是否安装到位",
	"fpv_tip_0x15210023":             "毫米波雷达功能异常，定高避障功能不稳定，请重启无人机",
	"fpv_tip_0x15210024":             "毫米波雷达功能异常，定高避障功能不稳定，请重启无人机",
	"fpv_tip_0x15220020":             "后下雷达断连，请重启或检查更换",
	"fpv_tip_0x15300020":             "毫米波雷达感知功能异常，请检查固件版本",
	"fpv_tip_0x15300021":             "毫米波雷达感知功能异常，请检查固件版本",
	"fpv_tip_0x15300022":             "毫米波雷达感知功能异常，请检查固件版本",
	"fpv_tip_0x16000001":             "无法起飞：飞控系统负载高，请重启无人机",
	"fpv_tip_0x16000001_sky":         "飞控系统负载高，如若持续存在请尽快降落",
	"fpv_tip_0x16000002":             "飞行数据记录异常，请尽快联系就近代理商或大疆售后服务。",
	"fpv_tip_0x16000002_sky":         "飞行数据记录异常，请尽快联系就近代理商或大疆售后服务。",
	"fpv_tip_0x1600000B":             "无人机疑似碰撞，返航后请检查机身和桨叶",
	"fpv_tip_0x16010001":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010001_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010005":             "传感器系统初始化中，请等待初始化完成",
	"fpv_tip_0x16010005_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010007":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010007_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x1601000A":             "传感器系统初始化中，请等待初始化完成",
	"fpv_tip_0x1601000A_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x1601000D":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x1601000D_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010010":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010010_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010013":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010013_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010016":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010016_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010019":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010019_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x1601001C":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x1601001C_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x1601001F":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x1601001F_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010022":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010022_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010025":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010025_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010026":             "无法起飞：检测到非官方RTK模块",
	"fpv_tip_0x16010026_sky":         "检测到非官方RTK模块",
	"fpv_tip_0x16010028":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010028_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x1601002B":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x1601002B_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x1601002F":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x1601002F_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010032":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010032_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010033":             "视觉定位观测较差，请注意飞行安全",
	"fpv_tip_0x16010034":             "卫星定位观测较差，请注意飞行安全",
	"fpv_tip_0x16010035":             "定位质量较差，请注意飞行安全",
	"fpv_tip_0x16010036":             "视觉定位观测不佳，请注意飞行安全",
	"fpv_tip_0x16010037":             "卫星定位观测不佳，请注意飞行安全",
	"fpv_tip_0x16010038":             "定位质量不佳，请注意飞行安全",
	"fpv_tip_0x16010039":             "无卫星定位，请等待搜星，注意飞行安全",
	"fpv_tip_0x16010041":             "传感器系统异常，请重启无人机",
	"fpv_tip_0x16010041_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16010042":             "无人机定位系统严重故障，即将强制降落，请尽快选择空旷地区降落",
	"fpv_tip_0x16010050":             "当前仅使用雷达定位，请勿在狭窄走廊、隧道等环境飞行",
	"fpv_tip_0x16020001":             "IMU异常，请重启无人机",
	"fpv_tip_0x16020001_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16020004":             "IMU异常，请重启无人机",
	"fpv_tip_0x16020004_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16020007":             "IMU异常，请重启无人机",
	"fpv_tip_0x16020007_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x1602000A":             "IMU异常，请重启无人机",
	"fpv_tip_0x1602000A_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x1602000D":             "IMU异常，请重启无人机",
	"fpv_tip_0x1602000D_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16020016":             "IMU偏差过大，请校准 IMU",
	"fpv_tip_0x16020016_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16020027":             "IMU异常，请重启无人机",
	"fpv_tip_0x16020027_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x1602002A":             "IMU异常，请重启无人机",
	"fpv_tip_0x1602002A_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x1602002B":             "IMU恒温系统异常，请重启无人机",
	"fpv_tip_0x16020620":             "返航中断：检测到反向杆量",
	"fpv_tip_0x16020621":             "返航中断：检测到障碍物",
	"fpv_tip_0x16020622":             "返航中断：风速过大或有限飞区",
	"fpv_tip_0x16020623":             "返航中断：卫星定位异常",
	"fpv_tip_0x16030001":             "IMU异常，请重启无人机",
	"fpv_tip_0x16030001_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16030004":             "IMU异常，请重启无人机",
	"fpv_tip_0x16030004_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16030007":             "IMU异常，请重启无人机",
	"fpv_tip_0x16030007_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x1603000A":             "IMU异常，请重启无人机",
	"fpv_tip_0x1603000A_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x1603000D":             "IMU异常，请重启无人机",
	"fpv_tip_0x1603000D_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16030010":             "IMU异常，请重启无人机",
	"fpv_tip_0x16030010_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16030013":             "IMU异常，请重启无人机",
	"fpv_tip_0x16030013_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x16030016":             "IMU偏差过大，请校准 IMU",
	"fpv_tip_0x16030016_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x1603001C":             "IMU异常，请校准 IMU",
	"fpv_tip_0x1603001C_sky":         "IMU异常,请返航或降落",
	"fpv_tip_0x1603001D":             "无人机异常振动，请尽快返航或降落",
	"fpv_tip_0x16040001":             "气压计异常，请重启无人机",
	"fpv_tip_0x16040001_sky":         "气压计异常,请返航或降落",
	"fpv_tip_0x16040004":             "气压计异常，请重启无人机",
	"fpv_tip_0x16040004_sky":         "气压计异常,请返航或降落",
	"fpv_tip_0x16040007":             "气压计异常，请重启无人机",
	"fpv_tip_0x16040007_sky":         "气压计异常,请返航或降落",
	"fpv_tip_0x1604000A":             "气压计异常，请重启无人机",
	"fpv_tip_0x1604000A_sky":         "气压计异常,请返航或降落",
	"fpv_tip_0x16040010":             "气压计异常，请重启无人机",
	"fpv_tip_0x16040010_sky":         "气压计异常,请返航或降落",
	"fpv_tip_0x16040013":             "气压计异常，请重启无人机",
	"fpv_tip_0x16040013_sky":         "气压计异常,请返航或降落",
	"fpv_tip_0x16040016":             "气压计异常，请重启无人机",
	"fpv_tip_0x16040016_sky":         "气压计异常,请返航或降落",
	"fpv_tip_0x16040019":             "气压计异常，请重启无人机",
	"fpv_tip_0x16040019_sky":         "气压计异常,请返航或降落",
	"fpv_tip_0x16050001":             "卫星定位异常，请重启无人机",
	"fpv_tip_0x16050001_sky":         "卫星定位异常,请返航或降落",
	"fpv_tip_0x16050004":             "卫星定位异常，请重启无人机",
	"fpv_tip_0x16050004_sky":         "卫星定位异常,请返航或降落",
	"fpv_tip_0x16050019":             "卫星定位异常，请重启无人机",
	"fpv_tip_0x16050019_sky":         "卫星定位异常,请返航或降落",
	"fpv_tip_0x1605001C":             "卫星定位异常，请重启无人机",
	"fpv_tip_0x1605001C_sky":         "卫星定位异常,请返航或降落",
	"fpv_tip_0x1605001F":             "无人机卫星定位信号受到干扰，谨慎飞行",
	"fpv_tip_0x16050020":             "卫星定位信号干扰强，无人机自动返航",
	"fpv_tip_0x16050021":             "RTK 启用未收敛，请将飞机置于空旷环境等待 RTK 收敛后执行任务",
	"fpv_tip_0x16050022":             "当前飞行环境卫星定位误差大，存在安全风险，请主动切换至姿态挡飞行",
	"fpv_tip_0x1605002B":             "进入单北斗模式，搜星质量可能变差",
	"fpv_tip_0x16060001":             "指南针异常，请重启无人机",
	"fpv_tip_0x16060001_sky":         "指南针异常,请返航或降落",
	"fpv_tip_0x16060007":             "指南针异常，请重启无人机",
	"fpv_tip_0x16060007_sky":         "指南针异常,请返航或降落",
	"fpv_tip_0x1606000A":             "指南针异常，请重启无人机",
	"fpv_tip_0x1606000A_sky":         "指南针异常,请返航或降落",
	"fpv_tip_0x1606000D":             "指南针受到干扰，请校准指南针",
	"fpv_tip_0x1606000D_sky":         "指南针受到干扰,请远离干扰源",
	"fpv_tip_0x16060010":             "指南针受到干扰，请校准指南针",
	"fpv_tip_0x16060010_sky":         "指南针受到干扰,请远离干扰源",
	"fpv_tip_0x16070001":             "飞控温度过高，返航中",
	"fpv_tip_0x16070002":             "飞控温度过高，返航中",
	"fpv_tip_0x16070003":             "飞控温度过高，强制降落中",
	"fpv_tip_0x16070020":             "无人机未接收到RTK数据",
	"fpv_tip_0x16070021":             "RTK 数据异常，请重启无人机",
	"fpv_tip_0x16070022":             "RTK双天线安装错误",
	"fpv_tip_0x16070023":             "RTK 数据更新频率异常",
	"fpv_tip_0x16070024":             "RTK 数据异常",
	"fpv_tip_0x16070025":             "RTK 定位数据停止更新",
	"fpv_tip_0x16070026":             "RTK 定位数据跳变",
	"fpv_tip_0x16070027":             "RTK 高度与传感器高度数据不一致",
	"fpv_tip_0x16070028":             "RTK 定位与卫星定位不一致",
	"fpv_tip_0x16070029":             "无人机静止时 RTK 定位缓变",
	"fpv_tip_0x16070030":             "RTK 固件版本不匹配",
	"fpv_tip_0x16070031":             "RTK信号源切换异常",
	"fpv_tip_0x16070032":             "D-RTK 基准站倾倒",
	"fpv_tip_0x16070033":             "D-RTK 基准站发生移动",
	"fpv_tip_0x16070034":             "RTK 航向与其他源不一致",
	"fpv_tip_0x16070035":             "机载 D-RTK 天线异常",
	"fpv_tip_0x16070036":             "无法起飞：RTK天线断连，请联系DJI售后",
	"fpv_tip_0x16070036_sky":         "RTK天线断连，请尽快返航或降落",
	"fpv_tip_0x16070037":             "RTK认证错误",
	"fpv_tip_0x16070038":             "RTK主天线信号异常",
	"fpv_tip_0x16070039":             "RTK副天线信号异常",
	"fpv_tip_0x16070040":             "RTK 启用未收敛，请将飞机置于空旷环境等待 RTK 收敛后执行任务",
	"fpv_tip_0x16070042":             "RTK基站数据异常，请检查网络RTK或D-RTK是否断联",
	"fpv_tip_0x16070043":             "卫星定位受异常信号干扰，请谨慎飞行或返航",
	"fpv_tip_0x16070043_sky":         "卫星定位受异常信号干扰，请谨慎起飞",
	"fpv_tip_0x16070044":             "卫星定位受到欺骗，请立即返航或降落",
	"fpv_tip_0x16070044_sky":         "卫星定位受到欺骗，请谨慎起飞",
	"fpv_tip_0x16070046":             "检测到电离层闪烁，RTK定位性能受影响，请谨慎飞行",
	"fpv_tip_0x16070047":             "无人机已连接中继，当前RTK服务类型不可用，请切换RTK服务类型至D-RTK3中继",
	"fpv_tip_0x16080020":             "%index号电机堵转，请停止飞行。",
	"fpv_tip_0x16080021":             "%index号电调短路，请停止飞行，并重启无人机。",
	"fpv_tip_0x16080022":             "%index号电机过载，请停止飞行。",
	"fpv_tip_0x16080023":             "%index号电机通信异常，请停止飞行，并重启无人机。",
	"fpv_tip_0x16080024":             "%index号电机油门过高，请回中摇杆。",
	"fpv_tip_0x16080025":             "%index号电机通信异常。",
	"fpv_tip_0x16080026":             "%index号电机通信异常",
	"fpv_tip_0x16080027":             "%index号电机通信异常",
	"fpv_tip_0x16080028":             "%index号电机通信异常",
	"fpv_tip_0x16080029":             "%index号电机射桨、空载或轻载",
	"fpv_tip_0x1608002A":             "%index号电调温度过高",
	"fpv_tip_0x1608002B":             "%index号电调温度过高",
	"fpv_tip_0x1608002C":             "%index号电调电压过高",
	"fpv_tip_0x1608002D":             "%index号电调电压过低",
	"fpv_tip_0x1608002E":             "%index号电调闪存异常",
	"fpv_tip_0x1608002F":             "%index号电调自检异常",
	"fpv_tip_0x16080030":             "%index号电调自检异常",
	"fpv_tip_0x16080031":             "%index号电调自检异常",
	"fpv_tip_0x16080032":             "%index号电调自检异常",
	"fpv_tip_0x16080033":             "%index号电调自检异常",
	"fpv_tip_0x16080034":             "%index号电调自检异常",
	"fpv_tip_0x16080035":             "%index号电调自检异常",
	"fpv_tip_0x16080036":             "%index号电调自检异常",
	"fpv_tip_0x16080037":             "%index号电调自检异常",
	"fpv_tip_0x16080038":             "%index号电调自检异常",
	"fpv_tip_0x16080039":             "%index号电调自检异常",
	"fpv_tip_0x1608003a":             "%index号电调自检异常",
	"fpv_tip_0x1608003b":             "%index号电调自检异常",
	"fpv_tip_0x1608003c":             "%index号电调自检异常",
	"fpv_tip_0x1608003d":             "%index号电调自检异常",
	"fpv_tip_0x1608003e":             "%index号电调自检异常",
	"fpv_tip_0x1608003f":             "%index号电调自检异常",
	"fpv_tip_0x16080040":             "%index号电调自检异常",
	"fpv_tip_0x16080041":             "%index号电调自检异常",
	"fpv_tip_0x16080042":             "%index号电调自检异常",
	"fpv_tip_0x16080043":             "%index号电调自检异常",
	"fpv_tip_0x16080044":             "%index号电调电压过低",
	"fpv_tip_0x16080045":             "%index号电调电压过高",
	"fpv_tip_0x16080046":             "%index号电调自检异常",
	"fpv_tip_0x16080047":             "%index号电调自检异常",
	"fpv_tip_0x16080048":             "%index号电调自检异常",
	"fpv_tip_0x16080049":             "%index号电调自检异常",
	"fpv_tip_0x1608004a":             "%index号电调温度过高",
	"fpv_tip_0x1608004b":             "%index号电调温度过低",
	"fpv_tip_0x1608004c":             "%index号电调自检异常",
	"fpv_tip_0x1608004d":             "%index号电调自检异常",
	"fpv_tip_0x1608004e":             "%index号电调自检异常",
	"fpv_tip_0x1608004f":             "%index号电调自检异常",
	"fpv_tip_0x16080050":             "无法起飞：电调异常，请尝试重启无人机",
	"fpv_tip_0x16080050_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x16080051":             "电调异常，请谨慎飞行",
	"fpv_tip_0x16080052":             "无法起飞：动力系统异常，桨叶未展开",
	"fpv_tip_0x16080052_sky":         "动力系统异常，桨叶未展开",
	"fpv_tip_0x16080053":             "电调异常，请谨慎飞行",
	"fpv_tip_0x16080054":             "无法起飞：电调异常，请尝试重启无人机",
	"fpv_tip_0x16080054_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x16080055":             "%index号电机温度较高，请尽快降落",
	"fpv_tip_0x16080056":             "电机运行温度较高，已限制飞行速度",
	"fpv_tip_0x16080056_sky":         "电机运行温度较高，已限制飞行速度",
	"fpv_tip_0x16080057":             "电调异常，请尽快返航或降落",
	"fpv_tip_0x16080057_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x16080058":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x16080058_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x16080059":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x16080059_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x1608005A":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005A_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x1608005B":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005B_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x1608005C":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005C_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x1608005D":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005D_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x1608005E":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005F":             "电机超温，强制降落",
	"fpv_tip_0x1608005a":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005b":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005c":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005d":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005e":             "无法起飞：电调异常，请联系售后",
	"fpv_tip_0x1608005e_sky":         "电调异常，请尽快返航或降落",
	"fpv_tip_0x16080077":             "%index号电调严重超温(%alarmid)",
	"fpv_tip_0x16080078":             "%index号电调严重超温(%alarmid)",
	"fpv_tip_0x16080079":             "%index号电调固件与飞机机型不匹配(%alarmid)",
	"fpv_tip_0x1608007A":             "%index号电调固件与飞机机型不匹配(%alarmid)",
	"fpv_tip_0x1608007B":             "%index号电调自检异常(%alarmid)",
	"fpv_tip_0x1608007C":             "%index号电调自检异常(%alarmid)",
	"fpv_tip_0x1608007D":             "%index号电调或电机自检异常(%alarmid)",
	"fpv_tip_0x1608007E":             "%index号电调或电机自检异常(%alarmid)",
	"fpv_tip_0x1608007F":             "%index号电调或电机自检异常(%alarmid)",
	"fpv_tip_0x16080080":             "%index号电调或电机自检异常(%alarmid)",
	"fpv_tip_0x16080083":             "%d号电机启动失败，请检查桨叶是否展开以及桨叶周围是否有障碍物后打杆重试",
	"fpv_tip_0x16090050":             "%battery_index电池供电异常，请尽快返航或降落",
	"fpv_tip_0x16090051":             "%battery_index电池供电异常，请尽快返航或降落",
	"fpv_tip_0x16090052":             "%battery_index电池温度传感器异常，请尽快返航或降落",
	"fpv_tip_0x16090061":             "%battery_index电池温度过高，请尽快返航或降落，停止使用该电池",
	"fpv_tip_0x16090062":             "%battery_index电池MOS温度过高，请尽快返航或降落，等待%battery_index电池温度降低",
	"fpv_tip_0x16090063":             "%battery_index电池总电压过高，请尽快返航或降落",
	"fpv_tip_0x16090064":             "%battery_index电池总电压过低，请尽快返航或降落",
	"fpv_tip_0x16090065":             "%battery_index电池单电芯电压过高，请尽快返航或降落",
	"fpv_tip_0x16090066":             "%battery_index电池单电芯电压过低，请尽快返航或降落",
	"fpv_tip_0x16090067":             "%battery_index电池静态压差过大，请停止使用该电池",
	"fpv_tip_0x16090068":             "%battery_index电池充电电芯间压差过大，请尽快返航或降落，并停止使用该电池",
	"fpv_tip_0x16090069":             "%battery_index电池放电电芯间压差过大，请尽快返航或降落，并停止使用该电池",
	"fpv_tip_0x1609006A":             "%battery_index电池MOS连接异常，请尽快返航或降落",
	"fpv_tip_0x1609006B":             "%battery_index电池MOS阻抗异常，请尽快返航或降落",
	"fpv_tip_0x1609006C":             "%battery_index电池老化，可能影响飞行安全，请尽快返航或降落",
	"fpv_tip_0x1609006D":             "%battery_index电池微短路，可能影响飞行安全，请尽快返航或降落",
	"fpv_tip_0x1609006F":             "%battery_index电池连接器温度过高，请尽快返航或降落",
	"fpv_tip_0x16090070":             "%battery_index电池电压过低，请尽快返航或降落",
	"fpv_tip_0x16090071":             "电池电压过压保护",
	"fpv_tip_0x16090072":             "%battery_index电池充电过流，请检查充电设备",
	"fpv_tip_0x16090073":             "%battery_index电池放电过流，请检查无人机是否有负重",
	"fpv_tip_0x16090074":             "%battery_index电池放电过载，请检查无人机是否有负重",
	"fpv_tip_0x16090075":             "%battery_index电池系统异常，请重启%battery_index电池后重试",
	"fpv_tip_0x16090076":             "%battery_index电池内部模块异常，请尽快返航或降落",
	"fpv_tip_0x16090077":             "%battery_index电池充电温度过高，禁止充电，等待%battery_index电池温度降低",
	"fpv_tip_0x16090078":             "%battery_index电池放电温度过高，请尽快返航或降落，等待%battery_index电池温度降低",
	"fpv_tip_0x16090079":             "%battery_index电池充电温度过低，禁止充电，请预热%battery_index电池后充电",
	"fpv_tip_0x1609007A":             "%battery_index电池放电温度过低，请尽快返航或降落，请预热%battery_index电池后再起飞",
	"fpv_tip_0x1609007B":             "%battery_index电池电压过高，请拔插%battery_index电池后再起飞",
	"fpv_tip_0x1609007C":             "%battery_index电池电压过低，请拔插%battery_index电池后再起飞",
	"fpv_tip_0x1609007D":             "充电器电压过高，请检查充电设备",
	"fpv_tip_0x1609007E":             "充电器电压过低，请检查充电设备",
	"fpv_tip_0x1609007F":             "%battery_index电池温度过高，无法起飞，等待%battery_index电池温度降低",
	"fpv_tip_0x16090080":             "%battery_index电池温度过高，请尽快返航或降落，等待%battery_index电池温度降低",
	"fpv_tip_0x16090081":             "%battery_index电池温度过高，请尽快返航或降落，等待%battery_index电池温度降低",
	"fpv_tip_0x16090082":             "%battery_index电池内部通讯异常，请尽快返航或降落",
	"fpv_tip_0x16090083":             "%battery_index电池内部通讯异常，请尽快返航或降落",
	"fpv_tip_0x16090084":             "%battery_index电池内部通讯异常，请尽快返航或降落",
	"fpv_tip_0x16090085":             "%battery_index电池对外通信异常，请尽快返航或降落",
	"fpv_tip_0x16090086":             "%battery_index电池连接器温度过高，影响飞行安全",
	"fpv_tip_0x16090086_sky":         "%battery_index电池连接器温度过高，影响飞行安全，请尽快返航或降落",
	"fpv_tip_0x16090092":             "Vpack电压采样异常",
	"fpv_tip_0x16090093":             "%battery_index电池电压不稳定，请尽快返航或降落后重启电池",
	"fpv_tip_0x16090094":             "%battery_index电池电压采集异常，请尽快返航或降落",
	"fpv_tip_0x16090095":             "%battery_index电池电压过高，请尽快返航或降落后重启电池",
	"fpv_tip_0x16090096":             "%battery_index电池需要保养，请谨慎飞行",
	"fpv_tip_0x16090097":             "%battery_index电池需要保养，请谨慎飞行",
	"fpv_tip_0x16090098":             "放电压差大告警",
	"fpv_tip_0x16090099":             "%battery_index电池电流过大，请谨慎飞行",
	"fpv_tip_0x1609009B":             "%battery_index电池温度高，请尽快返航或降落，等待%battery_index电池温度降低",
	"fpv_tip_0x1609009C":             "%battery_index电池温度高，请尽快返航或降落，等待%battery_index电池温度降低",
	"fpv_tip_0x1609009D":             "%battery_index电池连接器温度过高",
	"fpv_tip_0x1609009D_sky":         "%battery_index电池连接器温度过高，影响飞行安全，请尽快返航或降落",
	"fpv_tip_0x1609009E":             "%battery_index电池电压高，请尽快返航或降落",
	"fpv_tip_0x1609009F":             "%battery_index电池电压低，请尽快返航或降落",
	"fpv_tip_0x160900A2":             "%index电池连接器温升异常，影响飞行安全，请谨慎飞行",
	"fpv_tip_0x160900A3":             "%index电池超过使用功率限制范围，影响飞行安全，请尽快返航或降落",
	"fpv_tip_0x160900A6":             "电池槽%battery_index电池接触不良，请尽快返航或降落",
	"fpv_tip_0x160900B0":             "%battery_index电池电量不准，请尽快返航或降落",
	"fpv_tip_0x160900B1":             "%battery_index电池电量变化过大，请尽快返航或降落",
	"fpv_tip_0x160900B2":             "SOC异常跳变",
	"fpv_tip_0x160900B3":             "%battery_index电池容量异常，请谨慎飞行",
	"fpv_tip_0x160900B4":             "%battery_index电池容量异常，请谨慎飞行",
	"fpv_tip_0x160900B5":             "%battery_index电池容量异常，请谨慎飞行",
	"fpv_tip_0x160900B6":             "%battery_index电池容量异常，请谨慎飞行",
	"fpv_tip_0x160900B7":             "%battery_index电池容量异常，请谨慎飞行",
	"fpv_tip_0x160900B8":             "%battery_index电池容量异常，请谨慎飞行",
	"fpv_tip_0x160900B9":             "%battery_index电池容量长期未更新，请谨慎飞行并进行容量校准",
	"fpv_tip_0x160900BA":             "%battery_index电池容量长期未更新，请谨慎飞行并进行容量校准",
	"fpv_tip_0x160900BB":             "%battery_index电池性能下降，请谨慎飞行",
	"fpv_tip_0x160900BC":             "电池内阻异常偏小",
	"fpv_tip_0x160900BD":             "%battery_index电池老化，可能影响飞行安全，请尽快返航或降落",
	"fpv_tip_0x160900BE":             "%battery_index电池老化，可能影响飞行安全，请尽快返航或降落",
	"fpv_tip_0x160900BF":             "%battery_index电池循环次数过高，建议更换该%battery_index电池",
	"fpv_tip_0x160900C0":             "%battery_index电池已超功率使用，无人机性能受限，请谨慎飞行",
	"fpv_tip_0x160900C1":             "电池均衡故障",
	"fpv_tip_0x160900C2":             "显示SOC出现跳变",
	"fpv_tip_0x160900C3":             "电池循环次数过高，继续使用有安全风险，建议更换该电池",
	"fpv_tip_0x160900C4":             "%battery_index电池电量需要校准，请静置2h尝试恢复",
	"fpv_tip_0x160900C8":             "D-RTK3电池电量低于20%，请及时充电",
	"fpv_tip_0x160900C9":             "D-RTK3电池电量低于10%，请及时充电",
	"fpv_tip_0x160900CA":             "D-RTK3电池充电过流，请断开充电设备后重新连接",
	"fpv_tip_0x160900CB":             "D-RTK3电池放电过流，请尝试重启",
	"fpv_tip_0x160900CD":             "D-RTK3电池温度大于70℃，请待电池冷却到常温后再使用",
	"fpv_tip_0x160900CE":             "D-RTK3电池温度大于75℃，请待电池冷却到常温后再使用",
	"fpv_tip_0x160900D0":             "D-RTK3电池温度过低，即将关机",
	"fpv_tip_0x160900D2":             "%battery_index电池电量长期未校准，请静置1h以上等待校准",
	"fpv_tip_0x160900D5":             "无法起飞：电池已达最大寿命，请更换电池",
	"fpv_tip_0x160900D5_sky":         "无法起飞：电池已达最大寿命，请更换电池",
	"fpv_tip_0x160900D6":             "电池健康度低，寿命预计在20循环或20天后耗尽，请及时更换电池",
	"fpv_tip_0x160900D7":             "识别到非标准的POE插入，请注意使用15W以上的POE接口",
	"fpv_tip_0x160900D8":             "当前插入的适配器功率过低，不支持D-RTK3在低于-20度环境下工作",
	"fpv_tip_0x160900DA":             "电池电压过低或系留供电异常，禁止起飞",
	"fpv_tip_0x160900DB":             "电池电压过低或系留供电异常，请尽快返航。",
	"fpv_tip_0x160900DC":             "系留电池充电中，禁止起飞。",
	"fpv_tip_0x160A0001":             "降落伞通信异常，请检查是否正确安装",
	"fpv_tip_0x160A0011":             "飞机实名登记状态已注销，存在飞行风险，请点击查看帮助文档，按照指引完成实名登记后飞行",
	"fpv_tip_0x160B0000":             "D-RTK3多功能基站断开连接",
	"fpv_tip_0x160B0001":             "D-RTK3多功能基站位置移动",
	"fpv_tip_0x160B0002":             "D-RTK3多功能基站RTK搜星异常，请检查信号是否有遮挡，并在搜星信号良好位置使用设备",
	"fpv_tip_0x160B0003":             "D-RTK3多功能基站初始化定位位置距离上次标定位置过远",
	"fpv_tip_0x160B0004":             "D-RTK3多功能基站初始化定位失败，请移动至搜星信号良好位置重新部署",
	"fpv_tip_0x16100001":             "无法起飞：指南针异常，请重启无人机",
	"fpv_tip_0x16100001_sky":         "指南针异常,请返航或降落",
	"fpv_tip_0x16100002":             "无法起飞：已连接DJI Assistant 软件，请先断开再起飞",
	"fpv_tip_0x16100002_sky":         "已连接DJI Assistant 软件，请先断开再飞行",
	"fpv_tip_0x16100003":             "无法起飞：请检查是否激活或尝试升级最新固件",
	"fpv_tip_0x16100003_sky":         "请检查是否激活或尝试升级最新固件",
	"fpv_tip_0x16100005":             "无法起飞：IMU异常，请重启无人机",
	"fpv_tip_0x16100005_sky":         "IMU异常，请重启无人机",
	"fpv_tip_0x16100006":             "无法起飞：IMU的SN标记错误，请返厂维修",
	"fpv_tip_0x16100006_sky":         "IMU的SN标记错误，请返航或降落",
	"fpv_tip_0x16100008":             "无法起飞：指南针校准中，请等待校准完成再起飞",
	"fpv_tip_0x16100008_sky":         "指南针校准中，请等待校准完成再起飞",
	"fpv_tip_0x16100009":             "无法起飞：传感器系统初始化中，请等待初始化完成",
	"fpv_tip_0x16100009_sky":         "传感器系统初始化中，请返航或降落",
	"fpv_tip_0x1610000A":             "无法起飞：新手模式下请在室外开阔环境飞行",
	"fpv_tip_0x1610000A_sky":         "新手模式下请在室外开阔环境飞行",
	"fpv_tip_0x1610000B":             "无法起飞：电池电芯错误，请联系售后服务",
	"fpv_tip_0x1610000B_sky":         "电池电芯错误，请返航或降落",
	"fpv_tip_0x1610000C":             "无法起飞：电池通信错误，请重新安装电池",
	"fpv_tip_0x1610000C_sky":         "电池通信错误，请返航",
	"fpv_tip_0x1610000D":             "无法起飞：严重低电压警报，请及时充电",
	"fpv_tip_0x1610000D_sky":         "严重低电压，请尽快返航或降落",
	"fpv_tip_0x1610000E":             "无法起飞：严重低电量警报，请及时充电",
	"fpv_tip_0x1610000E_sky":         "严重低电量，请尽快返航或降落",
	"fpv_tip_0x1610000F":             "无法起飞：严重低电压警报，请及时充电",
	"fpv_tip_0x1610000F_sky":         "严重低电压，请尽快返航或降落",
	"fpv_tip_0x16100010":             "无法起飞：电池输出功率不足，请充电。若环境温度低，预热电池",
	"fpv_tip_0x16100010_sky":         "电池输出功率不足，请尽快返航或降落",
	"fpv_tip_0x16100011":             "无法起飞：严重低电量警报，请及时充电",
	"fpv_tip_0x16100011_sky":         "严重低电量，请尽快返航或降落",
	"fpv_tip_0x16100012":             "无法起飞：电池初始化中，请等待初始化完成",
	"fpv_tip_0x16100012_sky":         "电池异常初始化，请返航或降落",
	"fpv_tip_0x16100013":             "无法起飞：正在运行模拟器，起飞需要重启无人机",
	"fpv_tip_0x16100013_sky":         "正在运行模拟器，起飞需要重启无人机",
	"fpv_tip_0x16100014":             "无法起飞：起落架处于运输模式",
	"fpv_tip_0x16100015":             "无法起飞：无人机倾斜角度过大，请水平放置无人机后起飞",
	"fpv_tip_0x16100015_sky":         "无人机倾斜角度过大，请水平放置无人机后起飞",
	"fpv_tip_0x16100016":             "无法起飞：无人机未激活，请重启App进行激活",
	"fpv_tip_0x16100016_sky":         "无人机未激活，请重启App进行激活",
	"fpv_tip_0x16100017":             "无法起飞：无人机在限飞区，请查看地图寻找可飞行区域",
	"fpv_tip_0x16100017_sky":         "无人机在限飞区，请查看地图寻找可飞行区域",
	"fpv_tip_0x16100018":             "无法起飞：IMU 异常，请校准 IMU",
	"fpv_tip_0x16100018_sky":         "IMU 初始化异常，请返航或降落",
	"fpv_tip_0x16100019":             "无法起飞：电调异常，请联系售后服务",
	"fpv_tip_0x16100019_sky":         "电调异常，请立即降落",
	"fpv_tip_0x1610001A":             "无法起飞：传感器系统初始化中，请等待初始化完成",
	"fpv_tip_0x1610001A_sky":         "传感器系统初始化中，请返航或降落",
	"fpv_tip_0x1610001B":             "无法起飞：系统正在升级，请稍等",
	"fpv_tip_0x1610001B_sky":         "系统正在升级，请等待升级结束再起飞",
	"fpv_tip_0x1610001C":             "无法起飞：已运行模拟器，起飞需要重启无人机",
	"fpv_tip_0x1610001C_sky":         "已运行模拟器，起飞需要重启无人机",
	"fpv_tip_0x1610001D":             "无法起飞：IMU 校准中，请等待校准完毕再起飞",
	"fpv_tip_0x1610001D_sky":         "IMU 校准中，请等待校准完毕再起飞",
	"fpv_tip_0x1610001E":             "无法起飞：无人机倾斜角度过大，请水平放置无人机后起飞",
	"fpv_tip_0x1610001E_sky":         "无人机倾斜角度过大，请水平放置无人机后起飞",
	"fpv_tip_0x1610001F":             "无法起飞：飞控温度过高",
	"fpv_tip_0x1610001F_sky":         "飞控温度过高，返航中",
	"fpv_tip_0x16100020":             "无法起飞：飞控温度过高",
	"fpv_tip_0x16100020_sky":         "飞控温度过高，返航中",
	"fpv_tip_0x16100021":             "无法起飞：飞控温度过高",
	"fpv_tip_0x16100021_sky":         "飞控温度过高，强制降落中",
	"fpv_tip_0x16100022":             "无法起飞：飞机已多次失稳，为保证飞行安全，请联系大疆售后服务",
	"fpv_tip_0x16100023":             "AirSense功能异常，无法接收附近载人飞机信息",
	"fpv_tip_0x16100024":             "无法起飞：指南针受到干扰，请远离干扰或校准指南针",
	"fpv_tip_0x16100025":             "无法起飞：RTK模块不支持热插拔",
	"fpv_tip_0x16100025_sky":         "RTK异常，请尽快降落",
	"fpv_tip_0x16100026":             "无法起飞：检测到非官方RTK模块",
	"fpv_tip_0x16100026_sky":         "检测到非官方RTK模块",
	"fpv_tip_0x16100027":             "无法起飞：飞控系统异常",
	"fpv_tip_0x16100027_sky":         "飞控系统异常，请尽快降落",
	"fpv_tip_0x16100028":             "无法起飞：无人机型号与RTK模块不匹配",
	"fpv_tip_0x16100029":             "无法起飞：无人机SN异常，请联系就近代理商或者大疆售后服务",
	"fpv_tip_0x16100029_sky":         "无人机SN异常，请返航或降落",
	"fpv_tip_0x1610002D":             "无法起飞：卫星定位断开连接，请重启无人机",
	"fpv_tip_0x1610002D_sky":         "卫星定位断开连接,请返航或降落",
	"fpv_tip_0x1610002F":             "无法起飞：数据记录仪异常，请重启无人机",
	"fpv_tip_0x1610002F_sky":         "数据记录仪异常，请返航或降落",
	"fpv_tip_0x16100030":             "无法起飞：无人机机型与固件不匹配，请联系就近代理商或大疆售后服务",
	"fpv_tip_0x16100030_sky":         "无人机机型与固件不匹配，请返航或降落",
	"fpv_tip_0x16100031":             "无法起飞：空吊系统挂钩没有收到位，请将挂钩收到位",
	"fpv_tip_0x16100032":             "无法起飞：空吊系统线绳反绕，请整理线绳后起飞",
	"fpv_tip_0x16100032_sky":         "空吊系统线绳反绕，请勿操作空吊并尽快降落理线",
	"fpv_tip_0x16100033":             "飞机未完成实名登记，请点击查看帮助文档，按照指引完成实名登记后飞行",
	"fpv_tip_0x16100034":             "无人机实名登记状态已注销，请点击查看帮助文档，按照指引完成实名登记后飞行",
	"fpv_tip_0x16100038":             "无法起飞，电调电压异常请联系售后服务",
	"fpv_tip_0x16100038_sky":         "电调电压异常，请返航并联系售后服务",
	"fpv_tip_0x16100039":             "地理鸟笼系统准备中",
	"fpv_tip_0x16100039_sky":         "无法起飞：地理鸟笼系统准备中",
	"fpv_tip_0x1610003A":             "无法起飞：RTK 模块过热，无人机即将自动关机",
	"fpv_tip_0x1610003B":             "无法起飞：已安装喊话器，请重新校准指南针",
	"fpv_tip_0x1610003D":             "无法起飞：传感器系统断开，请重启无人机",
	"fpv_tip_0x1610003D_sky":         "传感器系统断开，请返航或降落",
	"fpv_tip_0x1610003E":             "无法起飞：遥控器校准中，请完成校准流程",
	"fpv_tip_0x16100041":             "无法起飞：摇杆不在中位，请校准遥控器",
	"fpv_tip_0x16100042":             "无法起飞：摇杆硬件异常，请联系售后",
	"fpv_tip_0x16100042_sky":         "摇杆硬件异常，请尽快降落，并联系售后",
	"fpv_tip_0x1610004A":             "无法起飞：传感器系统异常，请重启无人机",
	"fpv_tip_0x1610004A_sky":         "传感器系统异常，请返航或降落",
	"fpv_tip_0x1610004B":             "无法起飞：传感器系统异常，请重启无人机",
	"fpv_tip_0x1610004B_sky":         "传感器系统异常，请返航或降落",
	"fpv_tip_0x1610004C":             "无法起飞：遥控器摇杆需要校准",
	"fpv_tip_0x1610004D":             "无法起飞：飞控数据异常，请重启无人机",
	"fpv_tip_0x1610004D_sky":         "飞控数据异常，请返航或降落",
	"fpv_tip_0x1610004E":             "无法起飞：电池数量过少，请插入两块电池再起飞",
	"fpv_tip_0x1610004E_sky":         "电池数量过少，请尽快返航或降落",
	"fpv_tip_0x1610004F":             "无法起飞：电池认证失败，请更换DJI官方电池",
	"fpv_tip_0x1610004F_sky":         "电池认证失败，请更换DJI官方电池",
	"fpv_tip_0x16100051":             "无法起飞：两块电池间压差过大，更换电量相近的电池组继续飞行",
	"fpv_tip_0x16100051_sky":         "两块电池间压差过大，请返航或降落，更换电量相近的电池组继续飞行",
	"fpv_tip_0x16100053":             "无法起飞：无人机模块间固件不匹配，请升级固件版本",
	"fpv_tip_0x16100053_sky":         "无人机模块间固件不匹配，请升级固件版本",
	"fpv_tip_0x16100054":             "无法起飞：云台异常，请联系大疆售后服务",
	"fpv_tip_0x16100054_sky":         "云台异常，返航或降落",
	"fpv_tip_0x16100055":             "无法起飞：云台异常，请联系大疆售后服务",
	"fpv_tip_0x16100055_sky":         "云台异常，返航或降落",
	"fpv_tip_0x16100056":             "无法起飞：云台异常，请联系大疆售后服务",
	"fpv_tip_0x16100056_sky":         "云台异常，返航或降落",
	"fpv_tip_0x16100057":             "无法起飞：云台异常，请联系大疆售后服务",
	"fpv_tip_0x16100057_sky":         "云台异常，返航或降落",
	"fpv_tip_0x16100058":             "无法起飞：云台升级中，请稍后",
	"fpv_tip_0x16100058_sky":         "云台升级中，请稍后",
	"fpv_tip_0x1610005D":             "无法起飞：IMU 校准成功，需重启无人机",
	"fpv_tip_0x1610005D_sky":         "IMU 校准异常，请返航或降落",
	"fpv_tip_0x1610005E":             "无法起飞：起飞时侧翻，请检查桨叶安装是否正确",
	"fpv_tip_0x1610005E_sky":         "起飞时侧翻，请检查桨叶安装是否正确",
	"fpv_tip_0x1610005F":             "无法起飞：电机堵转，关机后检查电机能否自由旋转",
	"fpv_tip_0x1610005F_sky":         "电机堵转，请降落，在关机后检查电机能否自由旋转",
	"fpv_tip_0x16100060":             "无法起飞：电机转速异常，请重启无人机",
	"fpv_tip_0x16100060_sky":         "电机转速异常，请停桨后重启无人机",
	"fpv_tip_0x16100061":             "无法起飞：电机空转，无法起飞，请检查桨叶是否脱落或未正确安装桨叶",
	"fpv_tip_0x16100061_sky":         "电机空转，无法起飞，请检查桨叶是否脱落或未正确安装桨叶",
	"fpv_tip_0x16100062":             "无法起飞：电机无法启动，请检查无人机状态并重启无人机",
	"fpv_tip_0x16100062_sky":         "电机无法启动，请检查无人机状态并重启无人机",
	"fpv_tip_0x16100063":             "无法起飞：自动起飞失败",
	"fpv_tip_0x16100063_sky":         "自动起飞失败",
	"fpv_tip_0x16100064":             "无法起飞：无人机姿态翻滚，请重启无人机水平放置后起飞",
	"fpv_tip_0x16100064_sky":         "无人机姿态翻滚， 请返航或降落",
	"fpv_tip_0x16100065":             "无法起飞：电池固件版本不一致，请更换电池或将电池升级到最新版本",
	"fpv_tip_0x16100065_sky":         "电池固件版本不一致，请更换电池或将电池升级到最新版本",
	"fpv_tip_0x16100066":             "无法起飞：RTK 信号差，请至空旷地起飞，或关闭RTK功能",
	"fpv_tip_0x16100066_sky":         "RTK 信号差，请至空旷地起飞，或关闭RTK功能",
	"fpv_tip_0x16100067":             "无法起飞：指南针受到干扰，请校准指南针",
	"fpv_tip_0x16100067_sky":         "指南针受到干扰，请远离干扰源飞行",
	"fpv_tip_0x16100068":             "无法起飞：电调短路，请重启无人机",
	"fpv_tip_0x16100068_sky":         "电调短路，请重启无人机",
	"fpv_tip_0x16100069":             "无法起飞：电子调速器自检异常，请重启无人机",
	"fpv_tip_0x16100069_sky":         "电子调速器自检异常，请返航或降落",
	"fpv_tip_0x1610006A":             "电池不匹配，请更换电池",
	"fpv_tip_0x1610006B":             "无法起飞：电池固件需要升级",
	"fpv_tip_0x1610006B_sky":         "电池固件异常，请尽快返航或降落",
	"fpv_tip_0x1610006C":             "无法起飞：电池严重异常",
	"fpv_tip_0x1610006C_sky":         "电池严重异常：强制降落中",
	"fpv_tip_0x1610006D":             "无法起飞：已限制功率，原因请参考电池告警",
	"fpv_tip_0x1610006D_sky":         "已限制功率和飞行姿态，请尽快返航，原因请参考电池告警",
	"fpv_tip_0x16100070":             "还在用么？160没用这个了",
	"fpv_tip_0x16100070_sky":         "还在用么？160没用这个了",
	"fpv_tip_0x16100071":             "无法起飞：卫星定位异常，请重启无人机",
	"fpv_tip_0x16100071_sky":         "卫星定位异常，请降落或返航",
	"fpv_tip_0x16100072":             "无法起飞：云台校准中，请稍后起飞",
	"fpv_tip_0x16100072_sky":         "云台校准异常，请返航或降落",
	"fpv_tip_0x16100073":             "无法起飞：当前状态不支持起飞。固件版本过低或正在上传航线至无人机",
	"fpv_tip_0x16100073_sky":         "固件版本过低或正在上传航线至无人机，请返航或降落",
	"fpv_tip_0x16100074":             "无法起飞：无人机起飞高度异常，请重启无人机",
	"fpv_tip_0x16100074_sky":         "无人机起飞高度异常，请重启无人机",
	"fpv_tip_0x16100075":             "无法起飞：电调版本不一致，请更新无人机固件",
	"fpv_tip_0x16100075_sky":         "电调版本不一致，请更新无人机固件",
	"fpv_tip_0x16100076":             "无法起飞：IMU异常，请联系大疆售后服务",
	"fpv_tip_0x16100076_sky":         "IMU异常，请返航或降落",
	"fpv_tip_0x16100078":             "无法起飞：指南针异常，请联系大疆售后服务",
	"fpv_tip_0x16100078_sky":         "指南针异常，请返航或降落",
	"fpv_tip_0x1610007A":             "无法起飞：电调鸣叫中，关闭后可正常飞行",
	"fpv_tip_0x1610007A_sky":         "电调异常鸣叫中，请返航或降落",
	"fpv_tip_0x1610007B":             "无法起飞：电调温度过高，请关机至恢复正常温度",
	"fpv_tip_0x1610007B_sky":         "电调温度过高，请降落，并关机冷却至恢复正常温度",
	"fpv_tip_0x1610007C":             "电池安装不到位，请重新拔插电池",
	"fpv_tip_0x1610007D":             "无法起飞：检测到被撞击，无人机已停止飞行，请重启无人机",
	"fpv_tip_0x1610007D_sky":         "检测到被撞击，无人机已停止飞行，请重启无人机",
	"fpv_tip_0x1610007F":             "无法起飞：检测到无人机发生过撞击，请重启无人机",
	"fpv_tip_0x1610007F_sky":         "检测到无人机发生过撞击，请返航或降落",
	"fpv_tip_0x16100080":             "无法起飞：无人机高度控制异常，请重启无人机",
	"fpv_tip_0x16100080_sky":         "无人机高度控制异常，请返航或降落",
	"fpv_tip_0x16100081":             "无法起飞：电池固件版本过低，请更新无人机固件",
	"fpv_tip_0x16100081_sky":         "电池固件版本过低，请返航或降落，并更新无人机固件",
	"fpv_tip_0x16100082":             "无法起飞：电池电芯压差过大，请对电池进行保养",
	"fpv_tip_0x16100082_sky":         "电池电芯压差过大，请返航或降落，再对电池进行保养",
	"fpv_tip_0x16100083":             "无法起飞：电池安装不到位，请将电池锁紧旋钮旋转到位",
	"fpv_tip_0x16100083_sky":         "电池安装不到位，请返航或降落，并检查电池锁紧旋是否钮旋转到位",
	"fpv_tip_0x16100083_sky_pm440":   "电池安装不到位，请返航或降落，并检查电池提手是否安装到位",
	"fpv_tip_0x16100083_pm440":       "无法起飞：电池安装不到位，请将电池提手安装到位",
	"fpv_tip_0x16100084":             "无法起飞：上风扇异常，请检查无人机上风扇是否有堵转",
	"fpv_tip_0x16100084_sky":         "上风扇异常，请返航或降落，再检查无人机上风扇是否有堵转",
	"fpv_tip_0x16100085":             "无法起飞：无人机过热，请关闭无人机至恢复正常温度",
	"fpv_tip_0x16100085_sky":         "无人机过热，请返航或降落，再关闭无人机至恢复正常温度",
	"fpv_tip_0x16100087":             "无法起飞：已触发紧急停桨",
	"fpv_tip_0x16100087_sky":         "已触发紧急停浆",
	"fpv_tip_0x16100088":             "无法起飞：遥控器校准中，请先退出校准状态",
	"fpv_tip_0x16100088_sky":         "无法起飞：遥控器校准中，请先退出校准状态",
	"fpv_tip_0x16100089":             "无法起飞：绑定设备不匹配或正在校验中，请使用绑定遥控器或绑定当下遥控器。",
	"fpv_tip_0x16100089_sky":         "异常起飞：遥控器绑定校验失败，请使用绑定遥控器或绑定当下遥控器。",
	"fpv_tip_0x1610008A":             "无法起飞：飞控系统组件异常，请重启无人机",
	"fpv_tip_0x1610008A_sky":         "飞控系统组件异常，请返航或降落",
	"fpv_tip_0x1610008F":             "无法起飞：机载天线搜星质量较差，请至空旷地起飞",
	"fpv_tip_0x1610008F_sky":         "机载天线搜星质量较差，请谨慎飞行",
	"fpv_tip_0x16100090":             "无法起飞：传感器系统异常，请重启无人机",
	"fpv_tip_0x16100090_sky":         "传感器系统异常,请返航或降落",
	"fpv_tip_0x16100091":             "无法起飞：该无人机无法在此区域飞行",
	"fpv_tip_0x16100091_sky":         "该无人机无法在此区域飞行，请返航或降落",
	"fpv_tip_0x16100091_sky_special": "该无人机无法在此区域飞行，限高%1$s m",
	"fpv_tip_0x16100092":             "无法起飞：电池电量异常，请进行电池保养",
	"fpv_tip_0x16100092_sky":         "电池电量异常，请返航或降落，再进行电池保养",
	"fpv_tip_0x16100095":             "无法起飞：请在起飞前设置无人机悬停模式",
	"fpv_tip_0x16100096":             "%battery_index电池供电异常，无人机强制降落",
	"fpv_tip_0x16100099":             "无法起飞：云台启动异常，请检查云台是否能自由转动，再重启无人机",
	"fpv_tip_0x16100099_sky":         "云台启动异常，请返航或降落，再检查云台是否能自由转动，再重启无人机",
	"fpv_tip_0x1610009A":             "无法起飞：云台异常振动，请检查云台是否能自由转动、是否受损，再重启无人机",
	"fpv_tip_0x1610009A_sky":         "云台异常振动，请返航或降落，再检查云台是否能自由转动、是否受损，再重启无人机",
	"fpv_tip_0x1610009F":             "无法起飞：飞控系统组件异常，请重启无人机",
	"fpv_tip_0x1610009F_sky":         "飞控系统组件异常，请返航或降落",
	"fpv_tip_0x161000A0":             "无法起飞：限飞模块组件异常",
	"fpv_tip_0x161000A0_sky":         "限飞模块组件异常，请返航或降落",
	"fpv_tip_0x161000A1":             "无法起飞：请检查机臂套筒是否固定到位",
	"fpv_tip_0x161000A1_sky":         "请尽快降落并检查机臂套筒是否固定到位",
	"fpv_tip_0x161000A2":             "无法起飞：下风扇异常，请检查无人机下风扇是否有堵转",
	"fpv_tip_0x161000A2_sky":         "下风扇异常，请返航或降落，再检查无人机下风扇是否有堵转",
	"fpv_tip_0x161000A3":             "无法起飞，请断开飞机 USB 连接后重试",
	"fpv_tip_0x161000A4":             "无法起飞：无人机处于移动平台",
	"fpv_tip_0x161000A4_sky":         "飞控系统异常，请尽快返航或降落",
	"fpv_tip_0x161000A5":             "无法起飞：桨叶未展开",
	"fpv_tip_0x161000A5_sky":         "飞控系统异常，请尽快返航或降落",
	"fpv_tip_0x161000A6":             "无法起飞：遥控器电量过低，请给遥控器充电",
	"fpv_tip_0x161000A6_sky":         "遥控器电量过低，请尽快返航或降落",
	"fpv_tip_0x161000A7":             "无法起飞：遥控器绑定校验失败，临时起飞次数耗尽，请使用绑定遥控器或者换绑",
	"fpv_tip_0x161000A7_sky":         "飞控系统异常，请尽快返航或降落",
	"fpv_tip_0x161000A9":             "无法起飞：无人机处于慢转桨模式",
	"fpv_tip_0x161000A9_sky":         "无人机异常慢转桨，请尽快返航或降落",
	"fpv_tip_0x161000AA":             "无法起飞：限飞模块组件异常",
	"fpv_tip_0x161000AA_sky":         "限飞模块组件异常，请尽快返航或降落",
	"fpv_tip_0x161000AB":             "无法起飞：飞行安全数据库异常",
	"fpv_tip_0x161000AB_sky":         "飞行安全数据库异常，请尽快返航或降落",
	"fpv_tip_0x161000AC":             "无法起飞：飞控系统异常",
	"fpv_tip_0x161000AC_sky":         "飞控系统异常，请尽快降落",
	"fpv_tip_0x161000AF":             "无法起飞：飞控系统初始化中",
	"fpv_tip_0x161000AF_sky":         "飞控系统异常初始化，请尽快返航或降落",
	"fpv_tip_0x161000B1":             "起落架状态异常，请尝试解除起落架锁定，并点击变形按键尝试复位",
	"fpv_tip_0x161000B1_sky":         "起落架状态异常，请尝试点击变形按键复位",
	"fpv_tip_0x161000B2":             "无法起飞：起落架变形中",
	"fpv_tip_0x161000B3":             "无法起飞：起落架已进入运输模式，等待摘除云台",
	"fpv_tip_0x161000B4":             "无法起飞：Remote ID 播报异常，无法获取遥控器位置",
	"fpv_tip_0x161000B5":             "无法起飞，Remote ID 模块异常，请联系售后服务",
	"fpv_tip_0x161000B6":             "无法起飞：电池平均功率过低，请降低载重或更换满电电池",
	"fpv_tip_0x161000B7":             "已超最大允许载重，请降低载重或补充至满电",
	"fpv_tip_0x161000B7_sky":         "已超最大允许载重，请尽快返航或降落",
	"fpv_tip_0x161000B8":             "无法起飞：无人机周围有障碍物",
	"fpv_tip_0x161000BD":             "电池温度过高，无法起飞",
	"fpv_tip_0x161000BE":             "无法起飞：电池类型不匹配",
	"fpv_tip_0x161000BF":             "无法起飞：单电池模式未打开",
	"fpv_tip_0x161000C0":             "无法起飞：不支持当前卡口云台，请升级无人机固件",
	"fpv_tip_0x161000C4":             "无法起飞：电调自检中",
	"fpv_tip_0x161000C7":             "无法起飞：电池温度低于10度",
	"fpv_tip_0x161000C7_sky":         "电池温度低于10度，尽快返航或降落",
	"fpv_tip_0x161000C8":             "无法起飞：请确认usb是否已连接或者系统正在升级中",
	"fpv_tip_0x161000C8_sky":         "请确认usb是否已连接或者系统正在升级中",
	"fpv_tip_0x161000C9":             "无法起飞：请确认usb是否已连接或者系统正在升级中",
	"fpv_tip_0x161000C9_sky":         "请确认usb是否已连接或者系统正在升级中",
	"fpv_tip_0x161000D2":             "系留模式不支持 A/S/N 挡位，请切换挡位",
	"fpv_tip_0x161000D3":             "无法起飞：1号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D3_sky":         "1号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D4":             "无法起飞：2号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D4_sky":         "2号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D5":             "无法起飞：3号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D5_sky":         "3号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D6":             "无法起飞：4号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D6_sky":         "4号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D7":             "无法起飞：5号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D7_sky":         "5号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D8":             "无法起飞：6号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D8_sky":         "6号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D9":             "无法起飞：7号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000D9_sky":         "7号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000DA":             "无法起飞：8号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000DA_sky":         "8号电机曾发生严重超温，存在较高安全风险，请立即联系DJI售后服务寻求技术支持，检查电机线圈状态。",
	"fpv_tip_0x161000F1":             "无法起飞：请检查右前机臂是否展开并固定到位",
	"fpv_tip_0x161000F1_sky":         "右前机臂异常，请尽快降落",
	"fpv_tip_0x161000F2":             "无法起飞：请检查左前机臂是否展开并固定到位",
	"fpv_tip_0x161000F2_sky":         "左前机臂异常，请尽快降落",
	"fpv_tip_0x161000F3":             "无法起飞：请检查左后机臂是否展开并固定到位",
	"fpv_tip_0x161000F3_sky":         "左后机臂异常，请尽快降落",
	"fpv_tip_0x161000F4":             "无法起飞：请检查右后机臂是否展开并固定到位",
	"fpv_tip_0x161000F4_sky":         "右后机臂异常，请尽快降落",
	"fpv_tip_0x161000F5":             "无法起飞：系统初始化中",
	"fpv_tip_0x161000F5_sky":         "系统初始化中，请尽快降落",
	"fpv_tip_0x161000F6":             "无法起飞：飞控系统异常",
	"fpv_tip_0x161000F6_sky":         "飞控系统异常，请尽快降落",
	"fpv_tip_0x161000F7":             "无法起飞：感知系统初始化中",
	"fpv_tip_0x161000F7_sky":         "感知系统异常，请尽快降落或谨慎飞行",
	"fpv_tip_0x161000F8":             "无法起飞：感知系统初始化中",
	"fpv_tip_0x161000F8_sky":         "感知系统异常，请尽快降落",
	"fpv_tip_0x161000F9":             "无法起飞：遥控器信号弱，开启增强图传后请靠近无人机起飞",
	"fpv_tip_0x161000F9_sky":         "飞控系统异常，请尽快返航或降落",
	"fpv_tip_0x161000FA":             "无法起飞：遥控器信号弱，开启增强图传后请靠近无人机起飞",
	"fpv_tip_0x161000FA_sky":         "飞控系统异常，请尽快返航或降落",
	"fpv_tip_0x161000FB":             "无法起飞：桨叶损坏，请联系售后服务",
	"fpv_tip_0x161000FC":             "无法起飞：机场选址中",
	"fpv_tip_0x161000FD":             "无法起飞，请在自定义作业区内或远离边界10m以上",
	"fpv_tip_0x161000FD_sky":         "请在自定义作业区内作业",
	"fpv_tip_0x161000a1":             "无法起飞：请检查机臂套筒是否固定到位",
	"fpv_tip_0x161000a2":             "无法起飞：下风扇异常，请检查无人机下风扇是否有堵转",
	"fpv_tip_0x161000a2_sky":         "下风扇异常，请返航或降落，再检查无人机下风扇是否有堵转",
	"fpv_tip_0x161000a6":             "无法起飞：遥控器电量过低，请给遥控器充电",
	"fpv_tip_0x161000a6_sky":         "遥控器电量过低，请尽快返航或降落",
	"fpv_tip_0x161000b4":             "无法起飞：Remote ID 播报异常，无法获取遥控器位置",
	"fpv_tip_0x161000b5":             "无法起飞，Remote ID 模块异常，请联系售后服务",
	"fpv_tip_0x161000c0":             "无法起飞：左右电池型号不一致，请更换电池保持一致",
	"fpv_tip_0x161000c0_sky":         "左右电池型号不一致，请尽快降落更换电池",
	"fpv_tip_0x1611000A":             "失联返航中",
	"fpv_tip_0x16110010":             "无人机未起飞补光灯不生效，起飞后自动生效",
	"fpv_tip_0x16110023":             "动力临近饱和，请就近谨慎降落",
	"fpv_tip_0x16110029":             "配件已安装，飞行性能和抗风性能降低",
	"fpv_tip_0x1611002A":             "探照灯断开连接，请用工具锁紧螺丝",
	"fpv_tip_0x1611002B":             "喊话器断开连接，请用工具锁紧螺丝",
	"fpv_tip_0x1611002D":             "负载功率超过限制，负载已断电，请返航重启无人机",
	"fpv_tip_0x1611002E":             "避障雷达配件断开连接，请参照说明书指引重新安装",
	"fpv_tip_0x1611004F":             "电池认证失败",
	"fpv_tip_0x16110065":             "机身过热，AI 目标识别关闭，请降温后使用",
	"fpv_tip_0x16110066":             "RTK 模块过热，请降温后使用",
	"fpv_tip_0x16110067":             "RTK 模块过热并关闭，请降温后使用",
	"fpv_tip_0x16111001":             "避障雷达异常",
	"fpv_tip_0x16111002":             "避障雷达未激活",
	"fpv_tip_0x16111003":             "避障雷达不支持热插拔",
	"fpv_tip_0x1612000B":             "海拔过高，推荐使用高原桨",
	"fpv_tip_0x1612000C":             "海拔过高，请务必使用高原桨",
	"fpv_tip_0x1612000D":             "海拔即将超过最大飞行高度，请注意飞行安全并控制高度",
	"fpv_tip_0x16120010":             "接近地面，谨慎飞行",
	"fpv_tip_0x16130001":             "船上起降模式，无人机姿态过大，请谨慎飞行",
	"fpv_tip_0x16130002":             "船上起降模式，船身速度过大，请谨慎飞行",
	"fpv_tip_0x16130003":             "船上起降模式，无人机姿态过大，请谨慎飞行",
	"fpv_tip_0x16140001":             "PSDK设备连接异常，请检查连接并重启无人机",
	"fpv_tip_0x16140002":             "PSDK设备功率异常，请重启无人机",
	"fpv_tip_0x16200101":             "无法起飞：已连接USB",
	"fpv_tip_0x16200101_sky":         "USB接口异常，请谨慎飞行",
	"fpv_tip_0x16200102":             "无法起飞：清除日志中",
	"fpv_tip_0x16200102_sky":         "清除日志中，请谨慎飞行",
	"fpv_tip_0x16200103":             "无法起飞：固件升级中",
	"fpv_tip_0x16200103_sky":         "固件升级中，请谨慎飞行",
	"fpv_tip_0x16200104":             "无法起飞：固件需要升级或正在升级中",
	"fpv_tip_0x16200104_sky":         "固件强制升级中，请谨慎飞行",
	"fpv_tip_0x16200201":             "无法起飞：限飞数据库升级中",
	"fpv_tip_0x16200201_sky":         "限飞数据库升级中，请谨慎飞行",
	"fpv_tip_0x16200202":             "无法起飞：视觉传感器标定异常",
	"fpv_tip_0x16200202_sky":         "视觉传感器标定异常，请谨慎飞行",
	"fpv_tip_0x16200203":             "无法起飞：ToF标定异常",
	"fpv_tip_0x16200203_sky":         "ToF标定异常，请谨慎飞行",
	"fpv_tip_0x16200204":             "无法起飞：视觉传感器状态异常",
	"fpv_tip_0x16200204_sky":         "视觉传感器状态异常，请谨慎飞行",
	"fpv_tip_0x16200207":             "无法起飞: 下视红外传感器连接异常",
	"fpv_tip_0x16200208":             "桨叶异常，请确认桨叶是否完好无损，并联系售后服务",
	"fpv_tip_0x16200301":             "无法起飞：扩展口设备异常，请检查PSDK设备",
	"fpv_tip_0x16200301_sky":         "扩展口设备异常，请尽快返航或降落",
	"fpv_tip_0x16200302":             "无法起飞：第三方负载设备异常",
	"fpv_tip_0x16200302_sky":         "执行空中紧急停桨，请根据无人机当前位置预测降落点位置",
	"fpv_tip_0x16200401":             "无法起飞：APP限飞数据库强制升级中",
	"fpv_tip_0x16200401_sky":         "APP限飞数据库强制升级中，请谨慎飞行",
	"fpv_tip_0x16200402":             "无法起飞：APP固件强制升级中",
	"fpv_tip_0x16200402_sky":         "APP固件强制升级中，请谨慎飞行",
	"fpv_tip_0x16200501":             "无法起飞：电池版本不匹配",
	"fpv_tip_0x16200501_sky":         "电池版本不匹配，请尽快返航或降落",
	"fpv_tip_0x16200601":             "飞行系统初始化中，请等待",
	"fpv_tip_0x16200601_sky":         "飞行系统初始化中，请等待",
	"fpv_tip_0x16200A01":             "无法起飞：未接电池帽盖",
	"fpv_tip_0x16200A01_sky":         "电池通信异常，请谨慎飞行",
	"fpv_tip_0x16200C01":             "无法起飞：电池未开机",
	"fpv_tip_0x16200C01_sky":         "电池未开机",
	"fpv_tip_0x16200D01":             "激光雷达正在外参校准中，无人机阻飞",
	"fpv_tip_0x16200D02":             "激光雷达低温，无人机阻飞",
	"fpv_tip_0x16400002":             "AP主动释放控制权",
	"fpv_tip_0x16400003":             "AP控制指令异常",
	"fpv_tip_0x16400004":             "AP心跳异常",
	"fpv_tip_0x16400005":             "mcu侧mission调度器状态数据包异常",
	"fpv_tip_0x16400006":             "mission调度器异常",
	"fpv_tip_0x16400007":             "调度器没有mission运行",
	"fpv_tip_0x16400008":             "AP到MCU链路异常",
	"fpv_tip_0x16400009":             "AP没有发送控制指令",
	"fpv_tip_0x1640000A":             "AP挂死",
	"fpv_tip_0x1640000B":             "系统实时性异常",
	"fpv_tip_0x1640000C":             "业务实时性异常",
	"fpv_tip_0x16400100":             "抽象数据fly_core无内容",
	"fpv_tip_0x16400101":             "抽象数据fly_extend无内容",
	"fpv_tip_0x16400102":             "抽象数据fmu_gps无内容",
	"fpv_tip_0x16400103":             "抽象数据fmu_rtk无内容",
	"fpv_tip_0x16400104":             "抽象数据fusion_out无内容",
	"fpv_tip_0x16400105":             "抽象数据vio_result无内容",
	"fpv_tip_0x16400106":             "抽象数据image_status无内容",
	"fpv_tip_0x16400107":             "抽象数据radar_detect_info无内容",
	"fpv_tip_0x16400108":             "抽象数据gimbal_state无内容",
	"fpv_tip_0x16400109":             "抽象数据camera_state无内容",
	"fpv_tip_0x1640010A":             "抽象数据camera_cap_status无内容",
	"fpv_tip_0x1640010B":             "抽象数据camera_video_param无内容",
	"fpv_tip_0x1640010C":             "抽象数据camera_lens_param无内容",
	"fpv_tip_0x1640010D":             "抽象数据camera_lens_param_ir无内容",
	"fpv_tip_0x1640010E":             "抽象数据camera_data_hd无内容",
	"fpv_tip_0x1640010F":             "抽象数据camera_data_vga无内容",
	"fpv_tip_0x16400110":             "抽象数据gimbal_state无内容",
	"fpv_tip_0x16400111":             "抽象数据camera_state无内容",
	"fpv_tip_0x16400112":             "抽象数据camera_cap_status无内容",
	"fpv_tip_0x16400113":             "抽象数据camera_video_param无内容",
	"fpv_tip_0x16400114":             "抽象数据camera_lens_param无内容",
	"fpv_tip_0x16400115":             "抽象数据camera_lens_param_ir无内容",
	"fpv_tip_0x16400116":             "抽象数据camera_data_hd无内容",
	"fpv_tip_0x16400117":             "抽象数据camera_data_vga无内容",
	"fpv_tip_0x16400118":             "杆量直通rc异常",
	"fpv_tip_0x16400119":             "杆量直通motion_rc异常",
	"fpv_tip_0x16416001":             "DSP向ARM发出中断失败",
	"fpv_tip_0x16416002":             "DSP侧交互部分超时",
	"fpv_tip_0x16418000":             "使用者调用dsp接口出错",
	"fpv_tip_0x16418001":             "DSP输入参数错误",
	"fpv_tip_0x16418500":             "开发者设计dsp接口出错",
	"fpv_tip_0x16418501":             "DSP算子调用超时",
	"fpv_tip_0x16418502":             "DSP算子调用忙",
	"fpv_tip_0x16420000":             "飞行系统模块初始化失败",
	"fpv_tip_0x16420001":             "飞行系统AP_Linux模块init失败",
	"fpv_tip_0x16420002":             "飞行系统AP_Linux模块start失败",
	"fpv_tip_0x16420003":             "飞行系统AP_RTOS模块init失败",
	"fpv_tip_0x16420004":             "飞行系统AP_RTOS模块start失败",
	"fpv_tip_0x16420010":             "飞行系统功耗管理异常",
	"fpv_tip_0x16420011":             "sysmode未响应event事件",
	"fpv_tip_0x16420012":             "飞行系统event事件通知失败",
	"fpv_tip_0x16420013":             "飞行系统功耗场景切换超时",
	"fpv_tip_0x16420014":             "功耗管理消息队列发送超时",
	"fpv_tip_0x16420015":             "功耗管理节点状态同步失败",
	"fpv_tip_0x16420016":             "功耗管理子节点间V1消息不通",
	"fpv_tip_0x16420020":             "能力项参数同步失败",
	"fpv_tip_0x16420021":             "csdk断连",
	"fpv_tip_0x16420022":             "能力集参数表数据读取失败",
	"fpv_tip_0x16420100":             "dbus话题app_state无数据",
	"fpv_tip_0x16420101":             "dbus话题battery_state无数据",
	"fpv_tip_0x16420102":             "dbus话题flight_state无数据",
	"fpv_tip_0x16420103":             "dbus话题fly_limit无数据",
	"fpv_tip_0x16420104":             "dbus话题home_state无数据",
	"fpv_tip_0x16420105":             "dbus话题mission_config无数据",
	"fpv_tip_0x16420106":             "dbus话题rc_state无数据",
	"fpv_tip_0x16420107":             "dbus话题sensor_data无数据",
	"fpv_tip_0x16420108":             "dbus话题flight_record无数据",
	"fpv_tip_0x16420109":             "dbus话题motion_rc_info无数据",
	"fpv_tip_0x1642010A":             "dbus话题vio_result无数据",
	"fpv_tip_0x1642010B":             "dbus话题image_status无数据",
	"fpv_tip_0x1642010C":             "dbus话题fmu_gps无数据",
	"fpv_tip_0x1642010D":             "dbus话题fmu_rtk无数据",
	"fpv_tip_0x1642010E":             "dbus话题fusion_out无数据",
	"fpv_tip_0x1642010F":             "dbus话题radar_detect_info无数据",
	"fpv_tip_0x16420110":             "dbus话题gimbal_state无数据",
	"fpv_tip_0x16420111":             "dbus话题camera_state无数据",
	"fpv_tip_0x16420112":             "dbus话题camera_cap_status无数据",
	"fpv_tip_0x16420113":             "dbus话题camera_video_param无数据",
	"fpv_tip_0x16420114":             "dbus话题camera_lens_param无数据",
	"fpv_tip_0x16420115":             "dbus话题camera_lens_param_ir无数据",
	"fpv_tip_0x16420116":             "dbus话题camera_data_hd无数据",
	"fpv_tip_0x16420117":             "dbus话题camera_data_vga无数据",
	"fpv_tip_0x16430001":             "异常退出焦点跟随",
	"fpv_tip_0x16430002":             "异常退出到聚焦",
	"fpv_tip_0x16430003":             "异常退出到多目标扫描",
	"fpv_tip_0x16430004":             "焦点跟随异常提示",
	"fpv_tip_0x16430005":             "融合数据无效",
	"fpv_tip_0x16430006":             "传感器数据更新失败",
	"fpv_tip_0x16430007":             "未起飞",
	"fpv_tip_0x16430008":             "相机模式不支持",
	"fpv_tip_0x16430009":             "融合时间戳异常",
	"fpv_tip_0x1643000A":             "抽象数据指针为空",
	"fpv_tip_0x1643000B":             "DBUS获取数据指针为空",
	"fpv_tip_0x1643000C":             "TM异常",
	"fpv_tip_0x1643000D":             "飞机姿态过大",
	"fpv_tip_0x1643000E":             "环境光过暗",
	"fpv_tip_0x1643000F":             "遥控器断连",
	"fpv_tip_0x16430010":             "TKPlanner异常",
	"fpv_tip_0x16430011":             "TE异常",
	"fpv_tip_0x16430012":             "POI异常",
	"fpv_tip_0x16430013":             "ML异常",
	"fpv_tip_0x16430014":             "数据链路异常",
	"fpv_tip_0x16430015":             "IMU数据异常",
	"fpv_tip_0x16430016":             "云台数据异常",
	"fpv_tip_0x16430017":             "fov数据异常",
	"fpv_tip_0x16430018":             "ml状态异常",
	"fpv_tip_0x16430019":             "soe数据异常",
	"fpv_tip_0x1643001A":             "IMU数据无效",
	"fpv_tip_0x1643001B":             "云台数据无效",
	"fpv_tip_0x1643001C":             "fov数据无效",
	"fpv_tip_0x1643001D":             "ml状态数据无效",
	"fpv_tip_0x1643001E":             "soe数据无效",
	"fpv_tip_0x1643001F":             "环绕测距时间过长",
	"fpv_tip_0x16430020":             "环绕时距地面过近",
	"fpv_tip_0x16430021":             "限远",
	"fpv_tip_0x16430022":             "限高",
	"fpv_tip_0x16430023":             "目标类型不支持",
	"fpv_tip_0x16430024":             "将要进入Tracking/环绕时目标丢失",
	"fpv_tip_0x16430025":             "目标跟随中注意变焦范围",
	"fpv_tip_0x16430026":             "目标在画面中占比过大",
	"fpv_tip_0x16430101":             "异常退出一键短片",
	"fpv_tip_0x16430102":             "异常终止一键短片拍摄",
	"fpv_tip_0x16430103":             "传感器数据验证错误",
	"fpv_tip_0x16430104":             "空指针",
	"fpv_tip_0x16430105":             "抽象数据指针为空",
	"fpv_tip_0x16430106":             "DBUS获取数据指针为空",
	"fpv_tip_0x16430107":             "在地面不能执行一键短片",
	"fpv_tip_0x16430108":             "用户打杆",
	"fpv_tip_0x16430109":             "用户暂停",
	"fpv_tip_0x1643010A":             "限高",
	"fpv_tip_0x1643010B":             "限远",
	"fpv_tip_0x1643010C":             "录像停止错误",
	"fpv_tip_0x1643010D":             "录像开始错误",
	"fpv_tip_0x1643010E":             "避障",
	"fpv_tip_0x1643010F":             "遥控器信号丢失",
	"fpv_tip_0x16430110":             "初始化失败",
	"fpv_tip_0x16430111":             "夺权失败",
	"fpv_tip_0x16430112":             "储存空间不足",
	"fpv_tip_0x16430113":             "目标丢失",
	"fpv_tip_0x16430114":             "拍照失败",
	"fpv_tip_0x16430115":             "合成失败",
	"fpv_tip_0x16430116":             "加载相机参数失败",
	"fpv_tip_0x16430117":             "调整相机参数失败",
	"fpv_tip_0x16430118":             "相机角度错误",
	"fpv_tip_0x16430119":             "相机未知错误",
	"fpv_tip_0x1643011A":             "相机参数错误",
	"fpv_tip_0x1643011B":             "相机状态错误",
	"fpv_tip_0x1643011C":             "设置TOPMODE超时",
	"fpv_tip_0x1643011D":             "相机TOPMODE错误",
	"fpv_tip_0x1643011E":             "异常提示一键短片",
	"fpv_tip_0x16430201":             "大师镜头异常退出",
	"fpv_tip_0x16430202":             "大师镜头异常暂停",
	"fpv_tip_0x16430203":             "大师镜头发现空指针",
	"fpv_tip_0x16430204":             "大师镜头抽象数据指针为空",
	"fpv_tip_0x16430205":             "大师镜头DBUS获取数据指针为空",
	"fpv_tip_0x16430206":             "大师镜头传感器数据验证错误",
	"fpv_tip_0x16430207":             "大师镜头用户打杆",
	"fpv_tip_0x16430208":             "大师镜头用户暂停",
	"fpv_tip_0x16430209":             "大师镜头遇限高",
	"fpv_tip_0x1643020A":             "大师镜头遇限远",
	"fpv_tip_0x1643020B":             "大师镜头录像停止失败",
	"fpv_tip_0x1643020C":             "大师镜头录像开始失败",
	"fpv_tip_0x1643020D":             "大师镜头遇障碍物",
	"fpv_tip_0x1643020E":             "大师镜头遥控器信号丢失",
	"fpv_tip_0x1643020F":             "大师镜头初始化失败",
	"fpv_tip_0x16430210":             "大师镜头夺权失败",
	"fpv_tip_0x16430211":             "大师镜头储存空间不足",
	"fpv_tip_0x16430212":             "大师镜头目标丢失",
	"fpv_tip_0x16430213":             "大师镜头相机参数错误",
	"fpv_tip_0x16430214":             "大师镜头设置TOPMODE超时",
	"fpv_tip_0x16430215":             "大师镜头设置TOPMODE异常",
	"fpv_tip_0x16430216":             "大师镜头警告",
	"fpv_tip_0x16430217":             "大师镜头调整相机参数失败",
	"fpv_tip_0x16430218":             "大师镜头云台控制错误",
	"fpv_tip_0x16430219":             "大师镜头Yaw控制错误",
	"fpv_tip_0x1643021A":             "大师镜头VA控制错误",
	"fpv_tip_0x1643021B":             "大师镜头速度控制错误",
	"fpv_tip_0x1643021C":             "大师镜头相机控制错误",
	"fpv_tip_0x1643021D":             "大师镜头任务不匹配",
	"fpv_tip_0x16430301":             "异常退出延时摄影",
	"fpv_tip_0x16430302":             "延时摄影异常退出当前任务",
	"fpv_tip_0x16430303":             "延时摄影异常进入暂停状态",
	"fpv_tip_0x16430304":             "延时摄影异常提示",
	"fpv_tip_0x16430305":             "地面电机已启动",
	"fpv_tip_0x16430306":             "相机模式异常",
	"fpv_tip_0x16430307":             "当前任务不匹配",
	"fpv_tip_0x16430308":             "抽象数据为空",
	"fpv_tip_0x16430309":             "相机模式超时",
	"fpv_tip_0x1643030a":             "相机模式不匹配",
	"fpv_tip_0x1643030b":             "定向延时中框丢失",
	"fpv_tip_0x1643030c":             "返回首航点航线平台异常",
	"fpv_tip_0x1643030d":             "环绕测距超时",
	"fpv_tip_0x1643030e":             "限飞",
	"fpv_tip_0x1643030f":             "地面延时电量低",
	"fpv_tip_0x16430310":             "卫星定位信号差",
	"fpv_tip_0x16430311":             "避障",
	"fpv_tip_0x16430312":             "环绕测距触发限飞",
	"fpv_tip_0x16430313":             "轨迹延时触发限高",
	"fpv_tip_0x16430314":             "返回首航点触发限飞",
	"fpv_tip_0x16430315":             "避障失效",
	"fpv_tip_0x16430316":             "返回首航点触发避障",
	"fpv_tip_0x16430317":             "运行过程中触发避障",
	"fpv_tip_0x16430318":             "拍摄过程中触发限飞",
	"fpv_tip_0x16430319":             "航线平台异常终止",
	"fpv_tip_0x1643031a":             "相机存储慢",
	"fpv_tip_0x1643031b":             "卫星定位信号弱轨迹点记录失败",
	"fpv_tip_0x1643031c":             "已达到最大照片限制",
	"fpv_tip_0x1643031d":             "存储空间不足",
	"fpv_tip_0x1643031e":             "定向延时航线未锁定",
	"fpv_tip_0x1643031f":             "云台姿态变化太小",
	"fpv_tip_0x16430320":             "云台姿态变化超限",
	"fpv_tip_0x16430321":             "轨迹载入失败",
	"fpv_tip_0x16430322":             "增加1s失败",
	"fpv_tip_0x16430323":             "完成照片太少无法合成视频",
	"fpv_tip_0x16430324":             "返回首航点轨迹生成失败",
	"fpv_tip_0x16432400":             "异常退出 ApasMission",
	"fpv_tip_0x16432401":             "融合数据订阅异常",
	"fpv_tip_0x16432402":             "Json文件读取失败",
	"fpv_tip_0x16432403":             "FlyCore数据订阅异常",
	"fpv_tip_0x16432404":             "融合数据超时",
	"fpv_tip_0x16432405":             "融合定位数据无效",
	"fpv_tip_0x16432410":             "ApasMission 异常退出到关闭",
	"fpv_tip_0x16432411":             "ImageStatus 订阅异常",
	"fpv_tip_0x16432412":             "夜景模式",
	"fpv_tip_0x16432413":             "环境光过暗且飞行环境安全",
	"fpv_tip_0x16432414":             "环境光过暗且悬停无水平杆",
	"fpv_tip_0x16432415":             "APAS Planner 功能异常且飞行环境安全",
	"fpv_tip_0x16432416":             "APAS Planner 功能异常且悬停无水平杆",
	"fpv_tip_0x16432417":             "OA Planner 功能异常且飞行环境安全",
	"fpv_tip_0x16432418":             "OA Planner 功能异常且悬停无水平杆",
	"fpv_tip_0x16432419":             "在云雾中",
	"fpv_tip_0x16432420":             "ApasMission 绕行异常退出到刹停",
	"fpv_tip_0x16432421":             "限飞区边缘",
	"fpv_tip_0x16432422":             "距离地面过近小于0.5m",
	"fpv_tip_0x16432423":             "部分环境光过暗",
	"fpv_tip_0x16432424":             "APAS Planner功能异常",
	"fpv_tip_0x16432425":             "高帧率录像",
	"fpv_tip_0x16432430":             "ApasMission 异常触发刹车",
	"fpv_tip_0x16432431":             "环境光过暗普通刹车",
	"fpv_tip_0x16432432":             "环境光过暗紧急刹车",
	"fpv_tip_0x16432433":             "APAS Planner 功能异常普通刹车",
	"fpv_tip_0x16432434":             "APAS Planner 功能异常紧急刹车",
	"fpv_tip_0x16432435":             "OA Planner 功能异常普通刹车",
	"fpv_tip_0x16432436":             "OA Planner 功能异常紧急刹车",
	"fpv_tip_0x16432500":             "OA模块无法正常工作",
	"fpv_tip_0x16432501":             "OA模块因地图异常无法正常工作",
	"fpv_tip_0x16432502":             "OA模块因地图异常无法正常工作",
	"fpv_tip_0x16432503":             "相机处于夜景模式OA无法正常工作",
	"fpv_tip_0x16432504":             "OA模块因处于云雾中无法正常工作",
	"fpv_tip_0x16432505":             "OA模块因环境光过暗无法正常工作",
	"fpv_tip_0x16432506":             "OA模块tof避障无法正常工作",
	"fpv_tip_0x16432550":             "OA模块触发异常保护",
	"fpv_tip_0x16432551":             "OA模块触发紧急刹车",
	"fpv_tip_0x16450001":             "飞机未能返回机场",
	"fpv_tip_0x16450002":             "飞机机场外触发不可取消降落",
	"fpv_tip_0x16450011":             "飞机悬停超时",
	"fpv_tip_0x16450012":             "飞机无法接收到机场状态",
	"fpv_tip_0x16450013":             "机场暂时不允许降落",
	"fpv_tip_0x16450014":             "Marker水平位置源无效",
	"fpv_tip_0x16450015":             "Marker水平位置源偏差大",
	"fpv_tip_0x16450016":             "Marker水平位置源订阅失败",
	"fpv_tip_0x16450017":             "RTK水平位置源无效",
	"fpv_tip_0x16450018":             "RTK水平位置源电离层校验不通过",
	"fpv_tip_0x16450019":             "飞机水平控制能力弱",
	"fpv_tip_0x16450021":             "机场降落功能运行异常",
	"fpv_tip_0x16450022":             "飞机因为切姿态变为普通降落",
	"fpv_tip_0x16450023":             "机场没有设置备降点无法触发备降",
	"fpv_tip_0x16450024":             "飞机没有触发提前停桨",
	"fpv_tip_0x16450025":             "抽象数据更新失败",
	"fpv_tip_0x16450031":             "飞机触发备降",
	"fpv_tip_0x16450032":             "飞机MCU接管控制",
	"fpv_tip_0x16450033":             "机场状态异常永久不允许降落",
	"fpv_tip_0x16450034":             "飞机悬停到低电量触发备降",
	"fpv_tip_0x16450035":             "飞机未能到达备降点中途降落",
	"fpv_tip_0x16450101":             "航线异常暂停",
	"fpv_tip_0x16450102":             "航线异常中止",
	"fpv_tip_0x16450103":             "航线功能异常",
	"fpv_tip_0x16450104":             "航线限高",
	"fpv_tip_0x16450105":             "航线限低",
	"fpv_tip_0x16450106":             "航线限远",
	"fpv_tip_0x16450107":             "航线触碰到禁飞区",
	"fpv_tip_0x16450108":             "航线执行中检测到障碍物",
	"fpv_tip_0x16450109":             "航线执行中卫星定位信号弱",
	"fpv_tip_0x1645010A":             "低电量",
	"fpv_tip_0x1645010B":             "航线生成超时",
	"fpv_tip_0x1645010C":             "拿不到抽象数据",
	"fpv_tip_0x1645010D":             "参数表读取失败",
	"fpv_tip_0x1645010E":             "轨迹库计算失败",
	"fpv_tip_0x1645010F":             "请求起飞失败",
	"fpv_tip_0x16450110":             "请求航线任务失败",
	"fpv_tip_0x16450111":             "请求返航失败",
	"fpv_tip_0x16450112":             "请求降落失败",
	"fpv_tip_0x16450113":             "无人机失联",
	"fpv_tip_0x16450114":             "实时仿地过程中，相机状态有问题（过亮，过暗，两侧亮度不一致）",
	"fpv_tip_0x16450115":             "实时仿地过程中全局地图计算出错",
	"fpv_tip_0x16450116":             "航线动作组解析失败",
	"fpv_tip_0x16450117":             "飞行任务冲突，无法获取飞机控制权",
	"fpv_tip_0x16450118":             "要执行的文件解析错误",
	"fpv_tip_0x16450119":             "等待wpmz文件读取时间过长",
	"fpv_tip_0x1645011A":             "航线已经开始，不能再次开始",
	"fpv_tip_0x1645011B":             "相机抽象设备异常",
	"fpv_tip_0x1645011C":             "云台抽象设备异常",
	"fpv_tip_0x1645011D":             "app链路异常",
	"fpv_tip_0x1645011E":             "工厂文件未删除",
	"fpv_tip_0x1645011F":             "RTK状态跳变，航线已退出",
	"fpv_tip_0x16450120":             "请求快速起飞失败",
	"fpv_tip_0x16450121":             "自动起飞运行失败",
	"fpv_tip_0x16450122":             "航线触碰到自定义飞行区",
	"fpv_tip_0x16450123":             "航线全景拍照动作出错",
	"fpv_tip_0x16450124":             "执行全景拍照过程超时",
	"fpv_tip_0x16450125":             "全景拍照设置相机topmode失败",
	"fpv_tip_0x16450126":             "全景拍照设置相机submode失败",
	"fpv_tip_0x16450127":             "全景拍照前飞机转yaw超时",
	"fpv_tip_0x16450128":             "全景拍照后飞机恢复yaw超时",
	"fpv_tip_0x16450129":             "全景拍照空指针错误",
	"fpv_tip_0x16461000":             "融合定位系统异常切换",
	"fpv_tip_0x16461001":             "ap_fusion 断联",
	"fpv_tip_0x16461002":             "ap_fusion 存在严重故障",
	"fpv_tip_0x16461003":             "默认融合定位系统被修改",
	"fpv_tip_0x16462001":             "霍尔设备断开",
	"fpv_tip_0x16462004":             "霍尔校准数据异常",
	"fpv_tip_0x16462007":             "霍尔传感器缺少磁环，工作异常",
	"fpv_tip_0x1646200A":             "霍尔传感器间互检失败",
	"fpv_tip_0x16463001":             "RTK No NLFix",
	"fpv_tip_0x16463004":             "主天线观测质量差",
	"fpv_tip_0x16463005":             "信号频繁失锁",
	"fpv_tip_0x16463006":             "电离层活跃",
	"fpv_tip_0x16463007":             "主天线SNR陡降",
	"fpv_tip_0x16463008":             "副天线SNR陡降",
	"fpv_tip_0x16463009":             "主天线开路或短路",
	"fpv_tip_0x1646300A":             "副天线开路或短路",
	"fpv_tip_0x1646300B":             "RTK Heading No NLFix",
	"fpv_tip_0x16463011":             "RTK基站RTCM数据异常",
	"fpv_tip_0x16463012":             "2607未收到RTCM",
	"fpv_tip_0x16463013":             "2607 RTCM V1组包异常",
	"fpv_tip_0x16463014":             "2607RTCM传输误码",
	"fpv_tip_0x16463021":             "2906未收到RTCM",
	"fpv_tip_0x16463022":             "2906RTK数据源不一致",
	"fpv_tip_0x16463023":             "2906RTK数据源无效",
	"fpv_tip_0x16463024":             "2906RTK数据源初始化失败",
	"fpv_tip_0x16463031":             "2606未收到RTCM",
	"fpv_tip_0x16463032":             "2606RTK数据源不一致",
	"fpv_tip_0x16463033":             "2606RTK数据源无效",
	"fpv_tip_0x16463034":             "2606未收到RTK信息源的信息",
	"fpv_tip_0x16471000":             "定位无输出，可能是FC传感器未就绪",
	"fpv_tip_0x16471001":             "定位异常，系统异常或loading异常",
	"fpv_tip_0x16471002":             "定位输出速度位置一致性差",
	"fpv_tip_0x16471003":             "飞控传感器未就绪",
	"fpv_tip_0x16471004":             "状态实时性delay太大",
	"fpv_tip_0x16471005":             "loop耗时异常",
	"fpv_tip_0x16471006":             "状态时间戳异常",
	"fpv_tip_0x16471007":             "IMU BIAS随机游走异常",
	"fpv_tip_0x16471010":             "视觉定位多方向同步异常",
	"fpv_tip_0x16471012":             "硬件加速器调用异常",
	"fpv_tip_0x16471100":             "IMU 时间戳异常",
	"fpv_tip_0x16471101":             "视觉时间戳异常",
	"fpv_tip_0x16471102":             "UBLOX GPS时间戳异常",
	"fpv_tip_0x16471103":             "DJI GPS时间戳异常",
	"fpv_tip_0x16471201":             "IMU静止检测更新异常",
	"fpv_tip_0x16471204":             "气压计更新异常",
	"fpv_tip_0x16471205":             "TOF更新异常",
	"fpv_tip_0x16471207":             "视觉更新异常",
	"fpv_tip_0x16471209":             "卫星定位更新异常",
	"fpv_tip_0x16476000":             "VIO 前端异常",
	"fpv_tip_0x16476001":             "初始化异常",
	"fpv_tip_0x16476002":             "前端启动错误",
	"fpv_tip_0x16476003":             "视觉定位前端获取里程计异常,尝试重启后再工作",
	"fpv_tip_0x16476004":             "TF树异常",
	"fpv_tip_0x16476005":             "circular_tracking错误",
	"fpv_tip_0x16476006":             "DSP translation KLT异常",
	"fpv_tip_0x16476007":             "DSP eis KLT异常",
	"fpv_tip_0x16476008":             "输入harris点数大于最大设置点数",
	"fpv_tip_0x16476009":             "相机参数未更新",
	"fpv_tip_0x16476100":             "VIO 动态切图异常",
	"fpv_tip_0x16476101":             "动态切图初始化异常",
	"fpv_tip_0x16476102":             "动态切图参数异常",
	"fpv_tip_0x16476103":             "acc_rectify_channel_update异常",
	"fpv_tip_0x16476104":             "insert_rectify_one_channel异常",
	"fpv_tip_0x16476105":             "ss_dbus_shm_acquire异常",
	"fpv_tip_0x16476106":             "ss_dbus_shm_publish异常",
	"fpv_tip_0x16476107":             "获取相机参数超时",
	"fpv_tip_0x16479000":             "V1-RTK功能异常",
	"fpv_tip_0x16479001":             "RTK初始化异常",
	"fpv_tip_0x16479002":             "RTK DSP运行异常",
	"fpv_tip_0x16479003":             "RTK进程调度异常",
	"fpv_tip_0x16479004":             "RTK输出频率异常",
	"fpv_tip_0x16479005":             "RTK MIPI数据异常",
	"fpv_tip_0x16479006":             "RTK 设备驱动异常",
	"fpv_tip_0x16479011":             "RTK 设备配置异常",
	"fpv_tip_0x16479012":             "RTK初始化资源申请异常",
	"fpv_tip_0x16479013":             "MIPI初始化异常",
	"fpv_tip_0x16479014":             "RTK初始化参数配置异常",
	"fpv_tip_0x16479015":             "RTK固件版本不匹配",
	"fpv_tip_0x16479021":             "RTK算子异常挂死",
	"fpv_tip_0x16479022":             "RTK算子运行超时",
	"fpv_tip_0x16479031":             "RTK系统中断异常",
	"fpv_tip_0x16479032":             "RTK系统实时性异常",
	"fpv_tip_0x16479041":             "RTK时序错误",
	"fpv_tip_0x16479051":             "RTK MIPI数据同步错误",
	"fpv_tip_0x16479052":             "RTK MIPI数据丢帧",
	"fpv_tip_0x1647C010":             "指南针需要校准",
	"fpv_tip_0x1647C011":             "指南针模长过大，需要校准",
	"fpv_tip_0x1647C012":             "指南针Yaw误差大，需要校准",
	"fpv_tip_0x1647C013":             "指南针yaw不一致，需要校准",
	"fpv_tip_0x1647C014":             "指南针模长不一致，需要校准",
	"fpv_tip_0x1647C015":             "指南针航向不一致，需要校准",
	"fpv_tip_0x1647C016":             "指南针长距离未校准，需要校准",
	"fpv_tip_0x1647C017":             "指南针长时间未校准，需要校准",
	"fpv_tip_0x1647C018":             "指南针长时间异常，需要校准",
	"fpv_tip_0x1647C019":             "指南针航向与融合航向不一致，需要校准",
	"fpv_tip_0x1647C020":             "需要移动飞机",
	"fpv_tip_0x1647C021":             "指南针校准完后模长过大，需要移动飞机",
	"fpv_tip_0x1647C022":             "指南针校准完后Yaw误差大，需要移动飞机",
	"fpv_tip_0x1647C023":             "指南针校准完后yaw不一致，需要移动飞机",
	"fpv_tip_0x1647C024":             "指南针校准完后模长不一致，需要移动飞机",
	"fpv_tip_0x1647C025":             "指南针校准完后航向不一致，需要移动飞机",
	"fpv_tip_0x1647C030":             "需要移动飞机或等待RTK-FIX",
	"fpv_tip_0x1647C031":             "指南针校准完后模长过大，需要移动飞机或等待RTK-FIX",
	"fpv_tip_0x1647C032":             "指南针校准完后Yaw误差大，需要移动飞机或等待RTK-FIX",
	"fpv_tip_0x1647C033":             "指南针校准完后yaw不一致，需要移动飞机或等待RTK-FIX",
	"fpv_tip_0x1647C034":             "指南针校准完后模长不一致，需要移动飞机或等待RTK-FIX",
	"fpv_tip_0x1647C035":             "指南针校准完后航向不一致，需要移动飞机或等待RTK-FIX",
	"fpv_tip_0x1647C040":             "指南针需要校准或等待RTK-FIX",
	"fpv_tip_0x1647C041":             "指南针模长过大，需要校准或等待RTK-FIX",
	"fpv_tip_0x1647C042":             "指南针Yaw误差大，需要校准或等待RTK-FIX",
	"fpv_tip_0x1647C043":             "指南针yaw不一致，需要校准或等待RTK-FIX",
	"fpv_tip_0x1647C044":             "指南针模长不一致，需要校准或等待RTK-FIX",
	"fpv_tip_0x1647C045":             "指南针航向不一致，需要校准或等待RTK-FIX",
	"fpv_tip_0x1647C046":             "指南针长距离未校准，需要校准或等待RTK-FIX",
	"fpv_tip_0x1647C047":             "指南针长时间未校准，需要校准或等待RTK-FIX",
	"fpv_tip_0x1647C048":             "指南针长时间异常，需要校准或等待RTK-FIX",
	"fpv_tip_0x16490001":             "tracking planner异常退出",
	"fpv_tip_0x16490002":             "tracking planner参数无效",
	"fpv_tip_0x16490003":             "垂直高度反馈异常",
	"fpv_tip_0x16490004":             "tracking planner跟随失败",
	"fpv_tip_0x16490005":             "tracking planner地图超时",
	"fpv_tip_0x16490006":             "tracking planner地图空指针",
	"fpv_tip_0x16490007":             "tracking planner避障异常退出",
	"fpv_tip_0x16490008":             "tracking planner目标安全性异常",
	"fpv_tip_0x16490009":             "tracking planner轨迹跟随异常",
	"fpv_tip_0x1649000A":             "tracking planner目标估计错误",
	"fpv_tip_0x1649000B":             "tracking planner失效",
	"fpv_tip_0x1649000C":             "tracking planner紧急刹车",
	"fpv_tip_0x1649000D":             "tracking planner平滑性异常",
	"fpv_tip_0x1649000E":             "tracking planner轨迹检测异常",
	"fpv_tip_0x1649000F":             "tracking planner刹车规划异常",
	"fpv_tip_0x16490010":             "tracking planner路径规划异常",
	"fpv_tip_0x16490011":             "tracking planner轨迹规划异常",
	"fpv_tip_0x16493000":             "全局地图：拿不到MCU的高频推送 fly_core",
	"fpv_tip_0x16493100":             "全局地图：拿不到MCU的低频推送 fly_extend",
	"fpv_tip_0x16493200":             "全局地图：拿不到有效的定位数据 fusion_output",
	"fpv_tip_0x16493300":             "全局地图：拿不到有效的标定数据",
	"fpv_tip_0x16493400":             "全局地图：拿不到有效的天空语义",
	"fpv_tip_0x16493500":             "全局地图：拿不到有效的局部地图",
	"fpv_tip_0x16493600":             "全局地图：局部地图处理失败/超时",
	"fpv_tip_0x16494000":             "APAS Planner 功能异常",
	"fpv_tip_0x16494001":             "局部地图订阅异常",
	"fpv_tip_0x16494002":             "环境限速订阅异常",
	"fpv_tip_0x16494003":             "APAS Planner 阻塞",
	"fpv_tip_0x16494004":             "APAS Follower 阻塞",
	"fpv_tip_0x16494100":             "APAS Planner 性能异常",
	"fpv_tip_0x16494101":             "APAS Planner 局部地图共享内存超时",
	"fpv_tip_0x16494102":             "APAS Planner 初始轨迹求解失败",
	"fpv_tip_0x16494103":             "APAS Planner 绕行轨迹求解失败",
	"fpv_tip_0x16494104":             "APAS Planner 触发紧急刹车",
	"fpv_tip_0x16494105":             "APAS Follower 跟踪误差过大",
	"fpv_tip_0x16494106":             "APAS Follower 控制序列碰撞检查失败",
	"fpv_tip_0x16497100":             "仿地绕行异常",
	"fpv_tip_0x16497110":             "仿地绕行任务设置异常",
	"fpv_tip_0x16497111":             "仿地绕行任务高度异常",
	"fpv_tip_0x16497112":             "仿地绕行任务速度异常",
	"fpv_tip_0x16497113":             "仿地绕行任务航线设置异常",
	"fpv_tip_0x16497120":             "仿地绕行感知输入异常",
	"fpv_tip_0x16497121":             "仿地绕行建图异常",
	"fpv_tip_0x16497122":             "仿地绕行地图超时",
	"fpv_tip_0x16497130":             "仿地绕行规划失败",
	"fpv_tip_0x16497131":             "仿地绕行搜索路径无效",
	"fpv_tip_0x16497132":             "仿地绕行搜索失败",
	"fpv_tip_0x16497133":             "仿地绕行轨迹优化失败",
	"fpv_tip_0x164A1005":             "当前为无位置保持模式，无人机无法稳定悬停，建议切换为普通模式后起飞",
	"fpv_tip_0x164A1005_sky":         "无位置保持模式下，无法稳定悬停，可能影响飞行安全，建议切换为普通模式",
	"fpv_tip_0x164C0000":             "局部地图模块异常",
	"fpv_tip_0x164C0040":             "局部地图模块接受数据异常",
	"fpv_tip_0x164C0080":             "局部地图模块数据处理异常",
	"fpv_tip_0x164C00C0":             "局部地图模块规划指令订阅异常",
	"fpv_tip_0x164C0100":             "局部地图模块订阅下视dpp异常",
	"fpv_tip_0x164C0101":             "局部地图模块订阅前视dpp异常",
	"fpv_tip_0x164C0102":             "局部地图模块订阅后视dpp异常",
	"fpv_tip_0x164C0103":             "局部地图模块订阅右视dpp异常",
	"fpv_tip_0x164C0104":             "局部地图模块订阅左视dpp异常",
	"fpv_tip_0x164C0105":             "局部地图模块订阅上视dpp异常",
	"fpv_tip_0x164C013E":             "局部地图模块订阅dpp异常",
	"fpv_tip_0x164C0140":             "局部地图模块订阅标定参数异常",
	"fpv_tip_0x164C0180":             "局部地图模块订阅相机状态异常",
	"fpv_tip_0x164C01C0":             "局部地图模块数据处理超时",
	"fpv_tip_0x164C0200":             "局部地图模块数据处理过程错误",
	"fpv_tip_0x164C0240":             "dpp模块下视视差图时序异常",
	"fpv_tip_0x164C0241":             "dpp模块前视视差图时序异常",
	"fpv_tip_0x164C0242":             "dpp模块后视视差图时序异常",
	"fpv_tip_0x164C0243":             "dpp模块右视视差图时序异常",
	"fpv_tip_0x164C0244":             "dpp模块左视视差图时序异常",
	"fpv_tip_0x164C0245":             "dpp模块上视视差图时序异常",
	"fpv_tip_0x164C027E":             "dpp模块视差图时序异常",
	"fpv_tip_0x164C0280":             "dpp模块下视视差图丢失",
	"fpv_tip_0x164C0281":             "dpp模块前视视差图丢失",
	"fpv_tip_0x164C0282":             "dpp模块后视视差图丢失",
	"fpv_tip_0x164C0283":             "dpp模块右视视差图丢失",
	"fpv_tip_0x164C0284":             "dpp模块左视视差图丢失",
	"fpv_tip_0x164C0285":             "dpp模块上视视差图丢失",
	"fpv_tip_0x164C02BE":             "dpp模块某方向固定丢失视差图",
	"fpv_tip_0x164C02C0":             "局部地图模块地图更新延迟大",
	"fpv_tip_0x164C0300":             "局部地图模块esdf计算延迟大",
	"fpv_tip_0x164C0440":             "局部地图模块订阅位姿数据异常",
	"fpv_tip_0x164C04BE":             "dpp模块订阅语义depth数据异常",
	"fpv_tip_0x164C04FE":             "dpp模块订阅sgbm数据异常",
	"fpv_tip_0x164C053E":             "dpp模块订阅语义炫光检测数据异常",
	"fpv_tip_0x164C057E":             "dpp模块订阅语义天空数据异常",
	"fpv_tip_0x164C05BE":             "dpp模块订阅语义桨叶检测数据异常",
	"fpv_tip_0x164C05FE":             "dpp模块数据处理异常",
	"fpv_tip_0x164C0600":             "语义depth模块初始化异常",
	"fpv_tip_0x164C0640":             "语义感知模块订阅主方向数据异常",
	"fpv_tip_0x164C0680":             "语义感知模块订阅图像状态数据异常",
	"fpv_tip_0x164C06C0":             "语义感知模块订阅输入图数据异常",
	"fpv_tip_0x164C0700":             "语义感知模块订阅最新帧号数据异常",
	"fpv_tip_0x164C0740":             "语义感知模块执行异常",
	"fpv_tip_0x164C0780":             "语义depth模块发布视差结果异常",
	"fpv_tip_0x164C07C0":             "语义感知模块调用nnf接口异常",
	"fpv_tip_0x164C0800":             "语义depth模块获取视差结果异常",
	"fpv_tip_0x164C0840":             "语义炫光模块初始化异常",
	"fpv_tip_0x164C0940":             "语义炫光模块发布炫光检测结果异常",
	"fpv_tip_0x164C09C0":             "语义天空模块初始化异常",
	"fpv_tip_0x164C0A40":             "语义感知模块订阅channel_manager数据异常",
	"fpv_tip_0x164C0B00":             "语义感知模块后处理异常",
	"fpv_tip_0x164C0BC0":             "语义天空模块发布分割结果异常",
	"fpv_tip_0x164C0C00":             "语义天空模块地平线计算异常",
	"fpv_tip_0x164C0C40":             "语义天空模块发布天空数量异常",
	"fpv_tip_0x164C0C80":             "语义天空模块订阅内参数据异常",
	"fpv_tip_0x164C0CC0":             "语义天空模块订阅外参数据异常",
	"fpv_tip_0x164C0D40":             "语义天空模块订阅坐标转换数据异常",
	"fpv_tip_0x164C0D80":             "语义桨叶检测模块初始化异常",
	"fpv_tip_0x164C0E80":             "语义桨叶检测模块发布分割结果异常",
	"fpv_tip_0x164C0EC0":             "marker识别模块初始化异常",
	"fpv_tip_0x164C0ED0":             "marker识别模块订阅fusion_data数据异常",
	"fpv_tip_0x164C0F40":             "marker识别模块发布marker_info数据异常",
	"fpv_tip_0x164C0F80":             "marker识别模块发布marker_status数据异常",
	"fpv_tip_0x164C0d00":             "语义天空模块订阅vio位姿数据异常",
	"fpv_tip_0x164D0000":             "环境检测异常",
	"fpv_tip_0x164D0001":             "环境检测模块初始化异常",
	"fpv_tip_0x164D0002":             "环境检测模块DBUS发布异常",
	"fpv_tip_0x164D0003":             "环境检测模块V1发布异常",
	"fpv_tip_0x164D0004":             "环境检测模块过暗标志推送异常",
	"fpv_tip_0x164D0005":             "环境检测模块进入低功耗",
	"fpv_tip_0x164D0006":             "环境检测模块退出低功耗",
	"fpv_tip_0x164D0040":             "照度检测模块异常",
	"fpv_tip_0x164D0041":             "水面检测模块异常",
	"fpv_tip_0x164D0042":             "云雾检测模块异常",
	"fpv_tip_0x164D0043":             "桨保检测模块异常",
	"fpv_tip_0x164D0044":             "脏污检测模块异常",
	"fpv_tip_0x164D0045":             "遮挡检测模块异常",
	"fpv_tip_0x164D0046":             "室内检测模块异常",
	"fpv_tip_0x164D0080":             "环境检测模块AEC数据订阅异常",
	"fpv_tip_0x164D0081":             "环境检测模块照度矩阵订阅异常",
	"fpv_tip_0x164D0082":             "环境检测模块maphex订阅异常",
	"fpv_tip_0x164D0083":             "环境检测模块视觉云雾数据异常",
	"fpv_tip_0x164D0084":             "环境检测模块VIO前端数据异常",
	"fpv_tip_0x164D0085":             "环境检测模块TOF云雾数据异常",
	"fpv_tip_0x164D0086":             "环境检测模块桨保图像订阅异常",
	"fpv_tip_0x164D0087":             "环境检测模块3DTOF数据异常",
	"fpv_tip_0x164D0088":             "环境检测模块CNN脏污数据异常",
	"fpv_tip_0x164D0089":             "环境检测模块CNN室内数据异常",
	"fpv_tip_0x164D008A":             "环境检测模块CNN水面数据异常",
	"fpv_tip_0x164D008B":             "环境检测模块CNN炫光数据异常",
	"fpv_tip_0x164D008C":             "环境检测模块FMU FLY CORE数据异常",
	"fpv_tip_0x164D008D":             "环境检测模块飞行状态数据异常",
	"fpv_tip_0x164D008E":             "环境检测模块PARSING态数据异常",
	"fpv_tip_0x164DFFF1":             "1DTOF异常",
	"fpv_tip_0x164DFFF2":             "3DtoF异常",
	"fpv_tip_0x164DFFF3":             "雷达传感器异常",
	"fpv_tip_0x164DFFF4":             "超声波传感器异常",
	"fpv_tip_0x164a0001":             "垂直高度控制异常",
	"fpv_tip_0x164a0002":             "垂直速度控制异常",
	"fpv_tip_0x164a0003":             "垂直高度反馈异常",
	"fpv_tip_0x164a0004":             "垂直速度反馈异常",
	"fpv_tip_0x164a0005":             "垂直动力饱和异常",
	"fpv_tip_0x164a1001":             "水平控制异常",
	"fpv_tip_0x164a1002":             "水平速度控制异常",
	"fpv_tip_0x164a1003":             "水平速度反馈异常",
	"fpv_tip_0x164a1004":             "姿态控制异常",
	"fpv_tip_0x16670000":             "前方未识别到线路，仿线任务已结束",
	"fpv_tip_0x16670001":             "返航点未设置，请检查刷新返航点",
	"fpv_tip_0x16670002":             "卫星定位信号差，停止仿线",
	"fpv_tip_0x16670003":             "仿线正在执行中，请停止仿线后重试",
	"fpv_tip_0x16670004":             "当前负载类型不支持仿线",
	"fpv_tip_0x16670005":             "卫星定位信号差，停止仿线",
	"fpv_tip_0x16670006":             "遥控器信号丢失，停止仿线",
	"fpv_tip_0x16670007":             "运动挡不支持仿线，请切换为 N 挡",
	"fpv_tip_0x16670101":             "障碍物距离过近，停止仿线",
	"fpv_tip_0x16670102":             "接近限低区，停止仿线",
	"fpv_tip_0x16670103":             "接近限高区，停止仿线",
	"fpv_tip_0x16670104":             "接近限远，停止仿线",
	"fpv_tip_0x16670105":             "接近限飞区，停止仿线",
	"fpv_tip_0x16670106":             "接近限高区，停止仿线",
	"fpv_tip_0x16670107":             "线路识别中，请稍等",
	"fpv_tip_0x16670108":             "线路识别中，请稍等",
	"fpv_tip_0x16670201":             "负载通信链路异常，停止仿线",
	"fpv_tip_0x16670202":             "激光雷达通信链路异常，停止仿线",
	"fpv_tip_0x16670203":             "点云数据接收异常，停止仿线",
	"fpv_tip_0x16670204":             "点云录制已停止，停止仿线",
	"fpv_tip_0x16670205":             "存储卡已满，请整理存储卡后执行仿线任务",
	"fpv_tip_0x16670301":             "仿线飞行速度设置超出范围，请检查确认",
	"fpv_tip_0x16670302":             "仿线飞行高度设置超出范围，请检查确认",
	"fpv_tip_0x16670303":             "云台俯仰角设置超出范围，请检查确认",
	"fpv_tip_0x16670304":             "已选路线识别失败，请重试",
	"fpv_tip_0x16670305":             "已选路线识别失败，请重试",
	"fpv_tip_0x16670401":             "云台回中失败，请重试",
	"fpv_tip_0x16670402":             "云台俯仰角设置失败，请重试",
	"fpv_tip_0x16670403":             "开启点云录制失败，请重试",
	"fpv_tip_0x16670501":             "已结束仿线",
	"fpv_tip_0x16670502":             "开始返航或降落，停止仿线",
	"fpv_tip_0x16670503":             "检测到客机距离过近，停止仿线",
	"fpv_tip_0x16670504":             "仿线任务已终止",
	"fpv_tip_0x16670505":             "相机画面获取失败，停止仿线",
	"fpv_tip_0x16670506":             "航向标定失败，请重试",
	"fpv_tip_0x17000001":             "主链路图传已断开",
	"fpv_tip_0x17000001_sky":         "主链路图传已断开，请尝试调整天线角度",
	"fpv_tip_0x17000002":             "遥控器信号微弱，请调整天线",
	"fpv_tip_0x17000002_sky":         "遥控器信号微弱，请调整天线",
	"fpv_tip_0x17000003":             "无人机所在位置信号干扰较强，请谨慎飞行",
	"fpv_tip_0x17000003_sky":         "无人机所在位置信号干扰较强，请谨慎飞行",
	"fpv_tip_0x17000004":             "图传信号弱，请调整遥控器朝向无人机",
	"fpv_tip_0x17000004_sky":         "图传信号弱，请调整遥控器朝向无人机",
	"fpv_tip_0x17000005":             "遥控器信号干扰较强，请注意飞行安全",
	"fpv_tip_0x17000005_sky":         "遥控器信号干扰较强，请注意飞行安全",
	"fpv_tip_0x17000006":             "国家码未更新，部分功能受限，请保证飞机卫星定位信号良好并连接 DJI Pilot 自动更新",
	"fpv_tip_0x17000006_sky":         "国家码未更新，部分功能受限",
	"fpv_tip_0x17000011":             "航电系统内存低，请重启无人机尝试恢复",
	"fpv_tip_0x17000011_sky":         "航电系统内存低，请谨慎飞行",
	"fpv_tip_0x17000013":             "遥控器信号受干扰",
	"fpv_tip_0x17000014":             "遥控器侧干扰较强，请远离其他遥控器或干扰源",
	"fpv_tip_0x17000015":             "无人机侧干扰较强，请尽快返航或飞离干扰源",
	"fpv_tip_0x17000016":             "遥控器信号微弱，请尝试调整天线",
	"fpv_tip_0x17000017":             "A控信号弱，请检查A控天线已展开并和飞机之间无遮挡",
	"fpv_tip_0x17000018":             "B控信号弱，请检查B控天线已展开并和飞机之间无遮挡",
	"fpv_tip_0x17000019":             "相机异常，请尝试重新插拔云台",
	"fpv_tip_0x1700001A":             "遥控器和中继连接信号弱，请调整遥控器或中继位置",
	"fpv_tip_0x1700001B":             "中继和无人机连接信号弱，请调整中继或无人机位置",
	"fpv_tip_0x1700001C":             "中继当前位置信号干扰较强，请谨慎飞行",
	"fpv_tip_0x17000041":             "无法获取遥控器杆量",
	"fpv_tip_0x17000071":             "航电系统负载高，请重启无人机尝试恢复",
	"fpv_tip_0x17000071_sky":         "航电系统负载高，请谨慎飞行",
	"fpv_tip_0x17000081":             "航电系统内存低，请重启无人机尝试恢复",
	"fpv_tip_0x17000081_sky":         "航电系统内存低，请谨慎飞行",
	"fpv_tip_0x17010021":             "飞控系统异常，请尽快返航",
	"fpv_tip_0x17110041":             "无法获取遥控器杆量信号",
	"fpv_tip_0x17110041_sky":         "遥控器杆量信号异常，请注意飞行安全，尽快降落",
	"fpv_tip_0x17200020":             "空吊电机堵转",
	"fpv_tip_0x17200022":             "空吊电机过载",
	"fpv_tip_0x1720002B":             "空吊电调温度过高",
	"fpv_tip_0x1720002C":             "空吊电调电压过高",
	"fpv_tip_0x1720002D":             "空吊电调电压过低",
	"fpv_tip_0x1720002F":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200030":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200031":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200032":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200033":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200034":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200035":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200036":             "空吊电调自检异常",
	"fpv_tip_0x17200037":             "空吊电调自检异常",
	"fpv_tip_0x17200038":             "空吊电调自检异常",
	"fpv_tip_0x17200039":             "空吊电调自检异常",
	"fpv_tip_0x1720003A":             "空吊电调自检异常",
	"fpv_tip_0x1720003B":             "空吊电调自检异常",
	"fpv_tip_0x1720003C":             "空吊电调或电机自检异常",
	"fpv_tip_0x1720003D":             "空吊电调或电机自检异常",
	"fpv_tip_0x1720003E":             "空吊电调或电机自检异常",
	"fpv_tip_0x1720003F":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200040":             "空吊电调自检异常",
	"fpv_tip_0x17200041":             "空吊电调自检异常",
	"fpv_tip_0x17200042":             "空吊电调自检异常",
	"fpv_tip_0x17200043":             "空吊电调自检异常",
	"fpv_tip_0x17200044":             "空吊电调电压过低",
	"fpv_tip_0x17200045":             "空吊电调电压过高",
	"fpv_tip_0x17200046":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200047":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200048":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200049":             "空吊电调自检异常",
	"fpv_tip_0x1720004A":             "空吊电调温度过高",
	"fpv_tip_0x1720004B":             "空吊电调温度过低",
	"fpv_tip_0x1720004C":             "空吊电调自检异常",
	"fpv_tip_0x1720004D":             "空吊电调自检异常",
	"fpv_tip_0x1720004E":             "空吊电调自检异常",
	"fpv_tip_0x17200055":             "空吊电机存在超温风险，不要反复带载快速收放",
	"fpv_tip_0x17200056":             "空吊电机严重超温，待电机冷却后重启使用",
	"fpv_tip_0x17200058":             "空吊电调转速、电流异常",
	"fpv_tip_0x1720005D":             "空吊电调自检异常",
	"fpv_tip_0x1720005E":             "空吊电调自检异常",
	"fpv_tip_0x17200077":             "空吊电调严重超温",
	"fpv_tip_0x17200078":             "空吊电调严重超温",
	"fpv_tip_0x17200079":             "空吊电调固件与飞机机型不匹配",
	"fpv_tip_0x1720007B":             "空吊电调自检异常",
	"fpv_tip_0x1720007C":             "空吊电调自检异常",
	"fpv_tip_0x1720007D":             "空吊电调或电机自检异常",
	"fpv_tip_0x1720007E":             "空吊电调或电机自检异常",
	"fpv_tip_0x1720007F":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200080":             "空吊电调或电机自检异常",
	"fpv_tip_0x17200083":             "空吊电调校准失败",
	"fpv_tip_0x17200200":             "空吊系统绕线卡塞或反转，请开启检修模式，整理绕线",
	"fpv_tip_0x17200201":             "空吊系统熔断器损坏，请及时更换，否则影响飞行安全",
	"fpv_tip_0x17200201_sky":         "空吊系统熔断器损坏，请及时更换，否则影响飞行安全",
	"fpv_tip_0x17200202":             "空吊系统空载放线，请带载放线",
	"fpv_tip_0x17200203":             "空吊系统电机未校准，请校准再工作",
	"fpv_tip_0x17200204":             "空吊系统三向力未校准，请校准再工作",
	"fpv_tip_0x17200205":             "空吊系统绳长未校准，请校准再工作",
	"fpv_tip_0x17200206":             "空吊系统制动器硬件已损坏，尝试重启后再工作",
	"fpv_tip_0x17200207":             "空吊系统制动器自检异常，无法起飞",
	"fpv_tip_0x17200208":             "空吊系统线绳自检异常，起飞前请确认是否反绕或不在位",
	"fpv_tip_0x17200209":             "空吊系统在位开关自检异常，请停桨手动波轮，观察空吊电机是否正常运行",
	"fpv_tip_0x1720020A":             "空吊在位开关功能异常，请检查在位开关是否粘连或损坏",
	"fpv_tip_0x17210000":             "降落伞传感器异常",
	"fpv_tip_0x17210001":             "降落伞点火器异常",
	"fpv_tip_0x17210002":             "降落伞链路异常",
	"fpv_tip_0x17210003":             "降落伞点火器通信异常",
	"fpv_tip_0x17210004":             "降落伞点火器连接异常",
	"fpv_tip_0x17210005":             "降落伞点火器电压异常",
	"fpv_tip_0x17210006":             "降落伞点火器电阻异常",
	"fpv_tip_0x17210007":             "降落伞点火器点火异常",
	"fpv_tip_0x17210008":             "降落伞IMU通信异常",
	"fpv_tip_0x17210009":             "降落伞IMU连接异常",
	"fpv_tip_0x1721000A":             "降落伞气压计通信异常",
	"fpv_tip_0x1721000B":             "降落伞机身存储异常",
	"fpv_tip_0x1721000C":             "降落伞控制链路异常",
	"fpv_tip_0x1721000D":             "降落伞融合链路异常",
	"fpv_tip_0x1721000E":             "降落伞融合速度误差大",
	"fpv_tip_0x1721000F":             "降落伞内置电池电压过低",
	"fpv_tip_0x17210010":             "降落伞内置电池温度过高",
	"fpv_tip_0x17210011":             "降落伞文件系统异常",
	"fpv_tip_0x17210013":             "降落伞已开伞",
	"fpv_tip_0x17210020":             "降落伞已经开伞，请注意周围行人安全",
	"fpv_tip_0x19000001":             "航电系统负载高，请确认是否正在进行日志回传操作，可以重启无人机尝试恢复",
	"fpv_tip_0x19000001_sky":         "航电系统负载高，请谨慎飞行",
	"fpv_tip_0x19000002":             "航电系统负载高，请确认是否正在进行日志回传操作，可以重启无人机尝试恢复",
	"fpv_tip_0x19000002_sky":         "航电系统负载高，请谨慎飞行",
	"fpv_tip_0x19000003":             "存在业务cpu loading超限",
	"fpv_tip_0x19000004":             "存在业务内存资源使用超限",
	"fpv_tip_0x19000005":             "中断频率过高",
	"fpv_tip_0x19000006":             "内存申请失败",
	"fpv_tip_0x19000007":             "rt进程cpu loading过高",
	"fpv_tip_0x19000008":             "系统内存不足",
	"fpv_tip_0x19000009":             "系统内存严重不足",
	"fpv_tip_0x1900000A":             "rcu stall异常",
	"fpv_tip_0x1900000B":             "进程长期处于D状态",
	"fpv_tip_0x1900000C":             "原子操作异常",
	"fpv_tip_0x1900000D":             "自旋锁操作异常",
	"fpv_tip_0x19000011":             "航电系统内存低，请重启无人机尝试恢复",
	"fpv_tip_0x19000011_sky":         "航电系统内存低，请谨慎飞行",
	"fpv_tip_0x19000012":             "航电系统内存低，请重启无人机尝试恢复",
	"fpv_tip_0x19000012_sky":         "航电系统内存低，请谨慎飞行",
	"fpv_tip_0x19000020":             "camera进程异常",
	"fpv_tip_0x19000021":             "相机回放进程异常",
	"fpv_tip_0x19000021_sky":         "相机回放进程异常",
	"fpv_tip_0x19000022":             "相机音频进程异常",
	"fpv_tip_0x19000022_sky":         "相机音频进程异常",
	"fpv_tip_0x19000023":             "相机麦克风进程异常",
	"fpv_tip_0x19000024":             "相机进程异常崩溃",
	"fpv_tip_0x19000025":             "相机进程发生crash，请拉日志分析",
	"fpv_tip_0x19000026":             "相机进程发生crash，请拉日志分析",
	"fpv_tip_0x19000027":             "相机进程发生crash，请拉日志分析",
	"fpv_tip_0x19000028":             "嵌入式dji_sys进程异常崩溃",
	"fpv_tip_0x19000029":             "嵌入式日志进程异常崩溃",
	"fpv_tip_0x1900002A":             "嵌入式图传进程异常崩溃",
	"fpv_tip_0x1900002B":             "嵌入式进程异常崩溃",
	"fpv_tip_0x1900002C":             "嵌入式进程dji_gateway异常崩溃",
	"fpv_tip_0x1900002D":             "进程发生crash，请拉日志分析",
	"fpv_tip_0x1900002E":             "进程发生crash，请拉日志分析",
	"fpv_tip_0x1900002F":             "相机进程发生crash，请拉日志分析",
	"fpv_tip_0x19000030":             "飞行系统感知进程异常崩溃",
	"fpv_tip_0x19000031":             "无法起飞：数据清除完成后需重启无人机",
	"fpv_tip_0x19000031_sky":         "数据清除完成后需重启无人机",
	"fpv_tip_0x19000032":             "飞行系统进程异常崩溃",
	"fpv_tip_0x19000033":             "飞行系统rtk_fusion进程异常崩溃",
	"fpv_tip_0x19000034":             "CRobot进程异常崩溃",
	"fpv_tip_0x19000035":             "进程发生crash，请拉日志分析",
	"fpv_tip_0x19000036":             "机器学习进程异常崩溃",
	"fpv_tip_0x19000037":             "进程发生crash，请拉日志分析",
	"fpv_tip_0x19000038":             "进程发生crash，请拉日志分析",
	"fpv_tip_0x19000039":             "电池进程异常崩溃",
	"fpv_tip_0x1900003A":             "进程发生crash，请拉日志分析",
	"fpv_tip_0x1900003B":             "进程发生crash，请拉日志分析",
	"fpv_tip_0x1900003C":             "激光雷达进程进程异常崩溃",
	"fpv_tip_0x1900003D":             "进程发生crash，请拉日志分析",
	"fpv_tip_0x1900003E":             "系统panic",
	"fpv_tip_0x1900003F":             "进程异常崩溃",
	"fpv_tip_0x19000041":             "无法起飞：连接USB阻飞，请拔下USB后重启无人机尝试恢复",
	"fpv_tip_0x19000041_sky":         "连接USB，请谨慎飞行并尽快降落",
	"fpv_tip_0x19000042":             "无法起飞：清除数据阻飞，请数据清除完成后重启无人机",
	"fpv_tip_0x19000042_sky":         "清除数据中，请谨慎飞行并尽快降落",
	"fpv_tip_0x19000051":             "无法起飞：固件升级中，请等待固件升级完成",
	"fpv_tip_0x19000051_sky":         "固件升级中，请谨慎飞行并尽快降落",
	"fpv_tip_0x19000060":             "Selinux 权限检查错误",
	"fpv_tip_0x19000061":             "Selinux 权限检查错误",
	"fpv_tip_0x19000070":             "文件系统异常:文件可能损坏或存储卡异常拔出，建议尝试拔插存储卡或格式化",
	"fpv_tip_0x190003ED":             "dji_wlm_slave cpu loading超限",
	"fpv_tip_0x190003EE":             "logd loading 超限",
	"fpv_tip_0x190003EF":             "dji_autoflight cpu loading 超限",
	"fpv_tip_0x190003F1":             "dji_sdrs_agent cpu loading 超限",
	"fpv_tip_0x190003F2":             "dji_lte cpu loading超限",
	"fpv_tip_0x190003F3":             "dji_wlm cpu loading超限",
	"fpv_tip_0x190003F5":             "dji_nn_server loading超限",
	"fpv_tip_0x190003F6":             "dji_ml cpu loading超限",
	"fpv_tip_0x190003F9":             "dji_media_server cpu loading 超限",
	"fpv_tip_0x190003FB":             "dji_rcam cpu loading  超限",
	"fpv_tip_0x190003FC":             "dji_camera cpu loading超限",
	"fpv_tip_0x190003FD":             "dji_perception cpu loading超限",
	"fpv_tip_0x190003FE":             "dji_blackbox loading超限",
	"fpv_tip_0x190003FF":             "dji_sys loading超限",
	"fpv_tip_0x19000400":             "SD异常",
	"fpv_tip_0x19000401":             "存储：sd卡状态异常（一直busy）",
	"fpv_tip_0x19000402":             "存储：sd卡进入hw reset并失败",
	"fpv_tip_0x19000403":             "存储：sd卡进入hw reset并成功",
	"fpv_tip_0x19000600":             "存储：访问无效的文件系统cluster",
	"fpv_tip_0x19000601":             "存储：访问无效的文件系统目录项",
	"fpv_tip_0x19000602":             "存储：访问无效的文件系统FAT表",
	"fpv_tip_0x19000603":             "存储：尝试截断零簇",
	"fpv_tip_0x19000604":             "存储：不允许分配cluster",
	"fpv_tip_0x19000605":             "存储：文件大小超过FAT表",
	"fpv_tip_0x19000606":             "存储：破损的FAT表",
	"fpv_tip_0x19000607":             "存储：新分配的虚假cluster",
	"fpv_tip_0x19000608":             "存储：请求的bmap超出范围",
	"fpv_tip_0x19000609":             "存储：非法的大小",
	"fpv_tip_0x1900060A":             "存储：文件大小超出FAT表",
	"fpv_tip_0x1900060B":             "存储：非空大小的文件起始于一个空cluster",
	"fpv_tip_0x19000700":             "文件系统异常:文件可能损坏或存储卡异常拔出，建议尝试拔插存储卡或格式化",
	"fpv_tip_0x19000701":             "磁盘扫描异常",
	"fpv_tip_0x19000702":             "文件系统检查异常",
	"fpv_tip_0x19000703":             "SD卡为非推荐卡，可能会影响使用体验",
	"fpv_tip_0x19000800":             "机身%index号风扇异常，请检查无人机风扇是否有堵转",
	"fpv_tip_0x19000801":             "相机风扇异常，请检查相机风扇是否有堵转",
	"fpv_tip_0x19000802":             "相机芯片温度过高，请待冷却后使用",
	"fpv_tip_0x19000803":             "相机温度过高，将无法录制，请待冷却后使用",
	"fpv_tip_0x19000810":             "飞机进入低功耗模式",
	"fpv_tip_0x19000811":             "air unit处于低功耗状态",
	"fpv_tip_0x19000814":             "妙算3超温预警，即将降频保护，请关闭应用或将无人机移至低温区域",
	"fpv_tip_0x19000815":             "妙算3超温告警，即将断电保护，请将无人机移至低温区域",
	"fpv_tip_0x19000816":             "妙算3负荷高，请关注系统负荷状态或关闭应用",
	"fpv_tip_0x19000C00":             "航电板与射频板连接异常，可能影响下雷达与图传",
	"fpv_tip_0x1900A001":             "飞行终止系统无法连接服务器，无法下发中止飞行指令，请检查无人机 DJI Cellular 模块",
	"fpv_tip_0x1900A002":             "飞行终止系统已触发，无人机已终止飞行或无法起飞",
	"fpv_tip_0x1900D004":             "一致性检查：一致性检查不通过",
	"fpv_tip_0x1900E001":             "日志空间不足，避免无法存储日志",
	"fpv_tip_0x1900E002":             "日志数据丢失",
	"fpv_tip_0x1900E003":             "日志数据丢失",
	"fpv_tip_0x1900E004":             "存储设备挂载异常",
	"fpv_tip_0x1900F001":             "眼镜只支持穿越摇杆飞行",
	"fpv_tip_0x19010002":             "三方负载认证失败，请重启飞机并插入DJI官方认证负载",
	"fpv_tip_0x19018001":             "系统处于工厂模式",
	"fpv_tip_0x1901C080":             "当前无人机不支持H30系列和L2同时工作，请单独挂载使用",
	"fpv_tip_0x1901C081":             "当前无人机不支持多个H30系列同时工作，请单独挂载使用",
	"fpv_tip_0x1901C082":             "当前无人机不支持多个L2同时工作，请单独挂载使用",
	"fpv_tip_0x19100041":             "视频直播网络不通",
	"fpv_tip_0x19100041_sky":         "视频直播网络不通",
	"fpv_tip_0x19100042":             "视频直播注册密码错误",
	"fpv_tip_0x19100042_sky":         "视频直播注册密码错误",
	"fpv_tip_0x19100043":             "视频直播注册超时",
	"fpv_tip_0x19100043_sky":         "视频直播注册超时",
	"fpv_tip_0x19100044":             "直播通道%d连接失败",
	"fpv_tip_0x19100044_sky":         "直播通道%d连接失败",
	"fpv_tip_0x19100045":             "直播通道%d URL参数格式错误",
	"fpv_tip_0x19100045_sky":         "直播通道%d URL参数格式错误",
	"fpv_tip_0x19100051":             "直播通道%d点播超时",
	"fpv_tip_0x19100051_sky":         "直播通道%d点播超时",
	"fpv_tip_0x19100052":             "直播通道%d点播参数错误",
	"fpv_tip_0x19100052_sky":         "直播通道%d点播参数错误",
	"fpv_tip_0x19100053":             "直播通道%d点播未响应",
	"fpv_tip_0x19100053_sky":         "直播通道%d点播未响应",
	"fpv_tip_0x19100054":             "直播通道%d长时间未有点播",
	"fpv_tip_0x19100054_sky":         "直播通道%d长时间未有点播",
	"fpv_tip_0x19100055":             "直播通道%d点播成功，无视频流数据",
	"fpv_tip_0x19100055_sky":         "直播通道%d点播成功，但未有视频流发送",
	"fpv_tip_0x19100056":             "直播通道%d直播码率异常",
	"fpv_tip_0x19100056_sky":         "直播通道%d直播码率过大或者过小",
	"fpv_tip_0x19100057":             "直播通道%d直播帧率异常",
	"fpv_tip_0x19100057_sky":         "直播通道%d直播帧率过大或者过小",
	"fpv_tip_0x19100058":             "直播通道%d视频数据发送失败",
	"fpv_tip_0x19100058_sky":         "直播通道%d视频数据发送失败",
	"fpv_tip_0x19100071":             "直播通道%d网络持续丢包",
	"fpv_tip_0x19100071_sky":         "直播通道%d网络持续丢包",
	"fpv_tip_0x19100072":             "直播通道%d网络延迟大",
	"fpv_tip_0x19100072_sky":         "直播通道%d网络延迟大",
	"fpv_tip_0x19100073":             "直播通道%d网络抖动明显",
	"fpv_tip_0x19100073_sky":         "直播通道%d网络抖动明显",
	"fpv_tip_0x19100074":             "直播通道%d出现网络拥塞",
	"fpv_tip_0x19100074_sky":         "直播通道%d出现网络拥塞",
	"fpv_tip_0x19100081":             "视频直播扩展数据回传频率异常",
	"fpv_tip_0x19100081_sky":         "视频直播扩展数据回传频率异常",
	"fpv_tip_0x19100082":             "视频直播未收到扩展数据",
	"fpv_tip_0x19100082_sky":         "视频直播未收到扩展数据",
	"fpv_tip_0x19100083":             "视频直播无法获取无人机位置",
	"fpv_tip_0x19100083_sky":         "视频直播无法获取无人机位置",
	"fpv_tip_0x19117042":             "飞机充电板未进入充电",
	"fpv_tip_0x19117100":             "电池无法加热保温",
	"fpv_tip_0x19117121":             "电池未进入保温状态",
	"fpv_tip_0x19117200":             "飞机开机发生故障",
	"fpv_tip_0x19117300":             "飞机关机失败",
	"fpv_tip_0x19117420":             "飞机电池温度过低，不能工作",
	"fpv_tip_0x19117440":             "飞机电池超45度，不能工作",
	"fpv_tip_0x19200001":             "负载系统CPU负载高，请重启无人机尝试恢复",
	"fpv_tip_0x19200001_sky":         "负载系统CPU负载高，可能影响作业",
	"fpv_tip_0x19200011":             "负载系统内存低，请重启无人机尝试恢复",
	"fpv_tip_0x19200011_sky":         "负载系统内存低，可能影响作业",
	"fpv_tip_0x19200021":             "负载系统异常，请重启无人机尝试恢复",
	"fpv_tip_0x19200021_sky":         "负载系统异常，可能影响作业",
	"fpv_tip_0x1A010004":             "视觉NN降噪异常(视觉降噪)",
	"fpv_tip_0x1A010006":             "视觉传感器图像链路异常",
	"fpv_tip_0x1A010007":             "当前处于工厂模式，请联系售后维修",
	"fpv_tip_0x1A010040":             "下视左侧视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010040_sky":         "下视左侧视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010041":             "下视右侧视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010041_sky":         "下视右侧视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010042":             "前视左侧视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010042_sky":         "前视左侧视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010043":             "前视右侧视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010043_sky":         "前视右侧视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010044":             "后视左侧视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010044_sky":         "后视左侧视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010045":             "后视右侧视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010045_sky":         "后视右侧视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010046":             "上视左侧视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010046_sky":         "上视左侧视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010047":             "上视右侧视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010047_sky":         "上视右侧视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010048":             "左视后部视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010048_sky":         "左视后部视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010049":             "左视前部视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A010049_sky":         "左视前部视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A01004A":             "右视前部视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A01004A_sky":         "右视前部视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A01004B":             "右视后部视觉传感器连接异常,请重启无人机",
	"fpv_tip_0x1A01004B_sky":         "右视后部视觉传感器连接异常,请谨慎飞行，返航或降落（%alarmid）",
	"fpv_tip_0x1A010080":             "下视左侧视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010080_sky":         "下视左侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010081":             "下视右侧视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010081_sky":         "下视右侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010082":             "前视左侧视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010082_sky":         "前视左侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010083":             "前视右侧视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010083_sky":         "前视右侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010084":             "后视左侧视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010084_sky":         "后视左侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010085":             "后视右侧视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010085_sky":         "后视右侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010086":             "上视左侧视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010086_sky":         "上视左侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010087":             "上视右侧视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010087_sky":         "上视右侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010088":             "左视后部视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010088_sky":         "右视后部视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A010089":             "左视前部视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A010089_sky":         "左视前部视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A01008A":             "右视前部视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A01008A_sky":         "右视前部视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A01008B":             "右视后部视觉传感器连接异常，请重启无人机",
	"fpv_tip_0x1A01008B_sky":         "右视后部视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C0":             "下视左侧视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C0_sky":         "下视左侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C1":             "下视右侧视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C1_sky":         "下视右侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C2":             "前视左侧视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C2_sky":         "前视左侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C3":             "前视右侧视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C3_sky":         "前视右侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C4":             "后视左侧视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C4_sky":         "后视左侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C5":             "后视右侧视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C5_sky":         "后视右侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C6":             "上视左侧视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C6_sky":         "上视左侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C7":             "上视右侧视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C7_sky":         "后视右侧视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C8":             "左视后部视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C8_sky":         "左视后部视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100C9":             "左视前部视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100C9_sky":         "左视前部视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100CA":             "右视前部视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100CA_sky":         "右视前部视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100CB":             "右视后部视觉传感器连接异常，请重启传感器",
	"fpv_tip_0x1A0100CB_sky":         "右视后部视觉传感器连接异常,请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0100F0":             "感知传感器初始化正常(仅用于工厂链路测试)",
	"fpv_tip_0x1A011340":             "视觉系统标定异常",
	"fpv_tip_0x1A011341":             "视觉系统标定异常",
	"fpv_tip_0x1A011342":             "视觉系统标定异常",
	"fpv_tip_0x1A011343":             "视觉系统标定异常",
	"fpv_tip_0x1A011344":             "视觉系统标定异常",
	"fpv_tip_0x1A011345":             "视觉系统标定异常",
	"fpv_tip_0x1A011346":             "视觉系统标定异常",
	"fpv_tip_0x1A011347":             "视觉系统标定异常",
	"fpv_tip_0x1A011348":             "视觉系统标定异常",
	"fpv_tip_0x1A011349":             "视觉系统标定异常",
	"fpv_tip_0x1A01134A":             "视觉系统标定异常",
	"fpv_tip_0x1A01134B":             "视觉系统标定异常",
	"fpv_tip_0x1A020040":             "下视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020040_sky":         "下视红外传感器连接异常,请注意控制降落着地速度，请返航或降落",
	"fpv_tip_0x1A020041":             "前视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020041_sky":         "前视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020042":             "右视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020042_sky":         "右视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020043":             "后视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020043_sky":         "后视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020044":             "左视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020044_sky":         "左视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020045":             "上视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020045_sky":         "上视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020080":             "下视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020080_sky":         "下视红外传感器连接异常,请注意控制降落着地速度，请返航或降落",
	"fpv_tip_0x1A020081":             "前视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020081_sky":         "前视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020082":             "右视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020082_sky":         "右视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020083":             "后视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020083_sky":         "后视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020084":             "左视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020084_sky":         "左视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A020085":             "上视红外传感器连接异常，请重启无人机",
	"fpv_tip_0x1A020085_sky":         "上视红外传感器连接异常，请谨慎飞行，返航或降落",
	"fpv_tip_0x1A0200C0":             "下视红外传感器连接异常",
	"fpv_tip_0x1A0200C0_sky":         "下视红外传感器连接异常，请注意控制降落着地速度，请返航或降落",
	"fpv_tip_0x1A0200C1":             "前视红外传感器连接异常",
	"fpv_tip_0x1A0200C1_sky":         "前视红外传感器连接异常，请返航或降落",
	"fpv_tip_0x1A0200C2":             "右视红外传感器连接异常",
	"fpv_tip_0x1A0200C2_sky":         "右视红外传感器连接异常，请返航或降落",
	"fpv_tip_0x1A0200C3":             "后视红外传感器连接异常",
	"fpv_tip_0x1A0200C3_sky":         "后视红外传感器连接异常，请返航或降落",
	"fpv_tip_0x1A0200C4":             "左视红外传感器连接异常",
	"fpv_tip_0x1A0200C4_sky":         "左视红外传感器连接异常，请返航或降落",
	"fpv_tip_0x1A0200C5":             "上视红外传感器连接异常",
	"fpv_tip_0x1A0200C5_sky":         "上视红外传感器连接异常，请返航或降落",
	"fpv_tip_0x1A020100":             "下视红外传感器标定异常",
	"fpv_tip_0x1A020101":             "前视红外传感器标定异常",
	"fpv_tip_0x1A020102":             "右视红外传感器标定异常",
	"fpv_tip_0x1A020103":             "后视红外传感器标定异常",
	"fpv_tip_0x1A020104":             "左视红外传感器标定异常",
	"fpv_tip_0x1A020105":             "上视红外传感器标定异常",
	"fpv_tip_0x1A020140":             "视觉红外传感器被遮挡",
	"fpv_tip_0x1A020180":             "视觉红外传感器温度过高，请尽快返航或降落，远离高温环境",
	"fpv_tip_0x1A020400":             "环境能见度差，请谨慎飞行",
	"fpv_tip_0x1A030040":             "下视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030040_sky":         "下视红外传感器连接异常,请注意控制降落着地速度,请返航或降落",
	"fpv_tip_0x1A030041":             "前视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030041_sky":         "前视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030042":             "后视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030042_sky":         "后视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030043":             "上视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030043_sky":         "上视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030044":             "左视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030044_sky":         "左视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030045":             "右视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030045_sky":         "右视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030080":             "下视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030080_sky":         "下视红外传感器连接异常,请注意控制降落着地速度，请返航或降落",
	"fpv_tip_0x1A030081":             "前视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030081_sky":         "前视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030082":             "后视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030082_sky":         "后视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030083":             "上视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030083_sky":         "上视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030084":             "左视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030084_sky":         "左视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030085":             "右视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030085_sky":         "右视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030180":             "下视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030180_sky":         "下视红外传感器连接异常,请注意控制降落着地速度，请返航或降落",
	"fpv_tip_0x1A030181":             "前视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030181_sky":         "前视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030182":             "后视红外传感器连接异常,请重启无人机",
	"fpv_tip_0x1A030182_sky":         "后视红外传感器连接异常,请返航或降落",
	"fpv_tip_0x1A030183":             "3DTOF 出图异常(上视)",
	"fpv_tip_0x1A030184":             "3DTOF 出图异常(左视)",
	"fpv_tip_0x1A030185":             "3DTOF 出图异常(右视)",
	"fpv_tip_0x1A0301C0":             "TOF3D_CALIBRATION_FAIL_0(下视)",
	"fpv_tip_0x1A0301C1":             "TOF3D_CALIBRATION_FAIL_1(前视)",
	"fpv_tip_0x1A0301C2":             "3DTOF 标定异常(后视)",
	"fpv_tip_0x1A0301C3":             "3DTOF 标定异常(上视)",
	"fpv_tip_0x1A0301C4":             "3DTOF 标定异常(左视)",
	"fpv_tip_0x1A0301C5":             "3DTOF 标定异常(右视)",
	"fpv_tip_0x1A040001":             "功耗切换异常，请重启无人机",
	"fpv_tip_0x1A050040":             "感知避障异常，请谨慎飞行",
	"fpv_tip_0x1A050041":             "感知避障异常，请谨慎飞行",
	"fpv_tip_0x1A050042":             "感知避障异常，请谨慎飞行",
	"fpv_tip_0x1A050043":             "感知避障异常，请谨慎飞行",
	"fpv_tip_0x1A060041":             "前雷达传感器连接异常",
	"fpv_tip_0x1A060042":             "后雷达传感器连接异常",
	"fpv_tip_0x1A060043":             "中心雷达传感器连接异常",
	"fpv_tip_0x1A060080":             "上雷达传感器标定异常",
	"fpv_tip_0x1A060081":             "前雷达传感器标定异常",
	"fpv_tip_0x1A060082":             "后雷达传感器标定异常",
	"fpv_tip_0x1A060083":             "中心雷达传感器标定异常",
	"fpv_tip_0x1A0600C0":             "上雷达传感器硬件故障",
	"fpv_tip_0x1A0600C1":             "前雷达传感器硬件故障",
	"fpv_tip_0x1A0600C2":             "后雷达传感器硬件故障",
	"fpv_tip_0x1A0600C3":             "中心雷达传感器硬件故障",
	"fpv_tip_0x1A080080":             "下前超声波传感器硬件故障",
	"fpv_tip_0x1A080081":             "下后超声波传感器硬件故障",
	"fpv_tip_0x1A080082":             "下左超声波传感器硬件故障",
	"fpv_tip_0x1A080083":             "下右超声波传感器硬件故障",
	"fpv_tip_0x1A080084":             "中心左超声波传感器硬件故障",
	"fpv_tip_0x1A080085":             "中心右超声波传感器硬件异常",
	"fpv_tip_0x1A090043":             "上视激光雷达连接异常，请谨慎飞行",
	"fpv_tip_0x1A090043_sky":         "上视激光雷达连接异常，请谨慎飞行并尽快返航",
	"fpv_tip_0x1A210180":             "感知系统异常，请尽快降落或谨慎飞行",
	"fpv_tip_0x1A210181":             "感知系统异常，请尽快降落或谨慎飞行",
	"fpv_tip_0x1A210182":             "感知系统异常，请尽快降落或谨慎飞行",
	"fpv_tip_0x1A210183":             "感知系统异常，请尽快降落或谨慎飞行",
	"fpv_tip_0x1A210184":             "感知系统异常，请尽快降落或谨慎飞行",
	"fpv_tip_0x1A210185":             "感知系统异常，请尽快降落或谨慎飞行",
	"fpv_tip_0x1A2101C0":             "感知系统异常，请重启无人机",
	"fpv_tip_0x1A2101C1":             "感知系统异常，请重启无人机",
	"fpv_tip_0x1A2101C2":             "感知系统异常，请重启无人机",
	"fpv_tip_0x1A2101C3":             "感知系统异常，请重启无人机",
	"fpv_tip_0x1A2101C4":             "感知系统异常，请重启无人机",
	"fpv_tip_0x1A2101C5":             "感知系统异常，请重启无人机",
	"fpv_tip_0x1A210240":             "下视视觉遮挡，避障能力下降，请谨慎飞行",
	"fpv_tip_0x1A210241":             "前视视觉遮挡，避障能力下降，请谨慎飞行",
	"fpv_tip_0x1A210242":             "后视视觉遮挡，避障能力下降，请谨慎飞行",
	"fpv_tip_0x1A210243":             "右视视觉遮挡，避障能力下降，请谨慎飞行",
	"fpv_tip_0x1A210244":             "左视视觉遮挡，避障能力下降，请谨慎飞行",
	"fpv_tip_0x1A210245":             "上视视觉遮挡，避障能力下降，请谨慎飞行",
	"fpv_tip_0x1A2102C0":             "激光雷达链路存在问题导致丢包，可能引起安全问题",
	"fpv_tip_0x1A310980":             "视觉定位系统异常",
	"fpv_tip_0x1A310981":             "视觉定位系统异常",
	"fpv_tip_0x1A420040":             "下视障碍物感知系统异常",
	"fpv_tip_0x1A420041":             "前视障碍物感知系统异常",
	"fpv_tip_0x1A420042":             "右视障碍物感知系统异常",
	"fpv_tip_0x1A420043":             "后视障碍物感知系统异常",
	"fpv_tip_0x1A420044":             "左视障碍物感知系统异常",
	"fpv_tip_0x1A420045":             "上视障碍物感知系统异常",
	"fpv_tip_0x1A420440":             "障碍物感知系统异常",
	"fpv_tip_0x1A4205C0":             "障碍物感知系统异常",
	"fpv_tip_0x1A420680":             "障碍物感知系统异常",
	"fpv_tip_0x1A420BC0":             "下视环境光过暗，下视视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420BC1":             "前视环境光过暗，前视视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420BC2":             "后视环境光过暗，后视视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420BC3":             "右视环境光过暗，右视视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420BC4":             "左视环境光过暗，左视视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420BC5":             "上视环境光过暗，上视视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420BC6":             "水平方向环境光过暗，水平方向视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420C00":             "下视环境光过亮，下方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C01":             "前视环境光过亮，前向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C02":             "后视环境光过亮，后向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C03":             "右视环境光过亮，右向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C04":             "左视环境光过亮，左向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C05":             "上视环境光过亮，上方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C06":             "水平方向环境光过亮，水平方向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C40":             "下视可能遮挡，下方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C41":             "前视可能遮挡，前向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C42":             "后视可能遮挡，后向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C43":             "右视可能遮挡，右向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C44":             "左视可能遮挡，左向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C45":             "上视可能遮挡，上方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1A420C46":             "探照灯已打开，避障失效，请谨慎飞行",
	"fpv_tip_0x1A420C47":             "探照灯已打开，避障失效，请谨慎飞行",
	"fpv_tip_0x1A420C80":             "下视可能脏污，下方视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420C81":             "前视可能脏污，前向视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420C82":             "后视可能脏污，后向视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420C83":             "右视可能脏污，右向视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420C84":             "左视可能脏污，左向视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420C85":             "上视可能脏污，上方视觉避障失效，请谨慎飞行",
	"fpv_tip_0x1A420CC0":             "无人机姿态角过大，避障失效，请谨慎飞行",
	"fpv_tip_0x1A420D00":             "无人机姿态角过大，降落保护失效，请手动降落",
	"fpv_tip_0x1A420D40":             "正飞向障碍物感知盲区，可能无法检测障碍物，请谨慎飞行",
	"fpv_tip_0x1A421B00":             "视觉避障失效，已切换为雷达避障，注意可避障区间变化",
	"fpv_tip_0x1A421B40":             "激光雷达已连接",
	"fpv_tip_0x1A421B80":             "激光雷达连接已断开",
	"fpv_tip_0x1A430680":             "环境光线差异较大，视觉系统及避障可能失效，请注意飞行安全",
	"fpv_tip_0x1A510380":             "下视传感器标定异常",
	"fpv_tip_0x1A510381":             "前视传感器标定异常",
	"fpv_tip_0x1A510382":             "后视传感器标定异常",
	"fpv_tip_0x1A510383":             "上视传感器标定异常",
	"fpv_tip_0x1A510384":             "左视传感器标定异常",
	"fpv_tip_0x1A510385":             "右视传感器标定异常",
	"fpv_tip_0x1A5103C0":             "下视传感器标定异常",
	"fpv_tip_0x1A5103C1":             "前视传感器标定异常",
	"fpv_tip_0x1A5103C2":             "后视传感器标定异常",
	"fpv_tip_0x1A5103C3":             "上视传感器标定异常",
	"fpv_tip_0x1A5103C4":             "左视传感器标定异常",
	"fpv_tip_0x1A5103C5":             "右视传感器标定异常",
	"fpv_tip_0x1A5104C0":             "下视避障感知异常，请谨慎飞行",
	"fpv_tip_0x1A5104C1":             "前视避障感知异常，请谨慎飞行",
	"fpv_tip_0x1A5104C2":             "后视避障感知异常，请谨慎飞行",
	"fpv_tip_0x1A5104C3":             "上视避障感知异常，请谨慎飞行",
	"fpv_tip_0x1A5104C4":             "左视避障感知异常，请谨慎飞行",
	"fpv_tip_0x1A5104C5":             "右视避障感知异常，请谨慎飞行",
	"fpv_tip_0x1A5104C6":             "环视避障感知异常，请谨慎飞行",
	"fpv_tip_0x1A5104C7":             "上下避障感知异常，请谨慎飞行",
	"fpv_tip_0x1A680040":             "桨叶保护罩已安装，避障功能失效，飞行性能和抗风性能降低",
	"fpv_tip_0x1A680080":             "桨叶保护罩已移除",
	"fpv_tip_0x1A6800D8":             "环境光线过暗，避障失效，请谨慎飞行",
	"fpv_tip_0x1A6800E8":             "环境光线过亮，避障失效，请谨慎飞行",
	"fpv_tip_0x1A6800F7":             "视觉传感器可能遮挡，避障行为退化，请谨慎飞行",
	"fpv_tip_0x1A6800F8":             "视觉传感器可能遮挡，避障失效，请谨慎飞行",
	"fpv_tip_0x1A680106":             "视觉传感器可能脏污，避障行为退化，请谨慎飞行",
	"fpv_tip_0x1A680107":             "视觉传感器可能脏污，避障行为退化，请谨慎飞行",
	"fpv_tip_0x1AFC0040":             "设置了ssd_en，但是没有插SSD",
	"fpv_tip_0x1AFC0080":             "存储模块丢图",
	"fpv_tip_0x1AFC0100":             "感知关键日志即将到达上限，请尽快降落拉取日志",
	"fpv_tip_0x1AFC0140":             "存在炸机日志，拉取并清理炸机日志后再进行飞测",
	"fpv_tip_0x1AFD0040":             "无法起飞：感知传感器系统异常，禁止起飞",
	"fpv_tip_0x1AFD0040_sky":         "感知传感器系统异常，请尽快返航或降落",
	"fpv_tip_0x1AFE0040":             "视觉系统负载过高，请谨慎飞行至开阔环境",
	"fpv_tip_0x1B010001":             "导航系统异常，请重启无人机",
	"fpv_tip_0x1B010002":             "当前负载不支持目标锁定功能",
	"fpv_tip_0x1B010003":             "相机模式异常，智能跟踪已关闭",
	"fpv_tip_0x1B010004":             "距离目标物体过近",
	"fpv_tip_0x1B010005":             "目标已丢失，目标锁定功能停止",
	"fpv_tip_0x1B010006":             "云台已偏离航向，请谨慎飞行",
	"fpv_tip_0x1B010007":             "目标锁定功能停止，请切换飞行挡位至P挡",
	"fpv_tip_0x1B010007_n_mode":      "目标锁定功能停止，请切换飞行挡位至N挡",
	"fpv_tip_0x1B010008":             "目标锁定功能开启失败",
	"fpv_tip_0x1B010009":             "遇到障碍物，停止环绕，请手动控制飞机远离障碍",
	"fpv_tip_0x1B01000A":             "遇到限飞区，停止环绕，请手动控制飞机远离限飞区",
	"fpv_tip_0x1B01000B":             "智能跟踪功能停止",
	"fpv_tip_0x1B01000C":             "目标物体运动过快，停止环绕",
	"fpv_tip_0x1B01000D":             "目标发生异常移动，请重新确认目标",
	"fpv_tip_0x1B01000E":             "卫星定位信号弱且视觉定位失效，无人机切换至姿态模式，请手动控制无人机",
	"fpv_tip_0x1B01000F":             "目标丢失，正在搜索中",
	"fpv_tip_0x1B010010":             "智能跟踪时只能在一定范围内控制云台",
	"fpv_tip_0x1B010011":             "智能跟踪时只能在一定范围内控制变焦",
	"fpv_tip_0x1B010401":             "相机发送数据异常，目标锁定功能停止，请稍后重试",
	"fpv_tip_0x1B010402":             "相机发送数据异常，智能跟踪功能停止，请稍后重试",
	"fpv_tip_0x1B010403":             "相机发送数据异常，智能跟踪功能停止",
	"fpv_tip_0x1B010404":             "相机数据发送异常，智能跟踪功能停止，请稍后重试",
	"fpv_tip_0x1B010405":             "相机发送数据异常，智能跟踪功能停止，请稍后重试",
	"fpv_tip_0x1B010406":             "相机发送数据异常，智能跟踪功能停止，请稍后重试",
	"fpv_tip_0x1B010407":             "相机发送数据异常，智能跟踪功能停止，请稍后重试",
	"fpv_tip_0x1B010408":             "相机发送数据异常，智能跟踪功能停止，请稍后重试",
	"fpv_tip_0x1B010801":             "APP状态异常，目标锁定功能停止，请重启APP",
	"fpv_tip_0x1B010802":             "智能跟踪功能异常，请重启APP",
	"fpv_tip_0x1B010803":             "智能跟踪功能异常，请重启APP",
	"fpv_tip_0x1B010C01":             "飞控数据发送异常，目标锁定功能停止，请重启无人机",
	"fpv_tip_0x1B010C02":             "飞控数据发送异常，智能跟踪功能停止，请重启无人机",
	"fpv_tip_0x1B010C03":             "飞控数据发送异常，智能跟踪功能停止，请重启无人机",
	"fpv_tip_0x1B011001":             "云台数据发送异常，目标锁定功能停止，请重新拔插负载",
	"fpv_tip_0x1B011002":             "云台数据发送异常，智能跟踪功能停止，请重新拔插负载",
	"fpv_tip_0x1B011003":             "云台数据发送异常，智能跟踪功能停止，请重新拔插负载",
	"fpv_tip_0x1B011801":             "遥控器数据发送异常，目标锁定功能停止，请重启遥控器",
	"fpv_tip_0x1B011802":             "遥控器数据发送异常，目标锁定功能停止，请检查遥控器与无人机的连接状况",
	"fpv_tip_0x1B030001":             "遇到障碍物，自动返航停止，请手动控制飞机远离障碍",
	"fpv_tip_0x1B030002":             "遇到限飞区，自动返航停止，请手动控制飞机远离限飞区",
	"fpv_tip_0x1B030003":             "自动返航异常，请切换至手动飞行",
	"fpv_tip_0x1B030004":             "无人机失联，开始返航",
	"fpv_tip_0x1B030005":             "自动返航避障功能启动失败，请切换至手动返航",
	"fpv_tip_0x1B030006":             "遇到障碍物或环境逆光，请按遥控器上暂停键退出自动返航，进行手动返航",
	"fpv_tip_0x1B030007":             "检测到打杆下降，已退出自动返航",
	"fpv_tip_0x1B030008":             "检测到打杆后退，已退出自动返航",
	"fpv_tip_0x1B03000D":             "返航过程中定位不佳，无法继续返航。请手动控制飞机返航",
	"fpv_tip_0x1B030010":             "临近或已处于夜晚，请提前设置好返航高度",
	"fpv_tip_0x1B030011":             "智能返航失效，将执行设定高度返航",
	"fpv_tip_0x1B030012":             "返航加速中，电量消耗加速",
	"fpv_tip_0x1B030013":             "返航过程中用户反向打杆，退出返航。",
	"fpv_tip_0x1B030014":             "电量不足请尽快返航",
	"fpv_tip_0x1B030015":             "返航点在限高区内，将降低返航高度",
	"fpv_tip_0x1B030016":             "环境复杂，返航中断，请手动返航。",
	"fpv_tip_0x1B030017":             "电量不足以完成返航，可能提前降落，请注意选择备降位置",
	"fpv_tip_0x1B030018":             "限高过低影响返航飞行安全",
	"fpv_tip_0x1B030019":             "无人机已偏离航线，航线安全返航失效",
	"fpv_tip_0x1B03001A":             "视觉定位失效无法继续返航，请确认下方安全后手动降落",
	"fpv_tip_0x1B03002B":             "返航中触发避障,请注意剩余电量,及时接管",
	"fpv_tip_0x1B03002C":             "返航路线变化，返航剩余电量不准。拉取log并提单给规划同事分析",
	"fpv_tip_0x1B03002D":             "环境风速变化大，返航剩余电量不准。拉取log给规划同事分析，无需提单",
	"fpv_tip_0x1B03002E":             "返航速度不稳定，返航剩余电量不准。拉取log并提单给规划同事分析",
	"fpv_tip_0x1B03002F":             "无人机功率异常，返航剩余电量不准。拉取log并提单给规划同事分析",
	"fpv_tip_0x1B030030":             "动态返航已完成，自动退出返航模式，请注意接管无人机",
	"fpv_tip_0x1B030031":             "电池能量估计异常，返航剩余电量不准。拉取log并提单给电池同事分析",
	"fpv_tip_0x1B030032":             "电池能量估计偏差过大，返航剩余电量不准。拉取log并提单给电池同事分析",
	"fpv_tip_0x1B030033":             "无人机功率偏大，请注意剩余电量，安全飞行",
	"fpv_tip_0x1B030035":             "激光避障失效，请注意返航路径安全性",
	"fpv_tip_0x1B030036":             "自动返航未就绪，请手动刷新返航点或卫星定位良好后手动飞行水平距离50米以上",
	"fpv_tip_0x1B030C01":             "",
	"fpv_tip_0x1B030C02":             "卫星定位信号弱，自动返航精度低，建议切换至手动返航",
	"fpv_tip_0x1B040001":             "当前负载不支持精准拍照",
	"fpv_tip_0x1B040002":             "精准拍照失败，执行普通拍照",
	"fpv_tip_0x1B040003":             "精准复拍失败，执行普通拍照",
	"fpv_tip_0x1B040004":             "未挂载相机，精准拍照失败",
	"fpv_tip_0x1B040401":             "相机变焦超时，精准复拍失败，请稍后重试",
	"fpv_tip_0x1B040402":             "切换相机拍照模式失败，精准复拍失败，请稍后重试",
	"fpv_tip_0x1B040403":             "相机变焦超时，精准复拍失败，请稍后重试",
	"fpv_tip_0x1B040801":             "精准拍照示意图未找到，请重新上传航线",
	"fpv_tip_0x1B040802":             "目标框参数错误，请调整目标框",
	"fpv_tip_0x1B041001":             "云台异常，精准拍照失败，请重新拔插负载",
	"fpv_tip_0x1B050010":             "无法获取遥控器定位信息，无法刷新动态返航点",
	"fpv_tip_0x1B050011":             "重新获取遥控器定位信息，继续刷新动态返航点",
	"fpv_tip_0x1B050012":             "当前遥控器定位精度低，无法刷新动态返航点",
	"fpv_tip_0x1B050013":             "当前遥控器进入禁飞区，暂停刷新动态返航点",
	"fpv_tip_0x1B050014":             "当前遥控器离开禁飞区，继续刷新动态返航点",
	"fpv_tip_0x1B050015":             "当前遥控器距离无人机过远，暂停刷新返航点",
	"fpv_tip_0x1B050016":             "无法获取遥控器定位信息，无法刷新动态返航点",
	"fpv_tip_0x1B050017":             "当前遥控器定位精度低，无法刷新动态返航点",
	"fpv_tip_0x1B060001":             "飞行速度受限，固件需要升级",
	"fpv_tip_0x1B060005":             "当前飞行方向避障失效，触发紧急刹车，将摇杆恢复到中间位置再拨杆，可重新控制飞机",
	"fpv_tip_0x1B070001":             "货物超重，请减少货物或修改飞行计划",
	"fpv_tip_0x1B070001_sky":         "货物超重，请尽快降落，减少货物",
	"fpv_tip_0x1B070002":             "货物重心偏移，请重新摆放货物",
	"fpv_tip_0x1B070002_sky":         "货物重心偏移，请尽快降落，重新摆放货物",
	"fpv_tip_0x1B070003":             "货箱称重传感器异常",
	"fpv_tip_0x1B070004":             "称重异常，请重新校准货箱称重传感器",
	"fpv_tip_0x1B070005":             "称重异常，请重新校准货箱称重传感器",
	"fpv_tip_0x1B070006":             "货物超重，请减少货物或修改飞行计划",
	"fpv_tip_0x1B070006_sky":         "货物超重，请尽快降落，减少货物",
	"fpv_tip_0x1B070008":             "空吊称重传感器异常",
	"fpv_tip_0x1B070009":             "称重异常，请重新校准空吊称重传感器",
	"fpv_tip_0x1B07000A":             "称重异常，请重新校准空吊称重传感器",
	"fpv_tip_0x1B07000B":             "空吊摆角过大，请谨慎飞行",
	"fpv_tip_0x1B07000C":             "空吊绳索断裂，请检查吊挂货物",
	"fpv_tip_0x1B07000D":             "空吊电机堵转，请检查吊挂货物",
	"fpv_tip_0x1B07000E":             "空吊电机温度过高，请检查货物是否超重",
	"fpv_tip_0x1B080001":             "Remote ID 播报异常，无法获取遥控器位置",
	"fpv_tip_0x1B080002":             "Remote ID 模块异常，请联系售后服务",
	"fpv_tip_0x1B090001":             "停止目标追踪",
	"fpv_tip_0x1B090002":             "停止智能跟踪",
	"fpv_tip_0x1B090003":             "停止智能跟踪",
	"fpv_tip_0x1B092C01":             "目标识别异常",
	"fpv_tip_0x1B092C02":             "目标识别异常",
	"fpv_tip_0x1B092C03":             "目标识别异常",
	"fpv_tip_0x1B092C04":             "目标识别异常",
	"fpv_tip_0x1B092C05":             "目标识别异常",
	"fpv_tip_0x1B092C06":             "目标识别异常",
	"fpv_tip_0x1B092C07":             "目标识别异常",
	"fpv_tip_0x1B092C08":             "目标识别异常",
	"fpv_tip_0x1B092C09":             "目标识别异常",
	"fpv_tip_0x1B092C0A":             "目标识别异常",
	"fpv_tip_0x1B092C0B":             "目标识别异常",
	"fpv_tip_0x1B092C0C":             "目标识别异常",
	"fpv_tip_0x1B092C0D":             "目标识别异常",
	"fpv_tip_0x1B092C0E":             "目标识别异常",
	"fpv_tip_0x1B092C0F":             "目标识别异常",
	"fpv_tip_0x1B092C10":             "目标识别异常",
	"fpv_tip_0x1B092C11":             "目标识别异常",
	"fpv_tip_0x1B092C12":             "目标识别异常",
	"fpv_tip_0x1B092C13":             "目标识别异常",
	"fpv_tip_0x1B092C14":             "目标识别异常",
	"fpv_tip_0x1B092C15":             "目标识别异常",
	"fpv_tip_0x1B092C16":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C17":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C18":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C19":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C1A":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C1B":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C1C":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C1D":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C1E":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C1F":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C20":             "智能追踪功能启动失败，请确保框选有效目标",
	"fpv_tip_0x1B092C21":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C22":             "智能追踪功能启动失败，请缩小目标区域",
	"fpv_tip_0x1B092C23":             "智能追踪功能启动失败，请扩大目标区域",
	"fpv_tip_0x1B092C24":             "智能跟踪功能启动失败，请确保框选有效目标",
	"fpv_tip_0x1B092C25":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B092C26":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093001":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093002":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093003":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093004":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093005":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093006":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093007":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093008":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093009":             "智能跟踪功能启动失败，请扩大目标区域",
	"fpv_tip_0x1B09300A":             "智能跟踪功能启动失败，请确保框选有效目标",
	"fpv_tip_0x1B09300B":             "目标已丢失，目标锁定功能已退出",
	"fpv_tip_0x1B09300C":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B09300D":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B09300E":             "相机参数被修改，目标锁定功能退出",
	"fpv_tip_0x1B09300F":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093010":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093011":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093012":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093013":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093014":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093015":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093016":             "目标已丢失，智能跟踪功能已退出",
	"fpv_tip_0x1B093017":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B093018":             "目标距离过远，目标锁定功能已退出",
	"fpv_tip_0x1B093019":             "相机参数被修改，智能跟踪功能退出",
	"fpv_tip_0x1B09301A":             "智能跟踪功能启动失败，请重试",
	"fpv_tip_0x1B0A0001":             "卫星定位信号弱，地理感知功能降级，请谨慎飞行",
	"fpv_tip_0x1B0A0002":             "动态安全数据加载失败，地理感知功能降级，建议更新飞行安全数据库",
	"fpv_tip_0x1B0A0003":             "动态安全数据查询失败，地理感知功能降级，建议更新飞行安全数据库",
	"fpv_tip_0x1B0A0004":             "限飞数据库在当前区域数据密度较高，可能会出现无人机解析不完整的情况，建议更新飞行安全数据库。",
	"fpv_tip_0x1B0B0001":             "无法起飞：未获取无人机位置，请在开阔区域起飞",
	"fpv_tip_0x1B0B0001_sky":         "未获取无人机位置，请在开阔区域飞行",
	"fpv_tip_0x1B0B0002":             "无法起飞：请在地理鸟笼区域内起飞",
	"fpv_tip_0x1B0B0002_sky":         "请在地理鸟笼区域内飞行",
	"fpv_tip_0x1B0B0003":             "无法起飞：无人机无地理鸟笼数据",
	"fpv_tip_0x1B0B0003_sky":         "无人机无地理鸟笼数据，请尽快返航",
	"fpv_tip_0x1B0B0004":             "无法起飞：地理鸟笼数据异常",
	"fpv_tip_0x1B0B0004_sky":         "飞行鸟笼数据异常，请尽快返航",
	"fpv_tip_0x1B0B0005":             "无法起飞：地理鸟笼功能异常",
	"fpv_tip_0x1B0B0005_sky":         "地理鸟笼功能异常，请尽快返航",
	"fpv_tip_0x1B0B0006":             "无法自动飞行，请手动操控返回作业区域",
	"fpv_tip_0x1B0B0006_sky":         "无法自动飞行，请手动操控返回作业区域",
	"fpv_tip_0x1B0B0007":             "返航点需设置在地理鸟笼作业区域内",
	"fpv_tip_0x1B0B0007_sky":         "返航点需设置在地理鸟笼作业区域内",
	"fpv_tip_0x1B0B0008":             "无法自动返航，请手动操控返回作业区域",
	"fpv_tip_0x1B0B0008_sky":         "无法自动返航，请手动操控返回作业区域",
	"fpv_tip_0x1B0C0001":             "禁降区数据库不可用，请重新导入禁降区数据库文件",
	"fpv_tip_0x1B0C0001_sky":         "禁降区数据库不可用，请重新导入禁降区数据库文件",
	"fpv_tip_0x1B0C0002":             "无人机处于禁降区内，10s 后将自动飞离禁降区，可持续向下打杆退出",
	"fpv_tip_0x1B0C0002_sky":         "无人机处于禁降区内，10s 后将自动飞离禁降区，可持续向下打杆退出",
	"fpv_tip_0x1B0C0003":             "正在飞离禁降区并返航",
	"fpv_tip_0x1B0C0003_sky":         "正在飞离禁降区并返航",
	"fpv_tip_0x1B0C0004":             "正在飞离禁降区，可持续向下打杆退出",
	"fpv_tip_0x1B0C0004_sky":         "正在飞离禁降区，可持续向下打杆退出",
	"fpv_tip_0x1B0C0005":             "自动飞离禁降区功能受限，无人机即将原地降落。请确认降落环境是否安全或打杆接管无人机",
	"fpv_tip_0x1B0C0005_sky":         "自动飞离禁降区功能受限，无人机即将原地降落。请确认降落环境是否安全或打杆接管无人机",
	"fpv_tip_0x1B0C0006":             "已成功飞离禁降区，无人机即将降落",
	"fpv_tip_0x1B0C0006_sky":         "已成功飞离禁降区，无人机即将降落",
	"fpv_tip_0x1B0C0007":             "已退出自动飞离禁降区功能，无人机即将原地降落，请确认降落环境是否安全",
	"fpv_tip_0x1B0C0007_sky":         "已退出自动飞离禁降区功能，无人机即将原地降落，请确认降落环境是否安全",
	"fpv_tip_0x1B100000":             "目标丢失，跟踪停止",
	"fpv_tip_0x1B100001":             "目标较小，容易丢失",
	"fpv_tip_0x1B300001":             "无法获得货箱数据，请检查安装情况",
	"fpv_tip_0x1B300002":             "空吊设备通信异常，请检查设备连接状况",
	"fpv_tip_0x1B300003":             "货物过重，请减少货物和修改飞行计划",
	"fpv_tip_0x1B300004":             "货物超重，请终止飞行计划",
	"fpv_tip_0x1B300005":             "货物偏离中心，请把货物摆放在货箱中心",
	"fpv_tip_0x1B300006":             "货物严重偏离中心，请把货物摆放在货箱中心",
	"fpv_tip_0x1B300007":             "货箱晃动，请绑好货箱或固定货物在货箱中的位置",
	"fpv_tip_0x1B300008":             "货箱晃动严重，请绑好货箱或固定货物在货箱中的位置",
	"fpv_tip_0x1B300009":             "挂载货物过重，请减少货物和修改飞行计划",
	"fpv_tip_0x1B30000A":             "挂载货物超重，请终止飞行计划",
	"fpv_tip_0x1B30000B":             "挂载货物小幅摆动，请注意飞行安全",
	"fpv_tip_0x1B30000C":             "挂载货物大幅摆动，请注意飞行安全",
	"fpv_tip_0x1B30000D":             "挂载货物接近障碍物，请注意飞行安全",
	"fpv_tip_0x1B30000E":             "挂载货物非常接近障碍物，请注意飞行安全",
	"fpv_tip_0x1B30000F":             "飞行过程中禁止收放空吊线缆，请悬停后再进行线缆收放",
	"fpv_tip_0x1B300010":             "货物摆动大，禁止空吊收绳，请等待摆动减小后再收绳",
	"fpv_tip_0x1B300011":             "已自动校准空吊称重",
	"fpv_tip_0x1B300011_sky":         "已自动校准空吊称重",
	"fpv_tip_0x1B310001":             "当前电量不够完成航线任务，请更改飞行计划",
	"fpv_tip_0x1B310001_sky":         "当前电量不够完成航线任务，请更改飞行计划",
	"fpv_tip_0x1B310002":             "当前电量不够前往备降点，请尽快降落",
	"fpv_tip_0x1B310002_sky":         "当前电量不够前往备降点，请尽快降落",
	"fpv_tip_0x1B310003":             "当前载重超过动力系统载重限制，可能导致电机超温，请降低载重后起飞",
	"fpv_tip_0x1B310003_sky":         "当前载重超过动力系统载重限制，可能导致电机超温，请尽快返航或降落",
	"fpv_tip_0x1B310004":             "当前载重超过电池限制，可能导致电池损坏，请降低载重后起飞",
	"fpv_tip_0x1B310004_sky":         "当前载重超过电池限制，可能导致电池损坏，请尽快返航或降落",
	"fpv_tip_0x1B310005":             "雷达模块高度探测失效，安全飞行高度发生变更，请注意飞行安全",
	"fpv_tip_0x1B310006":             "雷达模块高度探测恢复正常，安全飞行高度发生变更，请注意飞行安全",
	"fpv_tip_0x1B310007":             "无人机处于地形数据区域，请在安全飞行高度以下飞行",
	"fpv_tip_0x1B310007_sky":         "无人机进入地形数据区域，安全飞行高度发生变更",
	"fpv_tip_0x1B310008":             "无人机不在地形数据区域，请应用地形数据",
	"fpv_tip_0x1B310008_sky":         "无人机飞出地形数据区域，安全飞行高度发生变更，请注意飞行安全",
	"fpv_tip_0x1B310009":             "无人机当前飞行高度超过安全飞行高度，请在安全飞行高度以下飞行",
	"fpv_tip_0x1B31000A":             "空吊未收到位，降落存在打桨风险，请收绳后再降落",
	"fpv_tip_0x1B31000B":             "无人机急停，请注意飞行安全",
	"fpv_tip_0x1B31000B_sky":         "无人机急停，请注意飞行安全",
	"fpv_tip_0x1B310011":             "电池温度低，请加热后起飞",
	"fpv_tip_0x1B310011_sky":         "电池温度低，请谨慎飞行",
	"fpv_tip_0x1BA10000":             "当前在杆量保持中",
	"fpv_tip_0x1BA10001":             "未起飞，已终止定速巡航",
	"fpv_tip_0x1BA10002":             "飞行模式切换中，已终止定速巡航",
	"fpv_tip_0x1BA10003":             "紧急刹车中，已终止定速巡航",
	"fpv_tip_0x1BA10004":             "飞机速度受到限制，已终止定速巡航",
	"fpv_tip_0x1BA10005":             "飞机接近最大限制高度或最远限制距离，已终止定速巡航",
	"fpv_tip_0x1BA10006":             "当前飞行模式不支持定速巡航，已终止定速巡航",
	"fpv_tip_0x1BA10007":             "紧急刹车中，已终止杆量保持",
	"fpv_tip_0x1BA10008":             "遥控器信号丢失，已终止定速巡航",
	"fpv_tip_0x1BA10009":             "到达航线终点，定速巡航已退出",
	"fpv_tip_0x1C000001":             "X号云台相机过热，请等待降温后再使用",
	"fpv_tip_0x1C000103":             "相机芯片温度高（%alarmid），请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C000103_sky":         "相机芯片温度高（%alarmid），请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C000104":             "相机芯片温度过高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C000104_sky":         "相机芯片温度过高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C000201":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000202":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000203":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000204":             "相机图像传感器温度过高",
	"fpv_tip_0x1C000207":             "请检查FPV接线是否松脱或腐蚀",
	"fpv_tip_0x1C00020A":             "图像传感器标定数据异常，请重启",
	"fpv_tip_0x1C000303":             "镜头连接异常，请检查镜头是否正确安装",
	"fpv_tip_0x1C000305":             "快门使用次数达到十万次",
	"fpv_tip_0x1C000306":             "非DJI ENTERPRISE镜头",
	"fpv_tip_0x1C000310":             "相机镜头未连接",
	"fpv_tip_0x1C000311":             "相机镜头故障，请尝试重新安装镜头",
	"fpv_tip_0x1C000312":             "相机镜头故障，请尝试重新安装镜头",
	"fpv_tip_0x1C000313":             "相机镜头故障，请尝试重新安装镜头",
	"fpv_tip_0x1C000314":             "相机镜头故障，请尝试重新安装镜头",
	"fpv_tip_0x1C000317":             "镜头标定数据异常，请插拔镜头或重启",
	"fpv_tip_0x1C000401":             "无效存储卡，请更换存储卡（%alarmid）",
	"fpv_tip_0x1C000402":             "存储卡为慢速卡，请更换",
	"fpv_tip_0x1C000403":             "存储卡异常，请更换存储卡（%alarmid）",
	"fpv_tip_0x1C000404":             "无存储卡",
	"fpv_tip_0x1C000405":             "请确认存储卡读写属性",
	"fpv_tip_0x1C000407":             "存储卡正在格式化，请等待",
	"fpv_tip_0x1C000408":             "不支持该存储卡文件系统，请格式化后使用",
	"fpv_tip_0x1C00040A":             "存储卡已满，请清除内存",
	"fpv_tip_0x1C00040B":             "文件索引超出最大限制，请格式化SD卡",
	"fpv_tip_0x1C00040C":             "存储卡正在初始化，请等待",
	"fpv_tip_0x1C00040D":             "存储卡异常，请格式化存储卡后使用",
	"fpv_tip_0x1C00040E":             "存储卡写入速度过慢",
	"fpv_tip_0x1C00040F":             "存储卡写入速度过慢",
	"fpv_tip_0x1C000411":             "SD卡写入异常，请更换",
	"fpv_tip_0x1C000412":             "存储卡加密待验证，请输入密码验证后使用",
	"fpv_tip_0x1C000414":             "SD卡存储碎片化程度严重，请备份素材后格式化",
	"fpv_tip_0x1C000602":             "相机拍照环境过暗",
	"fpv_tip_0x1C000603":             "照片存在欠曝风险",
	"fpv_tip_0x1C000604":             "照片存在过曝风险",
	"fpv_tip_0x1C000901":             "PPS对时信号异常",
	"fpv_tip_0x1C000902":             "相机时间同步异常",
	"fpv_tip_0x1C000903":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000904":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000905":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000906":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000907":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000908":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000909":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C00090A":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000A01":             "录像丢帧，建议升级固件",
	"fpv_tip_0x1C000A02":             "录像丢帧，建议升级固件",
	"fpv_tip_0x1C000B01":             "相机硬件异常，请尝试重新启动",
	"fpv_tip_0x1C000D01":             "对焦失败",
	"fpv_tip_0x1C000D02":             "镜头未进行无穷远标定",
	"fpv_tip_0x1C000D03":             "无穷远标定数据过期，请重新标定",
	"fpv_tip_0x1C000D04":             "无穷远标定数据过期，请重新标定",
	"fpv_tip_0x1C000E01":             "镜头与相机内参检校不匹配",
	"fpv_tip_0x1C000E02":             "内参检校参数过旧",
	"fpv_tip_0x1C001001":             "DJI PROSSD存储碎片化即将满，建议格式化",
	"fpv_tip_0x1C001002":             "DJI PROSSD存储碎片化已满，建议格式化",
	"fpv_tip_0x1C001003":             "获取 DJI PROSSD 存储信息失败，避免存储风险，请备份素材后格式化",
	"fpv_tip_0x1C001101":             "DJI PROSSD读写量即将到达上限，建议更换",
	"fpv_tip_0x1C001102":             "DJI PROSSD读写量已达上限，建议更换",
	"fpv_tip_0x1C001103":             "获取 DJI PROSSD 存储信息失败，避免存储风险，请备份素材后格式化",
	"fpv_tip_0x1C001201":             "请使用DJI PROSSD以正常使用录像功能",
	"fpv_tip_0x1C001301":             "视频写入失败，正在停止录像",
	"fpv_tip_0x1C001302":             "DJI PROSSD写入速度慢，正在停止录像",
	"fpv_tip_0x1C001303":             "DJI PROSSD剩余空间不足，正在停止录像",
	"fpv_tip_0x1C001303_special":     "存储卡剩余空间不足，正在停止录像",
	"fpv_tip_0x1C001306":             "系统业务繁忙，禁止录像",
	"fpv_tip_0x1C001307":             "业务繁忙解除，允许录像",
	"fpv_tip_0x1C001401":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001402":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001403":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001404":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001405":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001406":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001407":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001408":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001409":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C00140A":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C00140E":             "相机异常，建议重启无人机",
	"fpv_tip_0x1C00140F":             "相机信号链路不稳定，请尝试重新拔插云台",
	"fpv_tip_0x1C001501":             "光照传感器异常，请联系售后服务",
	"fpv_tip_0x1C001502":             "光照传感器异常，请联系售后服务",
	"fpv_tip_0x1C001503":             "光照传感器标定数据异常，请联系售后服务",
	"fpv_tip_0x1C001603":             "DJI PROSSD存储不可挂载，请重新拔插",
	"fpv_tip_0x1C001604":             "无存储卡",
	"fpv_tip_0x1C001605":             "SSD文件系统异常，请重新拔插SSD，若无效请备份后格式化",
	"fpv_tip_0x1C001606":             "DJI PROSSD存储未格式化",
	"fpv_tip_0x1C001607":             "DJI PROSSD正在格式化",
	"fpv_tip_0x1C001608":             "DJI PROSSD文件系统不支持",
	"fpv_tip_0x1C001609":             "DJI PROSSD正在刷新",
	"fpv_tip_0x1C00160A":             "DJI PROSSD存储空间已满",
	"fpv_tip_0x1C00160B":             "DJI PROSSD文件序号溢出，请删除多余素材",
	"fpv_tip_0x1C00160C":             "DJI PROSSD正在初始化",
	"fpv_tip_0x1C00160D":             "DJI PROSSD建议重新格式化",
	"fpv_tip_0x1C00160E":             "DJI PROSSD存储文件正在修复中",
	"fpv_tip_0x1C00160F":             "DJI PROSSD存储速度慢，建议更换",
	"fpv_tip_0x1C001701":             "定时拍间隔过小，无法开始拍摄",
	"fpv_tip_0x1C001702":             "定时拍间隔过小，无法切换相机",
	"fpv_tip_0x1C001703":             "照片格式不同，无法切换相机",
	"fpv_tip_0x1C001801":             "TIMECODE错误，请重启无人机",
	"fpv_tip_0x1C001901":             "ProRes RAW编码格式未授权，请到官网购买使用授权",
	"fpv_tip_0x1C001902":             "CinemaDNG编码格式未授权，请到官网购买使用授权",
	"fpv_tip_0x1C001903":             "ProRes RAW编码格式未授权，请到官网购买使用授权",
	"fpv_tip_0x1C001904":             "CinemaDNG编码格式未授权，请到官网购买使用授权",
	"fpv_tip_0x1C001A01":             "DJI PROSSD写入速度慢，建议重新启动无人机",
	"fpv_tip_0x1C001B01":             "云台接口已达预设使用次数",
	"fpv_tip_0x1C001E04":             "系统业务繁忙，禁止拍照",
	"fpv_tip_0x1C001E05":             "业务繁忙解除，允许拍照",
	"fpv_tip_0x1C001F01":             "系统业务繁忙，禁止变焦",
	"fpv_tip_0x1C001F02":             "业务繁忙解除，允许变焦",
	"fpv_tip_0x1C002001":             "系统业务繁忙，禁止切换相机",
	"fpv_tip_0x1C002002":             "业务繁忙解除，允许切换相机",
	"fpv_tip_0x1C002004":             "H20 相机图像传感器 芯片异常",
	"fpv_tip_0x1C002101":             "相机接收激光雷达数据超时，请尝试重启无人机",
	"fpv_tip_0x1C002102":             "点云数据丢帧，最终点云模型可能不完整，请重启无人机",
	"fpv_tip_0x1C002103":             "激光雷达可能被遮挡，或者不在量程内",
	"fpv_tip_0x1C002104":             "激光测距采样失败，请稍后重试或重启无人机",
	"fpv_tip_0x1C002105":             "相机温度过高，激光雷达电源暂时关闭",
	"fpv_tip_0x1C002106":             "相机温度过高，激光雷达功能暂时不可用",
	"fpv_tip_0x1C002107":             "相机温度过高，红外补光灯功能暂时不可用",
	"fpv_tip_0x1C002201":             "相机SSD存在旧版本媒体文件，请及时备份避免丢失",
	"fpv_tip_0x1C002301":             "EIS的图像帧的vsync为0",
	"fpv_tip_0x1C002302":             "EIS图像帧的vsync顺序错误",
	"fpv_tip_0x1C002303":             "EIS获取不到IMU数据",
	"fpv_tip_0x1C002304":             "EIS获取IMU数据不足",
	"fpv_tip_0x1C002305":             "EIS获取IMU数据过多",
	"fpv_tip_0x1C002306":             "EIS获取IMU数据失败",
	"fpv_tip_0x1C002307":             "EIS获取到的IMU数据vsync顺序错误",
	"fpv_tip_0x1C002308":             "EIS的IMU target超出约束范围",
	"fpv_tip_0x1C002309":             "EIS迭代求解结果超出输入图像的边界",
	"fpv_tip_0x1C00230A":             "EIS的IMU对齐范围超出拿到的IMU数据的位置",
	"fpv_tip_0x1C00230B":             "EIS帧间时间间隔有问题，vsync不稳定",
	"fpv_tip_0x1C00230C":             "当前帧通过step模式匹配到了vsync信息",
	"fpv_tip_0x1C00230D":             "当前帧匹配到无效的vsync信息",
	"fpv_tip_0x1C00230E":             "当前帧匹配不到vsync信息",
	"fpv_tip_0x1C002501":             "相机超温预警，自动关闭镜头除雾、激光雷达功能",
	"fpv_tip_0x1C002502":             "相机温度过高，自动关闭镜头除雾、激光雷达电源",
	"fpv_tip_0x1C002502_sky":         "相机温度过高，自动关闭红外补光灯（仅红外版），镜头除雾功能",
	"fpv_tip_0x1C002503":             "相机温度过高，镜头除雾功能暂时不可用",
	"fpv_tip_0x1C002701":             "定时拍间隔过短，照片将无法记录AI识别信息",
	"fpv_tip_0x1C002702":             "全景照片将无法记录AI识别信息",
	"fpv_tip_0x1C002703":             "8K 照片将无法记录AI识别信息",
	"fpv_tip_0x1C003001":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C004001":             "无效存储卡，请更换存储卡",
	"fpv_tip_0x1C004002":             "存储卡为慢速卡，请更换快速存储卡",
	"fpv_tip_0x1C004003":             "存储卡异常，请更换存储卡",
	"fpv_tip_0x1C004004":             "无存储卡",
	"fpv_tip_0x1C100001":             "%component_index号云台相机过热，请等待降温后再使用",
	"fpv_tip_0x1C100101":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C100102":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C100103":             "相机芯片温度高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C100103_sky":         "相机芯片温度高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C100104":             "相机芯片温度过高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C100104_sky":         "相机芯片温度过高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C100105":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C100201":             "H20 相机图像传感器 芯片异常",
	"fpv_tip_0x1C100202":             "H20 相机图像传感器 芯片异常",
	"fpv_tip_0x1C100203":             "相机芯片温度过高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C100203_sky":         "相机芯片温度过高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C100204":             "H20 相机图像传感器 芯片异常",
	"fpv_tip_0x1C100205":             "FPV镜头标定异常，请重新标定",
	"fpv_tip_0x1C100301":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C100302":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C100303":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C100304":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C100401":             "无效存储卡，请更换存储卡",
	"fpv_tip_0x1C100401_index_1":     "无效EMMC，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C100402":             "存储卡为慢速卡，请更换",
	"fpv_tip_0x1C100402_index_1":     "EMMC速度异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C100403":             "存储卡异常，请更换存储卡",
	"fpv_tip_0x1C100403_index_1":     "EMMC异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C100404":             "无存储卡",
	"fpv_tip_0x1C100404_index_1":     "EMMC识别异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C100405":             "请确认存储卡读写属性",
	"fpv_tip_0x1C100406":             "存储卡未格式化，请格式化后使用",
	"fpv_tip_0x1C100407":             "存储卡正在格式化，请等待",
	"fpv_tip_0x1C100408":             "不支持该存储卡文件系统，请格式化后使用",
	"fpv_tip_0x1C100409":             "存储卡正在刷新，请等待",
	"fpv_tip_0x1C10040A":             "存储卡已满，请清除内存",
	"fpv_tip_0x1C10040B":             "文件索引超出最大限制，请格式化SD卡",
	"fpv_tip_0x1C10040C":             "存储卡正在初始化，请等待",
	"fpv_tip_0x1C10040D":             "存储卡异常，请格式化存储卡后使用",
	"fpv_tip_0x1C10040E":             "存储卡修复中，请等待",
	"fpv_tip_0x1C10040F":             "存储卡读写缓慢，请等待",
	"fpv_tip_0x1C200001":             "%component_index号云台相机过热，请等待降温后再使用",
	"fpv_tip_0x1C200101":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C200102":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C200103":             "相机芯片温度过高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C200103_sky":         "相机芯片温度高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C200104":             "相机芯片温度过高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C200104_sky":         "相机芯片温度过高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C200105":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C200201":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C200202":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C200203":             "相机芯片温度过高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C200203_sky":         "相机芯片温度过高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C200204":             "H20 相机图像传感器 芯片异常",
	"fpv_tip_0x1C200301":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C200302":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C200303":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C200304":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C200401":             "无效存储卡，请更换存储卡",
	"fpv_tip_0x1C200401_index_1":     "无效EMMC，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C200402":             "存储卡为慢速卡，请更换",
	"fpv_tip_0x1C200402_index_1":     "EMMC速度异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C200403":             "存储卡异常，请更换存储卡",
	"fpv_tip_0x1C200403_index_1":     "EMMC异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C200404":             "无存储卡",
	"fpv_tip_0x1C200404_index_1":     "EMMC识别异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C200405":             "请确认存储卡读写属性",
	"fpv_tip_0x1C200406":             "存储卡未格式化，请格式化后使用",
	"fpv_tip_0x1C200407":             "存储卡正在格式化，请等待",
	"fpv_tip_0x1C200408":             "不支持该存储卡文件系统，请格式化后使用",
	"fpv_tip_0x1C200409":             "存储卡正在刷新，请等待",
	"fpv_tip_0x1C20040A":             "存储卡已满，请清除内存",
	"fpv_tip_0x1C20040B":             "文件索引超出最大限制，请格式化SD卡",
	"fpv_tip_0x1C20040C":             "存储卡正在初始化，请等待",
	"fpv_tip_0x1C20040D":             "存储卡异常，请格式化存储卡后使用",
	"fpv_tip_0x1C20040E":             "存储卡修复中，请等待",
	"fpv_tip_0x1C20040F":             "存储卡读写缓慢，请等待",
	"fpv_tip_0x1C200410":             "存储卡加密待验证，请输入密码进行验证",
	"fpv_tip_0x1C300001":             "%component_index号云台相机过热，请等待降温后再使用",
	"fpv_tip_0x1C300101":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C300102":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C300103":             "相机芯片温度高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C300103_sky":         "相机芯片温度高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C300104":             "相机芯片温度过高，请关闭飞机，等待冷却后再使用",
	"fpv_tip_0x1C300104_sky":         "相机芯片温度过高，请尽快返航或降落，等待冷却后重启使用",
	"fpv_tip_0x1C300105":             "%component_index号云台相机异常，请重启相机",
	"fpv_tip_0x1C300301":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C300302":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C300303":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C300304":             "H20 镜头异常，请断电重启",
	"fpv_tip_0x1C300401":             "无效存储卡，请更换存储卡",
	"fpv_tip_0x1C300401_index_1":     "无效EMMC，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C300402":             "存储卡为慢速卡，请更换",
	"fpv_tip_0x1C300402_index_1":     "EMMC速度异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C300403":             "存储卡异常，请更换存储卡",
	"fpv_tip_0x1C300403_index_1":     "EMMC异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C300404":             "无存储卡",
	"fpv_tip_0x1C300404_index_1":     "EMMC识别异常，请重启相机，如问题还存在，请返厂维修",
	"fpv_tip_0x1C300405":             "请确认存储卡读写属性",
	"fpv_tip_0x1C300406":             "存储卡未格式化，请格式化后使用",
	"fpv_tip_0x1C300407":             "存储卡正在格式化，请等待",
	"fpv_tip_0x1C300408":             "不支持该存储卡文件系统，请格式化后使用",
	"fpv_tip_0x1C300409":             "存储卡正在刷新，请等待",
	"fpv_tip_0x1C30040A":             "存储卡已满，请清除内存",
	"fpv_tip_0x1C30040B":             "文件索引超出最大限制，请格式化SD卡",
	"fpv_tip_0x1C30040C":             "存储卡正在初始化，请等待",
	"fpv_tip_0x1C30040D":             "存储卡异常，请格式化存储卡后使用",
	"fpv_tip_0x1C30040E":             "存储卡修复中，请等待",
	"fpv_tip_0x1C30040F":             "存储卡读写缓慢，请等待",
	"fpv_tip_0x1C300601":             "环境温度过低，请在正常的工作温度范围内测温",
	"fpv_tip_0x1C300602":             "环境温度过高，请在正常的工作温度范围内测温",
	"fpv_tip_0x1C300603":             "H20T相机红外镜头正在切换工作模式，请等待",
	"fpv_tip_0x1C300604":             "H20T相机红外镜头标定数据缺失",
	"fpv_tip_0x1C300605":             "H20T相机红外镜头初始化异常，请断电重启",
	"fpv_tip_0x1C300606":             "测温失败，请调整相机参数后测温",
	"fpv_tip_0x1C300607":             "测温超出量程，请切换为低增益模式",
	"fpv_tip_0x1C300608":             "测温超出量程，建议安装红外衰减镜",
	"fpv_tip_0x1C300609":             "测温超出量程",
	"fpv_tip_0x1C300701":             "检测到太阳，红外快门关闭",
	"fpv_tip_0x1C300702":             "检测到太阳，请转动云台，避免太阳直射",
	"fpv_tip_0x1D001001":             "云台上电后无力",
	"fpv_tip_0x1D001101":             "云台无法完成自检",
	"fpv_tip_0x1D010001":             "云台被卡住",
	"fpv_tip_0x1D010002":             "云台自检失败",
	"fpv_tip_0x1D010003":             "云台电机过载",
	"fpv_tip_0x1D010C01":             "FPV云台被卡住",
	"fpv_tip_0x1D010C02":             "FPV云台自检失败",
	"fpv_tip_0x1D010C03":             "FPV云台电机过载",
	"fpv_tip_0x1D020001":             "云台标定错误",
	"fpv_tip_0x1D020002":             "云台IMU数据异常，请尝试重启无人机",
	"fpv_tip_0x1D020003":             "云台校准参数异常，请尝试云台自动校准",
	"fpv_tip_0x1D020C01":             "FPV云台标定错误",
	"fpv_tip_0x1D030001":             "云台无法获取无人机信息",
	"fpv_tip_0x1D030002":             "无人机与负载通讯链路中断，请检查负载连接情况",
	"fpv_tip_0x1D030002_sky":         "无人机与负载通讯链路中断，请返航并检查负载连接情况",
	"fpv_tip_0x1D030C01":             "FPV云台无法获取无人机信息",
	"fpv_tip_0x1D040001":             "云台异常振动",
	"fpv_tip_0x1D040002":             "云台传感器故障",
	"fpv_tip_0x1D040003":             "云台电调故障",
	"fpv_tip_0x1D040004":             "云台电调故障",
	"fpv_tip_0x1D040005":             "云台电调故障",
	"fpv_tip_0x1D040C01":             "FPV云台异常振动",
	"fpv_tip_0x1D040C02":             "FPV云台传感器故障",
	"fpv_tip_0x1D040C03":             "FPV云台电调故障",
	"fpv_tip_0x1D040C04":             "FPV云台电调故障",
	"fpv_tip_0x1D040C05":             "FPV云台电调故障",
	"fpv_tip_0x1D050001":             "云台%component_index横滚已达限位",
	"fpv_tip_0x1D050002":             "云台%component_index俯仰已达限位",
	"fpv_tip_0x1D050003":             "云台%component_index偏航已达限位",
	"fpv_tip_0x1D050004":             "云台低温结冰，请返回机场并尝试解冻云台",
	"fpv_tip_0x1D050006":             "云台供电不足，尝试重启云台，未恢复请咨询大疆售后",
	"fpv_tip_0x1D050301":             "云台重试次数过多",
	"fpv_tip_0x1D050302":             "相机曝光超时",
	"fpv_tip_0x1D05030F":             "云台三轴转动次数接近使用寿命",
	"fpv_tip_0x1D050A01":             "云台电压过低",
	"fpv_tip_0x1D050A02":             "云台时间同步功能异常",
	"fpv_tip_0x1D050C01":             "FPV云台已达限位",
	"fpv_tip_0x1D050C02":             "FPV云台俯仰已达限位",
	"fpv_tip_0x1D050C03":             "FPV云台偏航已达限位",
	"fpv_tip_0x1D0C0002":             "云台IMU异常",
	"fpv_tip_0x1D0C0003":             "云台初始化异常",
	"fpv_tip_0x1D0C0004":             "云台初始化异常",
	"fpv_tip_0x1D0C0005":             "云台电源电压异常，请使用官方电池",
	"fpv_tip_0x1D0C000B":             "云台电机温度过高",
	"fpv_tip_0x1D0C000E":             "镜头未安装紧固，请取下重装，并拧紧扳扣",
	"fpv_tip_0x1D0C000F":             "云台未配平，请正确安装保护镜/ND镜、遮光罩以及配重",
	"fpv_tip_0x1D0C0C02":             "FPV云台IMU异常",
	"fpv_tip_0x1D0C0C03":             "FPV云台初始化异常",
	"fpv_tip_0x1D0C0C04":             "FPV云台初始化异常",
	"fpv_tip_0x1D0C0C05":             "FPV云台电源电压异常，请使用官方电池",
	"fpv_tip_0x1D0C0C0B":             "FPV云台电机温度过高",
	"fpv_tip_0x1D0E0001":             "云台初始化异常",
	"fpv_tip_0x1D0E0C01":             "FPV云台初始化异常",
	"fpv_tip_0x1D100002":             "云台上电后无力",
	"fpv_tip_0x1D100006":             "云台上电后无力",
	"fpv_tip_0x1D110002":             "云台无法完成自检",
	"fpv_tip_0x1D110005":             "云台无法完成自检",
	"fpv_tip_0x1D120001":             "云台自检后异常",
	"fpv_tip_0x1D120002":             "云台自检后异常",
	"fpv_tip_0x1D120004":             "云台自检后异常",
	"fpv_tip_0x1D120006":             "云台自检后异常",
	"fpv_tip_0x1D130003":             "云台异常振动",
	"fpv_tip_0x1D130005":             "云台异常振动",
	"fpv_tip_0x1D13000A":             "云台异常振动",
	"fpv_tip_0x1D140001":             "云台电机过载",
	"fpv_tip_0x1D150002":             "云台自动漂移",
	"fpv_tip_0x1D150003":             "云台自动漂移",
	"fpv_tip_0x1D150004":             "云台自动漂移",
	"fpv_tip_0x1D150005":             "云台自动漂移",
	"fpv_tip_0x1D160004":             "相机视频歪斜",
	"fpv_tip_0x1D170001":             "云台功能异常",
	"fpv_tip_0x1D180001":             "云台校准数据异常",
	"fpv_tip_0x1D190002":             "云台升级失败",
	"fpv_tip_0x1E000000":             "喊话器与机身发生共振，供电已断开，请等待自行恢复",
	"fpv_tip_0x1E000001":             "%component_index号负载启动异常",
	"fpv_tip_0x1E000002":             "%component_index号负载通讯异常",
	"fpv_tip_0x1E000003":             "%component_index号负载温度过高",
	"fpv_tip_0x1E000004":             "%component_index号负载硬件异常",
	"fpv_tip_0x1E000005":             "PSDK %component_index 号负载处于测试模式，请注意飞行安全并请联系负载供应商处理",
	"fpv_tip_0x1E000006":             "负载与机身发生共振，供电已断开，请等待自行恢复",
	"fpv_tip_0x1E000101":             "无人机正在返航，探照灯自动关闭",
	"fpv_tip_0x1E000102":             "无人机尚未起桨，探照灯最大亮度限制40%",
	"fpv_tip_0x1E000103":             "探照灯功率受限，最大亮度降低",
	"fpv_tip_0x1E000104":             "探照灯温度过高，亮度将持续降低",
	"fpv_tip_0x1E000105":             "无人机电量低，探照灯自动关闭",
	"fpv_tip_0x1E000106":             "无人机近地降落中，探照灯自动关闭",
	"fpv_tip_0x1E000107":             "探照灯未激活，功能受限，请激活后使用",
	"fpv_tip_0x1E000108":             "探照灯温度传感器异常，灯光亮度受限，请重新安装探照灯",
	"fpv_tip_0x1E010001":             "喊话器温度过高，响度降低，请降温后使用",
	"fpv_tip_0x1E010002":             "喊话器硬件异常，请重启无人机",
	"fpv_tip_0x1E010003":             "喊话器过载，请重启无人机",
	"fpv_tip_0x1E010004":             "无人机低电量，喊话器自动关闭",
	"fpv_tip_0x1E010009":             "喊话器未激活，喊话功能受限，请激活后使用",
	"fpv_tip_0x1E01000A":             "喊话器温度传感器异常，最大响度可能降低",
	"fpv_tip_0x1E020001":             "探照灯云台被卡住",
	"fpv_tip_0x1E020002":             "探照灯云台自检失败",
	"fpv_tip_0x1E020003":             "探照灯云台电机过载",
	"fpv_tip_0x1E030001":             "探照灯云台标定错误",
	"fpv_tip_0x1E030003":             "探照灯云台校准参数异常",
	"fpv_tip_0x1E040001":             "探照灯云台被卡住",
	"fpv_tip_0x1E040002":             "探照灯云台自检失败",
	"fpv_tip_0x1E040003":             "探照灯云台电机过载",
	"fpv_tip_0x1E050001":             "探照灯云台标定错误",
	"fpv_tip_0x1E050002":             "探照灯云台传感器故障",
	"fpv_tip_0x1E050003":             "探照灯云台校准参数异常",
	"fpv_tip_0x1E050004":             "探照灯右侧电机异常，请尝试重启探照灯",
	"fpv_tip_0x1E050005":             "探照灯云台电机故障",
	"fpv_tip_0x1E060001":             "探照灯无法获取无人机信息",
	"fpv_tip_0x1E070002":             "探照灯云台传感器故障",
	"fpv_tip_0x1E070003":             "探照灯左侧电机异常，请尝试重启探照灯",
	"fpv_tip_0x1E070004":             "探照灯右侧电机异常，请尝试重启探照灯",
	"fpv_tip_0x1E070005":             "探照灯云台电机故障",
	"fpv_tip_0x1E0D0001":             "探照灯未激活",
	"fpv_tip_0x1E0D0002":             "探照灯无IMU数据",
	"fpv_tip_0x1E0D0003":             "探照灯云台电机未初始化",
	"fpv_tip_0x1E0F0001":             "探照灯未激活",
	"fpv_tip_0x1E0F0002":             "探照灯无IMU数据",
	"fpv_tip_0x1E0F0003":             "探照灯云台电机未初始化",
	"fpv_tip_0x1E110001":             "探照灯云台姿态初始化失败",
	"fpv_tip_0x1E120010":             "PSDK获取下载控制权失败，请关闭APP自动加载或退出回放或重启无人机",
	"fpv_tip_0x1F010000":             "4G服务：License无法获取，请联系DJI售后服务",
	"fpv_tip_0x1F010001":             "4G服务：License过期，请导入有效证书",
	"fpv_tip_0x1F010002":             "4G服务：License校验失败，请联系DJI售后服务",
	"fpv_tip_0x1F010003":             "4G服务：设备SN未授权，请授权设备SN",
	"fpv_tip_0x1F010004":             "4G服务：设备型号未授权，请购买扩容套餐",
	"fpv_tip_0x1F010005":             "4G服务：设备连接数量超过授权，请购买扩容套餐",
	"fpv_tip_0x1F010006":             "4G服务：设备固件版本过低，请升级固件",
	"fpv_tip_0x1F010007":             "4G服务：服务器版本过低，请升级服务器",
	"fpv_tip_0x1F010008":             "4G服务：请求参数错误，请联系DJI售后服务",
	"fpv_tip_0x1F010009":             "4G服务：服务器内部错误，请联系DJI售后服务",
	"fpv_tip_0x1F01000A":             "4G服务：证书无法读取，请联系DJI售后服务",
	"fpv_tip_0x1F01000B":             "4G服务：证书过期，请导入有效证书",
	"fpv_tip_0x1F01000C":             "4G服务：证书校验失败，请联系DJI售后服务",
	"fpv_tip_0x1F01000D":             "系统时间错误，请确认设备和服务器时间正确后重试",
	"fpv_tip_0x1F01000E":             "当前区域与证书区域不匹配，请检查证书适用区域",
	"fpv_tip_0x1F010063":             "4G服务：系统未知错误，请联系DJI售后服务",
	"fpv_tip_0x1F0B0001":             "飞机无法使用LTE链路，网络波动或SIM卡无法访问网络",
	"fpv_tip_0x1F0B0002":             "遥控器DJI Cellular模块无法访问网络",
	"fpv_tip_0x1F0B0003":             "LTE服务器不可用，LTE链路不可用",
	"fpv_tip_0x1F0B0004":             "LTE服务器不可用，LTE链路不可用",
	"fpv_tip_0x1F0B0005":             "LTE链路不可用，请确认遥控器和飞机已成功对频",
	"fpv_tip_0x1F0B0006":             "LTE链路不可用，请重启遥控器",
	"fpv_tip_0x1F0B0007":             "LTE链路不可用，请检查飞机4G dongle的网络能力",
	"fpv_tip_0x1F0B0008":             "LTE链路不可用，请重启飞机和遥控器",
	"fpv_tip_0x1F0B0009":             "LTE链路不可用，请重启遥控器",
	"fpv_tip_0x1F0B0016":             "LTE链路不可用，请检查遥控器的网络能力",
	"fpv_tip_0x1F0B0017":             "LTE链路不可用，请检查遥控器的网络能力",
	"fpv_tip_0x1F0B0018":             "LTE链路不可用，请重启飞机和遥控器",
	"fpv_tip_0x1F0B001A":             "LTE链路不可用，请检查飞机4G dongle的网络能力",
	"fpv_tip_0x1F0B001B":             "LTE链路不可用，请重启飞机和遥控器",
	"fpv_tip_0x1F0B001C":             "LTE链路异常，无人机缺少认证文件，请联系大疆售后服务",
	"fpv_tip_0x1F0B001c":             "LTE链路异常，无人机缺少认证文件，请联系大疆售后服务",
	"fpv_tip_0x1F0B001d":             "LTE链路异常，遥控器缺少认证文件，请联系大疆售后服务",
	"fpv_tip_0x1F0B0020":             "无人机 DJI Cellular 模块需要升级",
	"fpv_tip_0x1F0B0021":             "遥控器 DJI Cellular 模块需升级",
	"fpv_tip_0x1F0B0022":             "手机进入飞行模式或者关闭wifi及移动网络，无法发起移动网络通信",
	"fpv_tip_0x1F0B0023":             "LTE 信号弱，请谨慎飞行",
	"fpv_tip_0x1F0B0024":             "LTE 信号弱，请谨慎飞行",
	"fpv_tip_0x1F0B0025":             "LTE 信号弱，请谨慎飞行",
	"fpv_tip_0x1F0B0026":             "SIM卡流量不足，请谨慎飞行",
	"fpv_tip_0x1F0B0027":             "无人机 LTE 证书需更新，请联系售后或代理商",
	"fpv_tip_0x1F0B0028":             "遥控器 LTE 证书需更新，请联系售后或代理商",
	"fpv_tip_0x1F0B0029":             "证书过期，将无法正常建立通信链路",
	"fpv_tip_0x1F0B002A":             "固件版本不匹配，请返回首页进行升级",
	"fpv_tip_0x1F0B002B":             "固件版本不匹配，请返回首页进行升级",
	"fpv_tip_0x1F0B002C":             "固件版本不匹配，请在首页进行升级",
	"fpv_tip_0x1F0B002D":             "App版本需要升级",
	"fpv_tip_0x1F0B002E":             "遥控器网络信号弱，请谨慎飞行",
	"fpv_tip_0x1F0B0030":             "LTE服务器暂时无法连接，请稍后再试",
	"fpv_tip_0x1F0B0031":             "手机可网络信号弱，链路可能无法建立或者容易断开连接",
	"fpv_tip_0x1F0B0032":             "用户服务到期，不允许进入融合模式。固件版本需要升级",
	"fpv_tip_0x1F0B0035":             "dongle固件版本需要升级",
	"fpv_tip_0x1F0B0036":             "当前地区无法使用增强图传功能",
	"fpv_tip_0x1F0B0037":             "无人机 DJI Cellular 模块无法访问网络，请更换至网络更好的地点或稍后重试",
	"fpv_tip_0x1F0B0038":             "遥控器 DJI Cellular 模块无法访问网络",
	"fpv_tip_0x1F0B0039":             "目前网络无法正常建立，可以尝试更换地点，或者换其他运营商的卡。",
	"fpv_tip_0x1F0B003A":             "无人机 DJI Cellular 模块无法访问网络，请尝试使用其他运营商网络",
	"fpv_tip_0x1F0B003B":             "遥控器 DJI Cellular 模块无法访问网络",
	"fpv_tip_0x1F0B003C":             "目前网络无法正常建立，可以尝试更换地点，或者换其他运营商的卡。",
	"fpv_tip_0x1F0B003D":             "无法访问网络",
	"fpv_tip_0x1F0B003E":             "遥控器 DJI Cellular SIM卡暂时无法获取网络服务，建议检查或更换SIM卡",
	"fpv_tip_0x1F0B0040":             "遥控器无法访问网络",
	"fpv_tip_0x1F0B0040_sky":         "遥控器无法访问网络，请检查DJI Cellular模块或Wi-Fi连接状态",
	"fpv_tip_0x1F0B0045":             "增强链路操控延时较大，请谨慎飞行",
	"fpv_tip_0x1F0B0045_sky":         "增强链路操控延时较大，请谨慎飞行",
	"fpv_tip_0x1F0B0046":             "飞机的4G Dongle未插入SIM卡且eSIM未激活",
	"fpv_tip_0x1F0B0047":             "飞机的4G Dongle已插入SIM卡，选择了eSIM且eSIM未激活",
	"fpv_tip_0x1F0B0048":             "遥控器的4G Dongle未插入SIM卡且eSIM未激活",
	"fpv_tip_0x1F0B0049":             "遥控器的4G Dongle已插入SIM卡，选择了eSIM且eSIM未激活",
	"fpv_tip_0x1F0B004A":             "飞机无法使用LTE链路，网络波动或SIM卡%lte_index无法访问网络",
	"fpv_tip_0x1F0B004B":             "无人机 DJI Cellular 模块%lte_index无法访问网络，请更换至网络更好的地点或稍后重试",
	"fpv_tip_0x1F0B004C":             "无人机 DJI Cellular 模块%lte_index无法访问网络，请尝试使用其他运营商网络",
	"fpv_tip_0x1F0B004D":             "无法访问网络",
	"fpv_tip_0x1F0B004E":             "无人机的Cellular 模块%lte_index未插入SIM卡且eSIM未激活",
	"fpv_tip_0x1F0B004F":             "无人机的Cellular 模块%lte_index已插入SIM卡，选择了eSIM且eSIM未激活",
	"fpv_tip_0x1a020180":             "视觉红外传感器温度过高，请尽快返航或降落，远离高温环境",
	"fpv_tip_0x1a420bc0":             "下视环境光过暗，下方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420bc1":             "前视环境光过暗，前向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420bc2":             "后视环境光过暗，后向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420bc3":             "右视环境光过暗，右向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420bc4":             "左视环境光过暗，左向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420bc5":             "上视环境光过暗，上方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420bc6":             "水平方向环境光过暗，水平方向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c00":             "下视环境光过亮，下方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c01":             "前视环境光过亮，前向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c02":             "后视环境光过亮，后向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c03":             "右视环境光过亮，右向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c04":             "左视环境光过亮，左向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c05":             "上视环境光过亮，上方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c06":             "水平方向环境光过亮，水平方向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c40":             "下视可能遮挡，下方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c41":             "前视可能遮挡，前向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c42":             "后视可能遮挡，后向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c43":             "右视可能遮挡，右向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c44":             "左视可能遮挡，左向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c45":             "上视可能遮挡，上方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c80":             "下视可能脏污，下方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c81":             "前视可能脏污，前向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c82":             "后视可能脏污，后向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c83":             "右视可能脏污，右向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c84":             "左视可能脏污，左向视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420c85":             "上视可能脏污，上方视觉避障失效，仅红外传感器工作，请谨慎飞行",
	"fpv_tip_0x1a420cc0":             "无人机姿态角过大，避障失效，请谨慎飞行",
	"fpv_tip_0x1a420d00":             "无人机姿态角过大，降落保护失效，请手动降落",
	"fpv_tip_0x1a420d40":             "正飞向障碍物感知盲区，可能无法检测障碍物，请谨慎飞行",
	"fpv_tip_0x1a5103c0":             "下视传感器标定异常",
	"fpv_tip_0x1a5103c1":             "前视传感器标定异常",
	"fpv_tip_0x1a5103c2":             "后视传感器标定异常",
	"fpv_tip_0x1a5103c3":             "上视传感器标定异常",
	"fpv_tip_0x1a5103c4":             "左视传感器标定异常",
	"fpv_tip_0x1a5103c5":             "右视传感器标定异常",
	"fpv_tip_0x1afd0040":             "无法起飞：感知传感器系统异常，禁止起飞",
	"fpv_tip_0x1afd0040_sky":         "感知传感器系统异常，请尽快返航或降落",
	"fpv_tip_0x1afe0040":             "视觉系统负载过高，请谨慎飞行至开阔环境",
	"fpv_tip_0x1b030019":             "无人机已偏离航线，航线安全返航失效",
	"fpv_tip_0x1b030034":             "检测到定位信号强干扰或诱骗，请立即返航",
	"fpv_tip_0x1b080001":             "Remote ID 播报异常，无法获取遥控器位置",
	"fpv_tip_0x1b080002":             "Remote ID 模块异常，请联系售后服务",
	"fpv_tip_0x1c000603":             "照片存在欠曝风险",
	"fpv_tip_0x1c000604":             "照片存在过曝风险",
	"fpv_tip_0x1c100405":             "请确认存储卡读写属性",
	"fpv_tip_0x1c100405_index_1":     "请确认EMMC读写属性",
	"fpv_tip_0x1c100406":             "存储卡未格式化，请格式化后使用",
	"fpv_tip_0x1c100406_index_1":     "EMMC未格式化，请格式化后使用",
	"fpv_tip_0x1c100407":             "存储卡正在格式化，请等待",
	"fpv_tip_0x1c100407_index_1":     "EMMC正在格式化，请等待",
	"fpv_tip_0x1c100408":             "不支持该存储卡文件系统，请格式化后使用",
	"fpv_tip_0x1c100409":             "存储卡正在刷新，请等待",
	"fpv_tip_0x1c100409_index_1":     "EMMC正在刷新，请等待",
	"fpv_tip_0x1c10040B":             "文件索引超出最大限制，请格式化SD卡",
	"fpv_tip_0x1c10040a":             "存储卡已满，请清除内存",
	"fpv_tip_0x1c10040a_index_1":     "EMMC已满，请清除内存",
	"fpv_tip_0x1c10040b":             "文件索引超出最大限制，请格式化SD卡",
	"fpv_tip_0x1c10040b_index_1":     "EMMC内存溢出，请格式化EMMC并重启相机",
	"fpv_tip_0x1c10040c":             "存储卡正在初始化，请等待",
	"fpv_tip_0x1c10040c_index_1":     "EMMC正在初始化，请等待",
	"fpv_tip_0x1c10040d":             "存储卡异常，请格式化存储卡后使用",
	"fpv_tip_0x1c10040d_index_1":     "EMMC异常，请格式化EMMC后使用",
	"fpv_tip_0x1c10040e":             "存储卡修复中，请等待",
	"fpv_tip_0x1c10040e_index_1":     "EMMC修复中，请等待",
	"fpv_tip_0x1c10040f":             "存储卡读写缓慢，请等待",
	"fpv_tip_0x1c10040f_index_1":     "EMMC读写缓慢，请等待",
	"fpv_tip_0x1c200405_index_1":     "请确认EMMC读写属性",
	"fpv_tip_0x1c200406_index_1":     "EMMC未格式化，请格式化后使用",
	"fpv_tip_0x1c200407_index_1":     "EMMC正在格式化，请等待",
	"fpv_tip_0x1c200409_index_1":     "EMMC正在刷新，请等待",
	"fpv_tip_0x1c20040a_index_1":     "EMMC已满，请清除内存",
	"fpv_tip_0x1c20040b":             "文件索引超出最大限制，请格式化SD卡",
	"fpv_tip_0x1c20040b_index_1":     "EMMC内存溢出，请格式化EMMC并重启相机",
	"fpv_tip_0x1c20040c_index_1":     "EMMC正在初始化，请等待",
	"fpv_tip_0x1c20040d_index_1":     "EMMC异常，请格式化EMMC后使用",
	"fpv_tip_0x1c20040e_index_1":     "EMMC修复中，请等待",
	"fpv_tip_0x1c20040f_index_1":     "EMMC读写缓慢，请等待",
	"fpv_tip_0x1c200410":             "存储卡加密待验证，请输入密码验证后使用",
	"fpv_tip_0x1c300405_index_1":     "请确认EMMC读写属性",
	"fpv_tip_0x1c300406_index_1":     "EMMC未格式化，请格式化后使用",
	"fpv_tip_0x1c300407_index_1":     "EMMC正在格式化，请等待",
	"fpv_tip_0x1c300409_index_1":     "EMMC正在刷新，请等待",
	"fpv_tip_0x1c30040a_index_1":     "EMMC已满，请清除内存",
	"fpv_tip_0x1c30040b":             "文件索引超出最大限制，请格式化SD卡",
	"fpv_tip_0x1c30040b_index_1":     "EMMC内存溢出，请格式化EMMC并重启相机",
	"fpv_tip_0x1c30040c_index_1":     "EMMC正在初始化，请等待",
	"fpv_tip_0x1c30040d_index_1":     "EMMC异常，请格式化EMMC后使用",
	"fpv_tip_0x1c30040e_index_1":     "EMMC修复中，请等待",
	"fpv_tip_0x1c30040f_index_1":     "EMMC读写缓慢，请等待",
	"fpv_tip_0x1f0b0017":             "LTE链路不可用，请检查遥控器的网络能力",
	"fpv_tip_0x20010100":             "云台%1$s的控制权已被%2$s控获取",
	"fpv_tip_0x20010200":             "已成功获取该云台的控制权",
	"fpv_tip_0x20010301":             "自由模式",
	"fpv_tip_0x20010302":             "跟随模式",
	"fpv_tip_0x20010304":             "云台模式设置失败，请重试",
	"fpv_tip_0x20010400":             "提示：开启双云台同时控制模式时，只能使用1号云台指点变焦功能",
	"fpv_tip_0x20010501":             "云台回中",
	"fpv_tip_0x20010502":             "云台偏航回中",
	"fpv_tip_0x20010503":             "云台俯仰朝上",
	"fpv_tip_0x20010504":             "云台俯仰朝下",
	"fpv_tip_0x20010505":             "云台朝上",
	"fpv_tip_0x20010506":             "云台向下",
	"fpv_tip_0x20010507":             "云台俯仰回中",
	"fpv_tip_0x20010508":             "云台横滚回中",
	"fpv_tip_0x20010600":             "云台增稳开启",
	"fpv_tip_0x20010601":             "云台增稳关闭",
	"fpv_tip_0x20010700":             "失去%1$s的控制权，联动取消",
	"fpv_tip_0x20010701":             "失去%1$s的控制权，%2$s脱离联动",
	"fpv_tip_0x20010800":             "链路中断，您失去了对云台的控制权。",
	"fpv_tip_0x20010900":             "%1$s控连接中断，您已经获得所有云台的控制权。",
	"fpv_tip_0x20020000":             "已成功获取无人机的控制权，您当前的摇杆挡位为%1$s",
	"fpv_tip_0x20020001":             "控制权已解锁",
	"fpv_tip_0x20020002":             "控制权已锁定",
	"fpv_tip_0x20020003":             "无人机的控制权已经被%1$s控获取",
	"fpv_tip_0x20020004":             "当前状态下，不可夺取当前云台的控制权",
	"fpv_tip_0x20020005":             "无人机的控制权已被%1$s控锁定，无法获取控制权",
	"fpv_tip_0x20020006":             "当前状态下，不可夺取飞行控制权",
	"fpv_tip_0x20020100":             "提示：飞机已经达到最远距离， 请注意安全飞行",
	"fpv_tip_0x20020200":             "风速过大，谨慎飞行",
	"fpv_tip_0x20020201":             "风速过大，谨慎飞行",
	"fpv_tip_0x20020300":             "姿态模式",
	"fpv_tip_0x20020301":             "不允许切换飞行挡位，如需切到其他挡，请在飞控设置打开\\'允许切换飞行挡位\\'开关",
	"fpv_tip_0x20020302":             "已切换至S挡（运动）",
	"fpv_tip_0x20020303":             "已切换至P挡（定位）",
	"fpv_tip_0x20020304":             "已切换至T挡（三脚架）",
	"fpv_tip_0x20020305":             "已切换至A挡（姿态）",
	"fpv_tip_0x20020306":             "如果要以非P挡起飞，请切换挡位至其他挡再切回当前挡以确认。",
	"fpv_tip_0x20020307":             "空吊模式下，加速度受限，刹停时间变长",
	"fpv_tip_0x20020308":             "空吊自动放货执行成功",
	"fpv_tip_0x20020309":             "空吊自动放货执行失败",
	"fpv_tip_0x20020400":             "当前电量仅够返回返航点，请尽快返航。",
	"fpv_tip_0x20020500":             "返航点已记录，返航高度：%1$.1f %2$s",
	"fpv_tip_0x20020600":             "低电量，飞机开始返航",
	"fpv_tip_0x20020601":             "严重低电量，系统强制降落，无法取消，您可以通过推油门减缓下降速度，调整摇杆方向避开危险",
	"fpv_tip_0x20020602":             "系统计算您当前电量仅足够返回返航点，飞机开始返航",
	"fpv_tip_0x20020603":             "当前电量仅够当前高度降落，系统将强制下降，无法取消",
	"fpv_tip_0x20020604":             "严重低电压，系统强制降落，短按遥控器上的返航按钮停止降落",
	"fpv_tip_0x20020605":             "低电压报警，飞机开始返航，短按遥控器上的返航按钮停止返航",
	"fpv_tip_0x20020606":             "电池电压过低，系统强制降落，无法取消，您可以通过推油门减缓下降速度，调整摇杆方向避开危险",
	"fpv_tip_0x20020607":             "开始自动返航，最低返航高度%1$d米。如需调整返航高度，可在取消返航后到飞控设置中重新设置返航高度",
	"fpv_tip_0x20020608":             "开始自动返航，最低返航高度%1$d米。如需调整返航高度，可在取消返航后到飞控设置中重新设置返航高度",
	"fpv_tip_0x20020609":             "无人机失联，开始返航",
	"fpv_tip_0x2002060A":             "无人机在禁飞区内，开始自动降落",
	"fpv_tip_0x2002060B":             "无人机距离返航点太近，返航切换为自动降落",
	"fpv_tip_0x2002060C":             "无人机距离返航点太远，返航切换为自动降落",
	"fpv_tip_0x2002060D":             "开始自动降落",
	"fpv_tip_0x2002060E":             "App请求强制降落",
	"fpv_tip_0x20020610":             "返航时遇到障碍，正在降落",
	"fpv_tip_0x20020611":             "IMU异常，正在返航",
	"fpv_tip_0x20020612":             "开始智能高度返航",
	"fpv_tip_0x2002061F":             "检测到非法电池，正在降落",
	"fpv_tip_0x20020700":             "飞行挡位为A/P/S，且当前飞行挡位为%1$s挡",
	"fpv_tip_0x20020700_rc511":       "飞行挡位为A/N/S，且当前飞行挡位为%1$s挡",
	"fpv_tip_0x20020800":             "%1$s控请求获取无人机的控制权",
	"fpv_tip_0x20020900":             "任务已中断，可控制无人机手动飞行",
	"fpv_tip_0x20020A00":             "姿态模式",
	"fpv_tip_0x20020C00":             "目标点已同步",
	"fpv_tip_0x20020C01":             "飞行系统发生严重异常，请立即手动降落!",
	"fpv_tip_0x20020D00":             "起落架正在上升，避障失效",
	"fpv_tip_0x20020D01":             "起落架正在下降，避障失效",
	"fpv_tip_0x20020E01":             "遥控器已断联，暂停刷新返航点，请留意返航位置",
	"fpv_tip_0x20020E02":             "遥控器已连接，继续刷新返航点",
	"fpv_tip_0x20020b00":             "起飞",
	"fpv_tip_0x20020b01":             "取消起飞",
	"fpv_tip_0x20021000":             "无法起飞：请确认摇杆在中位",
	"fpv_tip_0x20021001":             "取消返航",
	"fpv_tip_0x20021002":             "取消降落",
	"fpv_tip_0x20021100":             "当前无人机不符合C1认证条件，请注意合规飞行",
	"fpv_tip_0x20022001":             "开始快速自旋",
	"fpv_tip_0x20022002":             "无卫星定位信号，飞行区域未知，限高%1$s",
	"fpv_tip_0x20022003":             "无卫星定位信号，飞行区域未知，环境光过暗，限高%1$s",
	"fpv_tip_0x20030000":             "附近有载人无人机，请谨慎飞行！",
	"fpv_tip_0x20040000":             "飞行速度过快，可能造成等距拍照失败",
	"fpv_tip_0x20040100":             "当前曝光模式处于M档，无法测光",
	"fpv_tip_0x20040200":             "激光测距过近",
	"fpv_tip_0x20040201":             "激光测距过远",
	"fpv_tip_0x20040300":             "当前无人机正在使用可见光相机进行一键全景拍摄",
	"fpv_tip_0x20040400":             "Zenmuse XT S不兼容其它红外相机",
	"fpv_tip_0x20040500":             "%component_index号云台相机过热，可能会无法拍照/录像",
	"fpv_tip_0x20040501":             "%component_index号云台相机过热，请等待降温后再使用",
	"fpv_tip_0x20040600":             "SDCard速度慢，无法录制4K视频",
	"fpv_tip_0x20040700":             "当前画面流畅度过低，请注意飞行安全",
	"fpv_tip_0x20040800":             "无人机处于返航或降落状态下，无法进入全景拍照模式",
	"fpv_tip_0x20040801":             "全景拍摄成功，可在回放中查看",
	"fpv_tip_0x20040802":             "全景拍摄失败",
	"fpv_tip_0x20040803":             "全景拍摄终止",
	"fpv_tip_0x20040804":             "飞机未起飞，无法开始全景拍摄",
	"fpv_tip_0x20040805":             "控制权获取失败，全景拍摄终止",
	"fpv_tip_0x20040806":             "未知相机错误，无法开始全景拍摄",
	"fpv_tip_0x20040807":             "相机超时，全景拍摄终止",
	"fpv_tip_0x20040808":             "存储空间不足，全景拍摄终止",
	"fpv_tip_0x20040809":             "无人机运动中，无法开始全景拍摄",
	"fpv_tip_0x2004080A":             "云台运动中，无法开始全景拍摄",
	"fpv_tip_0x2004080B":             "用户操作摇杆，全景拍摄终止",
	"fpv_tip_0x2004080C":             "触碰到限飞高度，全景拍摄终止",
	"fpv_tip_0x2004080D":             "触发距离限制，全景拍摄终止",
	"fpv_tip_0x2004080E":             "云台受阻，全景拍摄终止",
	"fpv_tip_0x2004080F":             "拍照失败，全景拍摄终止",
	"fpv_tip_0x20040810":             "全景图片拼接失败",
	"fpv_tip_0x20040811":             "加载标定参数失败，全景拍摄终止",
	"fpv_tip_0x20040812":             "调整相机参数失败，全景拍摄终止",
	"fpv_tip_0x20040813":             "全景图正在拼接合成，请耐心等待",
	"fpv_tip_0x20040900":             "主控已进入回放，辅控将断开图传",
	"fpv_tip_0x20040A00":             "相机正忙，无法进入回放",
	"fpv_tip_0x20040A01":             "无存储卡",
	"fpv_tip_0x20040B00":             "为保证激光雷达负载使用效果，建议在I号云台安装",
	"fpv_tip_0x20040C00":             "当前控为B控，不支持激光雷达负载，请切换为A控",
	"fpv_tip_0x20040D00":             "标定失败，请尝试重新标定。",
	"fpv_tip_0x20040D01":             "标定失败，请联系大疆售后服务。",
	"fpv_tip_0x20040E00":             "%s相机镜头未标定，会影响对焦效果，您可以通过相机设置中的『标定镜头』功能进行标定。",
	"fpv_tip_0x20040F00":             "实时点云传输不稳定，点云数据正常采集中",
	"fpv_tip_0x20041000":             "负载惯导预热已完成",
	"fpv_tip_0x20041100":             "点云模型录制中，无法开始录像",
	"fpv_tip_0x20041200":             "已完成标定飞行，请继续操作",
	"fpv_tip_0x20041300":             "标定飞行已被手动打断，请重新操作",
	"fpv_tip_0x20041400":             "负载与无人机连接通信异常",
	"fpv_tip_0x20041500":             "红外变焦已达最大倍数",
	"fpv_tip_0x20041600":             "切换成功，该模式提供更大的测温区间",
	"fpv_tip_0x20041601":             "切换成功，该模式提供更精准的测温能力",
	"fpv_tip_0x20041602":             "切换成功，红外超清模式提供更清晰的红外画面，不支持测温",
	"fpv_tip_0x20041700":             "光照传感器被遮挡，影响多光谱数据采集",
	"fpv_tip_0x20041801":             "摄像头参数不匹配，请重启APP进入首页更新参数",
	"fpv_tip_0x20050100":             "附近有1小时后生效的临时限飞区",
	"fpv_tip_0x20050200":             "附近有即将生效的临时限飞区（%1$d）",
	"fpv_tip_0x20050300":             "无人机将在%1$s秒后自动下降",
	"fpv_tip_0x20050400":             "无人机已触碰到解禁证书的区域边界，请注意飞行安全。",
	"fpv_tip_0x20050401":             "无人机已触碰到限飞区，返航可能中止。请尽快操控无人机远离。",
	"fpv_tip_0x20050402":             "无人机已触碰到限高区（限高 %1$s），当前飞行高度高于限飞高度，请操控无人机远离或下降",
	"fpv_tip_0x20050403":             "无人机进入限高区（限高 %1$s），请注意飞行安全",
	"fpv_tip_0x20050404":             "无人机已触碰到禁飞区，请尽快操控无人机远离",
	"fpv_tip_0x20050405":             "无人机处于警告区(%1$s)内，请注意飞行安全。",
	"fpv_tip_0x20050406":             "无人机在限高区（%1$s）下方，请注意飞行安全。",
	"fpv_tip_0x20050407":             "当前在限飞区，限高%1$s。",
	"fpv_tip_0x20050408":             "无人机在限高区（%s）下方，请注意飞行安全",
	"fpv_tip_0x20050500":             "即将达到限飞高度",
	"fpv_tip_0x20050600":             "无人机附近区域有多个加强警示区，请注意飞行安全",
	"fpv_tip_0x20050601":             "无人机下方区域有多个加强警示区，请注意飞行安全",
	"fpv_tip_0x20050602":             "无人机上方区域有多个加强警示区，请注意飞行安全",
	"fpv_tip_0x20050603":             "无人机附近有加强警示区（%s，高度区间：%s，生效时段：%s），请注意飞行安全",
	"fpv_tip_0x20050604":             "无人机下方区域有加强警示区（%s，高度区间：%s，生效时段：%s），请注意飞行安全",
	"fpv_tip_0x20050605":             "无人机上方区域有加强警示区（%s，高度区间：%s，生效时段：%s），请注意飞行安全",
	"fpv_tip_0x20050606":             "无人机在加强警示区（%s，区间：%s，生效时段：%s）内，请注意飞行安全",
	"fpv_tip_0x20050607":             "附近有多个加强警示区，请注意飞行安全",
	"fpv_tip_0x20050608":             "卫星定位信号差，地理感知功能降级，请谨慎飞行",
	"fpv_tip_0x20060100":             "卫星定位坐标已复制 %s",
	"fpv_tip_0x20060200":             "RNG信息已复制 %s",
	"fpv_tip_0x20070100":             "无人机动力异常，现已强制降落！请尽快打杆操作无人机降落至空旷地区",
	"fpv_tip_0x20070101":             "无人机动力异常，无法起飞!",
	"fpv_tip_0x20080100":             "已切换为Ocusync图传",
	"fpv_tip_0x20080101":             "图传链路已切换至LTE增强链路",
	"fpv_tip_0x20080200":             "双控模式或当前控为B控，无法使用LTE增强链路",
	"fpv_tip_0x20080300":             "当前LTE网络质量差， 请谨慎飞行！",
	"fpv_tip_0x20080400":             "图传信号微弱，请调整天线并谨慎打杆",
	"fpv_tip_0x20080500":             "遥控器信号微弱，请调整天线",
	"fpv_tip_0x20080600":             "遥控器侧干扰较强，请远离其他遥控器或干扰源",
	"fpv_tip_0x20080700":             "无人机侧干扰较强，请尽快返航或飞离干扰源",
	"fpv_tip_0x20090000":             "视觉增稳功能已暂停",
	"fpv_tip_0x20090001":             "视觉增稳功能已恢复",
	"fpv_tip_0x20090002":             "视觉增稳功能已暂停，原因：画面特征点过少",
	"fpv_tip_0x20090003":             "视觉增稳功能已暂停，原因：相机参数已变更",
	"fpv_tip_0x20090004":             "视觉增稳功能已暂停，原因：当前处于跟踪模式",
	"fpv_tip_0x20090100":             "打点成功",
	"fpv_tip_0x20090101":             "未获取到激光测距数据，打点失败",
	"fpv_tip_0x20090102":             "位置信息无效，打点失败",
	"fpv_tip_0x20090103":             "原地打点成功",
	"fpv_tip_0x20090200":             "开始智能跟踪",
	"fpv_tip_0x20090201":             "智能跟踪关闭",
	"fpv_tip_0x20090202":             "未识别到目标，请在屏幕上手动框选",
	"fpv_tip_0x200A0000":             "为避免信号干扰，请关闭WiFi后使用中继模式",
	"fpv_tip_0x20100000":             "D-RTK低电量警报",
	"fpv_tip_0x20100100":             "RTK状态异常，将进入精度维持模式，超时将自动退出RTK",
	"fpv_tip_0x20100200":             "未搜索到RTK基站，请检查RTK基站后重新搜索",
	"fpv_tip_0x2010D000":             "D-RTK 低电量警报",
	"fpv_tip_0x20110000":             "电池加热中",
	"fpv_tip_0x20120000":             "获取移动设备卫星定位失败",
	"fpv_tip_0x20120001":             "获取DJI设备卫星定位失败",
	"fpv_tip_0x20120200":             "请先将飞机降落至地面，再进行下一步操作",
	"fpv_tip_0x20120300":             "无人机自动飞至起始点，请耐心等待",
	"fpv_tip_0x20120301":             "无人机已到达起始点，请开始操作",
	"fpv_tip_0x20120302":             "退出飞行编辑",
	"fpv_tip_0x20120400":             "未绑定手机，已限高限远。请完成绑定手机再起飞。",
	"fpv_tip_0x20120500":             "媒体资源将从当前控上传云端",
	"fpv_tip_0x20120501":             "媒体资源将从另一个控上传云端",
	"fpv_tip_0x20120502":             "无人机已接受云端命令控制",
	"fpv_tip_0x20120503":             "无人机跨团队，云服务不可用",
	"fpv_tip_0x20120600":             "您已被当前项目移除，将无法查看该项目内容",
	"fpv_tip_0x20120700":             "您所在项目已归档，将无法查看该项目内容",
	"fpv_tip_0x20120800":             "您所在项目已解散，将无法查看该项目内容",
	"fpv_tip_0x20120900":             "实时建图停止",
	"fpv_tip_0x20120A00":             "已开启直播",
	"fpv_tip_0x20120B00":             "您已手动暂停航线任务",
	"fpv_tip_0x20120B01":             "RTK信号差，航线任务已暂停",
	"fpv_tip_0x20120B02":             "无人机已达到飞行最大高度，航线任务已暂停",
	"fpv_tip_0x20120B03":             "无人机已达到飞行最远距离，航线任务已暂停",
	"fpv_tip_0x20120B04":             "航线任务暂停，无人机检测到障碍物",
	"fpv_tip_0x20120B05":             "无人机接近限飞区，航线任务已暂停",
	"fpv_tip_0x20120B06":             "无人机已达到飞行最低高度，航线任务已暂停",
	"fpv_tip_0x20120B07":             "未知原因，航线任务已暂停",
	"fpv_tip_0x20120B08":             "超过机场限高区限高",
	"fpv_tip_0x20120B09":             "触发紧急刹停，航线任务已暂停",
	"fpv_tip_0x20120B0A":             "环境光过暗或检测到云雾环境",
	"fpv_tip_0x20120B0B":             "卫星定位信号差，航线任务已暂停",
	"fpv_tip_0x20120B0C":             "返航点未刷新，航线任务已暂停",
	"fpv_tip_0x20120B0D":             "遥控器断连，正在执行智能返航",
	"fpv_tip_0x20120B0E":             "自动起飞失败，请尝试重新上传航线",
	"fpv_tip_0x20120B0F":             "接近自定义飞行区，航线任务已暂停",
	"fpv_tip_0x20120B10":             "仿地高度异常（大于200m或小于30m），请修改参数",
	"fpv_tip_0x20120B11":             "风速过大，航线任务已暂停",
	"fpv_tip_0x20120B12":             "触碰到限高区或无人机高度过低，航线任务已中断",
	"fpv_tip_0x20120B13":             "该PSDK负载处于黑名单中，请移除后起飞",
	"fpv_tip_0x20120B14":             "目标点位于禁飞区域或者障碍物内，无法到达，航线任务已暂停",
	"fpv_tip_0x20120B15":             "飞行航线过程中轨迹规划失败，航线任务已暂停",
	"fpv_tip_0x20120B16":             "航向估计存在偏差，请水平加减速飞行20m以上进行校准",
	"fpv_tip_0x20120BA0":             "无人机校验中",
	"fpv_tip_0x20120C00":             "禁用视觉定位",
	"fpv_tip_0x20120C01":             "启用视觉定位",
	"fpv_tip_0x20120C02":             "已允许大疆司运指令",
	"fpv_tip_0x20120C03":             "执行云端急停指令",
	"fpv_tip_0x20120C04":             "执行云端降落指令",
	"fpv_tip_0x20120C05":             "执行云端返航指令",
	"fpv_tip_0x20120C06":             "执行云端空吊指令",
	"fpv_tip_0x20120C07":             "执行云端FPV指令",
	"fpv_tip_0x20120C08":             "执行云端航线指令",
	"fpv_tip_0x20120C09":             "已撤销云端指令许可",
	"fpv_tip_0x20120C0A":             "云端已取消返航",
	"fpv_tip_0x20120C0B":             "无人机已锁定",
	"fpv_tip_0x20120C0C":             "无人机已解锁",
	"fpv_tip_0x20120D01":             "全部避障已关闭，请安全飞行",
	"fpv_tip_0x20120D02":             "水平避障已关闭，请安全飞行",
	"fpv_tip_0x20120D03":             "上避障已关闭，请安全飞行",
	"fpv_tip_0x20120D04":             "下避障已关闭，请安全飞行",
	"fpv_tip_0x20120D05":             "全部避障已开启",
	"fpv_tip_0x20120D06":             "水平避障已开启",
	"fpv_tip_0x20120D07":             "上避障已开启",
	"fpv_tip_0x20120D08":             "下避障已开启",
	"fpv_tip_0x20120E00":             "当前地区电离层开始活跃，可能影响定位精度",
	"fpv_tip_0x20120E01":             "无人机与遥控器固件不匹配，请升级最新固件",
	"fpv_tip_0x20120F00":             "未检测到账号，已限高30m，限远50m，请登录",
}
