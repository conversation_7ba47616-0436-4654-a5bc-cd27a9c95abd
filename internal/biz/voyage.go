package biz

import (
	"context"
	"encoding/json"
	"math"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
)

type VoyageListQuery struct {
	ProjectInfo
	Sort
	Page            int
	Size            int
	DeviceId        *int64
	AirlineId       *int64
	StartTime       *time.Time
	EndTime         *time.Time
	notProjectCheck bool
}

func (q *VoyageListQuery) NotCheckProject() bool {
	return q.notProjectCheck
}

type VoyageLastQuery struct {
	ProjectInfo
	DeviceId  int64
	IsFlown   *bool
	TimeScope *TimeScope
}

type VoyageOverviewQuery struct {
	ProjectInfo
	DeviceId *int64
}

type VoyageOverview struct {
	DeviceId int64
	Times    int32
	Mileages int32
	Images   int32
	Videos   int32
}

type Statement struct {
	Cruise  int64
	Landing int64
	Launch  int64
	Return  int64
}

type VoyageMediaCount struct {
	Videos int32
	Images int32
}

type JointDevice struct {
	Id            int64          `json:"id,string"`
	Sn            string         `json:"sn"`
	Type          string         `json:"type"`
	Model         DeviceModel    `json:"model"`
	Category      DeviceCategory `json:"category"`
	Status        Status         `json:"status"`
	CabinStatus   bool           `json:"cabinStatus"`
	NetworkStatus bool           `json:"networkStatus"`
	LockStatus    LockStatus     `json:"lockStatus"`
	Deployment    *Deployment    `json:"deployment"`
	TenantId      int64          `json:"tenantId,string"`
	MerchantId    int64          `json:"merchantId,string"`
	FlyerId       int64          `json:"flyerId,string"`
	AvatarId      int64          `json:"avatarId,string"`
	VoyageId      *int64         `json:"voyageId,string,omitempty"`
	AirlineId     *int64         `json:"airlineId,string,omitempty"`
	MissionId     *int64         `json:"missionId,string,omitempty"`
}

func (jd *JointDevice) Copy(device *Device) {
	copier.Copy(jd, device)
}

func (jd *JointDevice) ToAlgo() *client.EventDeviceInfo {
	return &client.EventDeviceInfo{
		SourceId:   conv.Itoa(jd.Id),
		MerchantId: conv.Itoa(jd.MerchantId),
		TenantId:   conv.Itoa(jd.TenantId),
		Subsystem:  SystemName,
	}
}

type Voyage struct {
	Id         int64
	Sn         string
	Name       string
	DeviceId   int64
	AirlineId  int64
	Airline    *Airline
	FlightId   string
	Status     string
	IsFlown    bool
	TenantId   int64
	MerchantId int64
	Mileage    int32
	Runtime    int32
	Videos     int32
	Images     int32
	// 飞机拍摄媒体总数
	DroneMediaTotal int32
	Statement       AnyMap `copier:"-"`
	StartTime       time.Time
	EndTime         time.Time
	GuidePoints     []*GuidePoint
	CreatedTime     time.Time
	UpdatedTime     time.Time
	Extra           AnyMap
}

// 经过指定目标点
func (v *Voyage) Course(point *GuidePoint) AnyMap {
	v.GuidePoints = append(v.GuidePoints, point)
	guidePoints, _ := json.Marshal(v.GuidePoints)
	return AnyMap{"guide_points": guidePoints}
}

type VoyageRepo interface {
	CreateVoyage(context.Context, *Voyage) (*Voyage, error)
	UpdateVoyage(context.Context, int64, AnyMap) error
	LastVoyage(ctx context.Context, query *VoyageLastQuery) (*Voyage, error)
	ListVoyages(ctx context.Context, query *VoyageListQuery) (int32, []*Voyage, error)
	RangeVoyages(ctx context.Context, query *VoyageOverviewQuery) ([]*VoyageOverview, error)
	AggregateVoyages(ctx context.Context, query *VoyageOverviewQuery) (*VoyageOverview, error)
	GetVoyage(ctx context.Context, query *DetailQuery) (*Voyage, error)
	GetFlight(ctx context.Context, flightId string) (*Voyage, error)
	GetJointDevice(ctx context.Context, id int64) (*JointDevice, error)
	CacheJointDevice(ctx context.Context, id int64, device *JointDevice) error
	IncressVoyageMediaCount(ctx context.Context, id int64, c VoyageMediaCount) error
}

type VoyageUsecase struct {
	aRepo AuthRepo
	lRepo AirlineRepo
	vRepo VoyageRepo
	pRepo PropertyRepo
	log   *log.Helper
}

func NewVoyageUsecase(logger log.Logger, aRepo AuthRepo, lRepo AirlineRepo, vRepo VoyageRepo, pRepo PropertyRepo) *VoyageUsecase {
	return &VoyageUsecase{aRepo: aRepo, lRepo: lRepo, vRepo: vRepo, pRepo: pRepo, log: log.NewHelper(logger)}
}

func (uc *VoyageUsecase) ListVoyage(ctx context.Context, query *VoyageListQuery) (int32, []*Voyage, error) {
	total, list, err := uc.vRepo.ListVoyages(ctx, query)
	if err != nil {
		return 0, nil, err
	}
	airlineIds := lo.Map(list, func(o *Voyage, _ int) int64 { return o.AirlineId })
	_, airlines, err := uc.lRepo.ListAirlines(ctx, &AirlineListQuery{
		Ids:         lo.Uniq(airlineIds),
		ProjectInfo: query.ProjectInfo,
		Page:        1,
		Size:        100,
		Unscoped:    true,
	})
	if err != nil {
		return 0, nil, err
	}
	airlineMap := lo.KeyBy(airlines, func(a *Airline) int64 { return a.Id })
	for _, v := range list {
		v.Airline = airlineMap[v.AirlineId]
	}
	return total, list, nil
}

func (uc *VoyageUsecase) GetVoyage(ctx context.Context, query *DetailQuery) (*Voyage, error) {
	return uc.vRepo.GetVoyage(ctx, query)
}

func (uc *VoyageUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}

func (uc *VoyageUsecase) GetVoyageJointDevice(ctx context.Context, query *DetailQuery) (*JointDevice, error) {
	return uc.vRepo.GetJointDevice(ctx, query.Id)
}

func (uc *VoyageUsecase) GetVoyageMomentLnglat(ctx context.Context, sn string, timestamp int64) ([]float64, error) {
	_, properties, err := uc.pRepo.ListDockDroneProperties(ctx, &PropListQuery{
		Page:         1,
		Size:         10,
		Sn:           sn,
		disableCount: true,
		Start:        time.UnixMilli(timestamp - 5000),
		End:          time.UnixMilli(timestamp + 5000),
	})
	if err != nil {
		return nil, err
	}
	var lnglat []float64
	minDiff := math.MaxInt64
	for _, p := range properties {
		diff := int(math.Abs(float64(p.Timestamp.UnixMilli() - timestamp)))
		if diff < minDiff && p.FlightState != nil {
			minDiff = diff
			lnglat = []float64{p.FlightState.Longitude, p.FlightState.Latitude}
		}
	}
	return lnglat, nil
}

type VoyagePathPoint struct {
	Timestamp   time.Time
	PointLntlat []float64
	// 水平速度 m/s
	HorizontalSpeed float32
	// 垂直速度 m/s
	VerticalSpeed float32
	// 绝对高度	相对地球椭球面高度
	Height float32
	// 相对起飞点高度
	Elevation float32
}

func (uc *VoyageUsecase) GetVoyageFligthPath(ctx context.Context, query *DetailQuery) ([]VoyagePathPoint, error) {
	v, err := uc.vRepo.GetVoyage(ctx, query)
	if err != nil {
		return nil, err
	}
	_, properties, err := uc.pRepo.ListDockDroneProperties(ctx, &PropListQuery{
		Sn:           v.Sn,
		Start:        v.StartTime,
		End:          lo.Ternary(v.EndTime.IsZero(), time.Now(), v.EndTime),
		Page:         1,
		Size:         3600,
		disableCount: true,
	})
	if err != nil {
		return nil, err
	}
	return lo.FilterMap(properties, func(it *DroneProperties, _ int) (VoyagePathPoint, bool) {
		p := VoyagePathPoint{
			Timestamp: it.Timestamp,
		}
		if fs := it.FlightState; fs != nil {
			p.PointLntlat = []float64{fs.Longitude, fs.Latitude}
			p.HorizontalSpeed = fs.HorizontalSpeed
			p.VerticalSpeed = fs.VerticalSpeed
			p.Height = fs.Height
			p.Elevation = fs.Elevation
		} else {
			return p, false
		}
		return p, true
	}), nil
}
