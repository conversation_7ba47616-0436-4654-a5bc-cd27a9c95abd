package biz

import (
	"context"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
)

type MQTTSubscription struct {
	Node            string
	Topic           string
	ClientId        string
	Qos             byte
	NotLocal        byte
	RetainPublished byte
	RetainHanding   byte
}

type PublishResult struct {
	// 16 没能匹配到任何订阅
	// 131 消息转发时发生错误，例如 EMQX 服务重启
	// 144 主题名称非法
	// 151 受到了速率限制，或者消息尺寸过大。全局消息大小限制可以通过配置项 mqtt.max_packet_size 来进行修改
	Code    int
	Id      string
	Message string
}

type MQTTClientInfo struct {
	Username string
	ClientId string
	Password string
}

func (i *MQTTClientInfo) IsSuper() bool {
	return strings.HasPrefix(i.Username, "@sys")
}

// web、客户端应用
func (i *MQTTClientInfo) IsAppClient() bool {
	return strings.HasPrefix(i.Username, "@cus")
}

type MQTTConnectResult struct {
	Ok      bool
	IsSuper bool
}

type MQTTClientTopicAccessAction int

const (
	MQTTClientTopicAccessActionSubscribe MQTTClientTopicAccessAction = iota + 1
	MQTTClientTopicAccessActionPublish
)

type MQTTClientTopicAccessInfo struct {
	Username string
	ClientId string
	Topic    string
	Action   MQTTClientTopicAccessAction
}

type MQTTClientAccessResult struct {
	Ok bool
}

type MQTTRepo interface {
	Publish(ctx context.Context, topic string, qos byte, data []byte) (PublishResult, error)
	GetClientSubscriptions(ctx context.Context, clientId string) ([]*MQTTSubscription, error)
	CanClientConnect(ctx context.Context, info *MQTTClientInfo) (MQTTConnectResult, error)
	HasAccessToTopic(ctx context.Context, info *MQTTClientTopicAccessInfo) (MQTTClientAccessResult, error)
}

type MQTTUsecase struct {
	log  *log.Helper
	repo MQTTRepo
}

func NewMQTTUsecase(
	logger log.Logger,
	r MQTTRepo,
) *MQTTUsecase {
	return &MQTTUsecase{
		log:  log.NewHelper(logger),
		repo: r,
	}
}

func (u *MQTTUsecase) Publish(ctx context.Context, topic string, qos byte, data []byte) (string, error) {
	u.log.Infof("publish to topic %s data %s", topic, data)
	r, err := u.repo.Publish(ctx, topic, qos, data)
	if err != nil {
		u.log.Errorf("publish to topic %s failed %v", topic, err)
		return "", err
	}
	if r.Code != 0 {
		if r.Code == 131 {
			return "", errors.InternalServer("Publish.emqxFailed", r.Message)
		}
		return "", NewBadRequestError(fmt.Sprintf("Publish.invalidparam code %d msg %s", r.Code, r.Message), nil)
	}
	return r.Id, nil
}

func (u *MQTTUsecase) GetClientSubscriptions(ctx context.Context, clientId string) ([]*MQTTSubscription, error) {
	return u.repo.GetClientSubscriptions(ctx, clientId)
}

func (u *MQTTUsecase) CanClientConnect(ctx context.Context, info *MQTTClientInfo) (MQTTConnectResult, error) {
	return u.repo.CanClientConnect(ctx, info)
}

func (u *MQTTUsecase) HasAccessToTopic(ctx context.Context, info *MQTTClientTopicAccessInfo) (MQTTClientAccessResult, error) {
	return u.repo.HasAccessToTopic(ctx, info)
}
