package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
)

type FailureReason int

const (
	ReasonNone FailureReason = iota
	ReasonTimeout
	ReasonWeather
	ReasonOffline
	ReasonIndebug
	ReasonBattery
	ReasonFlying
	ReasonOutcabin
	ReasonDevice
	ReasonAirline
	ReasonUnbound
	ReasonRemoved
	ReasonLogging
	ReasonUnknown
)

func (s FailureReason) String() string {
	switch s {
	case ReasonTimeout:
		return "执行超时"
	case ReasonWeather:
		return "天气原因"
	case ReasonOffline:
		return "设备离线"
	case ReasonIndebug:
		return "设备处于调试模式"
	case ReasonBattery:
		return "电量不足"
	case ReasonFlying:
		return "正在飞行"
	case ReasonOutcabin:
		return "无人机离舱"
	case ReasonDevice:
		return "设备已删除"
	case ReasonAirline:
		return "航线已删除"
	case ReasonUnbound:
		return "绑定已解除"
	case ReasonRemoved:
		return "关系已变更"
	case ReasonLogging:
		return "拉取日志中"
	case ReasonUnknown:
		return "未知原因"
	default:
		return ""
	}
}

type ExecutionQuery struct {
	ProjectInfo
	Id           int64
	VoyageId     int64
	MissionId    int64
	OperationId  int64
	OccurredTime *time.Time
}

type ExecutionTopNQuery struct {
	ProjectInfo
	Size       int
	MissionIds []int64
	StartTime  time.Time
}

type ExecutionListQuery struct {
	ProjectInfo
	Sort
	Page      int
	Size      int
	MissionId int64
	Status    *OperationStatus
}

type SnapMission struct {
	Id            int64         `json:"id,string,omitempty"`
	Name          string        `json:"name,omitempty"`
	Type          MissionType   `json:"type,omitempty"`
	Status        MissionStatus `json:"status,omitempty"`
	AlgConfigs    []*AlgConfig  `json:"algConfigs,omitempty"`
	NoticeRules   []*NoticeRule `json:"noticeRules,omitempty"`
	LastExecution *Execution    `json:"lastExecution,omitempty"`
}

func (sm *SnapMission) Copy(mission *Mission) {
	copier.Copy(sm, mission)
}

func (sm *SnapMission) ToAlgo() *client.EventTaskInfo {
	if sm == nil {
		return nil
	}
	return &client.EventTaskInfo{
		Id:   conv.Itoa(sm.Id),
		Name: sm.Name,
		NoticeRules: lo.Map(sm.NoticeRules, func(nr *NoticeRule, _ int) *client.NoticeRule {
			return &client.NoticeRule{
				Time:        nr.Time,
				Cascade:     nr.Cascade,
				Multilevel:  nr.Multilevel,
				NoticeTypes: nr.NoticeTypes,
				Conditions:  nr.Conditions,
				Contacts: lo.Map(nr.Contacts, func(bc Contact, _ int) client.Contact {
					return client.Contact{Name: bc.Name, Contact: bc.Contact}
				}),
			}
		}),
	}
}

func (sm *SnapMission) TransEvent(atype AlgType) AnyMap {
	algoEvent := AnyMap{}
	if sm != nil && len(sm.AlgConfigs) > 0 {
		if config, ok := lo.Find(sm.AlgConfigs, func(item *AlgConfig) bool {
			return item.Type == atype
		}); ok {
			algoEvent["name"] = config.Name
			algoEvent["threshold"] = config.Threshold
		}
		if sm.LastExecution != nil {
			algoEvent["executionId"] = conv.Itoa(sm.LastExecution.Id)
		}
	}
	return algoEvent
}

type Execution struct {
	Id          int64
	Status      OperationStatus
	Reason      FailureReason
	TenantId    int64
	MerchantId  int64
	Mission     *SnapMission
	MissionId   int64
	VoyageId    int64
	DeviceId    int64
	AirlineId   int64
	OperationId int64
	AlarmCount  int32
	CreatedTime time.Time
	UpdatedTime time.Time
}

func (e *Execution) Voyage(voyageId int64) AnyMap {
	e.VoyageId = voyageId
	return AnyMap{"voyage_id": e.VoyageId}
}

func (e *Execution) Increment(count int32) AnyMap {
	e.AlarmCount += count
	return AnyMap{"alarm_count": e.AlarmCount}
}

func (e *Execution) Update(status OperationStatus, reason FailureReason) AnyMap {
	e.Status = status
	e.Reason = reason
	return AnyMap{"status": e.Status, "reason": e.Reason}
}

type ExecutionRepo interface {
	CreateExecution(ctx context.Context, body *Execution) (*Execution, error)
	UpdateExecution(ctx context.Context, id int64, body AnyMap) error
	ListExecutions(ctx context.Context, query *ExecutionListQuery) (int32, []*Execution, error)
	GetExecution(ctx context.Context, query *ExecutionQuery) (*Execution, error)
	GroupExecution(ctx context.Context, query *GroupQuery) ([]*GroupRet, error)
	TopNAlarmCount(ctx context.Context, query *ExecutionTopNQuery) ([]*CountRet, error)
}

type ExecutionUsecase struct {
	repo ExecutionRepo
	log  *log.Helper
}

func NewExecutionUsecase(logger log.Logger, repo ExecutionRepo) *ExecutionUsecase {
	return &ExecutionUsecase{repo: repo, log: log.NewHelper(logger)}
}

func (uc *ExecutionUsecase) CreateExecution(ctx context.Context, body *Execution) (*Execution, error) {
	return uc.repo.CreateExecution(ctx, body)
}

func (uc *ExecutionUsecase) ListExecution(ctx context.Context, query *ExecutionListQuery) (int32, []*Execution, error) {
	return uc.repo.ListExecutions(ctx, query)
}

func (uc *ExecutionUsecase) GetExecution(ctx context.Context, id int64) (*Execution, error) {
	return uc.repo.GetExecution(ctx, &ExecutionQuery{Id: id})
}

func (uc *ExecutionUsecase) UpdateExecution(ctx context.Context, id int64, body AnyMap) error {
	return uc.repo.UpdateExecution(ctx, id, body)
}
