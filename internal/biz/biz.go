package biz

import (
	"encoding/json"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/google/wire"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	list "gitlab.sensoro.com/go-sensoro/protoc-gen-list/list"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(
	NewAirlineUsecase,
	NewAuthUsecase,
	NewConnectUsecase,
	NewDelayUsecase,
	NewDeviceUsecase,
	NewMQTTUsecase,
	NewVoyageUsecase,
	NewOnemapUsecase,
	NewWaypointUsecase,
	NewThingUsecase,
	NewMediaUsecase,
	NewSessionUsecase,
	NewStorageUsecase,
	NewCloudUsecase,
	NewConfigUsecase,
	NewLiveUsecase,
	NewAiEventUsecase,
	NewMissionUsecase,
	NewExecutionUsecase,
	NewSnapshotUsecase,
	NewOperationUsecase,
	NewArchiveTaskUsecase,
	NewSubjectUsecase,
	NewAnnotationUsecase,
)

type DataListQuery interface {
	PageInfo() (int, int)
	Conditions() *list.Condition
}

type TimeScope struct {
	StartTime time.Time
	EndTime   time.Time
}

func NewTimeScope(start, end int64) *TimeScope {
	if start == 0 || end == 0 {
		endTime := time.Now()
		return &TimeScope{
			StartTime: endTime.Add(-24 * time.Hour),
			EndTime:   endTime,
		}
	}
	return &TimeScope{
		StartTime: time.UnixMilli(start),
		EndTime:   time.UnixMilli(end),
	}
}
func NewSimpleListQuery(page, size int32) *BaseListQuery {
	if page == 0 {
		page = 1
	}
	if size == 0 {
		size = 20
	}
	return &BaseListQuery{
		Page: page,
		Size: size,
	}
}

type BaseListQuery struct {
	Page      int32
	Size      int32
	Sort      Sort
	TimeScope *TimeScope
}

func (q *BaseListQuery) Limit() int {
	return int(q.Size)
}

func (q *BaseListQuery) Offset() int {
	return int((q.Page - 1) * q.Size)
}

const SystemName = "SKAI"
const AssetsDir = "./assets"
const ServiceTimeout = time.Second * 30
const JobServiceTimeout = time.Second * 90
const MaxTimestamp = int64(253_402_271_999_000)

type AnyMap = map[string]any
type StringMap map[string]string

type AlarmCategory string

const (
	AlarmCategoryReport  AlarmCategory = "REPORT"
	AlarmCategoryAnalyse AlarmCategory = "ANALYSE"
	SkaiCacheKeyPrefix   string        = "SKAI:CACHE"
	SkaiDelayKeyPrefix   string        = "SKAI:DELAY"
	SkaiThingKeyPrefix   string        = "SKAI:THING"
	SkaiIssueKeyPrefix   string        = "SKAI:ISSUE"
	SkaiLockKeyPrefix    string        = "SKAI:LOCK"
)

func (e AlarmCategory) String() string {
	return strings.ToLower(string(e))
}

type ListSortOrder int

const (
	ListSortOrderAsc = iota
	ListSortOrderDesc
)

func (o ListSortOrder) String() string {
	switch o {
	case ListSortOrderAsc:
		return "asc"
	case ListSortOrderDesc:
		return "desc"
	default:
		return "asc"
	}
}

type Sort struct {
	Field *string
	Order ListSortOrder
}

func (s *Sort) SortField() string {
	if s.Field == nil {
		return "created_time"
	}
	return *s.Field
}

var DefaultCreatedTimeDescSort = Sort{
	Field: lo.ToPtr("created_time"),
	Order: ListSortOrderDesc,
}

type AvatarInfo struct {
	Id       int64
	Mobile   string
	Nickname string
}

type ProjectInfo struct {
	AvatarId    int64
	TenantId    int64
	ProjectId   int64
	MerchantId  int64
	MerchantIds []int64
	merchants   []*Merchant
}

func (p *ProjectInfo) HasMerchantAccess(id int64) bool {
	return lo.Contains(p.MerchantIds, id)
}

func NewProjectInfo(av *SkaiAuthValue) *ProjectInfo {
	// 商户ID列表，默认填充-1，解决项目无任何资源库时，命中In条件
	merchantIds := []int64{-1}
	// 循环将每个资源库的ID添加到列表中
	for _, merchant := range av.Merchants {
		merchantIds = append(merchantIds, merchant.Id)
	}
	return &ProjectInfo{
		AvatarId:    av.AvatarId,
		TenantId:    av.TenantId,
		ProjectId:   av.ProjectId,
		MerchantId:  av.DefaultMerchant.Id,
		merchants:   av.Merchants,
		MerchantIds: merchantIds,
	}
}

type DetailQuery struct {
	Id       int64 // 主键ID
	Unscoped bool  // 查询软删除
	ProjectInfo
}

type AlarmsQuery struct {
	ProjectInfo
	Size      int
	Ids       []int64
	StartTime time.Time
}

type CountQuery struct {
	ProjectInfo
	CountBy string
}

type CountRet struct {
	Radix int64
	Count int32
	Name  string
}

type GroupQuery struct {
	ProjectInfo
	GroupBy   string
	SumBy     *string
	ReferIds  []int64
	StartTime time.Time
}

type GroupRet struct {
	Radix int64
	Total int32
	Count int32
}

type GeometryCoordinate [][]float64

func (g GeometryCoordinate) ToAnySlice() []any {
	return lo.Map(g, func(it []float64, _ int) any {
		return lo.ToAnySlice(it)
	})
}

type Geometry struct {
	Type        string               `json:"type"`
	Coordinates []GeometryCoordinate `json:"coordinates"`
}

func (g *Geometry) String() string {
	v, _ := json.Marshal(g)
	return string(v)
}

type LinsKafkaMessage struct {
	MsgId     string      `json:"msgId"`
	TraceId   string      `json:"traceId"`
	Action    string      `json:"action"`
	Data      interface{} `json:"data"`
	Timestamp *float64    `json:"timestamp,omitempty"`
	Key       *string     `json:"key,omitempty"`
}

func NewBadRequestError(reason string, meta map[string]string) error {
	return errors.BadRequest(reason, "参数非法").WithMetadata(meta)
}

func NewInternalError(reason string, meta map[string]string) error {
	return errors.InternalServer(reason, "网络错误").WithMetadata(meta)
}

func NewNotFoundError(resourceName, cond, val string) error {
	return errors.NotFound("", "资源不存在").WithMetadata(map[string]string{
		"resource": resourceName,
		"cond":     cond,
		"value":    val,
	})
}

func WrapCachekey(blocks ...string) string {
	bs := []string{SystemName}
	return strings.Join(append(bs, blocks...), ":")
}

var TimeLocation, _ = time.LoadLocation("Asia/Shanghai")

var TimeCopierConverter = copier.TypeConverter{
	SrcType: time.Time{},
	DstType: copier.Float64,
	Fn: func(src interface{}) (interface{}, error) {
		s, ok := src.(time.Time)
		if !ok {
			return nil, errors.InternalServer("type not matching", "类型转换错误")
		}
		return float64(s.UnixMilli()), nil
	},
}
