package biz

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
)

// m3u8文件内容签名时长
const M3U8SegmentSignatureTTL = 1 * time.Hour
const M3U8SegmentMaxDuration = 15 * time.Second

const (
	// 对于 M300 RTK 机型为视线随机头朝前，机身下方左云台。对于其他机型，对应主云台。
	LivePositionMainPTZ int32 = 0
	// 对于 M300 RTK 机型为视线随机头朝前，机身下方右云台。
	LivePositionRightPTZ      int32 = 1
	LivePositionUnderneathPTZ int32 = 2
	LivePositionFPV           int32 = 7
	//  100001 指机场外
	LivePositionDock int32 = 100001
	// 100002 机场内
	LivePositionDockInside int32 = 100002
)

type MediaProtocol string

const (
	MediaProtocolHTTP MediaProtocol = "HTTP"
	MediaProtocolRTMP MediaProtocol = "RTMP"
	MediaProtocolRTSP MediaProtocol = "RTSP"
)

const DefaultDroneliveClarity int32 = 4

type LiveHistoryQuery struct {
	Live      *Media
	StartTime time.Time
	EndTime   time.Time
}

type PlayList struct {
	Name     string
	Duration time.Duration
	TSs      []*TransportStream
	// 编码后的m3u8
	Data        []byte
	Createdtime time.Time
}

func NewPalyeListName(live *Media, start, end time.Time) string {
	return fmt.Sprintf("%d_%d-%d.m3u8", live.Id, start.Unix(), end.Unix())
}

func GetLiveIdFromName(name string) (int64, error) {
	ss := strings.Split(name, "_")
	if len(ss) != 2 {
		return 0, errors.InternalServer("invalid live name", "无效的文件名称")
	}
	return strconv.ParseInt(ss[0], 10, 64)
}

type Live struct {
	Id             int64
	DeviceId       int64
	SubDeviceIndex string
	MerchantId     int64
	CreatedTime    time.Time
	UpdatedTime    time.Time
	// 直播流： 0 离线，1 推流中
	Status int32
	// 对象存储key或直播时的streamId
	Key string
	// 录像图片地址或直播流地址
	URL  string
	Type VideoType
	//  清晰度 {"0":"自适应","1":"流畅","2":"标清","3":"高清","4":"超清"}
	Clarity int32
	// 0,1,2,7,100001, 100002
	Position int32
	Device   *Device
}

type CameraChannelListQuery struct {
	ProjectInfo
	BaseListQuery
	Search string
	Ids    []int64
}

type CameraChannelDetailQuery struct {
	ProjectInfo
	ChannelId int64
}

type CameraChannel = client.VideoChannel

type LiveRepo interface {
	GetLiveHistoryVideos(context.Context, *LiveHistoryQuery) (*PlayList, error)
	MakeValidPlayURL(ctx context.Context, urlValue string, ttl time.Duration) (string, error)
	GetLive(ctx context.Context, id int64) (*Media, error)
	GetLiveProtocolPlayUrl(ctx context.Context, live *Media, protocol MediaProtocol, private bool) (string, error)
	ListChannels(ctx context.Context, query *CameraChannelListQuery) (int64, []*CameraChannel, error)
	GetChannelDetail(ctx context.Context, query *CameraChannelDetailQuery) (*CameraChannel, error)
	UpdateLiveCamera(ctx context.Context, id int64, position int32) error
}

type LiveUsecase struct {
	log *log.Helper
	dr  DeviceRepo
	mr  MediaRepo
	lr  LiveRepo
	mq  MQTTRepo
	cr  ConnectRepo
	dlr DelayRepo
}

func NewLiveUsecase(logger log.Logger, mr MediaRepo, lr LiveRepo, mq MQTTRepo, cr ConnectRepo, dr DeviceRepo, dlr DelayRepo) *LiveUsecase {
	return &LiveUsecase{
		log: log.NewHelper(logger),
		dr:  dr,
		mr:  mr,
		lr:  lr,
		mq:  mq,
		cr:  cr,
		dlr: dlr,
	}
}

func (uc *LiveUsecase) GetLive(ctx context.Context, id int64) (*Media, error) {
	return uc.lr.GetLive(ctx, id)
}

func (uc *LiveUsecase) StartDockPushLive(ctx context.Context, live *Media) error {
	dev, err := uc.dr.GetDevice(ctx, &DetailQuery{Id: live.DeviceId})
	if err != nil {
		return err
	}
	if dev.Status == StatusOffline {
		return NewBadRequestError("StartDockPushLive.deviceOffline", map[string]string{
			"deviceId": conv.Itoa(live.DeviceId),
			"live":     conv.Itoa(live.Id),
			"liveKey":  live.Key,
		})
	}
	subDevice, ok := lo.Find(dev.Subdevices, func(item *DockSubdevice) bool {
		return item.Index == live.SubDeviceIndex
	})
	if !ok {
		return NewBadRequestError("StartDockPushLive.unknownSubDevice", map[string]string{
			"live": conv.Itoa(live.Id),
			"sd":   live.SubDeviceIndex,
		})
	}
	pc, err := uc.mr.GetLivePushConfig(ctx, dev, live)
	if err != nil {
		return err
	}
	// go func() {
	// 	if err := utilities.NewRecovedGOFunc(func() error {
	// 		time.Sleep(5 * time.Second)
	// 		gctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// 		defer cancel()
	// 		ls, err := uc.mr.GetDeviceLives(ctx, &DeviceLiveQuery{DeviceId: dev.Id, SubDeviceIndex: live.SubDeviceIndex})
	// 		if err == nil && len(ls) > 0 {
	// 			if v, ok := lo.Find(ls, func(item *Media) bool {
	// 				return item.Id == live.Id
	// 				// 离线流不需要切换镜头
	// 			}); ok && v.Status == 0 {
	// 				return nil
	// 			}
	// 		}
	// 		return uc.cr.SendDownlink(gctx, dev, uc.mq, DockServiceIdentifierSwitchLens.String(), &DockService{
	// 			Sn:         dev.Sn,
	// 			DeviceId:   dev.Id,
	// 			ServiceId:  utilities.MustNextID(),
	// 			Identifier: DockServiceIdentifierSwitchLens,
	// 			Timeout:    10 * time.Second,
	// 			Payload: &SwithLensPayload{
	// 				Live: live,
	// 				Lens: DefaultVideoType,
	// 			},
	// 		})
	// 	})(); err != nil {
	// 		uc.log.Error("StartDockPushLive.switchLens", err)
	// 	}
	// }()
	var clarity int32 = 0
	if live.GetLivePosition() == LivePositionMainPTZ {
		clarity = DefaultDroneliveClarity
	}
	return uc.cr.SendDownlink(ctx, dev, uc.mq, DockServiceIdentifierStartPushLive.String(), &DockService{
		Sn:         dev.Sn,
		DeviceId:   dev.Id,
		ServiceId:  utilities.MustNextID(),
		Identifier: DockServiceIdentifierStartPushLive,
		Timeout:    10 * time.Second,
		Payload: &StartPushLivePayload{
			Subdevice:  *subDevice,
			Live:       live,
			PushConfig: pc,
			Clarity:    clarity,
		},
	})
}

func (uc *LiveUsecase) CreateStartLiveDelayTask(ctx context.Context, dev *Device, live *Media, delay time.Duration) error {
	task := &DelayTask{
		Times:    1,
		Delay:    delay,
		Source:   "SKAI-CONNECT",
		Key:      fmt.Sprintf("%s:CONNECT:LIVE:%d", SkaiDelayKeyPrefix, live.Id),
		Callback: fmt.Sprintf("/internal/v1/media/live/%d/callback/start", live.Id),
		Payload: map[string]interface{}{
			"deviceId": conv.Itoa(dev.Id),
		},
	}
	if err := uc.dlr.CreateTask(ctx, task); err != nil {
		uc.log.Errorf("CreateStartLiveDelayTask for device %d failed %v", dev.Id, err)
		return err
	}
	uc.log.Infof("CreateStartLiveDelayTask for device %d live %d", dev.Id, live.Id)
	return nil
}

func (uc *LiveUsecase) ListChannels(ctx context.Context, query *CameraChannelListQuery) (int64, []*CameraChannel, error) {
	return uc.lr.ListChannels(ctx, query)
}
func (uc *LiveUsecase) GetChannelDetail(ctx context.Context, query *CameraChannelDetailQuery) (*CameraChannel, error) {
	return uc.lr.GetChannelDetail(ctx, query)
}
