package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/sql/geom/ewkb"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/pkg/id"
	"golang.org/x/sync/errgroup"
)

// 最低电量常量定义
const (
	// 自由飞行
	FREE_MIN_BATTERY int32 = 30
	// 一键返航
	RETURN_MIN_BATTERY int32 = 30
	// 取消返航
	CANCEL_MIN_BATTERY int32 = 20
	// 人工干预
	MANUAL_MIN_BATTERY int32 = 10
)

type DeviceModel string
type DeviceSource string
type DeviceCategory string

const (
	ModelDJIDock  DeviceModel = "DJI_DOCK"
	ModelDJILite  DeviceModel = "DJI_LITE"
	ModelDJIPilot DeviceModel = "DJI_PILOT"
)

func (m DeviceModel) String() string {
	return string(m)
}

const (
	SourceDJI DeviceSource = "DJI"
)

func (s DeviceSource) String() string {
	return string(s)
}

const (
	CategoryDock  DeviceCategory = "DOCK"
	CategoryPilot DeviceCategory = "PILOT"
)

func (c DeviceCategory) String() string {
	return string(c)
}

type Status int

const (
	StatusOffline Status = iota
	StatusService
	StatusIntest
	StatusDebug
	StatusUpgrade
	StatusStandby
	StatusExecution
)

func (s Status) String() string {
	switch s {
	case StatusOffline:
		return "OFFLINE"
	case StatusService:
		return "SERVICE"
	case StatusIntest:
		return "INTEST"
	case StatusDebug:
		return "DEBUG"
	case StatusUpgrade:
		return "UPGRADE"
	case StatusStandby:
		return "STANDBY"
	case StatusExecution:
		return "EXECUTION"
	default:
		return "OFFLINE"
	}
}

type Gradient string

const (
	// 无: None
	GradientNone Gradient = ""
	// 上线: Offline -> Standby
	GradientOnline Gradient = "A"
	// 离线: Standby -> Offline
	GradientOffline Gradient = "B"
	// 开始测试: Standby -> Intest
	GradientOnTest Gradient = "C"
	// 结束测试: Intest -> Standby
	GradientOffTest Gradient = "D"
	// 测试离线: Intest -> Offline
	GradientOutTest Gradient = "E"
	// 开启调试: Standby -> Debug
	GradientOnDebug Gradient = "F"
	// 关闭调试: Debug -> Standby
	GradientOffDebug Gradient = "G"
	// 调试离线: Debug -> Offline
	GradientOutDebug Gradient = "H"
	// 开始升级: Standby -> Upgrade
	GradientOnUpgrade Gradient = "I"
	// 结束升级: Upgrade -> Standby
	GradientOffUpgrade Gradient = "J"
	// 调试离线: Upgrade -> Offline
	GradientOutUpgrade Gradient = "K"
	// 起飞: Standby -> Execution
	GradientOnFlight Gradient = "L"
	// 返航: Execution -> Standby
	GradientOffFlight Gradient = "M"
	// 失联: Execution -> Offline
	GradientOutFlight Gradient = "N"
	// 开始检修: Standby -> Service
	GradientOnService Gradient = "O"
	// 结束检修: Service -> Standby
	GradientOffService Gradient = "P"
	// 检修离线: Service -> Offline
	GradientOutService Gradient = "Q"
	// 回收: Any -> Offline
	GradientRecycle Gradient = "Z"
)

type AeroMode int

const (
	NullMode AeroMode = iota
	FreeMode
	PointMode
	OrbitMode
)

func (m AeroMode) Name() string {
	switch m {
	case FreeMode:
		return "自由模式"
	case PointMode:
		return "指点模式"
	case OrbitMode:
		return "环绕模式"
	default:
		return "其他模式"
	}
}

type ActionControl int

const (
	NoneControl ActionControl = iota
	FlyControl
	HoverControl
	LensControl
	AeroControl
	SpeakerControl
)

func (c ActionControl) ToOperationType(enable bool) OperationType {
	var opType OperationType
	if c == HoverControl {
		opType = lo.Ternary(enable, TypePauseAirline, TypeBackAirline)
	} else if c == LensControl {
		opType = lo.Ternary(enable, TypeControlLens, TypeReleaseLens)
	} else if c == AeroControl {
		opType = lo.Ternary(enable, TypeControlAero, TypeReleaseAero)
	} else if c == SpeakerControl {
		opType = lo.Ternary(enable, TypeControlSpeaker, TypeReleaseSpeaker)
	}
	return opType
}

func (c ActionControl) Name() string {
	switch c {
	case FlyControl:
		return "起飞控制"
	case HoverControl:
		return "悬停控制"
	case LensControl:
		return "镜头控制"
	case AeroControl:
		return "飞行控制"
	case SpeakerControl:
		return "喊话控制"
	default:
		return "未知控制"
	}
}

func (c ActionControl) String() string {
	switch c {
	case FlyControl:
		return "flyControl"
	case HoverControl:
		return "hoverControl"
	case LensControl:
		return "lensControl"
	case AeroControl:
		return "aeroControl"
	case SpeakerControl:
		return "speakerControl"
	default:
		return "noneControl"
	}
}

func NewActionControl(ctrl string) ActionControl {
	switch ctrl {
	case "hoverControl":
		return HoverControl
	case "lensControl":
		return LensControl
	case "aeroControl":
		return AeroControl
	case "speakerControl":
		return SpeakerControl
	default:
		return NoneControl
	}
}

type ControlStatus int

const (
	// 已关闭
	DisabledStatus ControlStatus = iota
	// 开启中
	EnablingStatus
	// 已开启
	EnabledStatus
	// 关闭中
	DisablingStatus
)

/*
 1. LockStatus[717-1011001101] => ControlStatus
    -- 1-2 bit FlyControl
    -- 3-4 bit HoverControl
    -- 5-6 bit LensControl
    -- 7-8 bit AeroControl
    -- 9-10 bit SpeakerControl
 2. ControlStatus are 0/1/2/3:
    -- LockStatus & 3 is FlyControl
    -- LockStatus >> 2 & 3 is HoverControl
    -- LockStatus >> 4 & 3 is LensControl
    -- LockStatus >> 6 & 3 is AeroControl
    -- LockStatus >> 8 & 3 is SpeakerControl
 3. ControlStatus 0/2 are stable status only support +1; 1/3 are interim status both support ±1
 4. Success: ControlStatus +1 if overflow than reset
 5. Failure: ControlStatus -1 if overflow than exit!
*/
type LockStatus uint

func (l LockStatus) Scatter() []ControlStatus {
	fcStatus := ControlStatus(l & 3)
	hcStatus := ControlStatus(l >> 2 & 3)
	lcStatus := ControlStatus(l >> 4 & 3)
	acStatus := ControlStatus(l >> 6 & 3)
	scStatus := ControlStatus(l >> 8 & 3)
	return []ControlStatus{fcStatus, hcStatus, lcStatus, acStatus, scStatus}
}

func (l LockStatus) Ctrlable(action ActionControl, status ControlStatus) bool {
	if action == NoneControl {
		return false
	}
	return l.Scatter()[action-1] == status
}

func (l LockStatus) Compute(action ActionControl, flag bool) LockStatus {
	s, m := int(l), int(action*2)
	incr := int(math.Pow(2, float64(m-2)))
	ones := int(math.Pow(2, float64(m)) - 1)
	high, low := s>>m<<m, s&ones
	ret := lo.Ternary(flag, low+incr, low-incr) & ones
	return LockStatus(high + ret)
}

type DeviceCheck struct {
	ProjectInfo
	SourceSn string
	Model    *DeviceModel
}

type DeviceTimeout struct {
	SourceId   int64         `json:"sourceId"`
	Identifier string        `json:"identifier"`
	Timeout    time.Duration `json:"timeout"`
}

type DeviceJointQuery struct {
	ProjectInfo
	Status   *Status
	Lnglat   []float64
	Category *DeviceCategory
}

type DeviceListQuery struct {
	ProjectInfo
	Sort
	Page          int
	Size          int
	Ids           []int64
	Search        *string
	Type          *string
	Model         *DeviceModel
	Category      *DeviceCategory
	Models        []string
	Lnglat        []float64
	NetworkStatus *bool
}

type DeviceCountQuery struct {
	ProjectInfo
	CountBy string
	Filter  *string
	Id      *int64
}

type DeviceCounter struct {
	Name  string
	Count int32
	Kount int32
}

type DatalogsQuery struct {
	ProjectInfo
	Device *Device
	Page   int
	Size   int
}

type DeviceDatalogListQuery struct {
	Id     int64
	Start  time.Time
	End    time.Time
	Page   int32
	Size   int32
	Sort   Sort
	Device *Device
}

type Takeoffer struct {
	FlyerId     int64
	VoyageId    *int64
	FlightId    *string
	AirlineId   *int64
	MissionId   *int64
	Algorithm   *string
	AirlineName *string
}

type Images struct {
	DeviceImg string  `json:"deviceImg"`
	EnvImg    string  `json:"envImg"`
	ShopImg   *string `json:"shopImg"`
}

type Contact struct {
	Name    string `json:"name"`
	Contact string `json:"contact"`
}

type Deployment struct {
	Name         string     `json:"name"`
	Tags         []string   `json:"tags"`
	Images       Images     `json:"images"`
	Contacts     []Contact  `json:"contacts"`
	Status       bool       `json:"status"`
	Lnglat       []float64  `json:"lnglat"`
	Altitude     float32    `json:"altitude"`
	Location     string     `json:"location"`
	Time         *time.Time `json:"time"`
	ImageList    []string   `json:"imageList"`
	RelatedChIds []int64    `json:"relatedChIds"`
}

type BoxRegion struct {
	Left   int32 `json:"left"`
	Top    int32 `json:"top"`
	Right  int32 `json:"right"`
	Bottom int32 `json:"bottom"`
}

type AeroCamera struct {
	Index                  string     `json:"index,omitempty"`
	Mode                   CameraMode `json:"mode,omitempty"`
	PhotoState             int32      `json:"photoState,omitempty"`
	RecordingState         int32      `json:"recordingState,omitempty"`
	ZoomFactor             float32    `json:"zoomFactor,omitempty"`
	IrZoomFactor           float32    `json:"irZoomFactor,omitempty"`
	ZoomFocusMode          int32      `json:"zoomFocusMode,omitempty"`
	ZoomFocusValue         int32      `json:"zoomFocusValue,omitempty"`
	ZoomFocusState         int32      `json:"zoomFocusState,omitempty"`
	IrMeteringMode         int32      `json:"irMeteringMode,omitempty"`
	GimbalPitch            float64    `json:"gimbalPitch,omitempty"`
	GimbalYaw              float64    `json:"gimbalYaw,omitempty"`
	GimbalRoll             float64    `json:"gimbalRoll,omitempty"`
	MeasureTargetLongitude float64    `json:"measureTargetLongitude,omitempty"`
	MeasureTargetLatitude  float64    `json:"measureTargetLatitude,omitempty"`
	MeasureTargetAltitude  float64    `json:"measureTargetAltitude,omitempty"`
	MeasureTargetDistance  float64    `json:"measureTargetDistance,omitempty"`
	MeasureErrState        int32      `json:"measureErrState,omitempty"`
	LiveViewWorldRegion    BoxRegion  `json:"liveViewWorldRegion,omitempty"`
}

func (a *AeroCamera) GetMode() CameraMode {
	return lo.Ternary(a != nil, a.Mode, NoneMode)
}

// func (a *AeroCamera) IsPictured() bool {
// 	return a != nil && a.PhotoState == 1
// }

// func (a *AeroCamera) IsVideoed() bool {
// 	return a != nil && a.RecordingState == 1
// }

type SpeakerWidget struct {
	Sn              string `json:"sn"`
	Name            string `json:"name"`
	Type            string `json:"type"`
	Mode            int32  `json:"mode"`
	Index           string `json:"index"`
	Status          int32  `json:"status"`
	Volume          int32  `json:"volume"`
	Playmode        int32  `json:"playmode"`
	FirmwareVersion string `json:"firmwareVersion,omitempty"`
}

type CameraMode int

const (
	NoneMode CameraMode = iota - 1
	PictureMode
	VideoMode
	LumenMode
	PanoramaMode
)

// 一键起飞数据
type LaunchData AnyMap

type Device struct {
	Id              int64
	Sn              string
	Type            string
	Model           DeviceModel
	Category        DeviceCategory
	Source          DeviceSource
	Status          Status     // 设备状态
	LockStatus      LockStatus // 控制状态，多操作状态锁
	AeroMode        AeroMode   // 操作模式，仅当状态锁的人工控制开启时生效
	SourceSn        string
	TenantId        int64
	MerchantId      int64
	FlyerId         int64
	AvatarId        int64
	Avatar          *Avatar
	VoyageId        *int64
	AirlineId       *int64
	MissionId       *int64
	CabinStatus     bool
	VoyageTimes     int32
	NetworkType     int32
	StatusShift     bool   // 流转标识
	StatusFlower    string // 流转态记录串
	NetworkStatus   bool
	SignalQuality   string
	FirmwareVersion string
	Gradient        Gradient // 本次流转态
	Gradest         Gradient // 最后一次流转态
	Gradend         Gradient // 倒数第二流转态
	Deployment      *Deployment
	PropData        *DeviceProperty
	Speaker         *SpeakerWidget
	ExtraData       ExtraData
	LaunchData      LaunchData
	LastVoyage      *Voyage
	LiveVideos      []*Media
	AeroCameras     []*AeroCamera
	Subdevices      []*DockSubdevice
	UppedTime       *time.Time
	CreatedTime     time.Time
	UpdatedTime     time.Time
	SortOrder       int
}

func (d *Device) IsAllowed(avatar *Avatar) bool {
	// 操作者为空直接返回
	if avatar == nil {
		return false
	}
	avatarId := avatar.Id
	// 当前有操作者: 只有avatar可操作
	if d.AvatarId > 0 {
		return d.Status == StatusExecution && d.AvatarId == avatarId
	}
	// 当前无操作者: 1.非任务巡航flyer可操作
	if d.MissionId == nil {
		return d.Status == StatusExecution && d.FlyerId == avatarId
	}
	// 当前无操作者: 2.任务巡航时admin和flyer均可操作
	if avatar.RoleType == 1 {
		return d.Status == StatusExecution
	} else {
		return d.Status == StatusExecution && d.FlyerId == avatarId
	}
}

func (d *Device) IsVideoed() bool {
	return conv.Vtot(d.ExtraData["isVideoed"])
}

func (d *Device) IsSpeaking() bool {
	return conv.Vtot(d.ExtraData["isSpoke"])
}

func (d *Device) IsFreed() bool {
	return conv.Vtot(d.ExtraData["isFreed"])
}

func (d *Device) IsPointed() bool {
	return conv.Vtot(d.ExtraData["isPointed"])
}

func (d *Device) IsOrbited() bool {
	return conv.Vtot(d.ExtraData["isOrbited"])
}

func (d *Device) IsFused(battery int32) bool {
	return d.PropData.BatteryCapacity == nil || *d.PropData.BatteryCapacity < battery
}

func (d *Device) IsControlled(action ActionControl) bool {
	return d.Status == StatusExecution && d.LockStatus.Ctrlable(action, EnabledStatus)
}

func (d *Device) IsReleased(action ActionControl) bool {
	return d.Status == StatusExecution && d.LockStatus.Ctrlable(action, DisabledStatus)
}

func (d *Device) IsDeployed() bool {
	return d.MerchantId != 0 && bool(d.Deployment.Status)
}

// 存储喊话器信息
func (d *Device) Buzzing(widget *SpeakerWidget) AnyMap {
	speaker, _ := json.Marshal(widget)
	return AnyMap{"speaker": speaker}
}

// 存储一键起飞信息
func (d *Device) Launch(payload *LaunchPayload) AnyMap {
	launchData, _ := json.Marshal(payload)
	return AnyMap{"launch_data": launchData}
}

// 存储起飞成功信息
func (d *Device) Takeoff(offer *Takeoffer) AnyMap {
	d.FlyerId = offer.FlyerId
	d.VoyageId = offer.VoyageId
	d.AirlineId = offer.AirlineId
	d.MissionId = offer.MissionId
	// 设置本次飞行加载算法和起飞时间
	d.PropData.Algorithm = offer.Algorithm
	d.PropData.TakeoffTime = time.Now().UnixMilli()
	propData, _ := json.Marshal(d.PropData)
	data := AnyMap{
		"flyer_id":   d.FlyerId,
		"avatar_id":  d.FlyerId,
		"voyage_id":  d.VoyageId,
		"airline_id": d.AirlineId,
		"mission_id": d.MissionId,
		"prop_data":  propData,
	}
	// 任务巡航操作员默认为None
	if d.MissionId != nil && *d.MissionId > 0 {
		d.AvatarId = 0
		data["avatar_id"] = 0
	}
	return data
}

func (d *Device) Return() AnyMap {
	d.FlyerId = 0
	d.AvatarId = 0
	d.VoyageId = nil
	d.LockStatus = 0
	d.AeroMode = NullMode
	d.AirlineId = nil
	d.MissionId = nil
	d.PropData.Mileage = 0
	d.PropData.Algorithm = nil
	d.PropData.TakeoffTime = 0
	d.PropData.BatteryCapacity = nil
	d.PropData.ChildrenCapacity = make([]any, 0)
	d.PropData.CurrentWaypoint = nil
	d.PropData.CourseWaypoints = make([]any, 0)
	propData, _ := json.Marshal(d.PropData)
	d.ExtraData.ReturnReset()
	extraData, _ := json.Marshal(d.ExtraData)
	d.AeroCameras = make([]*AeroCamera, 0)
	aeroCameras, _ := json.Marshal(d.AeroCameras)
	return AnyMap{
		"flyer_id":     d.FlyerId,
		"avatar_id":    d.AvatarId,
		"voyage_id":    d.VoyageId,
		"airline_id":   d.AirlineId,
		"mission_id":   d.MissionId,
		"aero_mode":    d.AeroMode,
		"lock_status":  d.LockStatus,
		"prop_data":    propData,
		"extra_data":   extraData,
		"aero_cameras": aeroCameras,
	}
}

func (d *Device) Assign(tenantId, merchantId int64) AnyMap {
	d.TenantId = tenantId
	d.MerchantId = merchantId
	return AnyMap{"tenant_id": d.TenantId, "merchant_id": d.MerchantId}
}

func (d *Device) Deploy(deployment *Deployment) AnyMap {
	d.Deployment = deployment
	images, _ := json.Marshal(d.Deployment.Images)
	contacts, _ := json.Marshal(d.Deployment.Contacts)
	return AnyMap{
		"deployment_status":         true,
		"deployment_images":         images,
		"deployment_contacts":       contacts,
		"deployment_name":           d.Deployment.Name,
		"deployment_time":           d.Deployment.Time,
		"deployment_altitude":       d.Deployment.Altitude,
		"deployment_location":       d.Deployment.Location,
		"deployment_tags":           pq.StringArray(d.Deployment.Tags),
		"deployment_lnglat":         ewkb.Point(d.Deployment.Lnglat),
		"deployment_related_ch_ids": pq.Int64Array(d.Deployment.RelatedChIds),
	}
}

func (d *Device) Graduate(grade Gradient) string {
	flower := fmt.Sprintf("%s%s", grade, d.StatusFlower)
	if len(flower) > 32 {
		flower = flower[:32]
	}
	return flower
}

func (d *Device) Recycle() AnyMap {
	d.UppedTime = nil
	d.NetworkStatus = false
	d.SignalQuality = "NONE"
	d.Status = StatusOffline
	d.Deployment.Status = false
	d.StatusFlower = d.Graduate(GradientRecycle)
	d.Deployment.Lnglat = ewkb.Point([]float64{})
	d.PropData = nil
	propData, _ := json.Marshal(d.PropData)
	owner := d.Assign(0, 0)
	children := d.Children([]*DockSubdevice{})
	return lo.Assign(owner, children, AnyMap{
		"status":            d.Status,
		"prop_data":         propData,
		"upped_time":        d.UppedTime,
		"status_flower":     d.StatusFlower,
		"network_status":    d.NetworkStatus,
		"signal_quality":    d.SignalQuality,
		"deployment_status": d.Deployment.Status,
		"deployment_lnglat": d.Deployment.Lnglat,
	})
}

func (d *Device) IsAviated(aeroMode AeroMode) bool {
	// 无效飞行模式
	if aeroMode == NullMode {
		return false
	}
	switch d.Model {
	// 遥控不支持指令飞行
	case ModelDJIPilot:
		return false
	// 二代机场不支持环绕飞行
	case ModelDJILite:
		return aeroMode != OrbitMode
	// 一代机场支持所有模式
	case ModelDJIDock:
		return true
	}
	return false
}

func (d *Device) Aviate(aeroMode AeroMode) AnyMap {
	d.AeroMode = aeroMode
	return AnyMap{"aero_mode": d.AeroMode}
}

func (d *Device) ResetLock(status uint) AnyMap {
	d.LockStatus = LockStatus(status)
	return AnyMap{"lock_status": d.LockStatus}
}

func (d *Device) Control(action ActionControl, flag bool) AnyMap {
	d.LockStatus = d.LockStatus.Compute(action, flag)
	return AnyMap{"lock_status": d.LockStatus}
}

func (d *Device) Operate(avatarId int64) AnyMap {
	// 设置操作者条件：仅暂停航线，镜头控制，飞行控制，喊话成功，设备无操作者，本次有操作者
	controls := d.LockStatus.Scatter()
	if lo.Contains(controls[1:], EnabledStatus) && d.AvatarId == 0 && avatarId > 0 {
		d.AvatarId = avatarId
	}
	return AnyMap{"avatar_id": d.AvatarId}
}

func (d *Device) Offline() AnyMap {
	gradient := d.Gradient
	if d.Status == StatusService {
		gradient = GradientOutService
	} else if d.Status == StatusIntest {
		gradient = GradientOutTest
	} else if d.Status == StatusDebug {
		gradient = GradientOutDebug
	} else if d.Status == StatusUpgrade {
		gradient = GradientOutUpgrade
	} else if d.Status == StatusStandby {
		gradient = GradientOffline
	} else if d.Status == StatusExecution {
		gradient = GradientOutFlight
	}
	d.Gradient = gradient
	d.Status = StatusOffline
	d.NetworkStatus = false
	d.SignalQuality = "NONE"
	d.StatusFlower = d.Graduate(gradient)
	// 离线时失联上行属性(保留起飞计算参数)，参见Takeoff方法
	d.PropData = d.PropData.Dislink()
	propData, _ := json.Marshal(d.PropData)
	data := AnyMap{
		"status":         d.Status,
		"prop_data":      propData,
		"status_flower":  d.StatusFlower,
		"network_status": d.NetworkStatus,
		"signal_quality": d.SignalQuality,
	}
	if d.Category == CategoryPilot {
		d.Deployment.Lnglat = ewkb.Point([]float64{})
		data["deployment_lnglat"] = d.Deployment.Lnglat
	}
	return data
}

func (d *Device) Xross(data *AutoFlightTaskProgressEvent) AnyMap {
	index := data.CurrentWaypointIndex
	d.PropData.CurrentWaypoint = &index
	if _, ok := lo.Find(d.PropData.CourseWaypoints, func(item any) bool { return int32(conv.Vtoi(item)) == index }); !ok {
		d.PropData.CourseWaypoints = append(d.PropData.CourseWaypoints, index)
	}
	propData, _ := json.Marshal(d.PropData)
	return AnyMap{"prop_data": propData}
}

func (d *Device) Children(list []*DockSubdevice) AnyMap {
	d.Subdevices = list
	subdevices, _ := json.Marshal(d.Subdevices)
	return AnyMap{"subdevices": subdevices}
}

func (d *Device) GetGimbal() *DockSubdevice {
	// 1.查找无人机子设备
	if drone, ok := lo.Find(d.Subdevices, func(db *DockSubdevice) bool {
		return db.Domain == SubDeviceDomainDrone
	}); ok {
		// 查找无人机对应的云台camera子设备
		if gimbal, ok := lo.Find(d.Subdevices, func(db *DockSubdevice) bool {
			return db.Domain == SubDeviceDomainCamera && db.Sn == drone.Sn && db.Index != "39-0-7" && db.Index != "176-0-0"
		}); ok {
			return gimbal
		}
	}
	return nil
}

func (d *Device) GetCamera(subdevice *DockSubdevice) *AeroCamera {
	if subdevice == nil {
		return nil
	}
	if camera, ok := lo.Find(d.AeroCameras, func(item *AeroCamera) bool {
		return item.Index == subdevice.Index
	}); ok {
		return camera
	}
	return nil
}

// 检测健康事件
func (d *Device) DetectHMSEvents(body *DeviceProperty) []*DockHealMonitorEvent {
	events := make([]*DockHealMonitorEvent, 0)
	thingEvent := ThingEvent{Sn: d.SourceSn, Id: id.NewUUIDV1(), DeviceId: d.Id, OccurredTime: time.Now(), Type: ThingModelEventTypeHMS}
	sky := lo.Ternary(d.CabinStatus, int32(0), 1)
	// 飞机在舱表示空闲中，先检查充电状态，其次校验充电电量
	if d.CabinStatus {
		// 关机中则检查充电状态，充电中不生成事件
		if body.ChargeState != nil && *body.ChargeState == 0 {
			// 低电事件: 如果充电状态为0，则检查充电电量是否小于30
			if body.ChargePercent != nil && *body.ChargePercent < RETURN_MIN_BATTERY {
				event := &DockHealMonitorEvent{
					ThingEvent: thingEvent, Level: 2,
					Module: 3, Source: 2, InTheSky: sky, Imminent: 0,
					Code: "91000000", Extra: AnyMap{"name": d.Deployment.Name, "category": "机场", "customType": LowBatteryEvent},
				}
				events = append(events, event)
			}
			// 维保事件: 如果充电状态为0，则检查充电电量是否小于10
			if body.ChargePercent != nil && *body.ChargePercent < MANUAL_MIN_BATTERY {
				event := &DockHealMonitorEvent{
					ThingEvent: thingEvent, Level: 2,
					Module: 3, Source: 2, InTheSky: sky, Imminent: 0,
					Code: "92000001", Extra: AnyMap{"name": d.Deployment.Name, "category": "机场", "customType": MinBatteryEvent},
				}
				events = append(events, event)
			}
		}
	} else {
		// 低电事件: 飞机离舱表示飞行中，正常校验电量是否小于30
		if body.BatteryCapacity != nil && *body.BatteryCapacity < RETURN_MIN_BATTERY {
			event := &DockHealMonitorEvent{
				ThingEvent: thingEvent, Level: 2,
				Module: 3, Source: 2, InTheSky: sky, Imminent: 0,
				Code: "91000000", Extra: AnyMap{"name": d.Deployment.Name, "category": "机场", "customType": LowBatteryEvent},
			}
			events = append(events, event)
		}
		// 维保事件: 飞机离舱表示飞行中，正常校验电量是否小于10
		if body.BatteryCapacity != nil && *body.BatteryCapacity < MANUAL_MIN_BATTERY {
			event := &DockHealMonitorEvent{
				ThingEvent: thingEvent, Level: 2,
				Module: 3, Source: 2, InTheSky: sky, Imminent: 0,
				Code: "92000000", Extra: AnyMap{"name": d.Deployment.Name, "category": "机场", "customType": MinBatteryEvent},
			}
			events = append(events, event)
		}
	}
	return events
}

func (d *Device) AnalyzeStatus(body *DeviceProperty) {
	status := d.Status
	d.Status, d.Gradient = body.AnalyzeStatus(status)
	// 本次流转态不为空，且跟上次有变化，则记录变更且追加
	if d.Gradient != GradientNone && d.Gradient != d.Gradest {
		d.StatusShift = true
		d.StatusFlower = d.Graduate(d.Gradient)
	}
}

func (d *Device) AnalyzeTakeoffEvent(body *DeviceProperty) *ThingEvent {
	// 遥控上行不影响飞行记录数据变更，机场不用处理
	if d.PropData.MsgType == 2 || d.Category == CategoryDock {
		return nil
	}
	if d.Gradient == GradientOnFlight {
		return &ThingEvent{
			Sn:           d.SourceSn,
			Id:           id.NewUUIDV1(),
			DeviceId:     d.Id,
			OccurredTime: time.Now(),
			Type:         ThingModelEventTypePilotTakeoff,
		}
	}
	return nil
}

func (d *Device) AnalyzeReturnEvent(body *DeviceProperty) *FlightReturnEvent {
	// 遥控上行不影响飞行记录数据变更
	if d.PropData.MsgType == 2 {
		return nil
	}
	evented := false
	switch d.Gradient {
	case GradientOnline:
		// 本次流转态为在线且[上次或上上次]为失联，则清空Takeoff数据
		evented = lo.Contains([]Gradient{d.Gradest, d.Gradend}, GradientOutFlight)
	case GradientOffFlight:
		// 本次流转态为返航，则以当前时间更新航次结束时间
		evented = true
	}
	if evented {
		return &FlightReturnEvent{
			Extra:      AnyMap{},
			Status:     d.Gradient,
			Mileage:    body.Mileage,
			ThingEvent: ThingEvent{Sn: d.SourceSn, Id: id.NewUUIDV1(), DeviceId: d.Id, OccurredTime: time.Now(), Type: ThingModelEventTypeFlightReturn},
		}
	}
	return nil
}

func (d *Device) AnalyzeStartLiveEvent(body *DeviceProperty) *StartLiveEvent {
	if body.LastDroneState == nil || body.DroneState == nil {
		return nil
	}
	// 上次飞机状态小于当前状态，且当前手动飞行/自动起飞/航线飞行，则判定为飞机升空，等价于直播流开启
	if *body.LastDroneState < *body.DroneState && *body.LastDroneState < 3 && lo.Contains([]int32{3, 4, 5}, *body.DroneState) {
		return &StartLiveEvent{
			Status:     true,
			Extra:      AnyMap{},
			TaskId:     *d.VoyageId,
			ThingEvent: ThingEvent{Sn: d.SourceSn, Id: id.NewUUIDV1(), DeviceId: d.Id, OccurredTime: time.Now(), Type: ThingModelEventTypeStartLive},
		}
	}
	return nil
}

func (d *Device) AnalyzeForceBackEvent(body *DeviceProperty) *ThingEvent {
	// 目前仅设备低电+自由飞行会触发
	if body.BatteryCapacity == nil || !d.IsFreed() {
		return nil
	}
	if *body.BatteryCapacity < FREE_MIN_BATTERY {
		return &ThingEvent{Sn: d.SourceSn, Id: id.NewUUIDV1(), DeviceId: d.Id, OccurredTime: time.Now(), Type: ThingModelEventTypeFlightForceBack}
	}
	return nil
}

type deviceEventAnalyzeState struct {
	ts   time.Time
	from map[string]int32
	to   map[string]int32
}

var deviceEventAnalyzeStatesRecords = map[int64]*deviceEventAnalyzeState{}
var asrLock = sync.Mutex{}

func init() {
	go func() {
		t := time.NewTicker(time.Minute)
		for {
			<-t.C
			now := time.Now()
			asrLock.Lock()
			for k, v := range deviceEventAnalyzeStatesRecords {
				if now.Sub(v.ts) > time.Minute {
					delete(deviceEventAnalyzeStatesRecords, k)
				}
			}
			asrLock.Unlock()
		}
	}()
}

func noNeedForDeviceAnalyzeState(id int64, stateName string, from, to int32) bool {
	if from == to {
		return true
	}
	asrLock.Lock()
	defer asrLock.Unlock()
	asr, ok := deviceEventAnalyzeStatesRecords[id]
	if !ok {
		asr = &deviceEventAnalyzeState{
			ts: time.Now(),
			from: map[string]int32{
				stateName: from,
			},
			to: map[string]int32{
				stateName: to,
			},
		}
		deviceEventAnalyzeStatesRecords[id] = asr
		return false
	}
	now := time.Now()
	if asr.from[stateName] == from && asr.to[stateName] == to && now.Sub(asr.ts) < time.Second {
		return true
	}
	asr.ts = now
	asr.from[stateName] = from
	asr.to[stateName] = to
	deviceEventAnalyzeStatesRecords[id] = asr
	return false
}

func (d *Device) AnalyzeCoverOperationEvent(body *DeviceProperty, lastState *DeviceProperty) (bool, OperationType, *OperationProgressEvent) {
	if d.Status != StatusDebug {
		return false, TypeUnknown, nil
	}
	ot := TypeUnknown
	if lastState.CoverState != nil && body.CoverState != nil {
		last := lo.FromPtr(lastState.CoverState)
		now := lo.FromPtr(body.CoverState)
		st := DockServiceIdentifierUnknown
		if noNeedForDeviceAnalyzeState(d.Id, "cover", last, now) {
			return false, ot, nil
		}
		if (last == 1 || last == 2) && now == 0 {
			st = DockServiceIdentifierCloseCover
			ot = TypeCloseCover
		}
		if (last == 0 || last == 2) && now == 1 {
			st = DockServiceIdentifierOpenCover
			ot = TypeOpenCover
		}
		if st != DockServiceIdentifierUnknown {
			return true, ot, &OperationProgressEvent{
				ThingEvent:  ThingEvent{Sn: d.SourceSn, Id: id.NewUUIDV1(), DeviceId: d.Id, OccurredTime: time.Now(), Type: ThingModelEventTypeOperationProgress},
				Status:      string(ServiceReplyStatusOk),
				ServiceType: st.String(),
				Progress:    100,
			}
		}
	}
	return false, ot, nil
}

func (d *Device) AnalyzeDroneOnOffOperationEvent(body *DeviceProperty, lastState *DeviceProperty) (bool, OperationType, *OperationProgressEvent) {
	if d.Status != StatusDebug {
		return false, TypeUnknown, nil
	}
	ot := TypeUnknown
	if lastState.DroneOnlineState == nil || body.DroneOnlineState == nil {
		return false, ot, nil
	}
	last := lo.FromPtr(lastState.DroneOnlineState)
	now := lo.FromPtr(body.DroneOnlineState)
	st := DockServiceIdentifierUnknown
	if noNeedForDeviceAnalyzeState(d.Id, "drone", last, now) {
		return false, ot, nil
	}
	if last == 0 && now == 1 {
		st = DockServiceIdentifierOpenDrone
		ot = TypeOpenDrone
	}
	if last == 1 && now == 0 {
		st = DockServiceIdentifierCloseDrone
		ot = TypeCloseDrone
	}
	if st == DockServiceIdentifierUnknown {
		return false, ot, nil
	}
	return true, ot, &OperationProgressEvent{
		ThingEvent:  ThingEvent{Sn: d.SourceSn, Id: id.NewUUIDV1(), DeviceId: d.Id, OccurredTime: time.Now(), Type: ThingModelEventTypeOperationProgress},
		Status:      string(ServiceReplyStatusOk),
		ServiceType: st.String(),
		Progress:    100,
	}
}

func (d *Device) AnalyseChargeStateOperationEvent(body *DeviceProperty, lastState *DeviceProperty) (bool, OperationType, *OperationProgressEvent) {
	if d.Status != StatusDebug {
		return false, TypeUnknown, nil
	}
	ot := TypeUnknown
	if lastState.ChargeState == nil || body.ChargeState == nil {
		return false, ot, nil
	}
	last := lo.FromPtr(lastState.ChargeState)
	now := lo.FromPtr(body.ChargeState)
	if noNeedForDeviceAnalyzeState(d.Id, "charge", last, now) {
		return false, ot, nil
	}
	st := DockServiceIdentifierUnknown
	if last == 1 && now == 0 {
		ot = TypeCloseCharge
		st = DockServiceIdentifierCloseCharge
	}
	if last == 0 && now == 1 {
		ot = TypeOpenCharge
		st = DockServiceIdentifierOpenCharge
	}
	if ot == TypeUnknown {
		return false, ot, nil
	}
	return true, ot, &OperationProgressEvent{
		ThingEvent:  ThingEvent{Sn: d.SourceSn, Id: id.NewUUIDV1(), DeviceId: d.Id, OccurredTime: time.Now(), Type: ThingModelEventTypeOperationProgress},
		Status:      string(ServiceReplyStatusOk),
		ServiceType: st.String(),
		Progress:    100,
	}
}

func (d *Device) AnalyzePutterStateOperationEvent(body *DeviceProperty, lastState *DeviceProperty) (bool, OperationType, *OperationProgressEvent) {
	if d.Status != StatusDebug {
		return false, TypeUnknown, nil
	}
	ot := TypeUnknown
	if lastState.PutterState == nil || body.PutterState == nil {
		return false, ot, nil
	}
	last := lo.FromPtr(lastState.PutterState)
	now := lo.FromPtr(body.PutterState)
	st := DockServiceIdentifierUnknown
	if noNeedForDeviceAnalyzeState(d.Id, "putter", last, now) {
		return false, ot, nil
	}
	if (last == 0 || last == 2) && now == 1 {
		st = DockServiceIdentifierOpenPutter
		ot = TypeOpenPutter
	}
	if (last == 1 || last == 2) && now == 0 {
		st = DockServiceIdentifierClosePutter
		ot = TypeClosePutter
	}
	if st == DockServiceIdentifierUnknown {
		return false, ot, nil
	}
	return true, ot, &OperationProgressEvent{
		ThingEvent:  ThingEvent{Sn: d.SourceSn, Id: id.NewUUIDV1(), DeviceId: d.Id, OccurredTime: time.Now(), Type: ThingModelEventTypeOperationProgress},
		Status:      string(ServiceReplyStatusOk),
		ServiceType: st.String(),
		Progress:    100,
	}
}

func (d *Device) PropertyUp(body *DeviceProperty) AnyMap {
	data := AnyMap{"upped_time": lo.ToPtr(time.UnixMilli(body.UppedTime))}
	// 单独存储飞行相机，并将body原有置空避免冗余存储
	if len(body.AeroCameras) > 0 {
		if aeroCameras, err := json.Marshal(body.AeroCameras); err == nil {
			data["aero_cameras"] = aeroCameras
		}
	}
	// 机舱无海拔高度则存储
	if d.Deployment.Altitude == 0 && body.DockHeight != nil && *body.DockHeight > 0 {
		data["deployment_altitude"] = *body.DockHeight
	}
	// 清空冗余字段
	body.Speaker = nil
	body.AeroCameras = nil
	if propData, err := json.Marshal(body); err == nil {
		data["prop_data"] = propData
	}
	if d.StatusShift {
		data["status"] = d.Status
		data["status_flower"] = d.StatusFlower
	}
	if !d.NetworkStatus {
		data["network_status"] = true
	}
	if body.NetworkType != 0 {
		data["network_type"] = body.NetworkType
	}
	if body.SignalQuality != "" {
		data["signal_quality"] = body.SignalQuality
	}
	if body.FirmwareVersion != nil {
		data["firmware_version"] = *body.FirmwareVersion
	}
	if body.CabinStatus != nil && lo.Contains([]int32{0, 1}, *body.CabinStatus) {
		data["cabin_status"] = *body.CabinStatus == 1
	}
	if body.PilotLatitude != nil && body.PilotLongitude != nil {
		data["deployment_lnglat"] = ewkb.Point([]float64{*body.PilotLongitude, *body.PilotLatitude})
	}
	return data
}

func (d *Device) getBaseMessage(avatarId int64) (message BaseInfo) {
	return BaseInfo{
		SourceId:   d.Id,
		SourceSn:   d.Sn,
		SourceType: d.Type,
		AvatarId:   avatarId,
		TenantId:   d.TenantId,
		MerchantId: d.MerchantId,
		Subsystem:  SystemName,
	}
}

func (d *Device) getDeviceMessage() (message DeviceInfo) {
	status := lo.Ternary(d.Status == StatusOffline, "DISCONNECT", "NORMAL")
	return DeviceInfo{
		Type:          d.Type,
		Category:      d.Category,
		Status:        status,
		TenantId:      d.TenantId,
		MerchantId:    d.MerchantId,
		NetworkStatus: d.NetworkStatus,
		SignalQuality: d.SignalQuality,
	}
}

func (d *Device) ToBizEventOccurred(eventInfo EventInfo) EventOccurredMsg {
	return EventOccurredMsg{
		Type:      EventOccurred,
		BaseInfo:  d.getBaseMessage(0),
		EventInfo: eventInfo,
	}
}

func (d *Device) InDebugMode() bool {
	return d.PropData != nil && d.PropData.DockState != nil && *d.PropData.DockState == 2
}

type DeviceRepo interface {
	DeleteDevice(ctx context.Context, id int64) error
	CreateDevice(ctx context.Context, body *Device) (*Device, error)
	GetDevice(ctx context.Context, query *DetailQuery) (*Device, error)
	CheckDevice(ctx context.Context, query *DeviceCheck) (*Device, error)
	UpdateDevice(ctx context.Context, id int64, body AnyMap) error
	IntegrateDevice(ctx context.Context, imsg IntegrateMsg) error
	JointDevices(ctx context.Context, query *DeviceJointQuery) ([]*Device, error)
	ListDevices(ctx context.Context, query *DeviceListQuery) (int32, []*Device, error)
	CountDevices(ctx context.Context, query *DeviceCountQuery) ([]*DeviceCounter, error)
	// 根据source model等判断接入凭据
	GetDeviceAccessConfig(ctx context.Context, dev *Device) (AnyMap, error)
}

type DeviceUsecase struct {
	aRepo AuthRepo
	eRepo EventRepo
	tRepo DelayRepo
	dRepo DeviceRepo
	mRepo MQTTRepo
	oRepo OperationRepo
	vRepo VoyageRepo
	pRepo PropertyRepo
	lRepo LogfileRepo
	sRepo SimpleStorageRepo
	log   *log.Helper
	conf  *conf.Data
}

func NewDeviceUsecase(conf *conf.Data, logger log.Logger, aRepo AuthRepo, eRepo EventRepo, tRepo DelayRepo, dRepo DeviceRepo, mRepo MQTTRepo, oRepo OperationRepo, vRepo VoyageRepo, pRepo PropertyRepo, lRepo LogfileRepo, sRepo SimpleStorageRepo) *DeviceUsecase {
	return &DeviceUsecase{aRepo: aRepo, dRepo: dRepo, eRepo: eRepo, tRepo: tRepo, mRepo: mRepo, oRepo: oRepo, vRepo: vRepo, pRepo: pRepo, lRepo: lRepo, sRepo: sRepo, log: log.NewHelper(logger), conf: conf}
}

func (uc *DeviceUsecase) CheckDevice(ctx context.Context, query *DeviceCheck) (*Device, error) {
	return uc.dRepo.CheckDevice(ctx, query)
}

func (uc *DeviceUsecase) CreateDevice(ctx context.Context, body *Device) (*Device, error) {
	return uc.dRepo.CreateDevice(ctx, body)
}

func (uc *DeviceUsecase) GetDevice(ctx context.Context, query *DetailQuery) (*Device, error) {
	return uc.dRepo.GetDevice(ctx, query)
}

func (uc *DeviceUsecase) UpdateDevice(ctx context.Context, id int64, body AnyMap) error {
	return uc.dRepo.UpdateDevice(ctx, id, body)
}

func (uc *DeviceUsecase) DeleteDevice(ctx context.Context, id int64) error {
	return uc.dRepo.DeleteDevice(ctx, id)
}

func (uc *DeviceUsecase) IntegrateDevice(ctx context.Context, imsg IntegrateMsg) error {
	return uc.dRepo.IntegrateDevice(ctx, imsg)
}

func (uc *DeviceUsecase) OfflineDevice(ctx context.Context, device *Device) error {
	// save offline event
	sky := lo.Ternary(device.CabinStatus, int32(0), 1)
	uc.eRepo.CreateEvent(ctx, &Event{
		Sn:           device.Sn,
		Type:         OfflineEvent,
		Code:         "90000000",
		Level:        1,
		Place:        sky,
		OccurredTime: time.Now(),
	})
	// set dislink delay task
	uc.tRepo.CreateTask(ctx, &DelayTask{
		Times:    1,
		Delay:    10 * time.Minute,
		Source:   "SKAI-DEVICE",
		Key:      fmt.Sprintf("%s:DEVICE:%d:DISLINK", SkaiDelayKeyPrefix, device.Id),
		Callback: fmt.Sprintf("/internal/v1/devices/%d/callback", device.Id),
		Payload:  map[string]interface{}{"type": "device", "action": "dislink", "sourceId": conv.Vtoa(device.Id)},
	})
	// temp-value avoid offline flow clear mileage
	mileage := device.PropData.Mileage
	// save device status
	if err := uc.dRepo.UpdateDevice(ctx, device.Id, device.Offline()); err != nil {
		return err
	}
	if device.Gradient == GradientOutFlight && device.VoyageId != nil {
		offlineTime := *device.UppedTime
		voyage, _ := uc.vRepo.GetVoyage(ctx, &DetailQuery{Id: *device.VoyageId})
		if voyage != nil {
			runtime := int32(offlineTime.Local().Sub(voyage.StartTime).Seconds())
			// update voyage info
			uc.vRepo.UpdateVoyage(ctx, voyage.Id, AnyMap{
				"is_flown":   false,
				"is_success": true,
				"end_time":   offlineTime,
				"runtime":    runtime,
				"status":     "offline",
				"mileage":    voyage.Mileage + mileage,
			})
		}
	}
	return nil
}

func (uc *DeviceUsecase) DislinkDevice(ctx context.Context, device *Device) error {
	// generate dislink event
	event := &Event{
		Sn:           device.Sn,
		Code:         "90000000",
		Type:         OfflineEvent,
		OccurredTime: time.Now(),
		ExtraData: AnyMap{
			"category":   "机场",
			"customType": "dislink",
			"name":       device.Deployment.Name,
		},
	}
	return uc.eRepo.SendManualSms(ctx, event)
}

func (uc *DeviceUsecase) PublishDevice(ctx context.Context, topic string, data []byte, delay time.Duration) error {
	// push mqtt propdata, support time delay
	if delay > 0 {
		time.Sleep(delay)
	}
	uc.mRepo.Publish(ctx, topic, 0, data)
	return nil
}

func (uc *DeviceUsecase) ListDevice(ctx context.Context, query *DeviceListQuery) (total int32, devices []*Device, err error) {
	return uc.dRepo.ListDevices(ctx, query)
}

func (uc *DeviceUsecase) TimeoutDevice(ctx context.Context, id int64, payload *DeviceTimeout) error {
	uc.tRepo.CreateTask(ctx, &DelayTask{
		Times:    1,
		Delay:    payload.Timeout,
		Source:   "SKAI-CONNECT",
		Key:      fmt.Sprintf("%s:SERVICE:%s:%d", SkaiThingKeyPrefix, payload.Identifier, payload.SourceId),
		Callback: fmt.Sprintf("/internal/v1/devices/%d/callback", id),
		Payload:  AnyMap{"type": "service", "action": "timeout", "sourceId": conv.Vtoa(payload.SourceId)},
	})
	return nil
}

func (uc *DeviceUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}

func (uc *DeviceUsecase) GetAvatar(ctx context.Context, id int64) (*Avatar, error) {
	return uc.aRepo.GetAvatar(ctx, id)
}

func (uc *DeviceUsecase) UpdateEvent(ctx context.Context, id int64, body AnyMap) error {
	return uc.eRepo.UpdateEvent(ctx, id, body)
}

func (uc *DeviceUsecase) ListEvent(ctx context.Context, query *EventListQuery) (int64, []*Event, error) {
	return uc.eRepo.ListEvents(ctx, query)
}

func (uc *DeviceUsecase) ExportEvent(ctx context.Context, query *EventListQuery) (<-chan *Event, error) {
	return uc.eRepo.ExportEvents(ctx, query)
}

func (uc *DeviceUsecase) CreateOperation(ctx context.Context, op *Operation) (*Operation, error) {
	operation, err := uc.oRepo.CreateOperation(ctx, op)
	if err != nil {
		return nil, err
	}
	// 异步操作，设置命令超时任务
	if op.Status == OperationStatusPending && op.Timeout > 0 {
		identifier := op.Type.ToServiceIdentifier().ToUpper()
		uc.TimeoutDevice(ctx, op.SourceId, &DeviceTimeout{Timeout: op.Timeout, Identifier: identifier, SourceId: operation.Id})
	}
	return operation, err
}

func (uc *DeviceUsecase) GetOperation(ctx context.Context, id int64) (*Operation, error) {
	return uc.oRepo.GetOperation(ctx, id)
}

func (uc *DeviceUsecase) UpdateOperation(ctx context.Context, opId int64, body AnyMap) error {
	return uc.oRepo.UpdateOperation(ctx, opId, body)
}

func (uc *DeviceUsecase) LastDockDatalog(ctx context.Context, sn string, duration time.Duration) (int64, []*DockProperties, error) {
	now := time.Now()
	return uc.pRepo.ListDockProperties(ctx, &PropListQuery{Sn: sn, End: now, Start: now.Add(-duration), Page: 1, Size: 300})
}

func (uc *DeviceUsecase) ListDatalog(ctx context.Context, query *DeviceDatalogListQuery) (int64, any, error) {
	dev := query.Device
	if dev.Deployment == nil || dev.Deployment.Time == nil {
		uc.log.Infof("ListDatalog for device %d %s has not been deplement", dev.Id, dev.Sn)
		return 0, nil, nil
	}
	start := query.Start
	if query.Start.Before(*dev.Deployment.Time) {
		start = *dev.Deployment.Time
	}
	lq := &PropListQuery{
		Sn:    dev.Sn,
		Start: start,
		End:   query.End,
		Page:  query.Page,
		Size:  query.Size,
	}
	switch dev.Model {
	case ModelDJIDock, ModelDJILite:
		return uc.pRepo.ListDockProperties(ctx, lq)
	case ModelDJIPilot:
		qs := query.Page * query.Size
		const batchSize = 1000
		marker, total, list, err := uc.listControllerDatalog(ctx, &PropListQuery{
			Sn:    dev.Sn,
			Start: start,
			End:   query.End,
			Page:  1,
			Size:  lo.Min([]int32{qs, batchSize}),
		})
		if err != nil {
			return 0, nil, err
		}
		if qs <= batchSize {
			return total, list[qs-query.Size:], nil
		} else {
			skip := int(qs - query.Size)
			if skip >= int(total) {
				return total, nil, nil
			}
			count := batchSize
			for {
				pagedQuery := &PropListQuery{
					Sn:           dev.Sn,
					Start:        start,
					End:          query.End,
					Page:         1,
					Size:         batchSize,
					disableCount: true,
					pageMark:     marker,
				}
				marker, _, list, err = uc.listControllerDatalog(ctx, pagedQuery)
				if err != nil {
					return 0, nil, err
				}
				count = count + len(list)
				if count > skip {
					list = list[skip%batchSize:]
					if len(list) < int(query.Size) {
						pagedQuery.Size = query.Size - int32(len(list))
						pagedQuery.pageMark = marker
						_, _, left, err := uc.listControllerDatalog(ctx, pagedQuery)
						if err != nil {
							return 0, nil, err
						}
						return total, append(list, left...), nil
					}
					return total, list[:query.Size], nil
				}
			}
		}
	default:
		return 0, nil, NewBadRequestError("ListDatalog.invalidModel", nil)
	}
}

type rcDatalogPageMark struct {
	rcTs    time.Time
	droneTs time.Time
}

func (uc *DeviceUsecase) listControllerDatalog(ctx context.Context, query *PropListQuery) (*rcDatalogPageMark, int64, []any, error) {
	var ct, dt int64
	var cps []*RemoteControllerProperties
	var dps []*DroneProperties
	eg, gctx := errgroup.WithContext(ctx)
	eg.Go(utilities.NewRecovedGOFunc(func() error {
		var err error
		q := &PropListQuery{
			Sn:           query.Sn,
			Start:        query.Start,
			End:          query.End,
			Page:         query.Page,
			Size:         query.Size,
			disableCount: query.disableCount,
		}
		if query.pageMark != nil {
			q.End = query.pageMark.rcTs.Add(-1 * time.Millisecond)
		}
		ct, cps, err = uc.pRepo.ListControllerProperties(gctx, q)
		return err
	}))
	eg.Go(utilities.NewRecovedGOFunc(func() error {
		var err error
		q := &PropListQuery{
			Sn:           query.Sn,
			Start:        query.Start,
			End:          query.End,
			Page:         query.Page,
			Size:         query.Size,
			disableCount: query.disableCount,
		}
		if query.pageMark != nil {
			q.End = query.pageMark.droneTs.Add(-1 * time.Millisecond)
		}
		dt, dps, err = uc.pRepo.ListDockDroneProperties(gctx, q)
		return err
	}))
	if err := eg.Wait(); err != nil {
		return nil, 0, nil, err
	}
	var ci, di int
	marker := &rcDatalogPageMark{}
	ret := make([]any, 0, query.Size)
	for i := 0; i < int(query.Size); i++ {
		var cp *RemoteControllerProperties
		var dp *DroneProperties
		if ci < len(cps) {
			cp = cps[ci]
		}
		if di < len(dps) {
			dp = dps[di]
			if cp == nil || dp.RxTime.After(cp.RxTime) {
				ret = append(ret, dp)
				marker.droneTs = dp.RxTime
				di++
				continue
			}
		}
		if cp != nil {
			marker.rcTs = cp.RxTime
			ret = append(ret, cp)
		}
		ci++
	}

	if marker.droneTs.IsZero() {
		if query.pageMark != nil {
			marker.droneTs = query.pageMark.droneTs
		} else {
			marker.droneTs = query.End
		}

	}
	if marker.rcTs.IsZero() {
		if query.pageMark != nil {
			marker.rcTs = query.pageMark.rcTs
		} else {
			marker.rcTs = query.End
		}
	}
	return marker, ct + dt, ret, nil
}

func (uc *DeviceUsecase) ExportDockDeviceDatalog(ctx context.Context, query *DeviceDatalogListQuery) (<-chan any, error) {
	dev := query.Device
	if dev.Deployment == nil || dev.Deployment.Time == nil {
		uc.log.Infof("ListDatalog for device %d %s has not been deplement", dev.Id, dev.Sn)
		return nil, nil
	}
	start := query.Start
	if query.Start.Before(*dev.Deployment.Time) {
		start = *dev.Deployment.Time
	}
	ch := make(chan any, 10)
	var exportErr error
	go func() {
		defer close(ch)
		var exporter func(ctx context.Context, query *PropListQuery) ([]any, error)
		switch dev.Model {
		case ModelDJIDock, ModelDJILite:
			exporter = func(ctx context.Context, query *PropListQuery) ([]any, error) {
				_, list, err := uc.pRepo.ListDockProperties(ctx, query)
				if err != nil {
					return nil, err
				}
				return lo.ToAnySlice(list), nil
			}
		case ModelDJIPilot:
			exporter = func(ctx context.Context, query *PropListQuery) ([]any, error) {
				_, _, list, err := uc.listControllerDatalog(ctx, query)
				if err != nil {
					return nil, err
				}
				return list, nil
			}
		default:
			uc.log.Error("ExportDockDeviceDatalog with unsupported model ", dev.Model, dev.Sn)
			return
		}
		duration := -10 * time.Minute
		end := query.End
		count := 0
		for i := end; i.Compare(start) >= 0; i = i.Add(duration) {
			blockEnd := i
			blockStart := i.Add(duration)
			if blockStart.Before(query.Start) {
				blockStart = query.Start
			}
			data, err := exporter(ctx, &PropListQuery{
				Sn:           dev.Sn,
				Start:        blockStart,
				End:          blockEnd,
				Page:         query.Page,
				Size:         1000,
				disableCount: true,
			})
			if err != nil {
				exportErr = err
				break
			}
			for _, v := range data {
				ch <- v
			}
			count = count + len(data)
			if count > (1 << 20) {
				return
			}
		}
	}()
	return ch, exportErr
}

func (uc *DeviceUsecase) ListOperation(ctx context.Context, query *OperationListQuery) (int64, []*Operation, error) {
	total, list, err := uc.oRepo.ListOperation(ctx, query)
	if err != nil {
		return 0, nil, err
	}
	avatarIds := lo.Map(list, func(o *Operation, _ int) int64 { return o.AvatarId })
	avatars, err := uc.aRepo.GetTenantAvatars(ctx, query.TenantId, lo.Uniq(avatarIds))
	if err != nil {
		return 0, nil, err
	}
	avatarMap := lo.KeyBy(avatars, func(a *Avatar) int64 { return a.Id })
	for _, op := range list {
		op.Belong(avatarMap[op.AvatarId])
	}
	return total, list, nil
}

func (uc *DeviceUsecase) DeleteTask(ctx context.Context, deviceId int64) error {
	// 释放自动返航锁
	if err := uc.eRepo.UnlockAutoback(ctx, deviceId); err != nil {
		return err
	}
	// 删除自动返航任务
	return uc.tRepo.DeleteTask(ctx, fmt.Sprintf("%s:DEVICE:AUTOBACK:%d", SkaiDelayKeyPrefix, deviceId))
}

func (uc *DeviceUsecase) CreateLogfiles(ctx context.Context, body []*Logfile) error {
	return uc.lRepo.CreateLogfiles(ctx, body)
}

func (uc *DeviceUsecase) ListLogfile(ctx context.Context, query *LogfileListQuery) (int32, []*Logfile, error) {
	return uc.lRepo.ListLogfiles(ctx, query)
}

func (uc *DeviceUsecase) GetLogfile(ctx context.Context, query *DetailQuery) (*Logfile, error) {
	logfile, err := uc.lRepo.GetLogfile(ctx, query)
	if err != nil {
		return nil, err
	}
	if logfile.Status != 1 {
		return nil, NewNotFoundError("Logfile", "status", "not success")
	}
	if signed, err := uc.sRepo.GetSignedObjectAddr(ctx, &StorageObject{Key: logfile.Url}, time.Hour*24); err == nil {
		logfile.Url = signed.ObjectUrl
	}
	return logfile, nil
}
