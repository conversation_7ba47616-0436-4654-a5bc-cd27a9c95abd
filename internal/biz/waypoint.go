package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type WaypointListQuery struct {
	ProjectInfo
	Sort
	Page      int
	Size      int
	AirlineId *int64
}

type Waypoint struct {
	Id          int64
	AirlineId   int64
	TenantId    int64
	MerchantId  int64
	Serial      int
	Height      float32
	Lnglat      []float64
	Speed       float32
	TurnMode    string
	TurnDamping float32
	CreatedTime time.Time
	UpdatedTime time.Time
}

type WaypointRepo interface {
	CreateWaypoints(ctx context.Context, body []*Waypoint) error
	UpdateWaypoint(ctx context.Context, id int64, body AnyMap) error
	ListWaypoints(ctx context.Context, query *WaypointListQuery) (int32, []*Waypoint, error)
	GetWaypoint(ctx context.Context, query *DetailQuery) (*Waypoint, error)
	DeleteWaypoint(ctx context.Context, id int64) error
	AirlineWaypoints(ctx context.Context, airlineId int64) ([]*Waypoint, error)
}

type WaypointUsecase struct {
	aRepo AuthRepo
	lRepo AirlineRepo
	wRepo WaypointRepo
	log   *log.Helper
}

func NewWaypointUsecase(logger log.Logger, aRepo AuthRepo, lRepo AirlineRepo, wRepo WaypointRepo) *WaypointUsecase {
	return &WaypointUsecase{aRepo: aRepo, lRepo: lRepo, wRepo: wRepo, log: log.NewHelper(logger)}
}

func (uc *WaypointUsecase) CreateWaypoints(ctx context.Context, body []*Waypoint) error {
	return uc.wRepo.CreateWaypoints(ctx, body)
}

func (uc *WaypointUsecase) BelongAirline(ctx context.Context, query *AirlineQuery) (*Airline, error) {
	return uc.lRepo.GetAirline(ctx, query)
}

func (uc *WaypointUsecase) ListWaypoint(ctx context.Context, query *WaypointListQuery) (int32, []*Waypoint, error) {
	return uc.wRepo.ListWaypoints(ctx, query)
}

func (uc *WaypointUsecase) GetWaypoint(ctx context.Context, query *DetailQuery) (*Waypoint, error) {
	return uc.wRepo.GetWaypoint(ctx, query)
}

func (uc *WaypointUsecase) UpdateWaypoint(ctx context.Context, id int64, body AnyMap) error {
	return uc.wRepo.UpdateWaypoint(ctx, id, body)
}

func (uc *WaypointUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}
