package biz

// 消息类型
type MsgType string

const (
	EventOccurred     MsgType = "Event"
	StatusChanged     MsgType = "StatusChanged"
	MerchantChanged   MsgType = "MerchantChanged"
	DeploymentChanged MsgType = "DeploymentChanged"
)

type BaseInfo struct {
	SourceId   int64  `json:"sourceId,string"`             // 设备源ID
	SourceSn   string `json:"sourceSn"`                    // 设备源SN
	SourceType string `json:"sourceType"`                  // 设备源类型
	Subsystem  string `json:"subsystem"`                   // 子系统名称
	AvatarId   int64  `json:"userId,string,omitempty"`     // 用户ID
	TenantId   int64  `json:"tenantId,string,omitempty"`   // 租户ID
	MerchantId int64  `json:"merchantId,string,omitempty"` // 资源库ID
}

type IntegrateMsg interface {
	PaddingData(device *Device, avatarId int64)
}

// 事件信息
type EventInfo struct {
	Category     string `json:"category"`     // 事件类型
	EventName    string `json:"eventName"`    // 事件名称
	Metadata     AnyMap `json:"metadata"`     // 事件元数据
	OccurredTime int64  `json:"occurredTime"` // 发生时间
}

type EventOccurredMsg struct {
	BaseInfo
	Type      MsgType   `json:"type"`
	EventInfo EventInfo `json:"data,omitempty"`
}

func (cm *EventOccurredMsg) PaddingData(device *Device, avatarId int64) {
	cm.Type = EventOccurred
	cm.BaseInfo = device.getBaseMessage(avatarId)
}

// 状态信息
type StatusInfo struct {
	Status        string `json:"status"`        // 设备状态
	NetworkStatus bool   `json:"networkStatus"` // 网络状态
	SignalQuality string `json:"signalQuality"` // 信号质量
}

type StatusChangedMsg struct {
	BaseInfo
	Type       MsgType    `json:"type"`
	StatusInfo StatusInfo `json:"statusInfo,omitempty"`
}

func (cm *StatusChangedMsg) PaddingData(device *Device, avatarId int64) {
	cm.Type = StatusChanged
	cm.BaseInfo = device.getBaseMessage(avatarId)
	statusInfo := StatusInfo{
		Status:        "NORMAL",
		NetworkStatus: true,
		SignalQuality: "GOOD",
	}
	if device.Status == StatusOffline {
		statusInfo.Status = "DISCONNECT"
		statusInfo.NetworkStatus = false
		statusInfo.SignalQuality = "NONE"
	}
	cm.StatusInfo = statusInfo
}

// 设备信息
type DeviceInfo struct {
	Type          string         `json:"type"`                        // 设备类型
	Status        string         `json:"status"`                      // 设备状态
	Category      DeviceCategory `json:"category"`                    // 设备大类
	TenantId      int64          `json:"tenantId,string,omitempty"`   // 租户ID
	MerchantId    int64          `json:"merchantId,string,omitempty"` // 资源库ID
	NetworkStatus bool           `json:"networkStatus"`               // 网络状态
	SignalQuality string         `json:"signalQuality"`               // 信号质量
}

type MerchantChangedMsg struct {
	BaseInfo
	Type       MsgType    `json:"type"`
	Device     DeviceInfo `json:"deviceInfo,omitempty"`
	CurenantId int64      `json:"currentTenantId,string,omitempty"`   // 当前租户ID
	CurchantId int64      `json:"currentMerchantId,string,omitempty"` // 当前资源库ID
	Deployment Deployment `json:"currentDeployment,omitempty"`        // 当前部署信息
}

func (cm *MerchantChangedMsg) PaddingData(device *Device, avatarId int64) {
	cm.Type = MerchantChanged
	cm.CurenantId = device.TenantId
	cm.CurchantId = device.MerchantId
	cm.Deployment = *device.Deployment
	cm.Device = device.getDeviceMessage()
	cm.BaseInfo = device.getBaseMessage(avatarId)
}

type DeploymentChangedMsg struct {
	BaseInfo
	Type       MsgType    `json:"type"`
	Device     DeviceInfo `json:"deviceInfo,omitempty"`
	Deployment Deployment `json:"currentDeployment,omitempty"`
}

func (cm *DeploymentChangedMsg) PaddingData(device *Device, avatarId int64) {
	cm.Type = DeploymentChanged
	cm.Deployment = *device.Deployment
	cm.Device = device.getDeviceMessage()
	cm.BaseInfo = device.getBaseMessage(avatarId)
}
