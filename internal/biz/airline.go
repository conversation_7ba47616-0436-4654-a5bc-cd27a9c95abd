package biz

import (
	"context"
	"io"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"golang.org/x/exp/slices"
)

type KMZFile struct {
	Url         string `json:"url,omitempty"`
	Fingerprint string `json:"fingerprint,omitempty"`
}

type Changable struct {
	Name        string
	Tags        []string
	DeviceIds   []int64
	Description string
}

type AirlineQuery struct {
	ProjectInfo
	Id         int64
	Scope      int32
	WithSigned bool
	WithDevice bool
}

type AirlineListQuery struct {
	ProjectInfo
	Sort
	Page      int
	Size      int
	Ids       []int64
	Type      *string
	Search    *string
	Unscoped  bool
	DeviceIds []int64
	StartTime *time.Time
	EndTime   *time.Time
	// DeviceId  *int64 // 针对单设备，是否需要排序
}

type Airline struct {
	Id              int64
	Name            string
	Type            string
	Tags            []string
	Speed           float32
	TranSpeed       float32
	Height          float32
	ReturnHeight    float32
	SecurityHeight  float32
	WaypointCount   int32
	EstimateMileage float32
	FlytoMode       string
	FinishAction    string
	ExitOnRCLost    string
	RCLostAction    string
	Description     string
	DeviceIds       []int64
	Devices         []*Device
	TenantId        int64
	MerchantId      int64
	KMZFile         KMZFile `copier:"-"`
	FenceArea       *Geometry
	CreatedTime     time.Time
	UpdatedTime     time.Time
	// SortKey          int // 排序字段
}

func (a *Airline) Recycle() AnyMap {
	return AnyMap{"tenant_id": 0, "merchant_id": 0}
}

func (d *Airline) Change(body *Changable) AnyMap {
	return AnyMap{"name": body.Name, "tags": pq.StringArray(body.Tags), "device_ids": pq.Int64Array(body.DeviceIds), "description": body.Description}
}

type AirlineRepo interface {
	CreateAirline(ctx context.Context, body *Airline) (*Airline, error)
	UpdateAirline(ctx context.Context, id int64, body AnyMap) error
	ListAirlines(ctx context.Context, query *AirlineListQuery) (int32, []*Airline, error)
	GetAirline(ctx context.Context, query *AirlineQuery) (*Airline, error)
	DeleteAirline(ctx context.Context, id int64) error
}

type AirlineUsecase struct {
	aRepo AuthRepo
	dRepo DeviceRepo
	lRepo AirlineRepo
	wRepo WaypointRepo
	sRepo SimpleStorageRepo
	log   *log.Helper
	c     *conf.Data
}

func NewAirlineUsecase(c *conf.Data, logger log.Logger, aRepo AuthRepo, dRepo DeviceRepo, lRepo AirlineRepo, wRepo WaypointRepo, sRepo SimpleStorageRepo) *AirlineUsecase {
	return &AirlineUsecase{aRepo: aRepo, dRepo: dRepo, lRepo: lRepo, wRepo: wRepo, sRepo: sRepo, log: log.NewHelper(logger), c: c}
}

func (uc *AirlineUsecase) CreateAirline(ctx context.Context, body *Airline) (*Airline, error) {
	return uc.lRepo.CreateAirline(ctx, body)
}

func (uc *AirlineUsecase) GetKmzFile(ctx context.Context, body *StorageObject) (*StorageObjectMeta, io.ReadCloser, error) {
	return uc.sRepo.GetObject(ctx, body)
}

func (uc *AirlineUsecase) CreateWaypoints(ctx context.Context, body []*Waypoint) error {
	return uc.wRepo.CreateWaypoints(ctx, body)
}

func (uc *AirlineUsecase) ListAirline(ctx context.Context, query *AirlineListQuery) (int32, []*Airline, error) {
	return uc.lRepo.ListAirlines(ctx, query)
	// TODO: 如果是一张图设备查询航线列表，需要将该设备上次使用航线默认排在首位
	// var lastAirlineId *int64
	// if query.DeviceId != nil {
	// 	if device, err := uc.dRepo.GetDevice(ctx, &DetailQuery{ProjectInfo: query.ProjectInfo, Id: *query.DeviceId}); err == nil {
	// 		lastAirlineId = device.AirlineId
	// 	}
	// }
	// total, list, err := uc.lRepo.ListAirlines(ctx, query)
	// if lastAirlineId != nil {
	// 	if last, ok := lo.Find(list, func(a *Airline) bool {
	// 		return a.Id == *lastAirlineId
	// 	}); ok {
	// 		last.SortKey = 9
	// 	}
	// }
	// slices.SortStableFunc(list, func(a, b *Airline) int { return b.SortKey - a.SortKey })
	// return total, list, err
}

func (uc *AirlineUsecase) GetDevice(ctx context.Context, query *DetailQuery) (*Device, error) {
	return uc.dRepo.GetDevice(ctx, query)
}

func (uc *AirlineUsecase) GetAirline(ctx context.Context, query *AirlineQuery) (*Airline, error) {
	airline, err := uc.lRepo.GetAirline(ctx, query)
	if err != nil {
		return nil, err
	}
	if query.WithSigned {
		// if signed, err := uc.sRepo.GetSignedObjectAddr(ctx, &StorageObject{Key: airline.KMZFile.Url}, time.Minute*30); err == nil {
		// 	airline.KMZFile.Url = signed.ObjectUrl
		// }
		airline.KMZFile.Url = newPublicFileURI(uc.c, airline.KMZFile.Url)
	}
	if query.WithDevice && len(airline.DeviceIds) > 0 {
		if _, devices, err := uc.dRepo.ListDevices(ctx, &DeviceListQuery{
			Page:        1,
			Size:        100,
			ProjectInfo: query.ProjectInfo,
			Ids:         airline.DeviceIds,
		}); err == nil {
			for _, device := range devices {
				if order := slices.Index(airline.DeviceIds, device.Id); order > -1 {
					device.SortOrder = order
				}
			}
			slices.SortFunc(devices, func(a, b *Device) int { return a.SortOrder - b.SortOrder })
			airline.Devices = devices
		}
	}
	return airline, nil
}

func (uc *AirlineUsecase) UpdateAirline(ctx context.Context, id int64, body AnyMap) error {
	return uc.lRepo.UpdateAirline(ctx, id, body)
}

func (uc *AirlineUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}
