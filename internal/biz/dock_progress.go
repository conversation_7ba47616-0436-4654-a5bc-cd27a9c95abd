package biz

var SpeakerProgressMap = StringMap{
	"ok":               "喊话成功",
	"play":             "开始播放",
	"change_work_mode": "切换工作模式",
	"encoding":         "编码pcm为opus",
	"upload":           "机场上传音频到psdk",
	"download":         "从云端下载音频文件到机场",
}

var FlightProgressMap = StringMap{
	"0":       "初始状态",
	"1":       "启动前检查，无人机是否在执行航线中",
	"2":       "启动前检查，机场是否退出工作模式",
	"3":       "启动前检查，航线执行中",
	"4":       "启动前检查，返航中",
	"5":       "航线执行进入准备状态，开始等待任务下发",
	"6":       "机场进入工作状态",
	"7":       "进入开机检查准备工作和开盖准备工作",
	"8":       "等待飞行系统准备就绪，推送连接建立",
	"9":       "等待 RTK 源监听有值上报",
	"10":      "检查 RTK 源是否是机场源，如果不是要重新设置",
	"11":      "等待飞行控制权通知",
	"12":      "机场无控制权，抢无人机控制权抢夺",
	"13":      "获取最新 KMZ URL",
	"14":      "下载 KMZ",
	"15":      "KMZ 上传中",
	"16":      "染色配置",
	"17":      "无人机起飞参数设置，备降点设置，起飞高度设置，染色设置",
	"18":      "无人机 flyto 起飞参数设置",
	"19":      "Home 点设置",
	"20":      "触发执行航线",
	"21":      "航线执行中",
	"22":      "进入返航的检查准备工作",
	"23":      "无人机降落机场",
	"24":      "降落以后的关盖",
	"25":      "机场退出工作模式",
	"26":      "机场异常恢复",
	"27":      "机场上传飞行系统日志",
	"28":      "相机录像状态检查",
	"29":      "获取媒体文件数量",
	"30":      "机场起飞开盖的异常恢复",
	"31":      "通知任务结果",
	"32":      "任务执行完成，通过配置文件配置，是否进行主动 log 拉取",
	"33":      "日志列表拉取 - 无人机列表",
	"34":      "日志列表拉取 - 拉取机场列表",
	"35":      "日志列表拉取 - 上传日志列表结果",
	"36":      "日志拉取-拉取无人机日志",
	"37":      "日志拉取-拉取机场日志",
	"38":      "日志拉取-压缩无人机日志",
	"39":      "日志拉取-压缩机场日志",
	"40":      "日志拉取-上传无人机日志",
	"41":      "日志拉取-上传机场日志",
	"42":      "日志拉取-通知结果",
	"0xFFFD":  "结束后等待服务回包",
	"0xFFFE":  "无具体状态",
	"0xFFFFF": "UNKNOWN",
}
