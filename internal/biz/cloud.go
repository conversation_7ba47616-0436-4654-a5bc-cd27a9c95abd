package biz

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
)

type CloudUsecase struct {
	aRepo AuthRepo
	lRepo AirlineRepo
	mRepo MediaRepo
	sRepo SimpleStorageRepo
	qRepo MQTTRepo
	dRepo DeviceRepo
	vRepo VoyageRepo
	log   *log.Helper
}

func NewCloudUsecase(logger log.Logger, aRepo AuthRepo, lRepo AirlineRepo, mRepo MediaRepo, sRepo SimpleStorageRepo, dRepo DeviceRepo, vRepo VoyageRepo, qRepo MQTTRepo) *CloudUsecase {
	return &CloudUsecase{aRepo: aRepo, lRepo: lRepo, mRepo: mRepo, sRepo: sRepo, dRepo: dRepo, vRepo: vRepo, qRepo: qRepo, log: log.NewHelper(logger)}
}

func (uc *CloudUsecase) ListAirline(ctx context.Context, query *AirlineListQuery) (int32, []*Airline, error) {
	return uc.lRepo.ListAirlines(ctx, query)
}

func (uc *CloudUsecase) GetAirline(ctx context.Context, query *AirlineQuery) (*Airline, error) {
	// 允许已删除航线被大疆请求
	airline, err := uc.lRepo.GetAirline(ctx, &AirlineQuery{Id: query.Id})
	if signed, err := uc.sRepo.GetSignedObjectAddr(ctx, &StorageObject{Key: airline.KMZFile.Url, Bucket: ""}, time.Minute*30); err == nil {
		airline.KMZFile.Url = signed.ObjectUrl
	}
	return airline, err
}

func (uc *CloudUsecase) GetProjectInfo(ctx context.Context) (*ProjectInfo, error) {
	authValue, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	return NewProjectInfo(authValue), nil
}

func (uc *CloudUsecase) GetPilotMediaUploadConfiig(ctx context.Context) (*VoyageMediaUplinkConfig, error) {
	av, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	if av.SN == "" {
		return nil, errors.Forbidden("GetPilotMediaUploadConfiig.noSN", "无此操作权限")
	}
	dev, err := uc.dRepo.CheckDevice(ctx, &DeviceCheck{
		SourceSn: av.SN,
	})
	if err != nil {
		uc.log.Warnf("GetPilotMediaUploadConfiig.CheckDevice %s failed: %v", av.SN, err)
		return nil, err
	}
	sc, err := uc.sRepo.GetSessionToken(ctx, SessionTokenOption{
		TTL: 3 * time.Hour,
	})
	if err != nil {
		uc.log.Errorf("GetPilotMediaUploadConfiig.GetSessionToken for dev %d failed: %v", dev.Id, err)
		return nil, err
	}
	return &VoyageMediaUplinkConfig{
		Credentials: sc,
		KeyPrefix:   fmt.Sprintf("pilot%s", dev.Sn),
	}, nil
}

type UploadedMediaData struct {
	DroneSn   string
	Name      string
	ObjectKey string
	// Path           string
	FlightId       string
	Meta           *MediaMeta
	Signature      string
	ShortSignature string
	SubDeviceIndex string
	IfPanorama     *bool
}

func (uc *CloudUsecase) getMediaType(name string) MediaType {
	fileName := strings.Split(name, ".")
	if l := len(fileName); l > 1 {
		extName := strings.ToLower(fileName[l-1])
		if lo.Contains([]string{"jpg", "png", "jpeg", "webp", "heif", "avif", "raw"}, extName) {
			return MediaTypePhoto
		}
	}
	return MediaTypeVideo
}

func (uc *CloudUsecase) UploadedMedia(ctx context.Context, data *UploadedMediaData) error {
	av, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return err
	}
	if av.SN == "" {
		return errors.Forbidden("GetPilotMediaUploadConfiig.noSN", "无此操作权限")
	}
	dev, err := uc.dRepo.CheckDevice(ctx, &DeviceCheck{
		SourceSn: av.SN,
	})
	if err != nil {
		return err
	}
	// 区分不同无人机
	data.FlightId = fmt.Sprintf("%s_%s", av.SN, data.FlightId)
	voyage, err := uc.vRepo.GetFlight(ctx, data.FlightId)
	if err != nil {
		if errors.IsNotFound(err) {
			voyage, err = uc.bindMediaVoyage(ctx, data, dev)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}
	if voyage != nil {
		// 处理异常结束大疆把两次飞行合并成一个的情况
		findByTime := false
		if !voyage.EndTime.IsZero() {
			if d := data.Meta.ShootTime.Sub(voyage.EndTime); d > 1*time.Hour {
				findByTime = true
			}
		} else {
			if d := data.Meta.ShootTime.Sub(voyage.StartTime); d > 2*time.Hour {
				findByTime = true
			}
		}
		if findByTime {
			v, err := uc.findVoyageByTime(ctx, data, dev)
			if err != nil {
				return err
			}
			voyage = v
		}
	}
	name, st := NewFileNameForDJIFileName(data.Name, dev, nil)
	m := &Media{
		Id:             utilities.MustNextID(),
		DeviceId:       dev.Id,
		SubDeviceIndex: data.SubDeviceIndex,
		WaypointId:     -1,
		MerchantId:     dev.MerchantId,
		Meta:           data.Meta,
		Key:            data.ObjectKey,
		Name:           name,
		SubType:        st,
		Type:           uc.getMediaType(data.Name),
		Extra:          map[string]any{},
		CreatedTime:    lo.Ternary(data.Meta.ShootTime.IsZero(), time.Now(), data.Meta.ShootTime),
		UpdatedTime:    time.Now(),
	}
	if v := data.IfPanorama; v != nil && m.Type == MediaTypePhoto {
		m.SetPhotoType(lo.Ternary(*v, 1, 0))
	}
	if voyage != nil {
		m.VoyageId = voyage.Id
		m.AirlineId = voyage.AirlineId
	} else {
		m.VoyageId = -404
	}
	m.SetSignature(data.Signature)
	m.SetShortSignature(data.ShortSignature)
	if m.Type == MediaTypePhoto {
		t, err := uc.sRepo.GenImageThumbnail(ctx, &StorageObject{
			Key: m.Key,
		})
		if err != nil {
			return err
		}
		m.SetThumbnail(t)
	} else if m.Type == MediaTypeVideo {
		sf := func() func() error {
			mi := &Media{}
			copier.CopyWithOption(mi, m, copier.Option{IgnoreEmpty: true, DeepCopy: true})
			return func() error {
				actx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
				defer cancel()
				if e := uc.getVideoSnapshot(actx, m); e != nil {
					uc.log.Errorf("getVideoSnapshot %d %s failed %+v", m.Id, m.Key, e)
				}
				return nil
			}
		}()
		go utilities.NewRecovedGOFunc(sf)()
	}
	if err = uc.mRepo.Create(ctx, []*Media{m}); err != nil {
		return err
	}
	if m.VoyageId > 0 {
		c := VoyageMediaCount{}
		switch m.Type {
		case MediaTypePhoto:
			c.Images = 1
		case MediaTypeVideo:
			c.Videos = 1
		}
		return uc.vRepo.IncressVoyageMediaCount(ctx, m.VoyageId, c)
	}
	go utilities.NewRecovedGOFunc(func() error {
		t, d := NewMediaUploadedMessage(m, uc.sRepo, 0, 0)
		actx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		_, err := uc.qRepo.Publish(actx, t, 0, d)
		if err != nil {
			uc.log.Warnf("publicMediaUploadMessage %s to %s failed %+v", d, t, err)
		}
		return nil
	})()
	return nil
}

func (uc *CloudUsecase) getVideoSnapshot(ctx context.Context, m *Media) error {
	vu, err := uc.sRepo.GetInternalSignedObjectAddr(ctx, &StorageObject{Key: m.Key}, 10*time.Minute)
	if err != nil {
		return err
	}
	uc.log.Infof("getVideoSnapshot for %s with tmp url %s", m.Key, vu.ObjectUrl)
	vd, err := uc.mRepo.MakeVideoSnap(ctx, vu.ObjectUrl)
	if err != nil {
		return err
	}
	defer vd.Close()
	r, err := uc.sRepo.GenImageThumbnail(ctx, &StorageObject{
		Key:  "snapshot/" + m.Key,
		Data: vd,
	})
	if err != nil {
		return err
	}
	uc.log.Infof("getVideoSnapshot for %s out success %s", m.Key, r.Key)
	m.SetThumbnail(r)
	return uc.mRepo.UpdateMediaExtra(ctx, m.Id, m.Extra)
}

func (uc *CloudUsecase) findVoyageByTime(ctx context.Context, data *UploadedMediaData, dev *Device) (*Voyage, error) {
	if data.Meta == nil {
		return nil, NewBadRequestError("bindMediaVoyage.emptyMeta", nil)
	}
	count, vs, err := uc.vRepo.ListVoyages(ctx, &VoyageListQuery{
		// ProjectInfo:     *NewProjectInfo(av),
		Page:            1,
		Size:            100,
		DeviceId:        &dev.Id,
		StartTime:       lo.ToPtr(data.Meta.ShootTime.Add(-1 * time.Hour)),
		EndTime:         lo.ToPtr(data.Meta.ShootTime.Add(2 * time.Hour)),
		notProjectCheck: true,
	})
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, nil
	}
	voyage := lo.MinBy(vs, func(a *Voyage, b *Voyage) bool {
		av := data.Meta.ShootTime.Sub(a.StartTime)
		if av < 0 {
			av = -av
		}
		bv := data.Meta.ShootTime.Sub(b.StartTime)
		if bv < 0 {
			bv = -bv
		}
		return av < bv
	})
	return voyage, nil
}

func (uc *CloudUsecase) bindMediaVoyage(ctx context.Context, data *UploadedMediaData, dev *Device) (*Voyage, error) {
	if data.Meta == nil {
		return nil, NewBadRequestError("bindMediaVoyage.emptyMeta", nil)
	}
	voyage, err := uc.findVoyageByTime(ctx, data, dev)
	if err != nil {
		return nil, err
	}
	if voyage == nil {
		return nil, nil
	}
	uc.vRepo.UpdateVoyage(ctx, voyage.Id, map[string]any{"flight_id": data.FlightId})
	return voyage, nil
}

type MediaFileWithSignature struct {
	DroneModel     string
	IsOriginal     bool
	PayloadModel   string
	Signature      string
	ShortSignature string
	Name           string
}

func (uc *CloudUsecase) CheckDeviceMediaFile(ctx context.Context, f *MediaFileWithSignature) (bool, error) {
	list, err := uc.GetDeviceExistMediaFiles(ctx, []string{f.ShortSignature})
	if err != nil {
		return false, err
	}
	return len(list) > 0, err
}

func (uc *CloudUsecase) GetDeviceExistMediaFiles(ctx context.Context, signatures []string) ([]*Media, error) {
	av, err := uc.aRepo.GetCurrentAuthValue(ctx)
	if err != nil {
		return nil, err
	}
	if av.SN == "" {
		return nil, errors.Forbidden("GetDeviceExistMediaFiles.noSN", "无此操作权限")
	}
	dev, err := uc.dRepo.CheckDevice(ctx, &DeviceCheck{
		SourceSn: av.SN,
	})
	if err != nil {
		return nil, err
	}
	now := time.Now()
	media, err := uc.mRepo.GetDeviceMedia(ctx, &DeviceMediaListQuery{
		BaseListQuery: BaseListQuery{
			Page: 1,
			Size: int32(len(signatures)),
			TimeScope: &TimeScope{
				StartTime: now.Add(-30 * 24 * time.Hour),
				EndTime:   now,
			},
		},
		Dev:             dev,
		ShortSignatures: signatures,
	})
	if err != nil {
		return nil, err
	}
	return media, nil
}
