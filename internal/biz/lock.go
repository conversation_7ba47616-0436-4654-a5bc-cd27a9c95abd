package biz

import (
	"context"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"gitlab.sensoro.com/go-sensoro/lins-common/utilities"
)

type Lock struct {
	Name string
	TTL  time.Duration
	id   int64
}

func (l *Lock) GetLockId() string {
	return strconv.FormatInt(l.id, 36)
}

func NewLock(name string, ttl time.Duration) *Lock {
	return &Lock{
		Name: name,
		TTL:  ttl,
		id:   utilities.MustNextID(),
	}
}

var ErrLockFailed = errors.BadRequest("tryLockFailed", "未能成功获取锁")

type LockRepo interface {
	TryLock(ctx context.Context, l *Lock) error
	Release(ctx context.Context, l *Lock) error
}
