package server

import (
	"strings"

	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.sensoro.com/go-sensoro/lins-common/kafka"
)

// NewSkaiKafkaServer new a kafka server.
func NewSkaiKafkaServer(
	logger log.Logger,
	c *conf.Data,
	linsConsumer *service.LinsConsumer,
) *kafka.Server {
	var log = log.NewHelper(logger)
	log.Infow("skaiKafkaServer", c.Kafka.GetGroupId())
	var opts = []kafka.ServerOption{
		kafka.Brokers(strings.Split(c.Kafka.GetBrokers(), ",")),
		kafka.GroupId(c.Kafka.GetGroupId()),
		kafka.HandleDeadMessage(func(d kafka.DeadMessage) error {
			log.Errorw(
				"kind", "linsConsumer",
				"type", "deadMessage",
				"topic", d.Message().Topic,
				"msg", string(d.Message().Value),
				"error", d.Error(),
			)
			return nil
		}),
		kafka.HandleErr(func(e error) {
			log.Error(
				"kind", "linsConsumer",
				"type", "runError",
				"error", e.Error(),
			)
		}),
		kafka.Logger(logger),
		kafka.ConsumeConcurrency(8),
	}

	srv := kafka.NewServer(opts...)

	srv.AddConsumer(linsConsumer)

	return srv
}
