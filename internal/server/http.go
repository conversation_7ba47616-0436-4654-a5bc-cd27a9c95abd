package server

import (
	adminV1 "gitlab.sensoro.com/skai/skai/api/admin/v1"
	airlineV1 "gitlab.sensoro.com/skai/skai/api/airlines/v1"
	annotationV1 "gitlab.sensoro.com/skai/skai/api/annotations/v1"
	cloudV1 "gitlab.sensoro.com/skai/skai/api/cloud/v1"
	configV1 "gitlab.sensoro.com/skai/skai/api/configs/v1"
	deviceV1 "gitlab.sensoro.com/skai/skai/api/devices/v1"
	mediaV1 "gitlab.sensoro.com/skai/skai/api/media/v1"
	missionV1 "gitlab.sensoro.com/skai/skai/api/missions/v1"
	onemapV1 "gitlab.sensoro.com/skai/skai/api/onemap/v1"
	sessionV1 "gitlab.sensoro.com/skai/skai/api/session/v1"
	subjectV1 "gitlab.sensoro.com/skai/skai/api/subjects/v1"
	voyageV1 "gitlab.sensoro.com/skai/skai/api/voyages/v1"
	waypointV1 "gitlab.sensoro.com/skai/skai/api/waypoints/v1"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	mw "gitlab.sensoro.com/skai/skai/internal/server/middleware"
	"gitlab.sensoro.com/skai/skai/internal/service"

	kmetrics "github.com/go-kratos/prometheus/metrics"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.opentelemetry.io/otel/trace"
)

// NewHTTPServer new a HTTP server.
func NewHTTPServer(
	logger log.Logger,
	tracer trace.TracerProvider,
	c *conf.Server,
	adminService *service.AdminService,
	authService *service.AuthService,
	airlineService *service.AirlineService,
	deviceService *service.DeviceService,
	voyageService *service.VoyageService,
	onemapService *service.OnemapService,
	sessionService *service.SessionService,
	waypointService *service.WaypointService,
	mediaService *service.MediaService,
	storageService *service.StorageService,
	cloudService *service.CloudService,
	configService *service.ConfigService,
	galleryService *service.GalleryService,
	missionService *service.MissionService,
	operationService *service.DeviceOperationService,
	interLiveService *service.InternalLiveService,
	subjectService *service.SubjectService,
	annotationService *service.AnnotationService,
) *http.Server {
	counter := prometheus.NewCounterVec(prometheus.CounterOpts{Name: "requests_total"}, []string{"method", "path", "code", "some"})

	requestHistogramVec := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "skai_api_http_request_histogram",
		Buckets: []float64{10.0, 50.0, 100.0, 200.0, 400.0, 1000.0, 3000.0, 5000.0},
	}, []string{"method", "uri"})

	requestSummaryMetric := promauto.NewSummaryVec(prometheus.SummaryOpts{
		Name:       "skai_api_http_request_summary",
		Help:       "collect the metric for each api request devided by method, path and statusCode with the type of summary",
		Objectives: map[float64]float64{0.5: 0.05, 0.8: 0.02, 0.95: 0.005, 0.99: 0.001},
	}, []string{"method", "uri"})
	mws := []middleware.Middleware{
		recovery.Recovery(),
	}
	if tracer != nil {
		mws = append(mws, tracing.Server(tracing.WithTracerProvider(tracer)), metrics.Server(metrics.WithRequests(kmetrics.NewCounter(counter)), metrics.WithSeconds(kmetrics.NewSummary(requestSummaryMetric)), metrics.WithSeconds(kmetrics.NewHistogram(requestHistogramVec))))
	}
	mws = append(mws,
		logging.Server(logger),
		validate.Validator(),
		mw.NewAuthMiddleware(logger, c, authService),
		mw.NewCloudAuthMw(logger, c, authService),
	)
	var opts = []http.ServerOption{
		http.Middleware(
			mws...,
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}

	srv := http.NewServer(opts...)

	adminV1.RegisterAdminHTTPServer(srv, adminService)
	airlineV1.RegisterAirlineHTTPServer(srv, airlineService)
	deviceV1.RegisterDeviceHTTPServer(srv, deviceService)
	voyageV1.RegisterVoyageHTTPServer(srv, voyageService)
	onemapV1.RegisterOnemapHTTPServer(srv, onemapService)
	sessionV1.RegisterSessionHTTPServer(srv, sessionService)
	waypointV1.RegisterWaypointHTTPServer(srv, waypointService)
	mediaV1.RegisterMediaHTTPServer(srv, mediaService)
	mediaV1.RegisterGalleryHTTPServer(srv, galleryService)
	cloudV1.RegisterCloudHTTPServer(srv, cloudService)
	configV1.RegisterConfigHTTPServer(srv, configService)
	missionV1.RegisterMissionHTTPServer(srv, missionService)
	deviceV1.RegisterDeviceOperationHTTPServer(srv, operationService)
	mediaV1.RegisterInternalLiveHTTPServer(srv, interLiveService)
	subjectV1.RegisterSubjectHTTPServer(srv, subjectService)
	annotationV1.RegisterAnnotationHTTPServer(srv, annotationService)

	srv.HandleFunc("/metrics", promhttp.Handler().ServeHTTP)
	srv.HandleFunc("/api/v1/export/voyages", mw.NewHttpHandlerFuncWithAuthValue(logger, c, authService, voyageService.NewExportVoyageListHandlerFunc()))
	srv.HandleFunc("/api/v1/export/events", mw.NewHttpHandlerFuncWithAuthValue(logger, c, authService, deviceService.NewExportDeviceEventsHandlerFunc()))
	srv.HandleFunc("/api/v1/export/datalogs", mw.NewHttpHandlerFuncWithAuthValue(logger, c, authService, deviceService.NewExportDeviceDatalogsHandlerFunc()))
	srv.HandleFunc(biz.PublicFileURIPrefix, mw.NewHttpHandlerFuncWithAuthValue(logger, c, authService, storageService.NewPublicFileHandlerFunc()))
	srv.HandlePrefix(biz.PublicFileURIPrefix+"/", storageService.NewPublicFileHandlerFunc())
	srv.HandlePrefix(biz.M3U8FileProxyURIPrefix, storageService.NewM3U8ProxyHandlerFunc())
	srv.HandleFunc("/api/v1/cloud/media/workspaces/{id}/files/tiny-fingerprints", mw.NewCustomHttpHandlerFuncWithAuthValue(&mw.CustomHttpWrapOption{
		Logger:    logger,
		Conf:      c,
		Auth:      authService,
		TokenFunc: mw.DJICloudHeaderTokenFunc,
	}, cloudService.NewExistMediaFilesHandler()))
	return srv
}
