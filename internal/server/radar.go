package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/pkg/radar"
)

func NewRadarSubServer(
	logger log.Logger,
	c *conf.Data,
) *radar.Subscriber {
	redisConf := &redis.UniversalOptions{
		Addrs:        []string{c.Redis.Addr},
		Password:     c.Redis.Password,
		DB:           int(c.Redis.Db),
		ReadTimeout:  c.Redis.ReadTimeout.AsDuration(),
		DialTimeout:  c.Redis.DialTimeout.AsDuration(),
		WriteTimeout: c.Redis.WriteTimeout.AsDuration(),
	}
	s, err := radar.NewRadarSubscriber(logger, redisConf)
	if err != nil {
		logger.Log(log.LevelFatal, "createRadarErr", err)
		panic(err)
	}
	return s
}
