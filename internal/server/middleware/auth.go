package mw

import (
	"context"
	"net/http"

	"gitlab.sensoro.com/skai/skai/internal/biz"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/service"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	kratosMW "github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/golang-jwt/jwt/v4"
	"github.com/samber/lo"
	"github.com/tidwall/conv"
	"gitlab.sensoro.com/go-sensoro/lins-common/client"
	"gitlab.sensoro.com/go-sensoro/lins-common/middleware"
)

var ErrTokenInvalid = errors.Unauthorized("UNAUTHORIZED", "Token is invalid")

func NewJWTKeyFunc(c *conf.Server_JWT) jwt.Keyfunc {
	key := []byte(c.Secret)
	return func(token *jwt.Token) (interface{}, error) {
		if token.Method.Alg() != c.Algorithm {
			return nil, jwt.ErrHashUnavailable
		}
		return key, nil
	}
}

func getStringEncodedInt64Value(claims jwt.MapClaims, key string) (int64, error) {
	if v, ok := claims[key]; ok {
		if v, ok := v.(string); ok {
			return conv.Atoi(v), nil
		}
	}
	return 0, ErrTokenInvalid
}

func NewSkaiAuthValue(claims jwt.MapClaims, header transport.Header) (*biz.SkaiAuthValue, error) {
	var accountId, avatarId, tenantId, projectId int64
	if v, err := getStringEncodedInt64Value(claims, "accountId"); err == nil {
		accountId = v
	} else {
		return nil, err
	}
	if v, err := getStringEncodedInt64Value(claims, "avatarId"); err == nil {
		avatarId = v
	} else if uid, err := getStringEncodedInt64Value(claims, "userId"); err == nil {
		// 兼容V1
		avatarId = uid
	} else {
		return nil, err
	}
	if v, err := getStringEncodedInt64Value(claims, "tenantId"); err == nil {
		tenantId = v
	}
	if v := header.Get("x-lins-projectid"); v != "" {
		projectId = conv.Atoi(v)
	}
	sn := ""
	if v, ok := claims["sn"].(string); ok {
		sn = v
	}
	return &biz.SkaiAuthValue{
		AvatarId:  avatarId,
		TenantId:  tenantId,
		ProjectId: projectId,
		AccountId: accountId,
		Token:     header.Get("Authorization"),
		Nickname:  claims["nickname"].(string),
		Username:  claims["username"].(string),
		SN:        sn,
	}, nil
}

func NewAuthValueFunc(log *log.Helper, as *service.AuthService) middleware.NewCtxAuthValueFunc {
	return func(ctx context.Context, claims jwt.MapClaims, header transport.Header) (interface{}, error) {
		v, err := NewSkaiAuthValue(claims, header)
		if err != nil {
			return nil, err
		}
		if merchants, err := as.GetProjectMerchants(ctx, v.ProjectId); err == nil {
			v.Merchants = merchants
			merchant := &biz.Merchant{}
			if len(merchants) > 0 {
				merchant = lo.FindOrElse(merchants, merchants[0], func(item *client.Merchant) bool {
					return item.IsProjectDefaultMerchant == 1
				})
			}
			v.DefaultMerchant = merchant
		} else {
			log.Warn("get project merchants error: ", err)
			return nil, err
		}
		return v, nil
	}
}

func NewAuthMiddleware(loggger log.Logger, c *conf.Server, as *service.AuthService) kratosMW.Middleware {
	//  不检验token的Operation白名单
	wl := map[string]struct{}{
		"api.cloud.v1.Cloud":                              {},
		"api.connects.v1.Connect":                         {},
		"api.session.v1.Session/Login":                    {},
		"api.devices.v1.Device/ExecuteDevice":             {},
		"api.devices.v1.Device/CallbackDevice":            {},
		"api.missions.v1.Mission/StatusMission":           {},
		"api.media.v1.Gallery/CleanUpTmpDownloadableFile": {},
		"api.media.v1.Media/StartLiveCallback":            {},
		"api.media.v1.InternalLive":                       {},
		"api.cloud.v1.AiEvent":                            {},
	}
	return middleware.NewAuthMiddleware(NewJWTKeyFunc(c.Jwt), NewAuthValueFunc(log.NewHelper(loggger), as), wl)
}

func getAuthToken(header http.Header) (string, bool) {
	bearerToken := header.Get("Authorization")
	if len(bearerToken) > 7 && bearerToken[0:7] == "Bearer " {
		return bearerToken[7:], true
	}
	return "", false
}

type headerCarrier http.Header

// Get returns the value associated with the passed key.
func (hc headerCarrier) Get(key string) string {
	return http.Header(hc).Get(key)
}

// Set stores the key-value pair.
func (hc headerCarrier) Set(key string, value string) {
	http.Header(hc).Set(key, value)
}

// Keys lists the keys stored in this carrier.
func (hc headerCarrier) Keys() []string {
	keys := make([]string, 0, len(hc))
	for k := range http.Header(hc) {
		keys = append(keys, k)
	}
	return keys
}

// Add append value to key-values pair.
func (hc headerCarrier) Add(key string, value string) {
	http.Header(hc).Add(key, value)
}
func (hc headerCarrier) Values(key string) []string {
	return http.Header(hc).Values(key)
}

func newHeader(h http.Header) transport.Header {
	return headerCarrier(h)
}

type CustomHttpWrapOption struct {
	Logger    log.Logger
	Conf      *conf.Server
	Auth      *service.AuthService
	TokenFunc func(http.Header) (string, bool)
}

func NewCustomHttpHandlerFuncWithAuthValue(opt *CustomHttpWrapOption, hf http.HandlerFunc) http.HandlerFunc {
	kf := NewJWTKeyFunc(opt.Conf.Jwt)
	log := log.NewHelper(opt.Logger)
	avFunc := NewAuthValueFunc(log, opt.Auth)
	tf := opt.TokenFunc
	return func(w http.ResponseWriter, r *http.Request) {
		claims := jwt.MapClaims{}
		tokenStr, ok := tf(r.Header)
		if !ok {
			service.ReponseHttpErr(middleware.ErrMissingJwtToken, w)
			return
		}
		token, err := jwt.ParseWithClaims(tokenStr, claims, kf)
		if err != nil {
			service.ReponseHttpErr(middleware.ErrTokenParseFail, w)
			return
		}
		if !token.Valid {
			service.ReponseHttpErr(middleware.ErrTokenInvalid, w)
			return
		}
		av, err := avFunc(r.Context(), claims, newHeader(r.Header))
		if err != nil {
			service.ReponseHttpErr(err, w)
			return
		}
		ctx := middleware.SetAuthValue(r.Context(), av)
		hf(w, r.WithContext(ctx))
	}
}

func NewHttpHandlerFuncWithAuthValue(loggger log.Logger, c *conf.Server, as *service.AuthService, hf http.HandlerFunc) http.HandlerFunc {
	return NewCustomHttpHandlerFuncWithAuthValue(&CustomHttpWrapOption{
		Logger:    loggger,
		Conf:      c,
		Auth:      as,
		TokenFunc: getAuthToken,
	}, hf)
}
