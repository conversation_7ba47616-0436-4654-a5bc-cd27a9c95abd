package mw

import (
	"context"
	"net/http"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/selector"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/golang-jwt/jwt/v4"
	lmw "gitlab.sensoro.com/go-sensoro/lins-common/middleware"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/service"
)

func NewCloudAuthMw(loggger log.Logger, c *conf.Server, as *service.AuthService) middleware.Middleware {
	return selector.Server(newCloudAuthMw(loggger, c, as)).Prefix("/api.cloud.v1.Cloud").Build()
}

func DJICloudHeaderTokenFunc(h http.Header) (string, bool) {
	t := h.Get("x-auth-token")
	return t, t != ""
}

func newCloudAuthMw(loggger log.Logger, c *conf.Server, as *service.AuthService) middleware.Middleware {
	kf := NewJWTKeyFunc(c.Jwt)
	log := log.NewHelper(loggger)
	avFunc := NewAuthValueFunc(log, as)
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			rpcRequest, ok := transport.FromServerContext(ctx)
			if !ok {
				return nil, lmw.ErrMissingJwtToken
			}
			header := rpcRequest.RequestHeader()
			tokenStr := header.Get("x-auth-token")
			if tokenStr == "" {
				return nil, errors.Unauthorized("CloudAuthMw.noToken", "Token is missing")
			}
			claims := jwt.MapClaims{}
			token, err := jwt.ParseWithClaims(tokenStr, claims, kf)
			if err != nil {
				return nil, lmw.ErrTokenParseFail
			}
			if !token.Valid {
				return nil, lmw.ErrTokenInvalid
			}
			av, err := avFunc(ctx, claims, header)
			if err != nil {
				return nil, err
			}
			ctx = lmw.SetAuthValue(ctx, av)
			return handler(ctx, req)
		}
	}
}
