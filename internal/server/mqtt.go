package server

import (
	"os"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/service"
	"gitlab.sensoro.com/skai/skai/pkg/mqtt"
)

var clientId = "skai-connect"

func NewMQTTSubServer(
	logger log.Logger,
	c *conf.Data,
	djiService *service.DJIConnectService,
) *mqtt.Subscriber {
	if strings.ToLower(os.Getenv("RUN_ENV")) != "edge" {
		clientId = uuid.NewString()
	}
	handlers := make([]mqtt.MessageHandler, 0)
	handlers = append(handlers, djiService.Handlers()...)
	s, err := mqtt.NewMQTTSubscriber(
		logger,
		mqtt.WithBroker(c.Mqtt.Url),
		mqtt.WithUsernamePassword("@sys:skai-connect", c.Mqtt.SuperToken),
		mqtt.WithClientId(clientId),
		mqtt.WithHandlers(handlers),
	)
	if err != nil {
		logger.Log(log.LevelFatal, "createMqttErr", err)
		panic(err)
	}

	return s
}
