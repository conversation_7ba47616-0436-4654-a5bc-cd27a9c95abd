package server

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"gitlab.sensoro.com/skai/skai/internal/conf"
	"gitlab.sensoro.com/skai/skai/internal/service"
)

type TaskServer struct {
	c   *conf.Server
	log *log.Helper
	ctx context.Context
	ats *service.ArchiveTaskService
}

func NewTaskServer(
	c *conf.Server,
	logger log.Logger,
	ats *service.ArchiveTaskService,
) *TaskServer {
	return &TaskServer{
		c:   c,
		log: log.NewHelper(logger),
		ats: ats,
	}
}

func (s *TaskServer) Start(ctx context.Context) error {
	s.ctx = ctx
	go s.run(ctx)
	return nil
}

func (s *TaskServer) Stop(ctx context.Context) error {
	return nil
}

func (s *TaskServer) run(ctx context.Context) {
	if s.c.ArchiveTask.Interval <= 0 {
		return
	}
	ticker := time.NewTicker(time.Duration(s.c.ArchiveTask.Interval) * time.Second)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			go s.runArchiveTask()
		}
	}
}

func (s *TaskServer) runArchiveTask() {
	if err, ok := lo.TryWithErrorValue(func() error {
		ctx, cancel := context.WithTimeout(s.ctx, time.Duration(s.c.ArchiveTask.Interval-60)*time.Second)
		defer cancel()
		return s.ats.RunNextTask(ctx, time.Duration(s.c.ArchiveTask.Interval)*time.Second)
	}); !ok {
		s.log.Errorf("run archive task error: %v", err)
	}
}
