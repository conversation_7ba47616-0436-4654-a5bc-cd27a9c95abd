globalConfig:
  ANTMAN_APP_GROUP: lingq
  ANTMAN_APP_NAME: skai
  ANTMAN_DOCKER_BUILDKIT: enabled
  ANTMAN_APP_TYPE: golang
  ANTMAN_APP_PORT: '8000'
deploy:
- name: skai-server
  command: ["/app/skai", "-conf", "/data/conf.yaml", "-mode", "server"]
  extra_tcp_port:
  - 9000
- name: skai-connect
  command: ["/app/skai", "-conf", "/data/conf.yaml", "-mode", "connect"]
  extra_tcp_port:
  - 9000
extra_service:
- deploy: skai-server
  service:
  - port: 9000
    protocol: TCP
    targetPort: 9000
    name: grpc
- deploy: skai-connect
  service:
  - port: 9000
    protocol: TCP
    targetPort: 9000
    name: grpc
ingress:
- type: web
  paths:
  - path: /api/skai
    deploy: skai-server
    middlewares:
      - name: middleware-cut-path-to-api-common
      - name: middleware-cors-common
      - name: middleware-compress-common
    xMiddlewares:
      - name: middleware-forwardauth-common
  # 存储接口
  - path: /api/skai/v1/storage
    deploy: skai-server
    middlewares:
      - name: middleware-cut-path-to-api-common
      - name: middleware-cors-common
  - path: /api/skai/v1/m3u8Storage
    deploy: skai-server
    middlewares:
      - name: middleware-cut-path-to-api-common
      - name: middleware-cors-common
  # 登录接口
  - path: /api/skai/v1/session/login
    deploy: skai-server
    middlewares:
      - name: middleware-cut-path-to-api-common
      - name: middleware-cors-common
      - name: middleware-compress-common
envFrom:
- secretRef:
    name: secret-skai-common
- secretRef:
    name: secret-kafka-common
- configMapRef:
    name: configmap-kafka-topic-common
env: []
